import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function globalTeardown() {
  console.log('🧹 Starting global teardown for Playwright tests...');

  try {
    // Clean up test data
    await prisma.adminUser.deleteMany({
      where: {
        email: {
          in: [
            '<EMAIL>',
            '<EMAIL>'
          ]
        }
      }
    });

    // Clean up test settings
    await prisma.setting.deleteMany({
      where: {
        key: {
          startsWith: 'test_'
        }
      }
    });

    await prisma.$disconnect();
    console.log('✅ Global teardown completed successfully');
  } catch (error) {
    console.error('❌ Global teardown failed:', error);
    throw error;
  }
}

export default globalTeardown;
