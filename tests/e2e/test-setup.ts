import { test as baseTest, Page } from "@playwright/test";
import { loginAsAdmin, loginAsModerator } from "./helpers/auth.helper";

// Extend base test với custom fixtures
export const test = baseTest.extend<{
  adminPage: Page;
  moderatorPage: Page;
}>({
  adminPage: async ({ page }, use) => {
    await loginAsAdmin(page);
    await use(page);
  },

  moderatorPage: async ({ page }, use) => {
    await loginAsModerator(page);
    await use(page);
  },
});

export { expect } from "@playwright/test";
