import { chromium, FullConfig } from "@playwright/test";
import { PrismaClient } from "@prisma/client";
import bcrypt from "bcryptjs";

const prisma = new PrismaClient();

async function globalSetup(config: FullConfig) {
  console.log("🚀 Starting global setup for Playwright tests...");

  try {
    // Clean up existing test data - handle foreign key constraints
    // First delete posts that reference admin users
    await prisma.post.deleteMany({
      where: {
        author: {
          email: {
            in: [
              "<EMAIL>",
              "<EMAIL>",
              "<EMAIL>",
              "<EMAIL>",
            ],
          },
        },
      },
    });

    // Then delete admin users
    await prisma.adminUser.deleteMany({
      where: {
        email: {
          in: [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
          ],
        },
      },
    });

    // Create test admin users
    const adminPassword = await bcrypt.hash("admin123", 12);
    const moderatorPassword = await bcrypt.hash("moderator123", 12);

    await prisma.adminUser.createMany({
      data: [
        {
          email: "<EMAIL>",
          name: "Admin User",
          password: adminPassword,
          role: "ADMIN",
          isActive: true,
          permissions: {
            manage_products: true,
            manage_orders: true,
            manage_categories: true,
            manage_users: true,
            view_analytics: true,
            manage_settings: true,
          },
        },
        {
          email: "<EMAIL>",
          name: "Moderator User",
          password: moderatorPassword,
          role: "MODERATOR",
          isActive: true,
          permissions: {
            manage_products: true,
            manage_orders: true,
            manage_categories: false,
            manage_users: false,
            view_analytics: true,
            manage_settings: false,
          },
        },
        {
          email: "<EMAIL>",
          name: "Test Admin",
          password: await bcrypt.hash("testpassword123", 12),
          role: "ADMIN",
          isActive: true,
        },
        {
          email: "<EMAIL>",
          name: "Test Moderator",
          password: await bcrypt.hash("testpassword123", 12),
          role: "MODERATOR",
          isActive: true,
        },
      ],
    });

    // Set up default settings for testing
    const defaultSettings = [
      { key: "siteName", value: "NS Shop Test", type: "string" },
      { key: "siteDescription", value: "Test description", type: "string" },
      { key: "siteUrl", value: "http://localhost:3000", type: "string" },
      { key: "contactEmail", value: "<EMAIL>", type: "string" },
      { key: "contactPhone", value: "0123456789", type: "string" },
    ];

    for (const setting of defaultSettings) {
      await prisma.setting.upsert({
        where: { key: setting.key },
        update: { value: setting.value },
        create: setting,
      });
    }

    console.log("✅ Global setup completed successfully");
  } catch (error) {
    console.error("❌ Global setup failed:", error);
    throw error;
  }
}

export default globalSetup;
