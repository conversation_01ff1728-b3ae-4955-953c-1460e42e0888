import { Page, expect, Locator } from "@playwright/test";

export interface PageData {
  title: string;
  content: string;
  excerpt?: string;
  slug?: string;
  status: "DRAFT" | "PUBLISHED" | "ARCHIVED";
  featured: boolean;
  featuredImage?: string;
  metaTitle?: string;
  metaDescription?: string;
}

/**
 * Page Object Model for Admin Pages Management
 * Handles pages list, create, edit, and delete operations
 */
export class AdminPagesPage {
  readonly page: Page;

  // List page elements
  readonly pageTitle: Locator;
  readonly createPageButton: Locator;
  readonly searchInput: Locator;
  readonly pagesTable: Locator;
  readonly tableRows: Locator;
  readonly loadingSpinner: Locator;
  readonly emptyState: Locator;
  readonly bulkDeleteButton: Locator;
  readonly selectAllCheckbox: Locator;

  // Form elements
  readonly titleInput: Locator;
  readonly contentEditor: Locator;
  readonly excerptTextarea: Locator;
  readonly slugInput: Locator;
  readonly statusSelect: Locator;
  readonly featuredToggle: Locator;
  readonly metaTitleInput: Locator;
  readonly metaDescriptionTextarea: Locator;
  readonly saveButton: Locator;
  readonly cancelButton: Locator;
  readonly previewButton: Locator;

  // Action buttons
  readonly editButton: Locator;
  readonly deleteButton: Locator;
  readonly featuredButton: Locator;

  // Modals and alerts
  readonly confirmDialog: Locator;
  readonly confirmDeleteButton: Locator;
  readonly cancelDeleteButton: Locator;
  readonly successToast: Locator;
  readonly errorToast: Locator;

  constructor(page: Page) {
    this.page = page;

    // List page elements
    this.pageTitle = page.locator("h1").filter({ hasText: "Quản lý Trang" });
    this.createPageButton = page
      .locator('a[href="/admin/pages/create"]')
      .first();
    this.searchInput = page.locator('input[placeholder="Tìm kiếm trang..."]');
    this.pagesTable = page.locator("table");
    this.tableRows = page.locator("tbody tr");
    this.loadingSpinner = page.locator(".animate-spin");
    this.emptyState = page.locator('text="Không có dữ liệu"');
    this.bulkDeleteButton = page
      .locator("button")
      .filter({ hasText: "Thao tác" });
    this.selectAllCheckbox = page.locator('thead input[type="checkbox"]');

    // Form elements
    this.titleInput = page.locator('input[id="title"]');
    this.contentEditor = page.locator(
      '.rich-text-editor, [data-testid="rich-text-editor"]'
    );
    this.excerptTextarea = page.locator('textarea[id="excerpt"]');
    this.slugInput = page.locator('input[id="slug"]');
    this.statusSelect = page.locator('[data-testid="status-select"]');
    this.featuredToggle = page.locator('button[id="featured"]');
    this.metaTitleInput = page.locator('input[id="metaTitle"]');
    this.metaDescriptionTextarea = page.locator(
      'textarea[id="metaDescription"]'
    );
    this.saveButton = page.locator('button[type="submit"]');
    this.cancelButton = page.locator("button").filter({ hasText: "Hủy" });
    this.previewButton = page
      .locator("button")
      .filter({ hasText: "Xem trước" });

    // Action buttons (these will be scoped to specific rows when needed)
    this.editButton = page
      .locator("button, a")
      .filter({ hasText: "Chỉnh sửa" });
    this.deleteButton = page.locator("button").filter({ hasText: "Xóa" });
    this.featuredButton = page.locator("button").filter({ hasText: /nổi bật/ });

    // Modals and alerts
    this.confirmDialog = page.locator(
      '[role="dialog"], .modal, [data-testid="confirm-dialog"]'
    );
    this.confirmDeleteButton = page
      .locator("button")
      .filter({ hasText: /Xác nhận|Xóa/ });
    this.cancelDeleteButton = page.locator("button").filter({ hasText: "Hủy" });
    this.successToast = page.locator(
      ".bg-green-50.border-green-200.text-green-800"
    );
    this.errorToast = page.locator(".bg-red-50.border-red-200.text-red-800");
  }

  /**
   * Navigate to pages list
   */
  async goto(): Promise<void> {
    await this.page.goto("/admin/pages");
  }

  /**
   * Wait for pages list to load
   */
  async waitForPageLoad(): Promise<void> {
    await this.page.waitForLoadState("networkidle");
    await expect(this.pageTitle).toBeVisible();
  }

  /**
   * Navigate to create page form
   */
  async goToCreatePage(): Promise<void> {
    await this.createPageButton.click();
    await this.page.waitForURL("/admin/pages/create");
    await this.waitForFormLoad();
  }

  /**
   * Wait for form to load
   */
  async waitForFormLoad(): Promise<void> {
    await this.page.waitForLoadState("networkidle");
    await expect(this.titleInput).toBeVisible();
    await expect(this.saveButton).toBeVisible();
  }

  /**
   * Fill page form with data
   */
  async fillPageForm(data: PageData): Promise<void> {
    // Fill basic information
    await this.titleInput.fill(data.title);

    // Handle content editor (could be rich text editor or textarea)
    if ((await this.contentEditor.count()) > 0) {
      await this.contentEditor.fill(data.content);
    }

    // Fill optional fields
    if (data.excerpt) {
      await this.excerptTextarea.fill(data.excerpt);
    }

    if (data.slug) {
      await this.slugInput.fill(data.slug);
    }

    // Set status
    if ((await this.statusSelect.count()) > 0) {
      await this.statusSelect.selectOption(data.status);
    }

    // Set featured status (Switch component)
    const currentState = await this.featuredToggle.getAttribute("data-state");
    const isCurrentlyChecked = currentState === "checked";

    if (data.featured && !isCurrentlyChecked) {
      await this.featuredToggle.click();
    } else if (!data.featured && isCurrentlyChecked) {
      await this.featuredToggle.click();
    }

    // Fill SEO fields
    if (data.metaTitle) {
      await this.metaTitleInput.fill(data.metaTitle);
    }

    if (data.metaDescription) {
      await this.metaDescriptionTextarea.fill(data.metaDescription);
    }
  }

  /**
   * Submit page form
   */
  async submitForm(): Promise<void> {
    await this.saveButton.click();
  }

  /**
   * Create a new page
   */
  async createPage(data: PageData): Promise<void> {
    await this.goToCreatePage();
    await this.fillPageForm(data);
    await this.submitForm();

    // Wait for success and redirect
    await this.expectSuccessToast();
    await this.page.waitForURL("/admin/pages");
  }

  /**
   * Search for pages
   */
  async searchPages(query: string): Promise<void> {
    await this.searchInput.fill(query);
    await this.page.keyboard.press("Enter");
    await this.waitForTableUpdate();
  }

  /**
   * Wait for table to update after search or filter
   */
  async waitForTableUpdate(): Promise<void> {
    await this.page.waitForTimeout(500); // Brief wait for debounce
    await this.page.waitForLoadState("networkidle");
  }

  /**
   * Get page row by title
   */
  getPageRow(title: string): Locator {
    return this.tableRows.filter({ hasText: title });
  }

  /**
   * Edit a page by title
   */
  async editPage(title: string): Promise<void> {
    const row = this.getPageRow(title);
    await row.locator(this.editButton).click();
    await this.page.waitForURL(/\/admin\/pages\/.*\/edit/);
    await this.waitForFormLoad();
  }

  /**
   * Delete a page by title
   */
  async deletePage(title: string): Promise<void> {
    const row = this.getPageRow(title);
    await row.locator(this.deleteButton).click();

    // Handle confirmation dialog
    await this.expectConfirmDialog();
    await this.confirmDeleteButton.click();

    await this.expectSuccessToast();
    await this.waitForTableUpdate();
  }

  /**
   * Toggle featured status of a page
   */
  async toggleFeatured(title: string): Promise<void> {
    const row = this.getPageRow(title);
    await row.locator(this.featuredButton).click();
    await this.expectSuccessToast();
    await this.waitForTableUpdate();
  }

  /**
   * Select page for bulk operations
   */
  async selectPage(title: string): Promise<void> {
    const row = this.getPageRow(title);
    await row.locator('input[type="checkbox"]').check();
  }

  /**
   * Select all pages
   */
  async selectAllPages(): Promise<void> {
    await this.selectAllCheckbox.check();
  }

  /**
   * Bulk delete selected pages
   */
  async bulkDeleteSelected(): Promise<void> {
    await this.bulkDeleteButton.click();
    await this.expectConfirmDialog();
    await this.confirmDeleteButton.click();
    await this.expectSuccessToast();
    await this.waitForTableUpdate();
  }

  /**
   * Get page count from table
   */
  async getPageCount(): Promise<number> {
    return await this.tableRows.count();
  }

  /**
   * Check if page exists in table
   */
  async pageExists(title: string): Promise<boolean> {
    return (await this.getPageRow(title).count()) > 0;
  }

  /**
   * Expect success toast to appear
   */
  async expectSuccessToast(): Promise<void> {
    await expect(this.successToast).toBeVisible({ timeout: 5000 });
  }

  /**
   * Expect error toast to appear
   */
  async expectErrorToast(): Promise<void> {
    await expect(this.errorToast).toBeVisible({ timeout: 5000 });
  }

  /**
   * Expect confirmation dialog to appear
   */
  async expectConfirmDialog(): Promise<void> {
    await expect(this.confirmDialog).toBeVisible();
  }

  /**
   * Cancel current operation
   */
  async cancel(): Promise<void> {
    await this.cancelButton.click();
  }

  /**
   * Get page data from form
   */
  async getFormData(): Promise<Partial<PageData>> {
    return {
      title: await this.titleInput.inputValue(),
      content: await this.contentEditor.inputValue(),
      excerpt: await this.excerptTextarea.inputValue(),
      slug: await this.slugInput.inputValue(),
      metaTitle: await this.metaTitleInput.inputValue(),
      metaDescription: await this.metaDescriptionTextarea.inputValue(),
    };
  }
}
