import { Page, expect, Locator } from "@playwright/test";

/**
 * Page Object Model for Admin Login Page
 * Handles admin authentication flow and login interactions
 */
export class AdminLoginPage {
  readonly page: Page;
  readonly emailInput: Locator;
  readonly passwordInput: Locator;
  readonly loginButton: Locator;
  readonly errorMessage: Locator;
  readonly loadingSpinner: Locator;
  readonly pageTitle: Locator;
  readonly demoCredentials: Locator;

  constructor(page: Page) {
    this.page = page;
    this.emailInput = page.locator('input[id="email"]');
    this.passwordInput = page.locator('input[id="password"]');
    this.loginButton = page.locator('button[type="submit"]');
    this.errorMessage = page.locator('[role="alert"], .text-red-500');
    this.loadingSpinner = page.locator('.animate-spin');
    this.pageTitle = page.locator('h1, .text-2xl').filter({ hasText: 'Admin Portal' });
    this.demoCredentials = page.locator('.text-muted-foreground').filter({ hasText: 'Tài khoản demo' });
  }

  /**
   * Navigate to admin login page
   */
  async goto(): Promise<void> {
    await this.page.goto('/admin/auth/signin');
  }

  /**
   * Wait for login page to load completely
   */
  async waitForPageLoad(): Promise<void> {
    await this.page.waitForLoadState('networkidle');
    await expect(this.pageTitle).toBeVisible();
    await expect(this.emailInput).toBeVisible();
    await expect(this.passwordInput).toBeVisible();
    await expect(this.loginButton).toBeVisible();
  }

  /**
   * Fill login form with credentials
   */
  async fillLoginForm(email: string, password: string): Promise<void> {
    await this.emailInput.fill(email);
    await this.passwordInput.fill(password);
  }

  /**
   * Submit login form
   */
  async submitLogin(): Promise<void> {
    await this.loginButton.click();
  }

  /**
   * Complete login process with credentials
   */
  async login(email: string, password: string): Promise<void> {
    await this.fillLoginForm(email, password);
    await this.submitLogin();
  }

  /**
   * Wait for login to complete and redirect to admin dashboard
   */
  async expectLoginSuccess(): Promise<void> {
    // Wait for redirect to admin dashboard
    await this.page.waitForURL('/admin', { 
      waitUntil: 'networkidle',
      timeout: 10000 
    });
    
    // Verify we're on admin dashboard
    await expect(this.page).toHaveURL('/admin');
    
    // Wait for admin layout to load
    await this.page.waitForSelector('[data-testid="admin-sidebar"], .admin-sidebar, nav', {
      state: 'visible',
      timeout: 5000
    });
  }

  /**
   * Expect login to fail with error message
   */
  async expectLoginFailure(expectedError?: string): Promise<void> {
    await expect(this.errorMessage).toBeVisible();
    
    if (expectedError) {
      await expect(this.errorMessage).toContainText(expectedError);
    }
    
    // Should still be on login page
    await expect(this.page).toHaveURL(/\/admin\/auth\/signin/);
  }

  /**
   * Check if login form is in loading state
   */
  async isLoading(): Promise<boolean> {
    return await this.loadingSpinner.isVisible();
  }

  /**
   * Wait for loading to complete
   */
  async waitForLoadingComplete(): Promise<void> {
    await expect(this.loadingSpinner).toBeHidden();
  }

  /**
   * Get demo credentials from the page
   */
  async getDemoCredentials(): Promise<{ admin: string; moderator: string }> {
    const demoText = await this.demoCredentials.textContent();
    
    // Extract admin and moderator credentials from demo text
    const adminMatch = demoText?.match(/Admin:\s*([^\s]+)\s*\/\s*([^\s]+)/);
    const moderatorMatch = demoText?.match(/Moderator:\s*([^\s]+)\s*\/\s*([^\s]+)/);
    
    return {
      admin: adminMatch ? `${adminMatch[1]}:${adminMatch[2]}` : '<EMAIL>:admin123',
      moderator: moderatorMatch ? `${moderatorMatch[1]}:${moderatorMatch[2]}` : '<EMAIL>:moderator123'
    };
  }

  /**
   * Login with admin demo credentials
   */
  async loginAsAdmin(): Promise<void> {
    await this.login('<EMAIL>', 'admin123');
    await this.expectLoginSuccess();
  }

  /**
   * Login with moderator demo credentials
   */
  async loginAsModerator(): Promise<void> {
    await this.login('<EMAIL>', 'moderator123');
    await this.expectLoginSuccess();
  }

  /**
   * Clear login form
   */
  async clearForm(): Promise<void> {
    await this.emailInput.clear();
    await this.passwordInput.clear();
  }

  /**
   * Check if form fields are empty
   */
  async isFormEmpty(): Promise<boolean> {
    const emailValue = await this.emailInput.inputValue();
    const passwordValue = await this.passwordInput.inputValue();
    return emailValue === '' && passwordValue === '';
  }

  /**
   * Check if login button is disabled
   */
  async isLoginButtonDisabled(): Promise<boolean> {
    return await this.loginButton.isDisabled();
  }

  /**
   * Verify page elements are present
   */
  async verifyPageElements(): Promise<void> {
    await expect(this.pageTitle).toBeVisible();
    await expect(this.emailInput).toBeVisible();
    await expect(this.passwordInput).toBeVisible();
    await expect(this.loginButton).toBeVisible();
    await expect(this.demoCredentials).toBeVisible();
  }
}
