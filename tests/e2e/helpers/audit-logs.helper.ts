/**
 * Audit Logs E2E Test Helper
 * Helper functions cho E2E testing của audit logs
 */

import { Page, expect } from "@playwright/test";

export interface AuditLogData {
  action: string;
  resource: string;
  resourceId?: string;
  description?: string;
  adminName?: string;
  ipAddress?: string;
}

export class AuditLogsHelper {
  constructor(private page: Page) {}

  /**
   * Navigate to audit logs page
   */
  async goto() {
    await this.page.goto("/admin/audit-logs");
    await this.waitForPageLoad();
  }

  /**
   * Wait for audit logs page to load
   */
  async waitForPageLoad() {
    await this.page.waitForSelector('[data-testid="audit-logs-page"]', {
      timeout: 10000,
    });
    await this.page.waitForLoadState("networkidle");
  }

  /**
   * Get audit logs table rows
   */
  async getAuditLogRows() {
    return this.page.locator('[data-testid="audit-log-row"]');
  }

  /**
   * Get audit logs count
   */
  async getAuditLogsCount(): Promise<number> {
    const rows = await this.getAuditLogRows();
    return await rows.count();
  }

  /**
   * Check if audit log exists by description
   */
  async auditLogExists(description: string): Promise<boolean> {
    try {
      await this.page.waitForSelector(`text=${description}`, { timeout: 5000 });
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Filter audit logs by action
   */
  async filterByAction(action: string) {
    await this.page.selectOption('[data-testid="action-filter"]', action);
    await this.page.waitForLoadState("networkidle");
  }

  /**
   * Filter audit logs by resource
   */
  async filterByResource(resource: string) {
    await this.page.selectOption('[data-testid="resource-filter"]', resource);
    await this.page.waitForLoadState("networkidle");
  }

  /**
   * Filter audit logs by admin
   */
  async filterByAdmin(adminId: string) {
    await this.page.selectOption('[data-testid="admin-filter"]', adminId);
    await this.page.waitForLoadState("networkidle");
  }

  /**
   * Set date range filter
   */
  async setDateRange(startDate: string, endDate: string) {
    await this.page.fill('[data-testid="start-date-input"]', startDate);
    await this.page.fill('[data-testid="end-date-input"]', endDate);
    await this.page.click('[data-testid="apply-date-filter"]');
    await this.page.waitForLoadState("networkidle");
  }

  /**
   * Search audit logs
   */
  async search(query: string) {
    await this.page.fill('[data-testid="search-input"]', query);
    await this.page.press('[data-testid="search-input"]', "Enter");
    await this.page.waitForLoadState("networkidle");
  }

  /**
   * Clear all filters
   */
  async clearFilters() {
    await this.page.click('[data-testid="clear-filters-btn"]');
    await this.page.waitForLoadState("networkidle");
  }

  /**
   * Click on audit log row to view details
   */
  async viewAuditLogDetails(index: number = 0) {
    const rows = await this.getAuditLogRows();
    await rows.nth(index).click();
    await this.page.waitForSelector('[data-testid="audit-log-detail-modal"]');
  }

  /**
   * Close audit log detail modal
   */
  async closeDetailModal() {
    await this.page.click('[data-testid="close-detail-modal"]');
    await this.page.waitForSelector('[data-testid="audit-log-detail-modal"]', {
      state: "hidden",
    });
  }

  /**
   * Export audit logs
   */
  async exportAuditLogs(format: "csv" | "excel" = "csv") {
    await this.page.click('[data-testid="export-btn"]');
    await this.page.selectOption('[data-testid="export-format"]', format);

    // Start download
    const downloadPromise = this.page.waitForEvent("download");
    await this.page.click('[data-testid="confirm-export-btn"]');
    const download = await downloadPromise;

    return download;
  }

  /**
   * Go to next page
   */
  async goToNextPage() {
    await this.page.click('[data-testid="next-page-btn"]');
    await this.page.waitForLoadState("networkidle");
  }

  /**
   * Go to previous page
   */
  async goToPreviousPage() {
    await this.page.click('[data-testid="prev-page-btn"]');
    await this.page.waitForLoadState("networkidle");
  }

  /**
   * Change page size
   */
  async changePageSize(size: number) {
    await this.page.selectOption(
      '[data-testid="page-size-select"]',
      size.toString()
    );
    await this.page.waitForLoadState("networkidle");
  }

  /**
   * Get pagination info
   */
  async getPaginationInfo() {
    const text = await this.page.textContent('[data-testid="pagination-info"]');
    return text || "";
  }

  /**
   * Verify audit log data in table
   */
  async verifyAuditLogInTable(data: AuditLogData, rowIndex: number = 0) {
    const rows = await this.getAuditLogRows();
    const row = rows.nth(rowIndex);

    if (data.action) {
      await expect(row.locator('[data-testid="audit-action"]')).toContainText(
        data.action
      );
    }
    if (data.resource) {
      await expect(row.locator('[data-testid="audit-resource"]')).toContainText(
        data.resource
      );
    }
    if (data.description) {
      await expect(
        row.locator('[data-testid="audit-description"]')
      ).toContainText(data.description);
    }
    if (data.adminName) {
      await expect(row.locator('[data-testid="audit-admin"]')).toContainText(
        data.adminName
      );
    }
  }

  /**
   * Verify audit log details in modal
   */
  async verifyAuditLogDetails(data: AuditLogData) {
    const modal = this.page.locator('[data-testid="audit-log-detail-modal"]');

    if (data.action) {
      await expect(
        modal.locator('[data-testid="detail-action"]')
      ).toContainText(data.action);
    }
    if (data.resource) {
      await expect(
        modal.locator('[data-testid="detail-resource"]')
      ).toContainText(data.resource);
    }
    if (data.resourceId) {
      await expect(
        modal.locator('[data-testid="detail-resource-id"]')
      ).toContainText(data.resourceId);
    }
    if (data.description) {
      await expect(
        modal.locator('[data-testid="detail-description"]')
      ).toContainText(data.description);
    }
    if (data.ipAddress) {
      await expect(
        modal.locator('[data-testid="detail-ip-address"]')
      ).toContainText(data.ipAddress);
    }
  }

  /**
   * Wait for audit log to appear (useful for testing real-time updates)
   */
  async waitForAuditLog(description: string, timeout: number = 10000) {
    await this.page.waitForSelector(`text=${description}`, { timeout });
  }

  /**
   * Refresh audit logs
   */
  async refresh() {
    await this.page.click('[data-testid="refresh-btn"]');
    await this.page.waitForLoadState("networkidle");
  }

  /**
   * Verify empty state
   */
  async expectEmptyState() {
    await expect(
      this.page.locator('[data-testid="empty-state"]')
    ).toBeVisible();
  }

  /**
   * Verify loading state
   */
  async expectLoadingState() {
    await expect(
      this.page.locator('[data-testid="loading-spinner"]')
    ).toBeVisible();
  }

  /**
   * Verify error state
   */
  async expectErrorState() {
    await expect(
      this.page.locator('[data-testid="error-message"]')
    ).toBeVisible();
  }

  /**
   * Verify success toast message
   */
  async expectSuccessToast(message?: string) {
    const toast = this.page.locator('[data-testid="success-toast"]');
    await expect(toast).toBeVisible();
    if (message) {
      await expect(toast).toContainText(message);
    }
  }

  /**
   * Verify error toast message
   */
  async expectErrorToast(message?: string) {
    const toast = this.page.locator('[data-testid="error-toast"]');
    await expect(toast).toBeVisible();
    if (message) {
      await expect(toast).toContainText(message);
    }
  }

  /**
   * Sort audit logs by column
   */
  async sortBy(
    column: "createdAt" | "action" | "resource" | "admin",
    order: "asc" | "desc" = "desc"
  ) {
    await this.page.click(`[data-testid="sort-${column}"]`);

    // Click again if we need ascending order and it's currently descending
    const currentOrder = await this.page.getAttribute(
      `[data-testid="sort-${column}"]`,
      "data-sort-order"
    );
    if (order === "asc" && currentOrder === "desc") {
      await this.page.click(`[data-testid="sort-${column}"]`);
    }

    await this.page.waitForLoadState("networkidle");
  }

  /**
   * Verify sort order
   */
  async verifySortOrder(column: string, order: "asc" | "desc") {
    const sortButton = this.page.locator(`[data-testid="sort-${column}"]`);
    await expect(sortButton).toHaveAttribute("data-sort-order", order);
  }
}
