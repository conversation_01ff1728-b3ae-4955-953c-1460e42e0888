import { Page, expect } from '@playwright/test';

/**
 * Đăng nhập admin
 */
export async function adminLogin(page: Page) {
  await page.goto('/admin/login');
  
  // Điền thông tin đăng nhập admin
  await page.fill('[data-testid="email-input"]', '<EMAIL>');
  await page.fill('[data-testid="password-input"]', 'admin123');
  
  // Click đăng nhập
  await page.click('[data-testid="login-button"]');
  
  // Đ<PERSON>i chuyển hướng đến dashboard
  await page.waitForURL('/admin/dashboard');
  await page.waitForLoadState('networkidle');
}

/**
 * Tạo menu test
 */
export async function createTestMenu(page: Page, menuData: {
  name: string;
  location: string;
  description?: string;
  isActive?: boolean;
}) {
  // Gọi API để tạo menu
  const response = await page.request.post('/api/admin/menus', {
    data: {
      name: menuData.name,
      location: menuData.location,
      description: menuData.description || '',
      isActive: menuData.isActive ?? true,
    },
    headers: {
      'Content-Type': 'application/json',
    },
  });
  
  expect(response.ok()).toBeTruthy();
  const result = await response.json();
  return result.data;
}

/**
 * Tạo menu item test
 */
export async function createTestMenuItem(page: Page, menuId: string, itemData: {
  title: string;
  type: string;
  url?: string;
  order?: number;
  isActive?: boolean;
  parentId?: string;
}) {
  const response = await page.request.post(`/api/admin/menus/${menuId}/items`, {
    data: {
      title: itemData.title,
      type: itemData.type,
      url: itemData.url || '',
      order: itemData.order || 1,
      isActive: itemData.isActive ?? true,
      parentId: itemData.parentId || null,
    },
    headers: {
      'Content-Type': 'application/json',
    },
  });
  
  expect(response.ok()).toBeTruthy();
  const result = await response.json();
  return result.data;
}

/**
 * Xóa menu test
 */
export async function deleteTestMenu(page: Page, menuId: string) {
  const response = await page.request.delete(`/api/admin/menus/${menuId}`, {
    headers: {
      'Content-Type': 'application/json',
    },
  });
  
  expect(response.ok()).toBeTruthy();
}

/**
 * Dọn dẹp dữ liệu test
 */
export async function cleanupTestData(page: Page, type: 'menus' | 'products' | 'categories' | 'users') {
  try {
    switch (type) {
      case 'menus':
        // Lấy danh sách menu test
        const menusResponse = await page.request.get('/api/admin/menus?search=Test');
        if (menusResponse.ok()) {
          const menusData = await menusResponse.json();
          const testMenus = menusData.data.filter((menu: any) => 
            menu.name.includes('Test') || menu.name.includes('test')
          );
          
          // Xóa từng menu test
          for (const menu of testMenus) {
            await deleteTestMenu(page, menu.id);
          }
        }
        break;
        
      // Có thể thêm các case khác cho products, categories, users...
      default:
        console.log(`Cleanup for ${type} not implemented yet`);
    }
  } catch (error) {
    console.error(`Error cleaning up ${type}:`, error);
  }
}

/**
 * Đợi toast notification xuất hiện
 */
export async function waitForToast(page: Page, message: string, timeout = 5000) {
  await expect(page.locator(`.sonner-toast:has-text("${message}")`)).toBeVisible({ timeout });
}

/**
 * Đợi dialog mở
 */
export async function waitForDialog(page: Page, title: string, timeout = 5000) {
  await expect(page.locator(`[role="dialog"]:has-text("${title}")`)).toBeVisible({ timeout });
}

/**
 * Đóng dialog
 */
export async function closeDialog(page: Page) {
  // Thử click nút đóng hoặc nút hủy
  const closeButton = page.locator('button:has-text("Đóng"), button:has-text("Hủy")').first();
  if (await closeButton.isVisible()) {
    await closeButton.click();
  } else {
    // Nếu không có nút, thử nhấn Escape
    await page.keyboard.press('Escape');
  }
}

/**
 * Điền form tạo menu
 */
export async function fillMenuForm(page: Page, menuData: {
  name: string;
  location: string;
  description?: string;
  isActive?: boolean;
}, isEdit = false) {
  const prefix = isEdit ? 'edit-' : '';
  
  await page.fill(`#${prefix}name`, menuData.name);
  
  // Chọn location
  const locationSelect = page.locator(`[data-testid="${prefix}location-select"]`);
  if (await locationSelect.isVisible()) {
    await locationSelect.selectOption(menuData.location);
  } else {
    // Fallback cho Select component
    await page.click(`#${prefix}location`);
    await page.click(`[data-value="${menuData.location}"]`);
  }
  
  if (menuData.description) {
    await page.fill(`#${prefix}description`, menuData.description);
  }
  
  if (menuData.isActive !== undefined) {
    const checkbox = page.locator(`#${prefix}isActive`);
    const isChecked = await checkbox.isChecked();
    if (isChecked !== menuData.isActive) {
      await checkbox.click();
    }
  }
}

/**
 * Kiểm tra menu trong danh sách
 */
export async function verifyMenuInList(page: Page, menuData: {
  name: string;
  location?: string;
  isActive?: boolean;
}) {
  const menuRow = page.locator(`tr:has-text("${menuData.name}")`);
  await expect(menuRow).toBeVisible();
  
  if (menuData.location) {
    const locationLabels = {
      header: 'Header Navigation',
      footer: 'Footer Navigation',
      sidebar: 'Sidebar Navigation',
      mobile: 'Mobile Navigation',
    };
    const locationLabel = locationLabels[menuData.location as keyof typeof locationLabels] || menuData.location;
    await expect(menuRow.locator(`td:has-text("${locationLabel}")`)).toBeVisible();
  }
  
  if (menuData.isActive !== undefined) {
    const statusText = menuData.isActive ? 'Hoạt động' : 'Tạm dừng';
    await expect(menuRow.locator(`td:has-text("${statusText}")`)).toBeVisible();
  }
}

/**
 * Click action menu item
 */
export async function clickMenuAction(page: Page, menuName: string, action: 'view' | 'edit' | 'delete') {
  const menuRow = page.locator(`tr:has-text("${menuName}")`);
  await menuRow.locator('button[data-testid="actions-menu"]').click();
  
  const actionTexts = {
    view: 'Xem chi tiết',
    edit: 'Chỉnh sửa',
    delete: 'Xóa',
  };
  
  await page.click(`button:has-text("${actionTexts[action]}")`);
}

/**
 * Kiểm tra menu detail dialog
 */
export async function verifyMenuDetailDialog(page: Page, menuData: {
  name: string;
  location: string;
  description?: string;
  isActive?: boolean;
  itemsCount?: number;
}) {
  await waitForDialog(page, `Chi tiết Menu: ${menuData.name}`);
  
  // Kiểm tra thông tin cơ bản
  await expect(page.locator(`text=${menuData.name}`)).toBeVisible();
  
  const locationLabels = {
    header: 'Header Navigation',
    footer: 'Footer Navigation',
    sidebar: 'Sidebar Navigation',
    mobile: 'Mobile Navigation',
  };
  const locationLabel = locationLabels[menuData.location as keyof typeof locationLabels] || menuData.location;
  await expect(page.locator(`text=${locationLabel}`)).toBeVisible();
  
  if (menuData.description) {
    await expect(page.locator(`text=${menuData.description}`)).toBeVisible();
  }
  
  if (menuData.isActive !== undefined) {
    const statusText = menuData.isActive ? 'Hoạt động' : 'Tạm dừng';
    await expect(page.locator(`text=${statusText}`)).toBeVisible();
  }
  
  if (menuData.itemsCount !== undefined) {
    await expect(page.locator(`text=${menuData.itemsCount} mục`)).toBeVisible();
  }
  
  // Kiểm tra các thành phần UI
  await expect(page.locator('text=Cấu trúc Menu')).toBeVisible();
  await expect(page.locator('button:has-text("Mở rộng tất cả")')).toBeVisible();
  await expect(page.locator('button:has-text("Đóng")')).toBeVisible();
}
