/**
 * MSW Server Setup
 * Thiết lập Mock Service Worker cho testing environment
 */

import { setupServer } from "msw/node";
import { handlers } from "./handlers";

// Setup MSW server với các handlers đã định nghĩa
export const server = setupServer(...handlers);

// Thiết lập server lifecycle cho Jest
beforeAll(() => {
  // Khởi động server trước khi chạy tests
  server.listen({
    onUnhandledRequest: "warn", // Cảnh báo khi có request không được handle
  });
});

afterEach(() => {
  // Reset handlers sau mỗi test để tránh side effects
  server.resetHandlers();
});

afterAll(() => {
  // Đóng server sau khi hoàn thành tất cả tests
  server.close();
});

// Export server để có thể sử dụng trong các test files
export { handlers } from "./handlers";
