/**
 * MSW server setup for testing
 * <PERSON><PERSON><PERSON> hình MSW server cho testing
 */

import { setupServer } from 'msw/node'
import { handlers } from './api-handlers'

// Setup MSW server with our handlers
export const server = setupServer(...handlers)

// Start server before all tests
beforeAll(() => {
  server.listen({
    onUnhandledRequest: 'warn',
  })
})

// Reset handlers after each test
afterEach(() => {
  server.resetHandlers()
})

// Close server after all tests
afterAll(() => {
  server.close()
})
