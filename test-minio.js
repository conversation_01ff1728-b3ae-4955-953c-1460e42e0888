const { Client } = require('minio');

// MinIO client configuration
const minioClient = new Client({
  endPoint: 'localhost',
  port: 9000,
  useSSL: false,
  accessKey: 'minioadmin',
  secretKey: 'minioadmin123',
});

const BUCKET_NAME = 'ns-shop-media';

async function testMinIO() {
  try {
    console.log('Testing MinIO connection...');
    
    // Test bucket exists
    const bucketExists = await minioClient.bucketExists(BUCKET_NAME);
    console.log(`Bucket ${BUCKET_NAME} exists:`, bucketExists);
    
    if (!bucketExists) {
      console.log('Creating bucket...');
      await minioClient.makeBucket(BUCKET_NAME, 'us-east-1');
      console.log(`Bucket ${BUCKET_NAME} created successfully`);
      
      // Set bucket policy to allow public read access
      const policy = {
        Version: "2012-10-17",
        Statement: [
          {
            Effect: "Allow",
            Principal: { AWS: ["*"] },
            Action: ["s3:GetObject"],
            Resource: [`arn:aws:s3:::${BUCKET_NAME}/*`],
          },
        ],
      };
      
      await minioClient.setBucketPolicy(BUCKET_NAME, JSON.stringify(policy));
      console.log(`Bucket policy set for ${BUCKET_NAME}`);
    }
    
    // Test upload a simple file
    const testContent = Buffer.from('Hello MinIO!', 'utf8');
    const objectName = `test/${Date.now()}-test.txt`;
    
    console.log('Uploading test file...');
    await minioClient.putObject(BUCKET_NAME, objectName, testContent, testContent.length, {
      'Content-Type': 'text/plain',
    });
    
    console.log('Test file uploaded successfully!');
    
    // Generate URL
    const url = `http://localhost:9000/${BUCKET_NAME}/${objectName}`;
    console.log('File URL:', url);
    
    // List objects
    console.log('Listing objects...');
    const stream = minioClient.listObjects(BUCKET_NAME, '', true);
    for await (const obj of stream) {
      console.log('Object:', obj.name, 'Size:', obj.size);
    }
    
  } catch (error) {
    console.error('MinIO test failed:', error);
  }
}

testMinIO();
