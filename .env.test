# Test Environment Variables
NODE_ENV=test

# Test Database
DATABASE_URL="postgresql://test:test@localhost:5499/ns_shop_test?schema=public"

# NextAuth Test Configuration
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="test-secret-key-for-testing-only"

# JWT Test Configuration
JWT_SECRET="test-jwt-secret-key-for-testing-only"

# App Configuration
NEXT_PUBLIC_APP_URL="http://localhost:3000"
NEXT_PUBLIC_BUILD_VERSION="test"

# Email Configuration (Mock)
EMAIL_SERVER_HOST="smtp.test.com"
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER="<EMAIL>"
EMAIL_SERVER_PASSWORD="test-password"
EMAIL_FROM="<EMAIL>"

# File Upload Configuration
UPLOAD_DIR="./tests/fixtures/uploads"
MAX_FILE_SIZE=1048576

# Redis Configuration (Mock)
REDIS_URL="redis://localhost:6379/1"
