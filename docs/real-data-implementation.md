# Triển khai Data Thật cho Homepage Components

## Tổng quan

Đã triển khai thành công việc thay thế sample data bằng data thật từ database cho các components chính của homepage.

## Các Components đã đượ<PERSON> cập nhật

### 1. FeaturedProducts Component
- **File**: `src/components/shop/featured-products.tsx`
- **Thay đổi**:
  - Sử dụng `useFeaturedProducts(6)` hook thay vì hardcoded data
  - Thêm loading, error và empty states
  - Cập nhật để sử dụng `product.avgRating` và `product.reviewCount`
  - Sử dụng `getProductImage()` helper để lấy hình ảnh từ media relationship
  - Sử dụng `isNewProduct()` helper để xác định sản phẩm mới

### 2. CategorySection Component
- **File**: `src/components/shop/category-section.tsx`
- **Thay đổi**:
  - Sử dụng `useRootCategories()` hook thay vì hardcoded data
  - Thêm loading, error và empty states
  - Hi<PERSON>n thị số lượng sản phẩm thực từ `_count.products`
  - Sử dụng `getCategoryImage()` helper cho hình ảnh category

### 3. TrendingProducts Component
- **File**: `src/components/shop/trending-products.tsx`
- **Thay đổi**:
  - Sử dụng `useTrendingProducts(4)` hook (tạm thời dùng featured products)
  - Thêm loading, error và empty states
  - Cập nhật ranking badge để sử dụng index thay vì hardcoded rank
  - Sửa wishlist function để sử dụng string ID thay vì number

### 4. ModernFeaturedProducts Component
- **File**: `src/components/shop/modern-featured-products.tsx`
- **Thay đổi**:
  - Sử dụng `useFeaturedProducts(6)` hook
  - Thêm loading, error và empty states
  - Thay thế colors section bằng category information
  - Cập nhật rating và review count

## Hooks mới được tạo

### 1. useProducts Hook
- **File**: `src/hooks/use-products.ts`
- **Chức năng**:
  - `useProducts(options)` - Hook chung cho việc fetch products với filters
  - `useFeaturedProducts(limit)` - Hook chuyên dụng cho featured products
  - `useTrendingProducts(limit)` - Hook chuyên dụng cho trending products

### 2. useCategories Hook
- **File**: `src/hooks/use-categories.ts`
- **Chức năng**:
  - `useCategories(options)` - Hook chung cho việc fetch categories
  - `useRootCategories()` - Hook chuyên dụng cho root categories

## Helper Functions

### getProductImage(product)
- Kiểm tra media relationship trước
- Fallback về legacy images field
- Default fallback là placeholder image

### getCategoryImage(category)
- Kiểm tra image relationship
- Fallback về slug-based image path

### isNewProduct(createdAt)
- Xác định sản phẩm được tạo trong 30 ngày qua

## API Endpoints được sử dụng

- `GET /api/products?featured=true&limit=6` - Lấy featured products
- `GET /api/categories?parentId=null` - Lấy root categories

## States được xử lý

### Loading State
- Hiển thị spinner và text "Đang tải..."
- Ẩn content chính khi đang loading

### Error State
- Hiển thị icon lỗi và thông báo lỗi
- Ẩn content chính khi có lỗi

### Empty State
- Hiển thị thông báo khi không có data
- Chỉ hiển thị khi không loading và không có lỗi

### Success State
- Hiển thị data thực từ database
- Bao gồm cả View All buttons

## Cách test

1. **Khởi động server**:
   ```bash
   yarn dev
   ```

2. **Truy cập homepage**: `http://localhost:6002`

3. **Kiểm tra các sections**:
   - Featured Products section
   - Category section
   - Trending Products section

4. **Test cases**:
   - Database có data: Hiển thị products/categories thực
   - Database trống: Hiển thị empty states
   - API lỗi: Hiển thị error states
   - Loading: Hiển thị loading spinners

## Lưu ý quan trọng

1. **Database cần có data**: Nếu database trống, sẽ hiển thị empty states
2. **Media relationships**: Cần đảm bảo products có images được upload đúng cách
3. **Category hierarchy**: Root categories (parentId = null) sẽ được hiển thị
4. **Featured flag**: Products cần có `featured = true` để hiển thị trong featured sections

## Tối ưu hóa tiếp theo

1. **Caching**: Có thể thêm SWR hoặc React Query để cache data
2. **Pagination**: Thêm pagination cho các sections có nhiều items
3. **Real trending logic**: Thay thế trending logic bằng thuật toán thực dựa trên views/sales
4. **Image optimization**: Tối ưu hóa việc load images
5. **Error retry**: Thêm retry mechanism cho failed requests

## Troubleshooting

### Không hiển thị products
- Kiểm tra database có products với `featured = true`
- Kiểm tra API endpoint `/api/products` hoạt động
- Xem console để check lỗi network

### Không hiển thị categories
- Kiểm tra database có categories với `parentId = null`
- Kiểm tra API endpoint `/api/categories` hoạt động

### Images không load
- Kiểm tra media relationships trong database
- Kiểm tra file images tồn tại trong public folder
- Fallback sẽ sử dụng placeholder image
