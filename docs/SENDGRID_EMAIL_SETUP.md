# Multi-Provider Email System - NS Shop

## Tổng quan

NS Shop đã được tích hợp với hệ thống email đa provider, hỗ trợ **SendGrid** và **Amazon SES** với khả năng chuyển đổi linh hoạt. Hệ thống email hỗ trợ nhiều loại email khác nhau với template được thiết kế đẹp mắt và responsive.

## Cấu hình

### 1. Biến môi trường

Thêm các biến sau vào file `.env`:

```env
# Email Provider Selection
EMAIL_PROVIDER="auto"  # "sendgrid", "ses", "auto"

# Common Email Settings
EMAIL_FROM="<EMAIL>"
EMAIL_FROM_NAME="NS Shop"
ADMIN_EMAIL="<EMAIL>"

# SendGrid Configuration
SENDGRID_API_KEY="your-sendgrid-api-key-here"

# Amazon SES Configuration
AWS_REGION="us-east-1"
AWS_ACCESS_KEY_ID="your-aws-access-key-id"
AWS_SECRET_ACCESS_KEY="your-aws-secret-access-key"
SES_FROM_EMAIL="<EMAIL>"
SES_FROM_NAME="NS Shop"

# Legacy SMTP (Fallback)
EMAIL_SERVER_HOST="smtp.gmail.com"
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER="<EMAIL>"
EMAIL_SERVER_PASSWORD="your-app-password"
```

### 2. Cài đặt Dependencies

```bash
# SendGrid
yarn add @sendgrid/mail

# Amazon SES
yarn add @aws-sdk/client-ses

# TypeScript types (optional)
yarn add -D @types/sendgrid
```

### 3. Provider Selection

Hệ thống hỗ trợ 3 chế độ chọn provider:

- **`auto`** (mặc định): Tự động chọn provider dựa trên cấu hình có sẵn
  - Ưu tiên SendGrid nếu có `SENDGRID_API_KEY`
  - Fallback sang Amazon SES nếu có AWS credentials
  - Cuối cùng fallback sang SMTP

- **`sendgrid`**: Bắt buộc sử dụng SendGrid
- **`ses`**: Bắt buộc sử dụng Amazon SES

## Các loại Email được hỗ trợ

### 1. Welcome Email

- **Khi nào**: Khi người dùng đăng ký tài khoản mới
- **Nội dung**: Chào mừng, thông tin tài khoản, link đăng nhập
- **Template**: `generateWelcomeEmail()`

### 2. Order Confirmation Email

- **Khi nào**: Sau khi đặt hàng thành công
- **Nội dung**: Chi tiết đơn hàng, thông tin giao hàng, link theo dõi
- **Template**: `generateOrderConfirmationEmail()`

### 3. Contact Form Email

- **Khi nào**: Khi khách hàng gửi form liên hệ
- **Nội dung**: Thông tin người gửi, nội dung tin nhắn
- **Template**: `generateContactFormEmail()`

### 4. Password Reset Email

- **Khi nào**: Khi người dùng yêu cầu đặt lại mật khẩu
- **Nội dung**: Link đặt lại mật khẩu, thời gian hết hạn
- **Template**: `generatePasswordResetEmail()`

### 5. Admin Notification Email

- **Khi nào**: Thông báo quan trọng cho admin
- **Nội dung**: Chi tiết thông báo, link hành động
- **Template**: `generateAdminNotificationEmail()`

## Cách sử dụng

### Gửi Welcome Email

```typescript
import { emailService } from "@/lib/email-service";
import type { WelcomeEmailData } from "@/lib/email-templates";

const welcomeData: WelcomeEmailData = {
  recipientName: "Nguyễn Văn A",
  recipientEmail: "<EMAIL>",
  loginUrl: `${process.env.NEXT_PUBLIC_APP_URL}/auth/signin`,
  supportEmail: "<EMAIL>",
};

await emailService.initialize();
await emailService.sendWelcomeEmail(welcomeData);
```

### Gửi Order Confirmation Email

```typescript
const orderData: OrderConfirmationEmailData = {
  recipientName: "Nguyễn Văn A",
  recipientEmail: "<EMAIL>",
  order: {
    id: "order_123",
    total: 500000,
    status: "PENDING",
    createdAt: new Date().toISOString(),
    items: [
      {
        name: "Áo thun nam",
        quantity: 2,
        price: 200000,
        image: "https://example.com/image.jpg",
      },
    ],
    shippingAddress: {
      fullName: "Nguyễn Văn A",
      address: "123 Đường ABC",
      city: "Hà Nội",
      postalCode: "100000",
      phone: "**********",
    },
  },
  trackingUrl: `${process.env.NEXT_PUBLIC_APP_URL}/orders/order_123`,
};

await emailService.sendOrderConfirmationEmail(orderData);
```

## Cấu hình Amazon SES

### 1. Tạo AWS Account và IAM User

1. Đăng ký AWS account tại [aws.amazon.com](https://aws.amazon.com)
2. Tạo IAM user với quyền SES:
   ```json
   {
     "Version": "2012-10-17",
     "Statement": [
       {
         "Effect": "Allow",
         "Action": [
           "ses:SendEmail",
           "ses:SendRawEmail",
           "ses:GetSendQuota",
           "ses:GetSendStatistics"
         ],
         "Resource": "*"
       }
     ]
   }
   ```

### 2. Verify Email Domain/Address

1. Truy cập [SES Console](https://console.aws.amazon.com/ses/)
2. Chọn region (khuyến nghị: us-east-1)
3. Verify domain hoặc email address
4. Cấu hình DKIM và SPF records

### 3. Request Production Access

- Mặc định SES ở sandbox mode (chỉ gửi được cho verified emails)
- Request production access để gửi email cho bất kỳ địa chỉ nào
- Thường mất 24-48h để được approve

### 4. Cấu hình Environment Variables

```env
EMAIL_PROVIDER="ses"
AWS_REGION="us-east-1"
AWS_ACCESS_KEY_ID="AKIA..."
AWS_SECRET_ACCESS_KEY="..."
SES_FROM_EMAIL="<EMAIL>"
SES_FROM_NAME="Your App Name"
```

## API Endpoints

### 1. Contact Form API

- **Endpoint**: `POST /api/contact`
- **Mô tả**: Xử lý form liên hệ và gửi email cho admin

### 2. Password Reset API

- **Endpoint**: `POST /api/auth/forgot-password`
- **Mô tả**: Gửi email đặt lại mật khẩu

### 3. Email Preview API (Admin)

- **Endpoint**: `GET /api/admin/email-preview?template=welcome&format=html`
- **Mô tả**: Xem trước email templates

### 4. Email Test API (Admin)

- **Endpoint**: `POST /api/admin/notifications/email`
- **Mô tả**: Gửi email test và kiểm tra kết nối

## Admin Dashboard

### Email Templates Page

- **URL**: `/admin/email-templates`
- **Chức năng**:
  - Xem trước tất cả email templates
  - Kiểm tra trạng thái tất cả email providers
  - Hiển thị provider hiện tại đang sử dụng
  - Test email với provider hiện tại
  - Test email với tất cả providers

### Email Status Check

- Kiểm tra kết nối tất cả providers
- Hiển thị trạng thái cấu hình từng provider
- Chuyển đổi provider tự động
- Test gửi email đa provider

## Tính năng nâng cao

### 1. Multi-Provider Architecture

- **Provider Factory Pattern**: Dễ dàng thêm providers mới
- **Interface-based Design**: Tất cả providers implement cùng interface
- **Auto Provider Selection**: Tự động chọn provider tốt nhất
- **Fallback System**: SendGrid → Amazon SES → SMTP

### 2. Provider Switching

- **Runtime Switching**: Chuyển đổi provider không cần restart
- **Environment-based**: Cấu hình qua biến môi trường
- **Health Checking**: Kiểm tra trạng thái providers
- **Load Balancing**: Phân tải giữa các providers (future)

### 3. Template System

- HTML templates responsive
- Text fallback cho mọi email
- Branding nhất quán với NS Shop
- Hỗ trợ tiếng Việt
- Template caching

### 4. Error Handling

- Graceful degradation
- Provider-specific error handling
- Detailed logging
- Không làm gián đoạn user flow
- Retry mechanisms

## Testing

### Unit Tests

```bash
npm run test:unit -- --testPathPatterns=email
```

### Integration Tests

```bash
npm run test:integration -- --testPathPatterns=contact
```

### Manual Testing

1. Truy cập `/admin/email-templates`
2. Kiểm tra trạng thái email service
3. Gửi email test
4. Xem trước templates

## Troubleshooting

### Lỗi thường gặp

1. **Provider không khởi tạo được**
   - Kiểm tra cấu hình trong `.env`
   - Xem logs để biết provider nào đang được sử dụng
   - Sử dụng admin panel để kiểm tra trạng thái

2. **SendGrid lỗi**
   - Kiểm tra `SENDGRID_API_KEY` trong `.env`
   - Đảm bảo API key có quyền gửi email
   - Verify domain trong SendGrid console

3. **Amazon SES lỗi**
   - Kiểm tra AWS credentials
   - Đảm bảo region đúng
   - Verify email/domain trong SES console
   - Kiểm tra sandbox mode vs production

4. **Email không được gửi**
   - Kiểm tra logs trong console
   - Test từng provider riêng lẻ
   - Kiểm tra spam folder
   - Verify DNS records (SPF, DKIM)

5. **Template hiển thị sai**
   - Kiểm tra data truyền vào template
   - Xem trước template qua admin panel
   - Test với nhiều email clients

### Debug Commands

```bash
# Kiểm tra trạng thái tất cả providers
curl -X GET http://localhost:3000/api/admin/notifications/email

# Gửi email test với provider hiện tại
curl -X POST http://localhost:3000/api/admin/notifications/email \
  -H "Content-Type: application/json" \
  -d '{"action": "test", "recipient": "<EMAIL>"}'

# Test tất cả providers
curl -X POST http://localhost:3000/api/admin/notifications/email \
  -H "Content-Type: application/json" \
  -d '{"action": "test", "recipient": "<EMAIL>", "testMode": "all"}'

# Kiểm tra provider hiện tại
node -e "
const { emailService } = require('./src/lib/email-service');
emailService.initialize().then(() => {
  console.log('Provider info:', emailService.getProviderInfo());
  emailService.getProviderStatus().then(status => {
    console.log('Provider status:', status);
  });
});
"
```

## Bảo mật

- API keys được lưu trong environment variables
- Email templates được sanitize
- Rate limiting cho email APIs
- Validation đầu vào nghiêm ngặt

## Performance

- Async email sending
- Bulk email support
- Template caching
- Minimal dependencies

## Monitoring

- Email delivery status
- Error tracking
- Performance metrics
- Admin notifications

---

## So sánh Providers

| Tính năng         | SendGrid         | Amazon SES        | SMTP           |
| ----------------- | ---------------- | ----------------- | -------------- |
| **Độ tin cậy**    | Cao              | Rất cao           | Trung bình     |
| **Tốc độ**        | Nhanh            | Nhanh             | Chậm           |
| **Chi phí**       | $14.95/tháng     | $0.10/1000 emails | Miễn phí       |
| **Setup**         | Dễ               | Trung bình        | Khó            |
| **Analytics**     | Có               | Có                | Không          |
| **IP Reputation** | Shared/Dedicated | Shared/Dedicated  | Phụ thuộc SMTP |

## Khuyến nghị

- **Production**: Sử dụng SendGrid hoặc Amazon SES
- **Development**: Có thể dùng SMTP hoặc auto mode
- **High Volume**: Amazon SES (cost-effective)
- **Ease of Use**: SendGrid (user-friendly)

---

**Lưu ý**:

- Đảm bảo cấu hình đúng API keys và verify domain trước khi deploy production
- Test cả hai providers để đảm bảo fallback hoạt động
- Monitor email delivery rates và reputation
- Cấu hình DNS records (SPF, DKIM, DMARC) cho domain
