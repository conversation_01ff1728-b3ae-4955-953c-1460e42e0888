# Admin User Management System

## Overview

The NS Shop admin system has been redesigned to separate admin and moderator accounts from regular user accounts. This provides better security, clearer separation of concerns, and more granular permission management.

## Key Changes

### 1. Separate AdminUser Table

- **Before**: Admin users were stored in the same `users` table with a `role` field
- **After**: Admin users are stored in a dedicated `admin_users` table

### 2. New Role System

- **ADMIN**: Full access to all admin features including user management
- **MODERATOR**: Limited access to specific admin features (products, orders, categories)

### 3. Enhanced Security

- Separate authentication flow for admin users
- Admin-specific JWT tokens and sessions
- Role-based access control with fine-grained permissions

## Database Schema

### AdminUser Model

```prisma
model AdminUser {
  id           String     @id @default(cuid())
  email        String     @unique
  name         String
  password     String
  role         AdminRole  @default(MODERATOR)
  avatar       String?
  phone        String?
  isActive     Boolean    @default(true)
  permissions  Json?      // Fine-grained permissions for moderators
  lastLoginAt  DateTime?
  department   String?    // Organization department
  createdBy    String?    // Which admin created this account
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt

  // Relations
  createdByAdmin AdminUser? @relation("AdminHierarchy", fields: [createdBy], references: [id])
  createdAdmins  AdminUser[] @relation("AdminHierarchy")

  @@map("admin_users")
}

enum AdminRole {
  ADMIN
  MODERATOR
}
```

### User Model Changes

The `User` model no longer has a `role` field. All users in this table are regular customers.

## Authentication System

### Admin Authentication

Admin users authenticate through a separate endpoint:
- **Login URL**: `/admin/auth/signin`
- **API Endpoint**: `/api/admin/auth/[...nextauth]`
- **Provider**: `admin-credentials`

### Session Structure

Admin sessions include additional fields:

```typescript
{
  user: {
    id: string;
    email: string;
    name: string;
    role: 'ADMIN' | 'MODERATOR';
    type: 'admin';
    permissions?: Record<string, boolean>;
  }
}
```

## Permission System

### Admin Permissions

Admins have full access to all features:
- Manage products, orders, categories
- Manage regular users
- Manage admin users
- Access analytics and reports
- Modify system settings

### Moderator Permissions

Moderators have limited access based on their permissions:

```json
{
  "manage_products": true,
  "manage_orders": true,
  "manage_categories": true,
  "view_analytics": true,
  "manage_users": true
}
```

Moderators **cannot**:
- Create, edit, or delete admin users
- Access system settings
- Change their own role or status

## API Endpoints

### Admin User Management

- `GET /api/admin/admins` - List admin users (Admin only)
- `POST /api/admin/admins` - Create admin user (Admin only)
- `GET /api/admin/admins/[id]` - Get admin user details
- `PUT /api/admin/admins/[id]` - Update admin user
- `DELETE /api/admin/admins/[id]` - Delete admin user (Admin only)

### Updated Admin Endpoints

All existing admin endpoints now check for `session.user.type === 'admin'` and appropriate role permissions.

## UI Components

### Admin Management Interface

- **Location**: `/admin/admins`
- **Access**: Admin only
- **Features**:
  - List all admin users with filtering and search
  - Create new admin users
  - Edit existing admin users
  - Deactivate/activate admin accounts
  - Delete admin users (except self)

### Admin Sidebar

The admin sidebar now includes role-based menu filtering:
- Admin-only items are hidden from moderators
- Includes new "Quản lý Admin" menu item

## Middleware Updates

The middleware now handles both user and admin authentication:

```typescript
// Admin routes protection
if (pathname.startsWith("/admin")) {
  if (pathname.startsWith("/admin/auth")) {
    return NextResponse.next(); // Allow admin auth routes
  }
  
  if (!token || token.type !== "admin") {
    return NextResponse.redirect("/admin/auth/signin");
  }
  
  // Role-based restrictions for moderators
  if (token.role === "MODERATOR") {
    const restrictedPaths = ["/admin/admins", "/admin/settings"];
    if (restrictedPaths.some(path => pathname.startsWith(path))) {
      return NextResponse.redirect("/admin");
    }
  }
}
```

## Default Admin Accounts

The system creates two default admin accounts:

1. **Admin Account**
   - Email: `<EMAIL>`
   - Password: `admin123`
   - Role: `ADMIN`

2. **Moderator Account**
   - Email: `<EMAIL>`
   - Password: `moderator123`
   - Role: `MODERATOR`

⚠️ **Important**: Change these default passwords after first login!

## Testing

Comprehensive tests are included for:

- **Unit Tests**: AdminUser model, API endpoints, UI components
- **Integration Tests**: Authentication flow, database operations
- **Middleware Tests**: Route protection and access control

Run admin system tests:

```bash
npm test -- --testPathPattern="admin"
```

## Security Considerations

1. **Separate Authentication**: Admin users authenticate through a separate system
2. **Role-Based Access**: Fine-grained permissions for different admin roles
3. **Audit Trail**: Track who created which admin accounts
4. **Self-Protection**: Admins cannot delete their own accounts
5. **Session Security**: Admin sessions are separate from user sessions

## Best Practices

1. **Principle of Least Privilege**: Give moderators only the permissions they need
2. **Regular Audits**: Review admin accounts and permissions regularly
3. **Strong Passwords**: Enforce strong password policies for admin accounts
4. **Account Lifecycle**: Deactivate accounts when admins leave the organization
5. **Monitoring**: Monitor admin actions for security purposes

## Troubleshooting

### Common Issues

1. **Cannot access admin panel**: Ensure you're using the admin login at `/admin/auth/signin`
2. **Permission denied**: Check if your account has the required role and permissions
3. **Session issues**: Clear browser cookies and log in again
4. **Database errors**: Ensure the migration has been run successfully

### Migration Issues

If you encounter issues during migration:

1. Check that the database migration completed successfully
2. Verify that default admin accounts were created
3. Ensure all existing admin API endpoints are updated
4. Test the admin authentication flow

## Future Enhancements

Potential future improvements:

1. **Two-Factor Authentication**: Add 2FA for admin accounts
2. **Advanced Permissions**: More granular permission system
3. **Audit Logging**: Detailed logging of admin actions
4. **Session Management**: Advanced session control and monitoring
5. **API Keys**: API key authentication for admin operations
