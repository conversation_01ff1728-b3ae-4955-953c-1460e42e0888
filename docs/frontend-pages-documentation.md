# NS Shop - Frontend Pages Documentation

## 📋 Overview

NS Shop is a modern fashion e-commerce platform built with Next.js 15, featuring both customer-facing storefront and admin dashboard. The application uses TypeScript, Tailwind CSS, and follows a pink/magenta design theme.

## 🛍️ Customer-Facing Pages

### 1. Homepage (`/`)

**File:** `src/app/page.tsx`

**Features:**

- Hero section with gradient background and floating animations
- Category showcase with product counts
- Featured products grid with ratings and sale badges
- Newsletter subscription section
- Responsive design with modern UI

**Components:**

- `HeroSection` - Main banner with call-to-action
- `CategorySection` - Product categories display
- `FeaturedProducts` - Highlighted products
- `NewsletterSection` - Email subscription

### 2. Products Listing (`/products`)

**File:** `src/app/products/page.tsx`

**Features:**

- Product grid/list view toggle
- Advanced filtering (category, price, search)
- Sorting options (price, name, date)
- Pagination with customizable page size
- Product cards with images, ratings, and sale badges
- Search functionality
- Responsive grid layout

**Key Functionality:**

- Real-time product filtering
- Price range filtering
- Category-based filtering
- Sort by price, popularity, newest

### 3. Product Detail (`/products/[slug]`)

**File:** `src/app/products/[slug]/page.tsx`

**Features:**

- Product image gallery with thumbnail navigation
- Detailed product information
- Price display with sale pricing
- Add to cart functionality
- Product reviews and ratings
- Related products suggestions
- Breadcrumb navigation
- Social sharing options

**Components:**

- Image carousel with zoom
- Product specifications
- Customer reviews section
- Add to cart with quantity selector

### 4. Categories (`/categories`)

**File:** `src/app/categories/page.tsx`

**Features:**

- Category grid layout
- Category hierarchy display
- Product count per category
- Category images and descriptions
- Navigation to category-specific products

### 5. Category Products (`/categories/[slug]`)

**File:** `src/app/categories/[slug]/page.tsx`

**Features:**

- Category-specific product listing
- Breadcrumb navigation
- Category information display
- Product filtering within category
- Pagination and sorting

### 6. Search Results (`/search`)

**File:** `src/app/search/page.tsx`

**Features:**

- Advanced search interface
- Search filters and sorting
- Grid/list view toggle
- Search suggestions
- No results handling
- Search history

### 7. Shopping Cart (`/cart`)

**File:** `src/app/cart/page.tsx`

**Features:**

- Cart items display with images
- Quantity adjustment controls
- Item removal functionality
- Price calculations (subtotal, tax, total)
- Continue shopping link
- Proceed to checkout button
- Empty cart state with call-to-action

**Key Functionality:**

- Real-time price updates
- Quantity validation
- Cart persistence
- Responsive design

### 8. Checkout (`/checkout`)

**File:** `src/app/checkout/page.tsx`

**Features:**

- Multi-step checkout process:
  1. Shipping information
  2. Payment method selection
  3. Order review and confirmation
- Address form with validation
- Payment method options (COD, Bank Transfer, Credit Card)
- Order summary with item details
- Billing address option
- Order notes field
- Form validation and error handling

**Payment Methods:**

- Cash on Delivery (COD)
- Bank Transfer
- Credit Card

### 9. Orders (`/orders`)

**File:** `src/app/orders/page.tsx`

**Features:**

- Order history listing
- Order status tracking
- Search and filter orders
- Order details preview
- Pagination
- Empty state for new users

**Order Information:**

- Order ID and date
- Order status and progress
- Total amount
- Item count
- Quick actions

### 10. Order Detail (`/orders/[id]`)

**File:** `src/app/orders/[id]/page.tsx`

**Features:**

- Detailed order information
- Order status timeline
- Item details with images
- Shipping and billing addresses
- Payment information
- Order tracking

### 11. User Profile (`/profile`)

**File:** `src/app/profile/page.tsx`

**Features:**

- Personal information management
- Profile editing form
- Address book management
- Account summary
- Order history quick view
- Avatar upload
- Form validation

**Editable Fields:**

- Name, phone, date of birth
- Gender selection
- Address management
- Profile picture

### 12. Wishlist (`/wishlist`)

**File:** `src/app/wishlist/page.tsx`

**Features:**

- Saved products display
- Add to cart from wishlist
- Remove from wishlist
- Product information preview
- Empty wishlist state
- Grid layout with product cards

### 13. Contact (`/contact`)

**File:** `src/app/contact/page.tsx`

**Features:**

- Contact form with validation
- Store information display
- FAQ section
- Contact methods (email, phone, address)
- Business hours
- Map integration (placeholder)

**Contact Form Fields:**

- Name, email, subject, message
- Form validation
- Success/error handling

## 🔐 Authentication Pages

### 14. Sign In (`/auth/signin`)

**File:** `src/app/auth/signin/page.tsx`

**Features:**

- Email/password login form
- Google OAuth integration
- Password visibility toggle
- Form validation
- Remember me option
- Link to sign up page
- Responsive design with gradient background

### 15. Sign Up (`/auth/signup`)

**File:** `src/app/auth/signup/page.tsx`

**Features:**

- Registration form with validation
- Password confirmation
- Terms and conditions checkbox
- Email verification
- Password strength indicators
- Link to sign in page
- Form error handling

## 🎨 Design Features

### Global Design Elements

- **Color Scheme:** Pink/magenta primary colors
- **Typography:** Geist font family
- **Animations:** Framer Motion for smooth transitions
- **Icons:** Lucide React icon library
- **Responsive:** Mobile-first design approach
- **Dark/Light Mode:** Automatic theme switching

### UI Components

- **Cards:** Product cards, info cards
- **Buttons:** Primary, secondary, ghost variants
- **Forms:** Validated input fields
- **Navigation:** Header, footer, breadcrumbs
- **Modals:** Confirmation dialogs
- **Toast:** Success/error notifications

### Layout Components

- **Header:** Navigation, search, cart, user menu
- **Footer:** Links, social media, newsletter
- **Sidebar:** Category navigation (mobile)
- **Breadcrumbs:** Page navigation trail

## 📱 Responsive Features

### Mobile Optimization

- Touch-friendly interface
- Collapsible navigation
- Optimized image loading
- Swipe gestures for carousels
- Mobile-specific layouts

### Tablet/Desktop

- Multi-column layouts
- Hover effects
- Keyboard navigation
- Advanced filtering options
- Larger image displays

## 🔧 Technical Features

### Performance

- Next.js App Router
- Image optimization
- Lazy loading
- Code splitting
- SEO optimization

### State Management

- React Context for cart
- Session management
- Form state handling
- Loading states
- Error boundaries

### API Integration

- RESTful API calls
- Real-time updates
- Error handling
- Loading indicators
- Optimistic updates

---

## 🔧 Admin Dashboard Pages

### 16. Admin Dashboard (`/admin`)

**File:** `src/app/admin/page.tsx`

**Features:**

- Overview statistics (revenue, orders, customers, products)
- Growth metrics with percentage changes
- Monthly revenue charts
- Top-selling products
- Top categories by revenue
- Order status distribution
- Recent activity feed
- Performance metrics

**Key Metrics:**

- Total revenue with growth percentage
- Order count and trends
- Customer acquisition
- Product inventory status
- Real-time dashboard updates

### 17. Admin Products (`/admin/products`)

**File:** `src/app/admin/products/page.tsx`

**Features:**

- Product management table with pagination
- Advanced search and filtering
- Bulk operations (delete, status change)
- Product creation and editing
- Image management
- Inventory tracking
- Category assignment
- Status management (Active/Inactive/Draft)

**Product Management:**

- Create, read, update, delete products
- Bulk product operations
- Image upload and management
- Price and inventory management
- SEO optimization fields

### 18. Admin Categories (`/admin/categories`)

**File:** `src/app/admin/categories/page.tsx`

**Features:**

- Hierarchical category tree view
- Category creation and editing
- Parent-child relationship management
- Category image upload
- Product count per category
- Drag-and-drop reordering
- Bulk category operations

**Category Features:**

- Nested category structure
- Category metadata management
- SEO-friendly URLs
- Category-specific settings

### 19. Admin Orders (`/admin/orders`)

**File:** `src/app/admin/orders/page.tsx`

**Features:**

- Order management dashboard
- Order status tracking and updates
- Customer information display
- Order details and items
- Payment status management
- Shipping information
- Order search and filtering
- Bulk order operations

**Order Management:**

- Status updates (Pending, Processing, Shipped, Delivered)
- Payment tracking
- Customer communication
- Order fulfillment workflow

### 20. Admin Users/Customers (`/admin/users`)

**File:** `src/app/admin/users/page.tsx`

**Features:**

- Customer database management
- Customer profile viewing
- Order history per customer
- Customer statistics
- Account status management
- Customer communication tools
- Export customer data

**Customer Information:**

- Personal details and contact info
- Order history and spending
- Account status and activity
- Address management

### 21. Admin Analytics (`/admin/analytics`)

**Features:**

- Sales analytics and reports
- Revenue tracking over time
- Product performance metrics
- Customer behavior analysis
- Conversion rate tracking
- Traffic and engagement metrics
- Export reports functionality

### 22. Admin Notifications (`/admin/notifications`)

**Features:**

- System notification management
- Customer communication
- Automated notification settings
- Notification templates
- Delivery status tracking
- Notification history

### 23. Admin Settings (`/admin/settings`)

**Features:**

- System configuration
- Store settings management
- Payment gateway configuration
- Shipping settings
- Tax configuration
- Email template management
- Security settings

### 24. Admin User Management (`/admin/admins`)

**Features:**

- Admin user creation and management
- Role-based access control
- Permission management
- Admin activity tracking
- Security settings
- Two-factor authentication

**Admin Roles:**

- Super Admin (full access)
- Admin (most features)
- Moderator (limited access)
- Custom role permissions

### 25. Audit Logs (`/admin/audit-logs`)

**Features:**

- System activity tracking
- User action logging
- Security event monitoring
- Data change history
- Compliance reporting
- Log filtering and search

### 26. Admin Authentication (`/admin/auth/signin`)

**File:** `src/app/admin/auth/signin/page.tsx`

**Features:**

- Secure admin login
- Role-based authentication
- Session management
- Security features
- Demo account access
- Admin-specific styling

**Security Features:**

- Encrypted password storage
- Session timeout
- Failed login tracking
- IP-based restrictions

## 🎛️ Admin Dashboard Features

### Navigation & Layout

- **Enhanced Sidebar:** Collapsible navigation with icons
- **Admin Header:** User profile, notifications, settings
- **Breadcrumb Navigation:** Page hierarchy tracking
- **Responsive Design:** Mobile-friendly admin interface

### Data Management

- **Advanced Tables:** Sorting, filtering, pagination
- **Bulk Operations:** Multi-select actions
- **Export/Import:** Data management tools
- **Real-time Updates:** Live data synchronization

### Security & Permissions

- **Role-Based Access:** Different permission levels
- **Audit Logging:** Activity tracking
- **Secure Authentication:** Admin-only access
- **Data Protection:** Encrypted sensitive data

### Analytics & Reporting

- **Dashboard Widgets:** Key performance indicators
- **Charts & Graphs:** Visual data representation
- **Export Reports:** PDF and Excel exports
- **Real-time Metrics:** Live business intelligence

## 🔒 Security Features

### Authentication & Authorization

- NextAuth.js integration
- JWT token management
- Role-based permissions
- Session security
- Password encryption (bcrypt)

### Data Protection

- Input validation and sanitization
- SQL injection prevention
- XSS protection
- CSRF protection
- Secure API endpoints

### Admin Security

- Separate admin authentication
- Admin-only routes protection
- Audit trail logging
- Failed login monitoring
- Session timeout management

## 📊 Performance Optimizations

### Frontend Performance

- Next.js App Router optimization
- Image optimization and lazy loading
- Code splitting and tree shaking
- Bundle size optimization
- Caching strategies

### Database Performance

- Prisma ORM optimization
- Database indexing
- Query optimization
- Connection pooling
- Caching layer

### User Experience

- Loading states and skeletons
- Optimistic updates
- Error boundaries
- Progressive enhancement
- Accessibility features

---

## 🚀 Technology Stack Summary

**Frontend Framework:** Next.js 15 with App Router
**Language:** TypeScript
**Styling:** Tailwind CSS v4
**UI Components:** Radix UI primitives
**Icons:** Lucide React
**Animation:** Framer Motion
**Forms:** React Hook Form + Zod validation
**Authentication:** NextAuth.js
**Database:** PostgreSQL with Prisma ORM
**State Management:** React Context + Zustand
**Deployment:** Vercel-ready configuration
