# Advanced Features Documentation

## Tổng quan

NS Shop đã được tích hợp 4 advanced features để nâng cao hiệu suất và trải nghiệm người dùng:

1. **WebSocket Support** - Real-time notifications
2. **Email Service Integration** - Multi-provider email system
3. **Performance Monitoring** - Metrics và monitoring
4. **Rate Limiting** - Cache-based rate limiting

## 1. WebSocket Support cho Real-time Notifications

### Tính năng

- **Real-time notifications** cho users và admins
- **Connection management** với auto-cleanup
- **Heartbeat mechanism** để maintain connections
- **User/Admin separation** với targeted messaging
- **Broadcasting capabilities** cho system-wide notifications

### Cách sử dụng

#### Khởi tạo WebSocket Server

```bash
# Start WebSocket server
GET /api/websocket
```

#### Client Connection

```javascript
// Connect to WebSocket
const ws = new WebSocket(
  "ws://localhost:8080/api/websocket?token=YOUR_JWT_TOKEN"
);

ws.onmessage = (event) => {
  const message = JSON.parse(event.data);
  console.log("Received notification:", message);
};
```

#### Send Notifications

```javascript
// Send to specific user
webSocketService.sendNotificationToUser("user123", {
  id: "notif_001",
  title: "New Order",
  message: "Your order has been confirmed",
  category: "success",
});

// Send to all admins
webSocketService.sendNotificationToAdmins({
  id: "admin_notif_001",
  title: "New User Registration",
  message: "A new user has registered",
  category: "info",
});
```

### API Endpoints

- `GET /api/websocket` - Initialize WebSocket server và get stats
- `POST /api/websocket` - Send test notifications

## 2. Email Service Integration

### Tính năng

- **Multiple providers** support (SendGrid, AWS SES, SMTP)
- **Template system** với variable substitution
- **Queue system** cho batch processing
- **Failover mechanism** giữa providers
- **Email templates** cho welcome, order confirmation, password reset

### Configuration

```env
# SendGrid
SENDGRID_API_KEY=your_sendgrid_api_key

# AWS SES
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key

# SMTP
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
```

### Cách sử dụng

#### Send Email với Template

```javascript
await emailService.sendEmail({
  to: "<EMAIL>",
  templateId: "welcome",
  templateData: {
    name: "John Doe",
  },
  priority: "normal",
  tags: ["welcome", "user_registration"],
});
```

#### Queue Email cho Batch Processing

```javascript
await emailService.queueEmail({
  to: "<EMAIL>",
  subject: "Order Confirmation",
  html: "<h1>Thank you for your order!</h1>",
  priority: "high",
});
```

#### Custom Templates

```javascript
emailService.addTemplate({
  id: "custom_template",
  name: "Custom Template",
  subject: "Hello {{name}}!",
  html: "<h1>Hello {{name}}!</h1><p>{{message}}</p>",
  variables: ["name", "message"],
});
```

## 3. Performance Monitoring với Metrics

### Tính năng

- **API performance tracking** (response time, error rate)
- **Database query monitoring**
- **Cache hit/miss rates**
- **System metrics** (memory, uptime)
- **Custom metrics** support
- **Alert system** cho performance thresholds
- **Dashboard API** với charts data

### Metric Types

1. **Counter** - Incremental values (API requests, errors)
2. **Gauge** - Current values (memory usage, active connections)
3. **Histogram** - Distribution of values (response times)
4. **Timer** - Duration measurements

### Cách sử dụng

#### Record Custom Metrics

```javascript
// Counter
metricsService.counter("user_registrations", 1, { source: "web" });

// Gauge
metricsService.gauge("active_users", 150);

// Histogram
metricsService.histogram("api_response_time", 250, {
  endpoint: "/api/products",
});

// Timer
const timer = metricsService.timer("database_query", { table: "products" });
// ... perform operation
const duration = timer.end();
```

#### Decorators cho Auto-tracking

```javascript
// API method tracking
@Metrics({ trackResponseTime: true })
async function getProducts() {
  // Method implementation
}

// Database query tracking
@DatabaseMetrics('SELECT', 'products')
async function findProducts() {
  // Database query
}

// Cache operation tracking
@CacheMetrics('products')
async function getCachedProducts() {
  // Cache operation
}
```

#### Dashboard API

```javascript
// Get metrics dashboard
GET /api/admin/metrics

// Test services
POST /api/admin/metrics
{
  "action": "test_websocket",
  "data": {}
}
```

## 4. Rate Limiting với Cache-based Counters

### Tính năng

- **3 strategies**: Sliding Window, Token Bucket, Fixed Window
- **Cache-based implementation** sử dụng existing CacheService
- **Configurable thresholds** cho different use cases
- **User-specific và IP-based** rate limiting
- **Predefined configs** cho common scenarios

### Rate Limiting Strategies

1. **Sliding Window** - Smooth rate limiting over time window
2. **Token Bucket** - Burst allowance với refill rate
3. **Fixed Window** - Simple counter per time window

### Cách sử dụng

#### Basic Rate Limiting

```javascript
const result = await rateLimitService.checkRateLimit("user123", {
  windowMs: 60000, // 1 minute
  maxRequests: 10,
  strategy: "sliding_window",
  message: "Too many requests",
});

if (!result.allowed) {
  throw new Error(result.message);
}
```

#### Predefined Configurations

```javascript
// API general rate limit
await rateLimitService.checkRateLimit(userId, RATE_LIMIT_CONFIGS.API_GENERAL);

// Login attempts
await rateLimitService.checkRateLimit(ip, RATE_LIMIT_CONFIGS.LOGIN_ATTEMPTS);

// Order creation
await rateLimitService.checkRateLimit(
  userId,
  RATE_LIMIT_CONFIGS.ORDER_CREATION
);
```

#### Middleware Integration

```javascript
export async function POST(request: NextRequest) {
  const userId = getUserIdFromRequest(request);

  const rateLimitResult = await rateLimitService.checkRateLimit(
    userId,
    RATE_LIMIT_CONFIGS.API_STRICT
  );

  if (!rateLimitResult.allowed) {
    return NextResponse.json(
      { error: rateLimitResult.message },
      { status: 429 }
    );
  }

  // Continue with request processing
}
```

## Integration với Existing System

### Dependency Injection

Tất cả services đã được register trong DI container:

```javascript
// Get services from container
const services = getServices();
const { webSocketService, emailService, metricsService, rateLimitService } =
  services;
```

### Event-driven Integration

Services tự động integrate với event system:

```javascript
// User registration triggers both WebSocket và email notifications
eventService.emit(EVENT_TYPES.USER_REGISTERED, {
  userId: "user123",
  email: "<EMAIL>",
  name: "John Doe",
});
```

### Notification Handlers

Automatic notifications cho business events:

- **User events**: Registration, profile updates
- **Order events**: Created, status changes, shipped
- **Product events**: Created, updated, low stock
- **Admin events**: Login, actions, system changes

## Testing và Monitoring

### Health Checks

```javascript
// WebSocket stats
GET / api / websocket;

// Email provider status
const emailProviders = emailService.getProviderStatus();

// Metrics summary
const summary = metricsService.getSummary();

// Rate limit status
const status = await rateLimitService.getRateLimitStatus(userId, config);
```

### Performance Alerts

System tự động tạo alerts khi:

- Response time > 1000ms
- Error rate > 5%
- Memory usage > 80%
- Cache hit rate < 70%

### Cleanup và Maintenance

```javascript
// Cleanup old metrics
metricsService.cleanup();

// Cleanup rate limit data
await rateLimitService.cleanup();

// Shutdown WebSocket connections
webSocketService.shutdown();
```

## Client-side Integration

### React Components

#### NotificationCenter

```jsx
import NotificationCenter from "@/components/notifications/NotificationCenter";

function App() {
  return (
    <div>
      {/* Your app content */}
      <NotificationCenter
        userToken={userToken}
        maxNotifications={10}
        autoHide={true}
        autoHideDelay={5000}
      />
    </div>
  );
}
```

#### ConnectionStatus

```jsx
import ConnectionStatus, {
  ConnectionBadge,
} from "@/components/websocket/ConnectionStatus";

function Layout() {
  return (
    <div>
      {/* Connection status indicator */}
      <ConnectionStatus
        token={userToken}
        showDetails={true}
        showControls={true}
        position="bottom-right"
      />

      {/* Simple badge in header */}
      <ConnectionBadge token={userToken} />
    </div>
  );
}
```

#### Admin Metrics Dashboard

```jsx
import MetricsDashboard from "@/components/admin/MetricsDashboard";

function AdminDashboard() {
  return <MetricsDashboard adminToken={adminToken} refreshInterval={5000} />;
}
```

### React Hooks

#### useWebSocket

```jsx
import { useWebSocket } from "@/hooks/useWebSocket";

function MyComponent() {
  const {
    connected,
    connecting,
    messages,
    notifications,
    send,
    connect,
    disconnect,
  } = useWebSocket({
    token: userToken,
    autoConnect: true,
  });

  const sendMessage = () => {
    send({
      type: "custom",
      data: { message: "Hello!" },
    });
  };

  return (
    <div>
      <p>Status: {connected ? "Connected" : "Disconnected"}</p>
      <button onClick={sendMessage}>Send Message</button>
    </div>
  );
}
```

#### useWebSocketNotifications

```jsx
import { useWebSocketNotifications } from "@/hooks/useWebSocket";

function NotificationComponent() {
  const { notifications, unreadCount, markAsRead, clearNotifications } =
    useWebSocketNotifications({
      token: userToken,
    });

  return (
    <div>
      <h3>Notifications ({unreadCount})</h3>
      {notifications.map((notification) => (
        <div key={notification.id}>
          <h4>{notification.title}</h4>
          <p>{notification.message}</p>
          <button onClick={() => markAsRead(notification.id)}>
            Mark as read
          </button>
        </div>
      ))}
    </div>
  );
}
```

## Best Practices

1. **WebSocket**: Always handle connection errors và implement reconnection logic
2. **Email**: Use queue cho bulk emails, implement retry logic
3. **Metrics**: Don't over-instrument, focus on business-critical metrics
4. **Rate Limiting**: Choose appropriate strategy based on use case
5. **Monitoring**: Set up alerts cho critical thresholds
6. **Testing**: Test failover scenarios và edge cases
7. **Client-side**: Implement proper error handling và loading states
8. **Performance**: Use React.memo và useCallback để optimize re-renders
9. **Security**: Always validate tokens và implement proper authentication

## Troubleshooting

### Common Issues

1. **WebSocket connection fails**: Check JWT token validity
2. **Email not sending**: Verify provider configuration
3. **Metrics not recording**: Ensure services are properly initialized
4. **Rate limiting too strict**: Adjust thresholds based on usage patterns
5. **React hydration errors**: Ensure client-side only components use proper checks
6. **Memory leaks**: Clean up WebSocket connections và intervals in useEffect cleanup

### Debug Commands

```bash
# Check TypeScript errors
npx tsc --noEmit

# Test WebSocket connection
curl http://localhost:6002/api/websocket

# Test email providers
curl -X POST http://localhost:6002/api/admin/metrics \
  -H "Content-Type: application/json" \
  -d '{"action": "test_email", "data": {"email": "<EMAIL>"}}'

# Check React component errors
npm run dev
# Open browser console for client-side errors
```

## File Structure

```
src/
├── lib/websocket/
│   └── client.ts              # WebSocket client utilities
├── hooks/
│   └── useWebSocket.ts        # React hooks for WebSocket
├── components/
│   ├── notifications/
│   │   └── NotificationCenter.tsx
│   ├── websocket/
│   │   └── ConnectionStatus.tsx
│   └── admin/
│       └── MetricsDashboard.tsx
├── app/api/
│   ├── services/              # All advanced services
│   ├── websocket/             # WebSocket API endpoint
│   └── utils/
│       └── admin-auth.ts      # Shared auth utilities
└── docs/
    └── advanced-features.md   # This documentation
```
