# NS Shop - Data Integration Analysis

## 📊 Overview

Tài liệu này phân tích cấu trúc dữ liệu hiện tại và lập kế hoạch triển khai hệ thống đổ dữ liệu lên client frontend cho NS Shop.

## 🗄️ Database Schema Analysis

### Core Models

#### 1. User Model
```typescript
interface User {
  id: string;
  email: string;
  name: string;
  password: string;
  avatarId?: string;
  phone?: string;
  dateOfBirth?: Date;
  gender?: "MALE" | "FEMALE" | "OTHER";
  isActive: boolean;
  
  // Relations
  orders: Order[];
  addresses: Address[];
  reviews: Review[];
  cart?: Cart;
  wishlistItems: WishlistItem[];
  avatar?: Media;
}
```

#### 2. Product Model
```typescript
interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  salePrice?: number;
  categoryId: string;
  brandId?: string;
  stock: number;
  sku: string;
  slug: string;
  featured: boolean;
  status: "ACTIVE" | "INACTIVE" | "OUT_OF_STOCK";
  tags: string[];
  avgRating: number;
  reviewCount: number;
  
  // Relations
  category: Category;
  brand?: Brand;
  media: ProductMedia[];
  cartItems: CartItem[];
  orderItems: OrderItem[];
  reviews: Review[];
  inventoryEntries: InventoryEntry[];
  ProductAttribute: ProductAttribute[];
  WishlistItem: WishlistItem[];
}
```

#### 3. Category Model
```typescript
interface Category {
  id: string;
  name: string;
  description?: string;
  slug: string;
  parentId?: string;
  imageId?: string;
  isActive: boolean;
  order: number;
  
  // Relations
  parent?: Category;
  children: Category[];
  products: Product[];
  image?: Media;
}
```

#### 4. Cart & Order Models
```typescript
interface Cart {
  id: string;
  userId: string;
  user: User;
  items: CartItem[];
}

interface Order {
  id: string;
  userId: string;
  user: User;
  items: OrderItem[];
  total: number;
  status: "PENDING" | "CONFIRMED" | "PROCESSING" | "SHIPPED" | "DELIVERED" | "CANCELLED";
  paymentMethod: "COD" | "BANK_TRANSFER" | "CREDIT_CARD";
  paymentStatus: "PENDING" | "PAID" | "FAILED" | "REFUNDED";
  shippingAddress: Address;
  billingAddress?: Address;
  notes?: string;
}
```

## 🔌 API Endpoints Analysis

### Existing API Routes

#### Products API
- `GET /api/products` - List products with pagination, filtering
- `POST /api/products` - Create product (Admin)
- `GET /api/products/[id]` - Get product details
- `PUT /api/products/[id]` - Update product (Admin)
- `DELETE /api/products/[id]` - Delete product (Admin)

#### Cart API
- `GET /api/cart` - Get user cart
- `POST /api/cart` - Add item to cart
- `PUT /api/cart/[itemId]` - Update cart item
- `DELETE /api/cart/[itemId]` - Remove cart item

#### Categories API
- `GET /api/categories` - List categories
- `POST /api/categories` - Create category (Admin)
- `GET /api/categories/[id]` - Get category details
- `PUT /api/categories/[id]` - Update category (Admin)

#### Admin APIs
- Various admin endpoints for management

### Missing API Endpoints (Cần tạo)

#### User Profile API
- `GET /api/user/profile` - Get user profile
- `PUT /api/user/profile` - Update user profile
- `GET /api/user/addresses` - Get user addresses
- `POST /api/user/addresses` - Add address
- `PUT /api/user/addresses/[id]` - Update address
- `DELETE /api/user/addresses/[id]` - Delete address

#### Orders API
- `GET /api/orders` - Get user orders
- `POST /api/orders` - Create order
- `GET /api/orders/[id]` - Get order details

#### Wishlist API
- `GET /api/wishlist` - Get user wishlist
- `POST /api/wishlist` - Add to wishlist
- `DELETE /api/wishlist/[productId]` - Remove from wishlist

#### Search API
- `GET /api/search` - Search products
- `GET /api/search/suggestions` - Search suggestions

## 🎯 Frontend Components Analysis

### Components Cần Data Integration

#### 1. Product Components
- `ProductCard` - Cần: product data, images, pricing, ratings
- `ProductGrid` - Cần: products list, pagination
- `ProductDetails` - Cần: full product data, reviews, related products
- `FeaturedProducts` - Cần: featured products list
- `ProductSearch` - Cần: search results, filters

#### 2. Category Components
- `CategoryNav` - Cần: categories hierarchy
- `CategoryPage` - Cần: category data, products in category
- `CategoryShowcase` - Cần: categories with product counts

#### 3. Cart Components
- `CartIcon` - Cần: cart items count
- `CartDrawer` - Cần: cart items, totals
- `CartPage` - Cần: full cart data, shipping info

#### 4. User Components
- `UserProfile` - Cần: user data, addresses
- `OrderHistory` - Cần: user orders
- `Wishlist` - Cần: wishlist items

#### 5. Layout Components
- `Header` - Cần: user info, cart count, categories
- `Footer` - Cần: site settings, links
- `Navigation` - Cần: menu items, categories

## 📋 Data Integration Plan

### Phase 1: Core Infrastructure
1. **API Client Setup**
   - Create centralized API client
   - Error handling utilities
   - Loading state management
   - Response caching

2. **Type Definitions**
   - Enhance existing types
   - Add API response types
   - Create form data types

### Phase 2: Data Fetching Hooks
1. **Product Hooks**
   - `useProducts()` - List products with filters
   - `useProduct(id)` - Single product
   - `useFeaturedProducts()` - Featured products
   - `useProductSearch()` - Search functionality

2. **Category Hooks**
   - `useCategories()` - All categories
   - `useCategory(id)` - Single category
   - `useCategoryTree()` - Hierarchical categories

3. **Cart Hooks**
   - `useCart()` - Cart state and actions
   - `useAddToCart()` - Add to cart mutation
   - `useUpdateCart()` - Update cart items

4. **User Hooks**
   - `useUser()` - Current user data
   - `useUserOrders()` - User order history
   - `useUserAddresses()` - User addresses
   - `useWishlist()` - User wishlist

### Phase 3: Context Enhancement
1. **Enhanced Cart Context**
   - Real-time cart updates
   - Optimistic updates
   - Error recovery

2. **User Context**
   - Authentication state
   - User profile data
   - Preferences

3. **Search Context**
   - Search history
   - Filters state
   - Recent searches

### Phase 4: Component Integration
1. **Product Components**
   - Replace mock data with real API calls
   - Add loading states
   - Error handling

2. **Navigation Components**
   - Dynamic menu generation
   - Category-based navigation

3. **User Interface Components**
   - Profile management
   - Order tracking
   - Wishlist management

## 🔄 Data Flow Architecture

```
API Layer (Prisma + Database)
    ↓
API Routes (/api/*)
    ↓
API Client (lib/api-client.ts)
    ↓
Custom Hooks (hooks/use-*.ts)
    ↓
React Contexts (contexts/*Context.tsx)
    ↓
Components (components/*)
    ↓
Pages (app/*/page.tsx)
```

## 📝 Implementation Priority

### High Priority
1. Product data integration (ProductCard, ProductGrid)
2. Cart functionality (CartContext, CartDrawer)
3. Category navigation (Header, CategoryNav)
4. User authentication (AuthContext, UserProfile)

### Medium Priority
1. Search functionality
2. Order management
3. Wishlist features
4. User addresses

### Low Priority
1. Advanced filtering
2. Product recommendations
3. Real-time notifications
4. Analytics integration

## 🎯 Success Metrics

- [ ] All product components display real data
- [ ] Cart functionality works end-to-end
- [ ] User authentication and profile management
- [ ] Category navigation is dynamic
- [ ] Search returns relevant results
- [ ] Error states are handled gracefully
- [ ] Loading states provide good UX
- [ ] Performance is optimized with proper caching

## 📚 Next Steps

1. Create API client utilities
2. Develop custom hooks for each entity
3. Enhance existing contexts
4. Integrate data into components
5. Add comprehensive error handling
6. Implement loading states
7. Add caching strategies
8. Write tests for all new functionality
