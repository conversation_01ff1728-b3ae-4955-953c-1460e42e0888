# Repository Pattern Documentation

## Tổng quan

Repository Pattern là một design pattern đ<PERSON><PERSON><PERSON> sử dụng để tách biệt logic truy cập dữ liệu khỏi business logic. Trong dự án NS Shop, chúng ta đã implement một hệ thống repository hoàn chỉnh để quản lý tất cả các thao tác database.

## Cấu trúc Repository

### Base Repository

Tất cả repositories đều kế thừa từ `BaseRepository` class, cung cấp các phương thức CRUD cơ bản:

```typescript
// src/app/api/repositories/base.repository.ts
export abstract class BaseRepository<T, CreateInput, UpdateInput> {
  // CRUD operations
  async create(data: CreateInput): Promise<T>
  async findById(id: string): Promise<T | null>
  async findMany(options?: FindManyOptions): Promise<T[]>
  async update(id: string, data: UpdateInput): Promise<T>
  async delete(id: string): Promise<T>
  
  // Pagination
  async findWithPagination(options: PaginationOptions): Promise<PaginatedResult<T>>
  
  // Bulk operations
  async createMany(data: CreateInput[]): Promise<T[]>
  async updateMany(where: any, data: UpdateInput): Promise<BatchResult>
  async deleteMany(where: any): Promise<BatchResult>
}
```

### Error Handling

Repository sử dụng custom error classes:

```typescript
export class RepositoryError extends Error
export class NotFoundError extends RepositoryError
export class ValidationError extends RepositoryError
export class ConflictError extends RepositoryError
```

## Danh sách Repositories

### Core Entity Repositories
- **UserRepository**: Quản lý users, authentication, profile
- **AdminUserRepository**: Quản lý admin users, roles, permissions
- **ProductRepository**: Quản lý products, search, filtering, ratings
- **CategoryRepository**: Quản lý categories với hierarchical structure
- **BrandRepository**: Quản lý brands và product associations
- **OrderRepository**: Quản lý orders, payments, status tracking

### E-commerce Feature Repositories
- **CartRepository**: Quản lý shopping cart và cart items
- **AddressRepository**: Quản lý user addresses với default logic
- **ReviewRepository**: Quản lý product reviews và ratings
- **WishlistRepository**: Quản lý user wishlists

### Content Management Repositories
- **PostRepository**: Quản lý blog posts với publishing workflow
- **PageRepository**: Quản lý static pages với hierarchical structure
- **MediaRepository**: Quản lý file uploads và media assets
- **MenuRepository**: Quản lý navigation menus và menu items

### System & Configuration Repositories
- **SettingRepository**: Quản lý system settings và configurations
- **NotificationRepository**: Quản lý user notifications
- **AuditLogRepository**: Quản lý audit trails và activity logs
- **ContactRepository**: Quản lý contact messages và contact info

### Advanced Feature Repositories
- **AttributeRepository**: Quản lý product attributes và values
- **InventoryRepository**: Quản lý stock levels và inventory movements
- **PromotionRepository**: Quản lý promotions và discount codes

## Repository Factory

### Sử dụng Factory Pattern

```typescript
import { getProductRepository, getUserRepository } from '@/app/api/repositories/repository.factory';

// Trong API route
export async function GET() {
  const productRepository = getProductRepository();
  const products = await productRepository.searchProducts({
    search: 'laptop',
    page: 1,
    limit: 20
  });
  
  return NextResponse.json(products);
}
```

### Dependency Injection Container

```typescript
import { RepositoryContainer } from '@/app/api/repositories/repository.factory';

// Initialize container
const container = RepositoryContainer.getInstance();
container.initializeRepositories();

// Resolve repository
const userRepo = container.resolve<UserRepository>('userRepository');
```

## Ví dụ Sử dụng

### 1. Product Management

```typescript
// Tìm kiếm products
const products = await productRepository.searchProducts({
  search: 'iPhone',
  categoryId: 'electronics',
  priceMin: 1000,
  priceMax: 2000,
  page: 1,
  limit: 20,
  sortBy: 'price',
  sortOrder: 'asc'
});

// Tạo product mới
const product = await productRepository.createProduct({
  name: 'iPhone 15 Pro',
  description: 'Latest iPhone model',
  price: 1200,
  categoryId: 'smartphones',
  sku: 'IP15PRO001',
  stock: 100,
  images: ['image1.jpg', 'image2.jpg']
});

// Cập nhật rating
await productRepository.updateProductRating('product-id', 4.5, 150);
```

### 2. User Management

```typescript
// Tạo user mới
const user = await userRepository.createUser({
  name: 'John Doe',
  email: '<EMAIL>',
  password: 'hashedPassword'
});

// Tìm user theo email
const user = await userRepository.findByEmail('<EMAIL>');

// Cập nhật profile
await userRepository.updateProfile('user-id', {
  name: 'John Smith',
  phone: '+1234567890'
});
```

### 3. Cart Management

```typescript
// Thêm vào giỏ hàng
const cart = await cartRepository.addToCart('user-id', 'product-id', 2);

// Lấy giỏ hàng
const cart = await cartRepository.getUserCart('user-id');

// Cập nhật quantity
await cartRepository.updateCartItemQuantity('cart-item-id', 3);

// Xóa item
await cartRepository.removeCartItem('cart-item-id');
```

### 4. Order Processing

```typescript
// Tạo order từ cart
const order = await orderRepository.createOrderFromCart('user-id', {
  shippingAddressId: 'address-id',
  paymentMethod: 'CREDIT_CARD',
  notes: 'Please deliver in the morning'
});

// Cập nhật order status
await orderRepository.updateOrderStatus('order-id', 'SHIPPED', 'admin-id');

// Lấy order history
const orders = await orderRepository.getUserOrders('user-id', {
  page: 1,
  limit: 10
});
```

## Migration từ Direct Prisma

### Trước (Direct Prisma)

```typescript
// API route cũ
export async function GET(request: NextRequest) {
  const products = await prisma.product.findMany({
    where: { status: 'ACTIVE' },
    include: { category: true },
    orderBy: { createdAt: 'desc' }
  });
  
  return NextResponse.json(products);
}
```

### Sau (Repository Pattern)

```typescript
// API route mới
export async function GET(request: NextRequest) {
  const productRepository = getProductRepository();
  const products = await productRepository.getActiveProducts();
  
  return NextResponse.json(products);
}
```

## Best Practices

### 1. Error Handling

```typescript
try {
  const product = await productRepository.findById('product-id');
  if (!product) {
    return NextResponse.json({ error: 'Product not found' }, { status: 404 });
  }
} catch (error) {
  if (error instanceof NotFoundError) {
    return NextResponse.json({ error: error.message }, { status: 404 });
  }
  if (error instanceof ValidationError) {
    return NextResponse.json({ error: error.message }, { status: 400 });
  }
  // Handle other errors
}
```

### 2. Validation

```typescript
// Repository tự động validate
const validation = productRepository.validateProductData(data);
if (!validation.valid) {
  throw new ValidationError(validation.errors.join(', '));
}
```

### 3. Transactions

```typescript
// Sử dụng transactions cho operations phức tạp
await prisma.$transaction(async (tx) => {
  const order = await orderRepository.createOrder(orderData, tx);
  await inventoryRepository.decreaseStock(productId, quantity, tx);
  await cartRepository.clearCart(userId, tx);
});
```

## Testing

### Unit Testing

```typescript
describe('ProductRepository', () => {
  let productRepository: ProductRepository;
  
  beforeEach(() => {
    productRepository = new ProductRepository();
  });
  
  it('should create product successfully', async () => {
    const productData = {
      name: 'Test Product',
      price: 100,
      // ... other fields
    };
    
    const product = await productRepository.createProduct(productData);
    expect(product.name).toBe('Test Product');
  });
});
```

### Integration Testing

```typescript
describe('Product API with Repository', () => {
  it('should return products list', async () => {
    const response = await request(app)
      .get('/api/products')
      .expect(200);
      
    expect(response.body.data).toBeInstanceOf(Array);
  });
});
```

## Performance Considerations

1. **Caching**: Implement caching ở repository level
2. **Pagination**: Luôn sử dụng pagination cho large datasets
3. **Selective Loading**: Chỉ load fields cần thiết
4. **Batch Operations**: Sử dụng bulk operations khi có thể
5. **Database Indexing**: Đảm bảo có indexes phù hợp

## Kết luận

Repository Pattern giúp:
- Tách biệt business logic khỏi data access logic
- Dễ dàng testing và mocking
- Code reusability và maintainability
- Consistent error handling
- Type safety với TypeScript
- Centralized data access logic

Việc migration từ direct Prisma sang Repository Pattern sẽ làm cho codebase dễ maintain và scale hơn trong tương lai.
