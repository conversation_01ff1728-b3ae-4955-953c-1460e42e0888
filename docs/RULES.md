# NS Shop - Project Rules & Long-term Memory

## 🎯 Project Overview

NS Shop is a modern fashion e-commerce platform built with Next.js 15, featuring a customer-facing storefront and an admin dashboard. The project emphasizes fashion-forward design with a pink/magenta color scheme and smooth animations.

## 🏗️ Architecture & Tech Stack

### Core Technologies

- **Framework**: Next.js 15 (App Router)
- **Language**: TypeScript (strict mode)
- **Styling**: Tailwind CSS v4 with custom fashion theme
- **Database**: PostgreSQL with Prisma ORM
- **UI Components**: Radix UI primitives
- **Icons**: Lucide React
- **Animation**: Framer Motion
- **Form Handling**: React Hook Form + Zod validation

### Key Dependencies

```json
{
  "next": "^15.3.2",
  "react": "^19.0.0",
  "typescript": "^5",
  "tailwindcss": "^4",
  "@prisma/client": "6.10.1",
  "@radix-ui/*": "Latest stable versions",
  "lucide-react": "^0.483.0",
  "framer-motion": "^12.6.2"
}
```

## 🎨 Design System

### Color Palette

- **Primary**: Fashion pink (#ec4899) - oklch(0.6 0.2 320)
- **Secondary**: Neutral grays
- **Accent**: Purple/magenta gradients
- **Success**: Green variants
- **Warning**: Yellow/orange variants
- **Error**: Red variants

### Typography

- **Primary Font**: Geist (--font-geist)
- **Mono Font**: Geist Mono (--font-geist-mono)
- **Hierarchy**: Clear heading scales with proper line-height

### Animation Principles

- **Subtle**: Prefer 200-300ms transitions
- **Smooth**: Use ease-out timing functions
- **Purposeful**: Animations should enhance UX, not distract
- **Performance**: Use transform and opacity for animations

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── admin/             # Admin dashboard pages
│   ├── products/          # Product-related pages
│   ├── categories/        # Category pages
│   ├── globals.css        # Global styles with custom CSS variables
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Homepage
├── components/
│   ├── ui/                # Base UI components (Button, Card, etc.)
│   ├── layout/            # Layout components (Header, Footer)
│   ├── shop/              # Customer-facing components
│   └── admin/             # Admin-specific components
├── lib/
│   └── utils.ts           # Utility functions (cn, formatters, etc.)
├── types/
│   └── index.ts           # TypeScript type definitions
├── hooks/                 # Custom React hooks
├── contexts/              # React contexts
└── providers/             # Context providers
```

## 🗄️ Database Schema

### Core Models

1. **User** - Authentication and user management
2. **Product** - Product catalog with images, pricing, inventory
3. **Category** - Hierarchical product categorization
4. **Order** - Order management with status tracking
5. **OrderItem** - Individual items within orders
6. **Cart** - Shopping cart functionality
7. **CartItem** - Items in shopping carts
8. **Address** - User shipping/billing addresses
9. **Review** - Product reviews and ratings

### Key Relationships

- User → Orders (1:many)
- User → Cart (1:1)
- Product → Category (many:1)
- Order → OrderItems (1:many)
- Cart → CartItems (1:many)

## 🎨 UI Component Guidelines

### Button Variants

- `default`: Standard primary button
- `fashion`: Gradient pink button for CTAs
- `luxury`: Dark gradient for premium actions
- `outline`: Border-only variant
- `ghost`: Transparent background
- `link`: Text-only link style

### Card Components

- Always use `fashion-card` class for hover effects
- Include proper spacing with CardHeader, CardContent, CardFooter
- Use consistent border-radius (--radius)

### Layout Principles

- **Mobile-first**: Design for mobile, enhance for desktop
- **Responsive**: Use Tailwind's responsive prefixes
- **Consistent spacing**: Use Tailwind's spacing scale
- **Accessibility**: Include proper ARIA labels and semantic HTML

## 🛍️ Shop Features

### Homepage Sections

1. **Hero Section**: Gradient background, floating elements, CTA buttons
2. **Category Section**: 6 colorful category cards with gradients
3. **Featured Products**: Product grid with ratings, prices, sale badges
4. **Newsletter**: Subscription form with voucher offer

### Product Display

- **Images**: Placeholder gradients with product initials
- **Pricing**: Support for sale prices with strikethrough
- **Ratings**: 5-star system with review counts
- **Badges**: "New" and "Sale" indicators
- **Actions**: Add to cart, wishlist, quick view

## 🔧 Admin Dashboard

### Layout Structure

- **Sidebar**: Collapsible navigation with icons and labels
- **Header**: Search, notifications, theme toggle, user menu
- **Main Content**: Responsive grid layouts for data

### Dashboard Components

- **Stats Cards**: Revenue, orders, customers, products with trend indicators
- **Recent Orders**: Table with status badges and actions
- **Charts**: Placeholder areas for analytics visualization

### Navigation Menu

- Dashboard (overview)
- Products (catalog management)
- Categories (taxonomy management)
- Orders (order processing)
- Customers (user management)
- Analytics (reports)
- Posts (content management)
- Settings (configuration)

## 🎯 Development Rules

### Code Style

- Use TypeScript strict mode
- Prefer functional components with hooks
- Use proper TypeScript types (avoid `any`)
- Follow ESLint configuration
- Use Prettier for formatting

### Component Patterns

- **Server Components**: Default for static content
- **Client Components**: Use 'use client' for interactivity
- **Composition**: Prefer composition over inheritance
- **Props**: Use proper TypeScript interfaces
- **Exports**: Use named exports for components

### File Naming

- **Components**: PascalCase (e.g., `HeroSection.tsx`)
- **Utilities**: camelCase (e.g., `formatCurrency.ts`)
- **Pages**: lowercase with hyphens (e.g., `product-detail`)
- **Types**: Descriptive interfaces (e.g., `ProductFilters`)

### State Management

- **Local State**: useState for component-specific state
- **Context**: For shared state across component trees
- **Server State**: Consider React Query for API data
- **Form State**: React Hook Form for complex forms

## 🚀 Performance Guidelines

### Image Optimization

- Use Next.js Image component
- Provide proper alt text
- Use appropriate sizes and formats
- Implement lazy loading

### Bundle Optimization

- Use dynamic imports for large components
- Implement code splitting at route level
- Minimize client-side JavaScript
- Use server components when possible

### SEO Considerations

- Proper meta tags in layout.tsx
- Semantic HTML structure
- Structured data for products
- Sitemap generation

## 🔐 Security & Best Practices

### Authentication

- Use NextAuth.js for authentication
- Implement proper session management
- Secure API routes with middleware
- Validate user permissions

### Data Validation

- Use Zod for runtime validation
- Validate on both client and server
- Sanitize user inputs
- Implement proper error handling

### Environment Variables

- Use .env.local for development
- Never commit sensitive data
- Use proper typing for env vars
- Validate required environment variables

## 📦 Deployment & DevOps

### Development Workflow

```bash
yarn dev          # Start development server
yarn build        # Build for production
yarn lint         # Run TypeScript checks
yarn p:m          # Run Prisma migrations
yarn p:s          # Open Prisma Studio
yarn dup          # Start Docker services
```

### Database Management

- Use Prisma migrations for schema changes
- Seed database with sample data
- Backup production data regularly
- Use connection pooling for production

### Docker Services

- PostgreSQL on port 5499
- Redis on port 6379 (optional)
- Use docker-compose for local development

## 🎨 Custom CSS Classes

### Animation Classes

- `.animate-gradient-x`: Horizontal gradient animation
- `.animate-gradient-text`: Text gradient animation
- `.animate-float`: Floating animation
- `.shimmer`: Loading shimmer effect

### Utility Classes

- `.text-gradient`: Gradient text effect
- `.glass`: Glass morphism background
- `.fashion-card`: Card with hover effects
- `.line-clamp-{n}`: Text truncation

## 🔄 Future Enhancements

### Planned Features

- User authentication system
- Shopping cart functionality
- Payment integration (Stripe/PayPal)
- Order tracking system
- Product search and filtering
- Inventory management
- Email notifications
- Multi-language support

### Technical Improvements

- Implement caching strategy
- Add comprehensive testing
- Set up CI/CD pipeline
- Implement monitoring and logging
- Add performance analytics
- Optimize for Core Web Vitals

---

**Last Updated**: 2024-01-15
**Version**: 1.0.0
**Maintainer**: NS Team
