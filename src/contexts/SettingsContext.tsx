"use client";

import React, { createContext, useContext, useEffect, useState } from "react";
import { SiteSettings } from "@/hooks/useSettings";

interface SettingsContextType {
  settings: SiteSettings;
  loading: boolean;
  error: string | null;
  refreshSettings: () => Promise<void>;
}

const DEFAULT_SETTINGS: SiteSettings = {
  siteName: "NS Shop",
  siteDescription: "Cửa hàng thời trang trực tuyến",
  siteUrl: "https://nsshop.com",
  logo: "",
  favicon: "",
  contactEmail: "<EMAIL>",
  contactPhone: "0123456789",
  address: "123 Đường ABC, Quận 1, TP.HCM",
  // Enhanced structure for better component integration
  siteInfo: {
    name: "NS Shop",
    description:
      "Khám phá xu hướng thời trang mới nhất và phong cách độc đáo tại NS Shop. Chúng tôi mang đến cho bạn những sản phẩm chất lượng cao với giá cả hợp lý.",
    url: "https://nsshop.com",
    logo: "",
    favicon: "",
  },
  contactInfo: {
    email: "<EMAIL>",
    phone: "+**************",
    address: "123 Đường ABC, Quận 1, TP.HCM",
    district: "Quận 1",
    city: "TP. Hồ Chí Minh",
    province: "TP. Hồ Chí Minh",
    postalCode: "700000",
    country: "Vietnam",
    googleMapsUrl: "",
    googleMapsEmbed: "",
    latitude: null,
    longitude: null,
    businessHours: {
      monday: { open: "08:00", close: "18:00", closed: false },
      tuesday: { open: "08:00", close: "18:00", closed: false },
      wednesday: { open: "08:00", close: "18:00", closed: false },
      thursday: { open: "08:00", close: "18:00", closed: false },
      friday: { open: "08:00", close: "18:00", closed: false },
      saturday: { open: "08:00", close: "17:00", closed: false },
      sunday: { open: "09:00", close: "17:00", closed: false },
    },
    whatsappNumber: "",
    telegramUsername: "",
    skypeId: "",
  },
  socialMedia: {
    facebook: "https://facebook.com/nsshop",
    instagram: "https://instagram.com/nsshop",
    twitter: "https://twitter.com/nsshop",
    youtube: "https://youtube.com/nsshop",
    linkedin: "",
    tiktok: "",
    zalo: "https://zalo.me/**********",
  },
  paymentMethods: {
    cod: true,
    bankTransfer: true,
    creditCard: false,
  },
  shippingSettings: {
    freeShippingThreshold: 500000,
    shippingFee: 30000,
    estimatedDelivery: "2-3 ngày",
  },
  emailSettings: {
    smtpHost: "",
    smtpPort: 587,
    smtpUser: "",
    smtpPassword: "",
    fromEmail: "<EMAIL>",
    fromName: "NS Shop",
  },
  notifications: {
    orderNotifications: true,
    stockAlerts: true,
    customerNotifications: true,
  },
  seoSettings: {
    metaTitle: "NS Shop - Thời trang trực tuyến",
    metaDescription:
      "Cửa hàng thời trang trực tuyến với những sản phẩm chất lượng cao",
    metaKeywords: "thời trang, quần áo, giày dép, phụ kiện",
    googleAnalytics: "",
    facebookPixel: "",
  },
  securitySettings: {
    enableTwoFactor: false,
    sessionTimeout: 24,
    maxLoginAttempts: 5,
    enableCaptcha: false,
  },
};

const SettingsContext = createContext<SettingsContextType | undefined>(
  undefined
);

export function SettingsProvider({ children }: { children: React.ReactNode }) {
  const [settings, setSettings] = useState<SiteSettings>(DEFAULT_SETTINGS);
  const [loading, setLoading] = useState(false); // Start with false to avoid blocking UI
  const [error, setError] = useState<string | null>(null);

  const fetchSettings = async () => {
    try {
      setLoading(true);
      setError(null);

      // Add timeout to prevent hanging
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

      const response = await fetch("/api/settings", {
        cache: "no-store",
        headers: {
          "Content-Type": "application/json",
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        console.error(
          "Settings API response not ok:",
          response.status,
          response.statusText
        );
        throw new Error(`Failed to fetch settings: ${response.status}`);
      }

      const data = await response.json();
      console.log("Settings fetched successfully:", data);

      // Map API data to settings structure
      const mappedSettings: SiteSettings = {
        ...DEFAULT_SETTINGS,
        ...data,
        contactInfo: {
          ...DEFAULT_SETTINGS.contactInfo,
          email: data.contactEmail || DEFAULT_SETTINGS.contactInfo.email,
          phone: data.contactPhone || DEFAULT_SETTINGS.contactInfo.phone,
          address: data.address || DEFAULT_SETTINGS.contactInfo.address,
          district: data.district || DEFAULT_SETTINGS.contactInfo.district,
          city: data.city || DEFAULT_SETTINGS.contactInfo.city,
          province: data.province || DEFAULT_SETTINGS.contactInfo.province,
          postalCode:
            data.postalCode || DEFAULT_SETTINGS.contactInfo.postalCode,
          country: data.country || DEFAULT_SETTINGS.contactInfo.country,
          googleMapsUrl:
            data.googleMapsUrl || DEFAULT_SETTINGS.contactInfo.googleMapsUrl,
          googleMapsEmbed:
            data.googleMapsEmbed ||
            DEFAULT_SETTINGS.contactInfo.googleMapsEmbed,
          latitude: data.latitude || DEFAULT_SETTINGS.contactInfo.latitude,
          longitude: data.longitude || DEFAULT_SETTINGS.contactInfo.longitude,
          businessHours:
            data.businessHours || DEFAULT_SETTINGS.contactInfo.businessHours,
          whatsappNumber:
            data.whatsappNumber || DEFAULT_SETTINGS.contactInfo.whatsappNumber,
          telegramUsername:
            data.telegramUsername ||
            DEFAULT_SETTINGS.contactInfo.telegramUsername,
          skypeId: data.skypeId || DEFAULT_SETTINGS.contactInfo.skypeId,
        },
        socialMedia: {
          ...DEFAULT_SETTINGS.socialMedia,
          ...data.socialMedia,
        },
      };

      setSettings(mappedSettings);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Unknown error";
      setError(errorMessage);
      console.error("Error fetching settings:", err);
      // Keep default settings on error
      setSettings(DEFAULT_SETTINGS);
    } finally {
      setLoading(false);
    }
  };

  const refreshSettings = async () => {
    await fetchSettings();
  };

  useEffect(() => {
    fetchSettings();
  }, []);

  const value: SettingsContextType = {
    settings,
    loading,
    error,
    refreshSettings,
  };

  return (
    <SettingsContext.Provider value={value}>
      {children}
    </SettingsContext.Provider>
  );
}

export function useSettingsContext() {
  const context = useContext(SettingsContext);
  if (context === undefined) {
    throw new Error(
      "useSettingsContext must be used within a SettingsProvider"
    );
  }
  return context;
}
