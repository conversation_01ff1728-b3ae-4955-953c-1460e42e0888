"use client";

import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  useCallback,
} from "react";
import { useRouter } from "next/navigation";

interface AdminUser {
  id: string;
  email: string;
  name: string;
  role: string;
  avatar?: string;
  type: "admin";
  department?: string;
  permissions?: Record<string, boolean>;
}

interface AdminAuthContextType {
  adminUser: AdminUser | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  login: (userData: AdminUser) => void;
  logout: () => void;
  refreshAuth: () => Promise<void>;
}

const AdminAuthContext = createContext<AdminAuthContextType | undefined>(
  undefined
);

const CACHE_KEY = "admin-auth-cache";
const CACHE_EXPIRY_HOURS = 6; // Cache for 6 hours

interface CachedAuthData {
  adminUser: AdminUser;
  timestamp: number;
  expiresAt: number;
}

// Helper functions for localStorage
const getCachedAuth = (): CachedAuthData | null => {
  if (typeof window === "undefined") return null;

  try {
    const cached = localStorage.getItem(CACHE_KEY);
    if (!cached) return null;

    const data: CachedAuthData = JSON.parse(cached);

    // Check if cache is expired
    if (Date.now() > data.expiresAt) {
      localStorage.removeItem(CACHE_KEY);
      return null;
    }

    return data;
  } catch {
    localStorage.removeItem(CACHE_KEY);
    return null;
  }
};

const setCachedAuth = (adminUser: AdminUser) => {
  if (typeof window === "undefined") return;

  const cacheData: CachedAuthData = {
    adminUser,
    timestamp: Date.now(),
    expiresAt: Date.now() + CACHE_EXPIRY_HOURS * 60 * 60 * 1000,
  };

  localStorage.setItem(CACHE_KEY, JSON.stringify(cacheData));
};

const clearCachedAuth = () => {
  if (typeof window === "undefined") return;
  localStorage.removeItem(CACHE_KEY);
};

export function AdminAuthProvider({ children }: { children: React.ReactNode }) {
  const [adminUser, setAdminUser] = useState<AdminUser | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const verifyAuth = useCallback(
    async (showLoading = true) => {
      try {
        if (showLoading) setIsLoading(true);
        setError(null);

        const response = await fetch("/api/admin-verify", {
          method: "GET",
          credentials: "include",
        });

        if (response.ok) {
          const data = await response.json();
          const userData: AdminUser = {
            id: data.admin.id,
            email: data.admin.email,
            name: data.admin.name || data.admin.email,
            role: data.admin.role,
            type: "admin",
          };

          setAdminUser(userData);
          setIsAuthenticated(true);
          setCachedAuth(userData);
        } else {
          setAdminUser(null);
          setIsAuthenticated(false);
          clearCachedAuth();

          // Only redirect if we're not already on auth page
          if (!window.location.pathname.startsWith("/admin/auth")) {
            router.push("/admin/auth/signin");
          }
        }
      } catch (error) {
        console.error("Error verifying admin auth:", error);
        setError("Có lỗi xảy ra khi kiểm tra quyền truy cập");
        setAdminUser(null);
        setIsAuthenticated(false);
        clearCachedAuth();

        if (!window.location.pathname.startsWith("/admin/auth")) {
          router.push("/admin/auth/signin");
        }
      } finally {
        if (showLoading) setIsLoading(false);
      }
    },
    [router]
  );

  const login = useCallback((userData: AdminUser) => {
    setAdminUser(userData);
    setIsAuthenticated(true);
    setError(null);
    setCachedAuth(userData);
  }, []);

  const logout = useCallback(async () => {
    try {
      // Call logout API
      await fetch("/api/admin-logout", {
        method: "POST",
        credentials: "include",
      });
    } catch (error) {
      console.error("Logout error:", error);
    } finally {
      // Clear state regardless of API call result
      setAdminUser(null);
      setIsAuthenticated(false);
      setError(null);
      clearCachedAuth();
      router.push("/admin/auth/signin");
    }
  }, [router]);

  const refreshAuth = useCallback(async () => {
    await verifyAuth(false); // Refresh without showing loading
  }, [verifyAuth]);

  useEffect(() => {
    // Check for cached auth data first
    const cachedAuth = getCachedAuth();

    if (cachedAuth) {
      // Use cached data immediately for better UX
      setAdminUser(cachedAuth.adminUser);
      setIsAuthenticated(true);
      setIsLoading(false);

      // Check if cache is close to expiry (within 1 hour)
      const oneHourFromNow = Date.now() + 60 * 60 * 1000;
      if (cachedAuth.expiresAt < oneHourFromNow) {
        // Refresh in background
        verifyAuth(false);
      }
    } else {
      // No cached data, need to verify
      verifyAuth(true);
    }
  }, [verifyAuth]);

  // Set up periodic refresh (every 30 minutes)
  useEffect(() => {
    if (!isAuthenticated) return;

    const interval = setInterval(
      () => {
        refreshAuth();
      },
      30 * 60 * 1000
    ); // 30 minutes

    return () => clearInterval(interval);
  }, [isAuthenticated, refreshAuth]);

  const value: AdminAuthContextType = {
    adminUser,
    isAuthenticated,
    isLoading,
    error,
    login,
    logout,
    refreshAuth,
  };

  return (
    <AdminAuthContext.Provider value={value}>
      {children}
    </AdminAuthContext.Provider>
  );
}

export function useAdminAuth() {
  const context = useContext(AdminAuthContext);
  if (context === undefined) {
    throw new Error("useAdminAuth must be used within an AdminAuthProvider");
  }
  return context;
}
