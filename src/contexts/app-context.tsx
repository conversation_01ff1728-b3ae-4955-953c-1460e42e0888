"use client";

import React, { ReactNode } from "react";
import { EnhancedCartProvider } from "./enhanced-cart-context";
import { UserProvider } from "./user-context";
import { SearchProvider } from "./search-context";
import { SettingsProvider } from "./SettingsContext";
import { NotificationProvider } from "./NotificationContext";
import { ContextErrorBoundary } from "@/components/providers/ErrorBoundaryProvider";

interface AppContextProviderProps {
  children: ReactNode;
}

/**
 * Main App Context Provider that wraps all other context providers
 * This ensures proper order and dependency management between contexts
 */
export function AppContextProvider({ children }: AppContextProviderProps) {
  return (
    <ContextErrorBoundary contextName="Settings">
      <SettingsProvider>
        <ContextErrorBoundary contextName="Notifications">
          <NotificationProvider>
            <ContextErrorBoundary contextName="User">
              <UserProvider>
                <ContextErrorBoundary contextName="Cart">
                  <EnhancedCartProvider>
                    <ContextErrorBoundary contextName="Search">
                      <SearchProvider>{children}</SearchProvider>
                    </ContextErrorBoundary>
                  </EnhancedCartProvider>
                </ContextErrorBoundary>
              </UserProvider>
            </ContextErrorBoundary>
          </NotificationProvider>
        </ContextErrorBoundary>
      </SettingsProvider>
    </ContextErrorBoundary>
  );
}

// Re-export all context hooks for convenience
export { useEnhancedCart, useCartContext } from "./enhanced-cart-context";
export { useUserContext } from "./user-context";
export { useSearchContext } from "./search-context";
export { useSettingsContext } from "./SettingsContext";
export { useNotifications } from "./NotificationContext";
