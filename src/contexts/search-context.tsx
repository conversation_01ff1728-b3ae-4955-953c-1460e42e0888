"use client";

import React, { createContext, useContext, ReactNode, useState, useCallback } from "react";
import { useSearch, useSearchSuggestions, useSearchHistory, usePopularSearches, useAdvancedSearch } from "@/hooks";
import { Product, Category } from "@/types";

interface SearchFilters {
  categoryId?: string;
  minPrice?: number;
  maxPrice?: number;
  inStock?: boolean;
  featured?: boolean;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

interface SearchContextType {
  // Search state
  query: string;
  results: {
    products: Product[];
    categories: Category[];
    total: number;
  };
  loading: boolean;
  error: string | null;
  hasSearched: boolean;

  // Search actions
  search: (query: string, options?: {
    includeCategories?: boolean;
    limit?: number;
    categoryId?: string;
  }) => Promise<void>;
  clearSearch: () => void;
  setQuery: (query: string) => void;

  // Advanced search
  advancedSearch: (filters: SearchFilters & { query?: string }) => Promise<void>;
  advancedResults: {
    products: Product[];
    total: number;
  };
  advancedLoading: boolean;
  advancedError: string | null;
  clearAdvancedSearch: () => void;

  // Search suggestions
  suggestions: string[];
  suggestionsLoading: boolean;
  getSuggestions: (query: string) => void;

  // Search history
  history: string[];
  addToHistory: (query: string) => void;
  removeFromHistory: (query: string) => void;
  clearHistory: () => void;

  // Popular searches
  popularSearches: string[];
  popularLoading: boolean;

  // Filters
  filters: SearchFilters;
  setFilters: (filters: SearchFilters) => void;
  clearFilters: () => void;

  // Utilities
  isSearching: boolean;
  hasResults: boolean;
  totalResults: number;
}

const SearchContext = createContext<SearchContextType | undefined>(undefined);

interface SearchProviderProps {
  children: ReactNode;
}

export function SearchProvider({ children }: SearchProviderProps) {
  // Local state
  const [query, setQuery] = useState("");
  const [filters, setFilters] = useState<SearchFilters>({});

  // Use our custom hooks
  const {
    results,
    loading,
    error,
    hasSearched,
    search,
    clearSearch,
  } = useSearch();

  const {
    results: advancedResults,
    loading: advancedLoading,
    error: advancedError,
    hasSearched: hasAdvancedSearched,
    advancedSearch,
    clearSearch: clearAdvancedSearch,
  } = useAdvancedSearch();

  const {
    suggestions,
    loading: suggestionsLoading,
  } = useSearchSuggestions(query, 300);

  const {
    history,
    addToHistory,
    removeFromHistory,
    clearHistory,
  } = useSearchHistory();

  const {
    searches: popularSearches,
    loading: popularLoading,
  } = usePopularSearches();

  // Enhanced search function
  const handleSearch = useCallback(async (searchQuery: string, options?: {
    includeCategories?: boolean;
    limit?: number;
    categoryId?: string;
  }) => {
    setQuery(searchQuery);
    await search(searchQuery, options);
  }, [search]);

  // Enhanced advanced search
  const handleAdvancedSearch = useCallback(async (searchFilters: SearchFilters & { query?: string }) => {
    setFilters(searchFilters);
    if (searchFilters.query) {
      setQuery(searchFilters.query);
    }
    await advancedSearch(searchFilters);
  }, [advancedSearch]);

  // Get suggestions
  const getSuggestions = useCallback((searchQuery: string) => {
    setQuery(searchQuery);
  }, []);

  // Clear filters
  const clearFilters = useCallback(() => {
    setFilters({});
  }, []);

  // Enhanced clear search
  const handleClearSearch = useCallback(() => {
    setQuery("");
    clearSearch();
    clearAdvancedSearch();
    clearFilters();
  }, [clearSearch, clearAdvancedSearch, clearFilters]);

  // Computed values
  const isSearching = loading || advancedLoading;
  const hasResults = (hasSearched && results.total > 0) || (hasAdvancedSearched && advancedResults.total > 0);
  const totalResults = hasAdvancedSearched ? advancedResults.total : results.total;

  const value: SearchContextType = {
    // Search state
    query,
    results,
    loading,
    error,
    hasSearched,

    // Search actions
    search: handleSearch,
    clearSearch: handleClearSearch,
    setQuery,

    // Advanced search
    advancedSearch: handleAdvancedSearch,
    advancedResults,
    advancedLoading,
    advancedError,
    clearAdvancedSearch,

    // Search suggestions
    suggestions,
    suggestionsLoading,
    getSuggestions,

    // Search history
    history,
    addToHistory,
    removeFromHistory,
    clearHistory,

    // Popular searches
    popularSearches,
    popularLoading,

    // Filters
    filters,
    setFilters,
    clearFilters,

    // Utilities
    isSearching,
    hasResults,
    totalResults,
  };

  return (
    <SearchContext.Provider value={value}>
      {children}
    </SearchContext.Provider>
  );
}

export function useSearchContext() {
  const context = useContext(SearchContext);
  if (context === undefined) {
    throw new Error("useSearchContext must be used within a SearchProvider");
  }
  return context;
}
