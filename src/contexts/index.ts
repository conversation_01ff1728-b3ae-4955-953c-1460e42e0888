// Main App Context Provider
export { AppContextProvider } from "./app-context";

// Theme Provider
export { ThemeProvider, useTheme, ThemeToggle } from "@/providers/theme-provider";

// Standardized Hook Exports (Recommended imports)
export { useSettingsContext as useSettings } from "./SettingsContext";
export { useUserContext as useUser } from "./user-context";
export { useEnhancedCart as useCart } from "./enhanced-cart-context";
export { useSearchContext as useSearch } from "./search-context";
export { useNotifications } from "./NotificationContext";

// Direct exports (for specific use cases)
export {
  EnhancedCartProvider,
  useEnhancedCart,
  useCartContext,
} from "./enhanced-cart-context";
export { UserProvider, useUserContext } from "./user-context";
export { SearchProvider, useSearchContext } from "./search-context";
export { useSettingsContext } from "./SettingsContext";

// Admin contexts
export { useAdminAuth } from "./AdminAuthContext";
export { useAdmin, useAdminState } from "./AdminContext";

// Legacy cart context (for backward compatibility)
export { CartProvider, useCart as useLegacyCart } from "./cart-context";
