"use client";

import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  useCallback,
  useMemo,
  useRef,
} from "react";
import { usePathname } from "next/navigation";
import {
  Notification,
  NotificationContextType,
  NotificationTarget,
} from "@/types/notification";

const NotificationContext = createContext<NotificationContextType | undefined>(
  undefined
);

export function useNotifications() {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error(
      "useNotifications must be used within a NotificationProvider"
    );
  }
  return context;
}

// Additional hook for notification utilities
export function useNotificationUtils() {
  const { createNotification } = useNotifications();

  const showSuccess = (
    title: string,
    message: string,
    options?: {
      actionUrl?: string;
      metadata?: any;
      targetType?: NotificationTarget;
      targetId?: string;
    }
  ) => {
    return createNotification({
      title,
      message,
      type: "SUCCESS",
      priority: "NORMAL",
      ...options,
    });
  };

  const showError = (
    title: string,
    message: string,
    options?: {
      actionUrl?: string;
      metadata?: any;
      targetType?: NotificationTarget;
      targetId?: string;
    }
  ) => {
    return createNotification({
      title,
      message,
      type: "ERROR",
      priority: "HIGH",
      ...options,
    });
  };

  const showWarning = (
    title: string,
    message: string,
    options?: {
      actionUrl?: string;
      metadata?: any;
      targetType?: NotificationTarget;
      targetId?: string;
    }
  ) => {
    return createNotification({
      title,
      message,
      type: "WARNING",
      priority: "NORMAL",
      ...options,
    });
  };

  const showInfo = (
    title: string,
    message: string,
    options?: {
      actionUrl?: string;
      metadata?: any;
      targetType?: NotificationTarget;
      targetId?: string;
    }
  ) => {
    return createNotification({
      title,
      message,
      type: "INFO",
      priority: "LOW",
      ...options,
    });
  };

  const showSystemAlert = (
    title: string,
    message: string,
    options?: {
      actionUrl?: string;
      metadata?: any;
      priority?: "LOW" | "NORMAL" | "HIGH" | "URGENT";
    }
  ) => {
    return createNotification({
      title,
      message,
      type: "SYSTEM",
      priority: options?.priority || "HIGH",
      targetType: "ALL_ADMINS",
      actionUrl: options?.actionUrl,
      metadata: options?.metadata,
    });
  };

  return {
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showSystemAlert,
  };
}

interface NotificationProviderProps {
  children: React.ReactNode;
}

export function NotificationProvider({ children }: NotificationProviderProps) {
  const pathname = usePathname();
  const isAdminRoute = pathname?.startsWith("/admin");

  // Memoize admin user to prevent recreation on every render
  const adminUser = useMemo(() => {
    return isAdminRoute ? { id: "admin", role: "ADMIN", type: "admin" } : null;
  }, [isAdminRoute]);

  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch notifications manually
  const refreshNotifications = useCallback(async () => {
    if (!adminUser || adminUser.type !== "admin") {
      return;
    }

    try {
      setIsLoading(true);
      const response = await fetch("/api/admin/notifications?limit=10");

      if (!response.ok) {
        throw new Error("Failed to fetch notifications");
      }

      const data = await response.json();
      setNotifications(data.notifications || []);

      // Calculate unread count
      const unread = (data.notifications || []).filter(
        (n: Notification) => !n.isRead
      ).length;
      setUnreadCount(unread);

      setError(null);
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Failed to fetch notifications"
      );
    } finally {
      setIsLoading(false);
    }
  }, [adminUser]);

  // Use ref to store stable reference for polling
  const refreshNotificationsRef = useRef(refreshNotifications);
  refreshNotificationsRef.current = refreshNotifications;

  // Initialize and set up polling for notifications
  useEffect(() => {
    if (adminUser && adminUser.type === "admin") {
      // Fetch notifications immediately
      refreshNotificationsRef.current();

      // Set up polling every 30 seconds
      const pollInterval = setInterval(() => {
        refreshNotificationsRef.current();
      }, 30000);

      return () => {
        clearInterval(pollInterval);
      };
    }
  }, [adminUser]); // Remove refreshNotifications from dependencies

  // Use refs to prevent dependency chains
  const notificationsRef = useRef(notifications);
  const adminUserRef = useRef(adminUser);
  notificationsRef.current = notifications;
  adminUserRef.current = adminUser;

  // Mark notification as read
  const markAsRead = useCallback(
    async (notificationId: string) => {
      try {
        // Check if notification is already read to prevent unnecessary API calls
        const notification = notificationsRef.current.find(
          (n) => n.id === notificationId
        );
        if (notification?.isRead) {
          return; // Already read, no need to make API call
        }

        const response = await fetch(
          `/api/admin/notifications/${notificationId}`,
          {
            method: "PUT",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ isRead: true }),
          }
        );

        if (!response.ok) {
          throw new Error("Failed to mark notification as read");
        }

        // Update local state optimistically
        setNotifications((prev) =>
          prev.map((n) =>
            n.id === notificationId
              ? { ...n, isRead: true, readAt: new Date().toISOString() }
              : n
          )
        );

        setUnreadCount((prev) => Math.max(0, prev - 1));

        // Remove setTimeout refresh - rely on polling interval
      } catch (err) {
        console.error("Error marking notification as read:", err);
        throw err;
      }
    },
    [] // Remove all dependencies
  );

  // Mark notification as unread
  const markAsUnread = useCallback(
    async (notificationId: string) => {
      try {
        // Check if notification is already unread to prevent unnecessary API calls
        const notification = notificationsRef.current.find(
          (n) => n.id === notificationId
        );
        if (!notification?.isRead) {
          return; // Already unread, no need to make API call
        }

        const response = await fetch(
          `/api/admin/notifications/${notificationId}`,
          {
            method: "PUT",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ isRead: false }),
          }
        );

        if (!response.ok) {
          throw new Error("Failed to mark notification as unread");
        }

        // Update local state optimistically
        setNotifications((prev) =>
          prev.map((n) =>
            n.id === notificationId
              ? { ...n, isRead: false, readAt: undefined }
              : n
          )
        );

        setUnreadCount((prev) => prev + 1);

        // Remove setTimeout refresh - rely on polling interval
      } catch (err) {
        console.error("Error marking notification as unread:", err);
        throw err;
      }
    },
    [] // Remove all dependencies
  );

  // Mark all notifications as read
  const markAllAsRead = useCallback(async () => {
    try {
      const unreadIds = notificationsRef.current
        .filter((n) => !n.isRead)
        .map((n) => n.id);

      if (unreadIds.length === 0) {
        return;
      }

      // Optimistic update - mark all as read immediately
      setNotifications((prev) =>
        prev.map((n) =>
          unreadIds.includes(n.id)
            ? { ...n, isRead: true, readAt: new Date().toISOString() }
            : n
        )
      );
      setUnreadCount(0);

      const response = await fetch("/api/admin/notifications/bulk", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action: "mark_read",
          notificationIds: unreadIds,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to mark all notifications as read");
      }

      // Remove setTimeout refresh - rely on polling interval
    } catch (err) {
      console.error("Error marking all notifications as read:", err);
      // On error, refresh immediately once
      refreshNotificationsRef.current();
      throw err;
    }
  }, []); // Remove all dependencies

  // Delete notification
  const deleteNotification = useCallback(
    async (notificationId: string) => {
      try {
        const response = await fetch(
          `/api/admin/notifications/${notificationId}`,
          {
            method: "DELETE",
          }
        );

        if (!response.ok) {
          throw new Error("Failed to delete notification");
        }

        // Optimistic update - remove from local state
        setNotifications((prev) => prev.filter((n) => n.id !== notificationId));

        const deletedNotification = notificationsRef.current.find(
          (n) => n.id === notificationId
        );
        if (deletedNotification && !deletedNotification.isRead) {
          setUnreadCount((prev) => Math.max(0, prev - 1));
        }

        // Remove immediate refresh - rely on polling interval
      } catch (err) {
        console.error("Error deleting notification:", err);
        // On error, refresh immediately once
        refreshNotificationsRef.current();
        throw err;
      }
    },
    [] // Remove all dependencies
  );

  // Create new notification
  const createNotification = useCallback(
    async (notification: Partial<Notification>) => {
      try {
        const response = await fetch("/api/admin/notifications", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(notification),
        });

        if (!response.ok) {
          throw new Error("Failed to create notification");
        }

        const data = await response.json();

        // Add to local state if it targets current user
        const newNotification = data.notification;
        const currentAdminUser = adminUserRef.current;
        const shouldShow =
          newNotification.targetType === "ALL_ADMINS" ||
          (newNotification.targetType === "SPECIFIC_ADMIN" &&
            newNotification.targetId === currentAdminUser?.id) ||
          newNotification.targetType === `ROLE_${currentAdminUser?.role}`;

        if (shouldShow) {
          setNotifications((prev) => [newNotification, ...prev]);
          if (!newNotification.isRead) {
            setUnreadCount((prev) => prev + 1);
          }
        }
      } catch (err) {
        console.error("Error creating notification:", err);
        throw err;
      }
    },
    [] // Remove adminUser dependency
  );

  const value: NotificationContextType = useMemo(
    () => ({
      notifications,
      unreadCount,
      isConnected: true, // Always true since we're using simple polling
      isLoading,
      error,
      markAsRead,
      markAsUnread,
      markAllAsRead,
      deleteNotification,
      refreshNotifications,
      createNotification,
    }),
    [
      notifications,
      unreadCount,
      isLoading,
      error,
      // refreshNotifications is stabilized via ref, so we don't need it here
      // All callbacks are now stable with empty deps
    ]
  );

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
}
