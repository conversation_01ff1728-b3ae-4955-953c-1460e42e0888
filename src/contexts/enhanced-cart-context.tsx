"use client";

import React, { createContext, useContext, ReactNode } from "react";
import { useCart, useQuickAddToCart, useCartValidation } from "@/hooks";
import { Cart } from "@/types";

interface EnhancedCartContextType {
  // Cart state
  cart: Cart | null;
  loading: boolean;
  error: string | null;
  summary: {
    subtotal: number;
    tax: number;
    shipping: number;
    total: number;
    itemCount: number;
    totalQuantity: number;
  } | null;

  // Cart actions
  addToCart: (data: {
    productId: string;
    quantity: number;
    variantId?: string;
  }) => Promise<void>;
  updateCartItem: (itemId: string, data: { quantity: number }) => Promise<void>;
  removeFromCart: (itemId: string) => Promise<void>;
  clearCart: () => Promise<void>;
  refetch: () => void;

  // Quick actions
  quickAdd: (productId: string, quantity?: number) => Promise<void>;
  quickAddLoading: boolean;

  // Validation
  validationErrors: Array<{
    itemId: string;
    message: string;
    type: "stock" | "price" | "availability";
  }>;
  isValid: boolean;

  // Utilities
  getItemQuantity: (productId: string) => number;
  isInCart: (productId: string) => boolean;
  canAddToCart: (productId: string, quantity: number) => boolean;
}

const EnhancedCartContext = createContext<EnhancedCartContextType | undefined>(
  undefined
);

interface EnhancedCartProviderProps {
  children: ReactNode;
}

export function EnhancedCartProvider({ children }: EnhancedCartProviderProps) {
  // Use our custom hooks
  const {
    cart,
    loading,
    error,
    summary,
    addToCart,
    updateCartItem,
    removeFromCart,
    clearCart,
    refetch,
  } = useCart();

  const { quickAdd, loading: quickAddLoading } = useQuickAddToCart();

  const { isValid, invalidItems } = useCartValidation(cart);

  // Transform invalidItems to validationErrors format
  const validationErrors = invalidItems.map(({ item, reason }) => ({
    itemId: item.id,
    message: reason,
    type: "availability" as const,
  }));

  // Utility functions
  const getItemQuantity = (productId: string): number => {
    if (!cart) return 0;
    const item = cart.items.find((item) => item.productId === productId);
    return item ? item.quantity : 0;
  };

  const isInCart = (productId: string): boolean => {
    return getItemQuantity(productId) > 0;
  };

  const canAddToCart = (productId: string, quantity: number): boolean => {
    if (!cart) return true;
    const currentQuantity = getItemQuantity(productId);
    const item = cart.items.find((item) => item.productId === productId);

    if (!item) return true; // New item, assume it can be added

    // Check stock availability (assuming product has stock info)
    const product = item.product;
    if (product && "stock" in product) {
      return currentQuantity + quantity <= (product as any).stock;
    }

    return true;
  };

  const value: EnhancedCartContextType = {
    // State
    cart,
    loading,
    error,
    summary,

    // Actions
    addToCart: async (data) => {
      await addToCart(data);
    },
    updateCartItem: async (itemId, data) => {
      await updateCartItem(itemId, data);
    },
    removeFromCart: async (itemId) => {
      await removeFromCart(itemId);
    },
    clearCart,
    refetch,

    // Quick actions
    quickAdd: async (productId, quantity) => {
      await quickAdd(productId, quantity);
    },
    quickAddLoading,

    // Validation
    validationErrors,
    isValid,

    // Utilities
    getItemQuantity,
    isInCart,
    canAddToCart,
  };

  return (
    <EnhancedCartContext.Provider value={value}>
      {children}
    </EnhancedCartContext.Provider>
  );
}

export function useEnhancedCart() {
  const context = useContext(EnhancedCartContext);
  if (context === undefined) {
    throw new Error(
      "useEnhancedCart must be used within an EnhancedCartProvider"
    );
  }
  return context;
}

// Backward compatibility hook
export function useCartContext() {
  return useEnhancedCart();
}
