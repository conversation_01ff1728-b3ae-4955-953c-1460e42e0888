"use client";

import { usePathname } from "next/navigation";
import { useCart } from "@/contexts/cart-context";

/**
 * Safe cart hook that handles admin routes gracefully
 * Returns null cart state for admin routes to prevent errors
 */
export function useCartSafe() {
  const pathname = usePathname();
  const isAdminRoute = pathname?.startsWith("/admin");
  
  // For admin routes, return a mock cart state
  if (isAdminRoute) {
    return {
      state: {
        cart: null,
        loading: false,
        error: null,
      },
      fetchCart: async () => {},
      addToCart: async () => {},
      updateCartItem: async () => {},
      removeFromCart: async () => {},
      clearCart: () => {},
    };
  }
  
  // For customer routes, use the real cart hook
  return useCart();
}
