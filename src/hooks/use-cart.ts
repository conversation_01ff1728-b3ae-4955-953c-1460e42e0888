"use client";

import { useState, useCallback, useEffect } from "react";
import { CartService } from "@/lib/services";
import { useAuth } from "@/providers/auth-provider";

interface UseCartState {
  cart: any | null;
  loading: boolean;
  error: string | null;
  summary: any | null;
}

export interface AddToCartData {
  productId: string;
  quantity: number;
  variantId?: string;
}

// Main cart hook
export function useCart() {
  const { isAuthenticated, isLoading: authLoading, queueRequest } = useAuth();
  const [state, setState] = useState<UseCartState>({
    cart: null,
    loading: true,
    error: null,
    summary: null,
  });

  const fetchCart = useCallback(async () => {
    if (!isAuthenticated) {
      setState({
        cart: null,
        loading: false,
        error: null,
        summary: null,
      });
      return;
    }

    try {
      setState((prev) => ({ ...prev, loading: true, error: null }));

      const response = (await queueRequest(() => CartService.getCart())) as any;

      const summary = response.data
        ? CartService.calculateCartSummary(response.data, 0, 0.1)
        : null;

      setState({
        cart: response.data || null,
        loading: false,
        error: null,
        summary,
      });
    } catch (error) {
      setState((prev) => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : "Failed to fetch cart",
      }));
    }
  }, [isAuthenticated, queueRequest]);

  // Only fetch when auth is resolved and user is authenticated
  useEffect(() => {
    if (!authLoading) {
      fetchCart();
    }
  }, [authLoading, fetchCart]);

  const addToCart = useCallback(
    async (data: AddToCartData) => {
      if (!isAuthenticated) {
        throw new Error("Not authenticated");
      }

      try {
        setState((prev) => ({ ...prev, loading: true, error: null }));

        const response = (await queueRequest(() =>
          CartService.addToCart(data)
        )) as any;

        const summary = response.data
          ? CartService.calculateCartSummary(response.data, 0, 0.1)
          : null;

        setState((prev) => ({
          ...prev,
          cart: response.data || null,
          loading: false,
          summary,
        }));

        return response;
      } catch (error) {
        setState((prev) => ({
          ...prev,
          loading: false,
          error:
            error instanceof Error ? error.message : "Failed to add to cart",
        }));
        throw error;
      }
    },
    [isAuthenticated, queueRequest]
  );

  const updateCartItem = useCallback(
    async (itemId: string, quantity: number) => {
      if (!isAuthenticated) {
        throw new Error("Not authenticated");
      }

      try {
        setState((prev) => ({ ...prev, loading: true, error: null }));

        const response = (await queueRequest(() =>
          CartService.updateCartItem(itemId, { quantity })
        )) as any;

        const summary = response.data
          ? CartService.calculateCartSummary(response.data, 0, 0.1)
          : null;

        setState((prev) => ({
          ...prev,
          cart: response.data || null,
          loading: false,
          summary,
        }));

        return response;
      } catch (error) {
        setState((prev) => ({
          ...prev,
          loading: false,
          error:
            error instanceof Error
              ? error.message
              : "Failed to update cart item",
        }));
        throw error;
      }
    },
    [isAuthenticated, queueRequest]
  );

  const removeFromCart = useCallback(
    async (itemId: string) => {
      if (!isAuthenticated) {
        throw new Error("Not authenticated");
      }

      try {
        setState((prev) => ({ ...prev, loading: true, error: null }));

        const response = (await queueRequest(() =>
          CartService.removeFromCart(itemId)
        )) as any;

        const summary = response.data
          ? CartService.calculateCartSummary(response.data, 0, 0.1)
          : null;

        setState((prev) => ({
          ...prev,
          cart: response.data || null,
          loading: false,
          summary,
        }));

        return response;
      } catch (error) {
        setState((prev) => ({
          ...prev,
          loading: false,
          error:
            error instanceof Error
              ? error.message
              : "Failed to remove from cart",
        }));
        throw error;
      }
    },
    [isAuthenticated, queueRequest]
  );

  const clearCart = useCallback(async () => {
    if (!isAuthenticated) {
      throw new Error("Not authenticated");
    }

    try {
      setState((prev) => ({ ...prev, loading: true, error: null }));

      await queueRequest(() => CartService.clearCart());

      setState({
        cart: null,
        loading: false,
        error: null,
        summary: null,
      });
    } catch (error) {
      setState((prev) => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : "Failed to clear cart",
      }));
      throw error;
    }
  }, [isAuthenticated, queueRequest]);

  return {
    ...state,
    fetchCart,
    addToCart,
    updateCartItem,
    removeFromCart,
    clearCart,
    refetch: fetchCart,
  };
}

// Cart item count hook (lightweight)
export function useCartItemCount() {
  const [count, setCount] = useState(0);
  const [loading, setLoading] = useState(true);

  const fetchCount = useCallback(async () => {
    try {
      setLoading(true);
      const response = await CartService.getCart();
      setCount(
        response.data?.items?.reduce((sum, item) => sum + item.quantity, 0) || 0
      );
    } catch {
      setCount(0);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchCount();
  }, [fetchCount]);

  return {
    count,
    loading,
    refetch: fetchCount,
  };
}

// Quick add to cart hook
export function useQuickAddToCart() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const quickAdd = useCallback(
    async (productId: string, quantity: number = 1) => {
      try {
        setLoading(true);
        setError(null);

        const response = await CartService.addToCart({
          productId,
          quantity,
        });

        return response;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to add to cart";
        setError(errorMessage);
        throw error;
      } finally {
        setLoading(false);
      }
    },
    []
  );

  return {
    quickAdd,
    loading,
    error,
  };
}

// Cart validation hook
export function useCartValidation(cart: any | null) {
  const [validation, setValidation] = useState<{
    isValid: boolean;
    invalidItems: Array<{ item: any; reason: string }>;
  }>({ isValid: true, invalidItems: [] });

  useEffect(() => {
    if (!cart) {
      setValidation({ isValid: true, invalidItems: [] });
      return;
    }

    const result = CartService.validateCart(cart);
    setValidation(result);
  }, [cart]);

  return validation;
}

// Guest cart merge hook
export function useGuestCartMerge() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const mergeGuestCart = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await CartService.mergeGuestCart();
      return response;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to merge guest cart";
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    mergeGuestCart,
    loading,
    error,
  };
}
