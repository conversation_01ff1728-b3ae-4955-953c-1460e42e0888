import { useState, useEffect } from "react";
import { toast } from "sonner";

export interface ReviewFormData {
  productId?: string;
  rating: number;
  title: string;
  comment: string;
}

export interface ReviewListFilters {
  productId?: string;
  rating?: number;
  sortBy?: string;
  limit?: number;
  offset?: number;
}

export interface ReviewData {
  id: string;
  rating: number;
  title: string;
  comment: string;
  productId: string;
  userId: string;
  createdAt: string;
  updatedAt: string;
}

export interface UseReviewsOptions {
  onSubmitSuccess?: (review: ReviewData) => void;
  onSubmitError?: (error: string) => void;
}

export function useReviews(options: UseReviewsOptions = {}) {
  const [reviews, setReviews] = useState<ReviewData[]>([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  });

  const fetchReviews = async (filters: ReviewListFilters = {}) => {
    setLoading(true);
    try {
      const params = new URLSearchParams();
      
      if (filters.productId) params.append("productId", filters.productId);
      if (filters.rating) params.append("rating", filters.rating.toString());
      if (filters.sortBy) params.append("sortBy", filters.sortBy);
      if (filters.limit) params.append("limit", filters.limit.toString());
      if (filters.offset) params.append("offset", filters.offset.toString());

      const response = await fetch(`/api/reviews?${params}`);
      const data = await response.json();

      if (response.ok) {
        setReviews(data.reviews || []);
        if (data.pagination) {
          setPagination(data.pagination);
        }
      } else {
        throw new Error(data.error || "Có lỗi xảy ra khi tải đánh giá");
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Có lỗi xảy ra khi tải đánh giá";
      toast.error(errorMessage);
      console.error("Fetch reviews error:", error);
    } finally {
      setLoading(false);
    }
  };

  const submitReview = async (reviewData: ReviewFormData) => {
    setSubmitting(true);
    try {
      const response = await fetch("/api/reviews", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(reviewData),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Có lỗi xảy ra khi gửi đánh giá");
      }

      toast.success("Gửi đánh giá thành công!");
      
      if (options.onSubmitSuccess && result.review) {
        options.onSubmitSuccess(result.review);
      }

      // Refresh reviews after successful submission
      await fetchReviews();

      return { success: true, review: result.review };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Có lỗi xảy ra khi gửi đánh giá";
      toast.error(errorMessage);
      
      if (options.onSubmitError) {
        options.onSubmitError(errorMessage);
      }

      return { success: false, error: errorMessage };
    } finally {
      setSubmitting(false);
    }
  };

  return {
    reviews,
    loading,
    submitting,
    pagination,
    fetchReviews,
    submitReview,
  };
}

export function useProductReviews(productId: string) {
  const [reviews, setReviews] = useState<ReviewData[]>([]);
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState({
    averageRating: 0,
    totalReviews: 0,
    ratingDistribution: {} as Record<number, number>,
  });

  const fetchProductReviews = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/reviews?productId=${productId}`);
      const data = await response.json();

      if (response.ok) {
        setReviews(data.reviews || []);
        if (data.stats) {
          setStats(data.stats);
        }
      } else {
        throw new Error(data.error || "Có lỗi xảy ra khi tải đánh giá");
      }
    } catch (error) {
      console.error("Fetch product reviews error:", error);
      toast.error("Có lỗi xảy ra khi tải đánh giả sản phẩm");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (productId) {
      fetchProductReviews();
    }
  }, [productId]);

  return {
    reviews,
    loading,
    stats,
    fetchProductReviews,
  };
}