/**
 * React Hooks for WebSocket Integration
 */

'use client';

import { useEffect, useRef, useState, useCallback } from 'react';
import { WebSocketClient, WebSocketMessage, NotificationData, createWebSocketClient } from '@/lib/websocket/client';

export interface UseWebSocketOptions {
  url?: string;
  token?: string;
  autoConnect?: boolean;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
}

export interface WebSocketState {
  connected: boolean;
  connecting: boolean;
  error: string | null;
  reconnectAttempts: number;
}

/**
 * Main WebSocket hook
 */
export function useWebSocket(options: UseWebSocketOptions = {}) {
  const clientRef = useRef<WebSocketClient | null>(null);
  const [state, setState] = useState<WebSocketState>({
    connected: false,
    connecting: false,
    error: null,
    reconnectAttempts: 0
  });

  const [messages, setMessages] = useState<WebSocketMessage[]>([]);
  const [notifications, setNotifications] = useState<NotificationData[]>([]);

  // Initialize client
  useEffect(() => {
    if (typeof window === 'undefined') return;

    clientRef.current = createWebSocketClient({
      url: options.url,
      token: options.token,
      reconnectInterval: options.reconnectInterval,
      maxReconnectAttempts: options.maxReconnectAttempts,
      
      onConnect: () => {
        setState(prev => ({
          ...prev,
          connected: true,
          connecting: false,
          error: null,
          reconnectAttempts: 0
        }));
      },
      
      onDisconnect: () => {
        setState(prev => ({
          ...prev,
          connected: false,
          connecting: false
        }));
      },
      
      onMessage: (message) => {
        setMessages(prev => [...prev.slice(-99), message]); // Keep last 100 messages
      },
      
      onNotification: (notification) => {
        setNotifications(prev => [...prev.slice(-49), notification]); // Keep last 50 notifications
      },
      
      onError: (error) => {
        setState(prev => ({
          ...prev,
          error: error.toString(),
          connecting: false
        }));
      }
    });

    // Auto-connect if enabled
    if (options.autoConnect !== false) {
      clientRef.current.connect();
    }

    return () => {
      if (clientRef.current) {
        clientRef.current.disconnect();
      }
    };
  }, [options.url, options.token, options.autoConnect, options.reconnectInterval, options.maxReconnectAttempts]);

  // Update state periodically
  useEffect(() => {
    const interval = setInterval(() => {
      if (clientRef.current) {
        const status = clientRef.current.getStatus();
        setState(prev => ({
          ...prev,
          connected: status.connected,
          connecting: status.connecting,
          reconnectAttempts: status.reconnectAttempts
        }));
      }
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const connect = useCallback((token?: string) => {
    if (clientRef.current) {
      setState(prev => ({ ...prev, connecting: true, error: null }));
      clientRef.current.connect(token);
    }
  }, []);

  const disconnect = useCallback(() => {
    if (clientRef.current) {
      clientRef.current.disconnect();
    }
  }, []);

  const send = useCallback((message: any) => {
    return clientRef.current?.send(message) || false;
  }, []);

  const updateToken = useCallback((token: string) => {
    if (clientRef.current) {
      clientRef.current.updateToken(token);
    }
  }, []);

  const clearMessages = useCallback(() => {
    setMessages([]);
  }, []);

  const clearNotifications = useCallback(() => {
    setNotifications([]);
  }, []);

  return {
    // State
    ...state,
    messages,
    notifications,
    
    // Actions
    connect,
    disconnect,
    send,
    updateToken,
    clearMessages,
    clearNotifications
  };
}

/**
 * Hook for notifications only
 */
export function useWebSocketNotifications(options: UseWebSocketOptions = {}) {
  const [notifications, setNotifications] = useState<NotificationData[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const clientRef = useRef<WebSocketClient | null>(null);

  useEffect(() => {
    if (typeof window === 'undefined') return;

    clientRef.current = createWebSocketClient({
      ...options,
      onNotification: (notification) => {
        setNotifications(prev => {
          const newNotifications = [...prev, notification];
          setUnreadCount(count => count + 1);
          return newNotifications.slice(-50); // Keep last 50
        });
      }
    });

    if (options.autoConnect !== false) {
      clientRef.current.connect();
    }

    return () => {
      if (clientRef.current) {
        clientRef.current.disconnect();
      }
    };
  }, [options.url, options.token, options.autoConnect]);

  const markAsRead = useCallback((notificationId?: string) => {
    if (notificationId) {
      // Mark specific notification as read
      setNotifications(prev => 
        prev.map(n => n.id === notificationId ? { ...n, read: true } : n)
      );
    } else {
      // Mark all as read
      setUnreadCount(0);
      setNotifications(prev => 
        prev.map(n => ({ ...n, read: true }))
      );
    }
  }, []);

  const clearNotifications = useCallback(() => {
    setNotifications([]);
    setUnreadCount(0);
  }, []);

  return {
    notifications,
    unreadCount,
    markAsRead,
    clearNotifications
  };
}

/**
 * Hook for admin notifications
 */
export function useAdminWebSocket(adminToken?: string) {
  return useWebSocket({
    url: 'ws://localhost:8080/api/websocket',
    token: adminToken,
    autoConnect: !!adminToken
  });
}

/**
 * Hook for user notifications
 */
export function useUserWebSocket(userToken?: string) {
  return useWebSocket({
    url: 'ws://localhost:8080/api/websocket',
    token: userToken,
    autoConnect: !!userToken
  });
}

/**
 * Hook for connection status only
 */
export function useWebSocketStatus(options: UseWebSocketOptions = {}) {
  const [status, setStatus] = useState<WebSocketState>({
    connected: false,
    connecting: false,
    error: null,
    reconnectAttempts: 0
  });

  const clientRef = useRef<WebSocketClient | null>(null);

  useEffect(() => {
    if (typeof window === 'undefined') return;

    clientRef.current = createWebSocketClient({
      ...options,
      onConnect: () => setStatus(prev => ({ ...prev, connected: true, connecting: false, error: null })),
      onDisconnect: () => setStatus(prev => ({ ...prev, connected: false, connecting: false })),
      onError: (error) => setStatus(prev => ({ ...prev, error: error.toString(), connecting: false }))
    });

    if (options.autoConnect !== false) {
      clientRef.current.connect();
    }

    // Update status periodically
    const interval = setInterval(() => {
      if (clientRef.current) {
        const clientStatus = clientRef.current.getStatus();
        setStatus(prev => ({
          ...prev,
          connected: clientStatus.connected,
          connecting: clientStatus.connecting,
          reconnectAttempts: clientStatus.reconnectAttempts
        }));
      }
    }, 2000);

    return () => {
      clearInterval(interval);
      if (clientRef.current) {
        clientRef.current.disconnect();
      }
    };
  }, [options.url, options.token, options.autoConnect]);

  return status;
}

/**
 * Custom hook for sending messages
 */
export function useWebSocketSender(client?: WebSocketClient) {
  const send = useCallback((message: any) => {
    return client?.send(message) || false;
  }, [client]);

  const sendNotification = useCallback((notification: Partial<NotificationData>) => {
    return send({
      type: 'notification',
      data: {
        id: `notif_${Date.now()}`,
        timestamp: new Date().toISOString(),
        ...notification
      }
    });
  }, [send]);

  const sendSystemMessage = useCallback((action: string, data: any = {}) => {
    return send({
      type: 'system',
      data: { action, ...data },
      timestamp: new Date().toISOString()
    });
  }, [send]);

  return {
    send,
    sendNotification,
    sendSystemMessage
  };
}
