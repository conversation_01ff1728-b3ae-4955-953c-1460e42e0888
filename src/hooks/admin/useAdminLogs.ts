import { useState, useCallback } from 'react';
import { toast } from 'sonner';

export interface LogEntry {
  id: string;
  action: string;
  userId?: string;
  userEmail?: string;
  details: any;
  ipAddress: string;
  userAgent: string;
  createdAt: string;
}

export interface LogFilters {
  action?: string;
  userId?: string;
  startDate?: string;
  endDate?: string;
}

export interface LogPagination {
  page: number;
  limit: number;
}

export const useAdminLogs = () => {
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [loading, setLoading] = useState(false);
  const [totalCount, setTotalCount] = useState(0);

  const fetchLogs = useCallback(async (
    filters: LogFilters = {},
    pagination: LogPagination = { page: 1, limit: 20 }
  ) => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
      });

      if (filters.action && filters.action !== "all") {
        params.append("action", filters.action);
      }
      if (filters.userId) params.append("userId", filters.userId);
      if (filters.startDate) params.append("startDate", filters.startDate);
      if (filters.endDate) params.append("endDate", filters.endDate);

      const response = await fetch(`/api/admin/logs?${params.toString()}`);
      if (response.ok) {
        const data = await response.json();
        setLogs(data.logs || []);
        setTotalCount(data.totalCount || 0);
      } else {
        toast.error("Có lỗi xảy ra khi tải nhật ký");
      }
    } catch {
      toast.error("Có lỗi xảy ra khi tải nhật ký");
    } finally {
      setLoading(false);
    }
  }, []);

  const exportLogs = useCallback(async (
    filters: LogFilters = {},
    format: 'csv' | 'json' = 'csv'
  ) => {
    try {
      const params = new URLSearchParams();
      if (filters.action && filters.action !== "all") {
        params.append("action", filters.action);
      }
      if (filters.userId) params.append("userId", filters.userId);
      if (filters.startDate) params.append("startDate", filters.startDate);
      if (filters.endDate) params.append("endDate", filters.endDate);

      const response = await fetch(`/api/admin/logs/export?${params.toString()}`);
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `logs-${new Date().toISOString().split("T")[0]}.${format}`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        
        toast.success("Đã xuất nhật ký thành công");
        return { success: true };
      } else {
        toast.error("Có lỗi xảy ra khi xuất nhật ký");
        return { success: false };
      }
    } catch {
      toast.error("Có lỗi xảy ra khi xuất nhật ký");
      return { success: false };
    }
  }, []);

  return {
    logs,
    loading,
    totalCount,
    fetchLogs,
    exportLogs,
  };
};