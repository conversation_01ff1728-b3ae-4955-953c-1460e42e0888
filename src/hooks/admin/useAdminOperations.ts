import { useState, useCallback } from "react";
import { toast } from "sonner";

export interface EmailStatus {
  service: string;
  status: 'working' | 'error' | 'disabled';
  message?: string;
  lastTested?: string;
}

export interface UseAdminOperationsOptions {
  onEmailTestSuccess?: () => void;
  onEmailTestError?: (error: string) => void;
}

export function useAdminOperations(options: UseAdminOperationsOptions = {}) {
  const [emailStatus, setEmailStatus] = useState<EmailStatus | null>(null);
  const [emailTesting, setEmailTesting] = useState(false);
  const [creatingNotification, setCreatingNotification] = useState(false);
  const [exportingLogs, setExportingLogs] = useState(false);

  const checkEmailStatus = useCallback(async () => {
    try {
      const response = await fetch("/api/admin/contacts/test-email");
      const data = await response.json();
      
      if (response.ok) {
        setEmailStatus(data);
      } else {
        console.error("Failed to check email status:", data.error);
      }
    } catch (error) {
      console.error("Email status check error:", error);
    }
  }, []);

  const testEmail = async (testType: 'single' | 'all' = 'single') => {
    setEmailTesting(true);
    try {
      const response = await fetch("/api/admin/contacts/test-email", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          testType,
          recipient: "<EMAIL>", // You might want to make this configurable
        }),
      });

      const result = await response.json();

      if (response.ok) {
        toast.success(
          testType === 'all' 
            ? "Đã gửi email thử nghiệm đến tất cả templates!"
            : "Đã gửi email thử nghiệm thành công!"
        );
        if (options.onEmailTestSuccess) {
          options.onEmailTestSuccess();
        }
        // Refresh email status after successful test
        await checkEmailStatus();
        return { success: true };
      } else {
        throw new Error(result.error || "Có lỗi xảy ra khi gửi email test");
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Có lỗi xảy ra khi gửi email test";
      toast.error(errorMessage);
      if (options.onEmailTestError) {
        options.onEmailTestError(errorMessage);
      }
      return { success: false, error: errorMessage };
    } finally {
      setEmailTesting(false);
    }
  };

  const createNotification = async (notificationData: {
    title: string;
    message: string;
    type: string;
    recipientId?: string;
    recipients?: string[];
  }) => {
    setCreatingNotification(true);
    try {
      const response = await fetch("/api/admin/notifications", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(notificationData),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Có lỗi xảy ra khi tạo thông báo");
      }

      toast.success("Tạo thông báo thành công!");
      return { success: true, notification: result.notification };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Có lỗi xảy ra khi tạo thông báo";
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setCreatingNotification(false);
    }
  };

  const exportLogs = async (filters: {
    startDate?: string;
    endDate?: string;
    level?: string;
    service?: string;
  } = {}) => {
    setExportingLogs(true);
    try {
      const params = new URLSearchParams();
      if (filters.startDate) params.append("startDate", filters.startDate);
      if (filters.endDate) params.append("endDate", filters.endDate);
      if (filters.level) params.append("level", filters.level);
      if (filters.service) params.append("service", filters.service);

      const response = await fetch(`/api/admin/logs/export?${params}`);
      
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `logs-${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        
        toast.success("Xuất logs thành công!");
        return { success: true };
      } else {
        const error = await response.json();
        throw new Error(error.error || "Có lỗi xảy ra khi xuất logs");
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Có lỗi xảy ra khi xuất logs";
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setExportingLogs(false);
    }
  };

  const logout = async () => {
    try {
      const response = await fetch("/api/admin-logout", {
        method: "POST",
        credentials: "include",
      });

      if (response.ok) {
        // Clear any local storage
        localStorage.removeItem('adminToken');
        // Redirect will be handled by the component
        return { success: true };
      } else {
        throw new Error("Logout failed");
      }
    } catch (error) {
      console.error("Logout error:", error);
      return { success: false, error: "Logout failed" };
    }
  };

  return {
    emailStatus,
    emailTesting,
    creatingNotification,
    exportingLogs,
    checkEmailStatus,
    testEmail,
    createNotification,
    exportLogs,
    logout,
  };
}