import { useState, useCallback } from 'react';
import { toast } from 'sonner';

interface SiteSettings {
  siteName: string;
  siteDescription: string;
  siteUrl: string;
  logo: string;
  favicon: string;
  banner: string;
  contactEmail: string;
  contactPhone: string;
  address: string;
  district: string;
  city: string;
  province: string;
  postalCode: string;
  country: string;
  googleMapsUrl: string;
  googleMapsEmbed: string;
  latitude: number | null;
  longitude: number | null;
  businessHours: Record<string, any>;
  whatsappNumber: string;
  telegramUsername: string;
  skypeId: string;
  seoSettings: {
    metaTitle: string;
    metaDescription: string;
    metaKeywords: string;
    ogTitle: string;
    ogDescription: string;
    ogImage: string;
    twitterCard: string;
    twitterSite: string;
    googleAnalyticsId: string;
    googleTagManagerId: string;
    facebookPixelId: string;
    robotsTxt: string;
    sitemapUrl: string;
  };
  socialMedia: {
    facebook: string;
    instagram: string;
    twitter: string;
    youtube: string;
    linkedin: string;
    tiktok: string;
    zalo: string;
  };
  paymentMethods: {
    cod: boolean;
    bankTransfer: boolean;
    creditCard: boolean;
  };
  shippingSettings: {
    freeShippingThreshold: number;
    shippingFee: number;
    estimatedDelivery: string;
  };
  emailSettings: {
    smtpHost: string;
    smtpPort: number;
    smtpUser: string;
    smtpPassword: string;
    fromEmail: string;
    fromName: string;
  };
  notifications: {
    orderNotifications: boolean;
    stockAlerts: boolean;
    customerNotifications: boolean;
  };
}

const defaultSettings: SiteSettings = {
  siteName: "",
  siteDescription: "",
  siteUrl: "",
  logo: "",
  favicon: "",
  banner: "",
  contactEmail: "",
  contactPhone: "",
  address: "",
  district: "",
  city: "",
  province: "",
  postalCode: "",
  country: "Vietnam",
  googleMapsUrl: "",
  googleMapsEmbed: "",
  latitude: null,
  longitude: null,
  businessHours: {
    monday: { open: "08:00", close: "18:00", closed: false },
    tuesday: { open: "08:00", close: "18:00", closed: false },
    wednesday: { open: "08:00", close: "18:00", closed: false },
    thursday: { open: "08:00", close: "18:00", closed: false },
    friday: { open: "08:00", close: "18:00", closed: false },
    saturday: { open: "08:00", close: "17:00", closed: false },
    sunday: { open: "09:00", close: "17:00", closed: false },
  },
  whatsappNumber: "",
  telegramUsername: "",
  skypeId: "",
  seoSettings: {
    metaTitle: "",
    metaDescription: "",
    metaKeywords: "",
    ogTitle: "",
    ogDescription: "",
    ogImage: "",
    twitterCard: "summary_large_image",
    twitterSite: "",
    googleAnalyticsId: "",
    googleTagManagerId: "",
    facebookPixelId: "",
    robotsTxt: "User-agent: *\nDisallow:",
    sitemapUrl: "/sitemap.xml",
  },
  socialMedia: {
    facebook: "",
    instagram: "",
    twitter: "",
    youtube: "",
    linkedin: "",
    tiktok: "",
    zalo: "",
  },
  paymentMethods: {
    cod: true,
    bankTransfer: true,
    creditCard: false,
  },
  shippingSettings: {
    freeShippingThreshold: 500000,
    shippingFee: 30000,
    estimatedDelivery: "2-3 ngày",
  },
  emailSettings: {
    smtpHost: "",
    smtpPort: 587,
    smtpUser: "",
    smtpPassword: "",
    fromEmail: "",
    fromName: "",
  },
  notifications: {
    orderNotifications: true,
    stockAlerts: true,
    customerNotifications: true,
  },
};

export const useAdminSettings = () => {
  const [settings, setSettings] = useState<SiteSettings>(defaultSettings);
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);

  const fetchSettings = useCallback(async () => {
    setInitialLoading(true);
    try {
      const response = await fetch("/api/admin/settings");
      const data = await response.json();

      if (response.ok) {
        setSettings((prev) => ({ ...prev, ...data }));
      } else {
        toast.error("Có lỗi xảy ra khi tải cài đặt");
      }
    } catch {
      toast.error("Có lỗi xảy ra khi tải cài đặt");
    } finally {
      setInitialLoading(false);
    }
  }, []);

  const saveSettings = useCallback(async () => {
    setLoading(true);
    try {
      const response = await fetch("/api/admin/settings", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ settings }),
      });

      const data = await response.json();

      if (response.ok) {
        toast.success("Cài đặt đã được lưu thành công");
        return true;
      } else {
        toast.error(data.error || "Có lỗi xảy ra khi lưu cài đặt");
        return false;
      }
    } catch {
      toast.error("Có lỗi xảy ra khi lưu cài đặt");
      return false;
    } finally {
      setLoading(false);
    }
  }, [settings]);

  const resetSettings = useCallback(async () => {
    setLoading(true);
    try {
      const response = await fetch("/api/admin/settings?action=reset", {
        method: "POST",
      });

      const data = await response.json();

      if (response.ok) {
        toast.success("Đã khôi phục cài đặt mặc định");
        await fetchSettings();
        return true;
      } else {
        toast.error(data.error || "Có lỗi xảy ra khi khôi phục cài đặt");
        return false;
      }
    } catch {
      toast.error("Có lỗi xảy ra khi khôi phục cài đặt");
      return false;
    } finally {
      setLoading(false);
    }
  }, [fetchSettings]);

  const updateSettings = useCallback((updates: Partial<SiteSettings>) => {
    setSettings((prev) => ({ ...prev, ...updates }));
  }, []);

  const updateNestedSettings = useCallback((section: string, field: string, value: any) => {
    setSettings((prev) => {
      const currentSection = prev[section as keyof SiteSettings];
      return {
        ...prev,
        [section]: {
          ...(typeof currentSection === "object" && currentSection !== null
            ? currentSection
            : {}),
          [field]: value,
        },
      };
    });
  }, []);

  return {
    settings,
    loading,
    initialLoading,
    fetchSettings,
    saveSettings,
    resetSettings,
    updateSettings,
    updateNestedSettings,
  };
};