import { useState, useCallback } from 'react';
import { toast } from 'sonner';

export interface Page {
  id: string;
  title: string;
  content: string;
  slug: string;
  featured: boolean;
  published: boolean;
  metaTitle?: string;
  metaDescription?: string;
  createdAt: string;
  updatedAt: string;
}

export interface PageFormData {
  title: string;
  content: string;
  slug?: string;
  featured: boolean;
  published: boolean;
  metaTitle?: string;
  metaDescription?: string;
}

export const useAdminPages = () => {
  const [pages, setPages] = useState<Page[]>([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);

  const fetchPages = useCallback(async () => {
    setLoading(true);
    try {
      const response = await fetch("/api/admin/pages");
      const data = await response.json();

      if (response.ok) {
        setPages(data.pages || []);
      } else {
        toast.error("Có lỗi xảy ra khi tải danh sách trang");
      }
    } catch {
      toast.error("Có lỗi xảy ra khi tải danh sách trang");
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchPage = useCallback(async (id: string) => {
    try {
      const response = await fetch(`/api/admin/pages/${id}`);
      const result = await response.json();

      if (response.ok) {
        return { success: true, data: result };
      } else {
        toast.error(result.error || "Có lỗi xảy ra khi tải trang");
        return { success: false, error: result.error };
      }
    } catch {
      toast.error("Có lỗi xảy ra khi tải trang");
      return { success: false, error: "Network error" };
    }
  }, []);

  const createPage = useCallback(async (pageData: PageFormData) => {
    setSaving(true);
    try {
      const response = await fetch('/api/admin/pages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(pageData),
      });

      const data = await response.json();

      if (response.ok) {
        toast.success('Trang đã được tạo thành công');
        return { success: true, data };
      } else {
        toast.error(data.error || 'Có lỗi xảy ra khi tạo trang');
        return { success: false, error: data.error };
      }
    } catch {
      toast.error('Có lỗi xảy ra khi tạo trang');
      return { success: false, error: 'Network error' };
    } finally {
      setSaving(false);
    }
  }, []);

  const updatePage = useCallback(async (id: string, pageData: PageFormData) => {
    setSaving(true);
    try {
      const response = await fetch(`/api/admin/pages/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(pageData),
      });

      const data = await response.json();

      if (response.ok) {
        toast.success("Trang đã được cập nhật thành công");
        return { success: true, data };
      } else {
        toast.error(data.error || "Có lỗi xảy ra khi cập nhật trang");
        return { success: false, error: data.error };
      }
    } catch {
      toast.error("Có lỗi xảy ra khi cập nhật trang");
      return { success: false, error: "Network error" };
    } finally {
      setSaving(false);
    }
  }, []);

  const toggleFeatured = useCallback(async (page: Page) => {
    try {
      const response = await fetch(
        `/api/admin/pages/${page.id}/toggle-featured`,
        {
          method: "PATCH",
        }
      );

      if (response.ok) {
        toast.success(
          page.featured ? "Đã bỏ đánh dấu nổi bật" : "Đã đánh dấu nổi bật"
        );
        return { success: true };
      } else {
        toast.error("Có lỗi xảy ra khi cập nhật");
        return { success: false };
      }
    } catch {
      toast.error("Có lỗi xảy ra khi cập nhật");
      return { success: false };
    }
  }, []);

  return {
    pages,
    loading,
    saving,
    fetchPages,
    fetchPage,
    createPage,
    updatePage,
    toggleFeatured,
  };
};