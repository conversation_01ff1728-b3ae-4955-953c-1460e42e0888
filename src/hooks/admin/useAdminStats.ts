import { useState, useEffect, useCallback } from "react";
import { toast } from "sonner";

export interface AdminStats {
  users: number;
  orders: number;
  products: number;
  revenue: number;
  growth: {
    users: number;
    orders: number;
    products: number;
    revenue: number;
  };
}

export interface DashboardOverview {
  totalRevenue: number;
  monthlyGrowth: number;
  totalOrders: number;
  totalCustomers: number;
  topProducts: Array<{
    id: string;
    name: string;
    sales: number;
    revenue: number;
  }>;
  recentOrders: Array<{
    id: string;
    customerName: string;
    total: number;
    status: string;
    createdAt: string;
  }>;
}

export interface UseAdminStatsOptions {
  autoRefresh?: boolean;
  refreshInterval?: number;
}

export function useAdminStats(options: UseAdminStatsOptions = {}) {
  const [stats, setStats] = useState<AdminStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchStats = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch("/api/admin/stats");
      const data = await response.json();

      if (response.ok) {
        setStats(data);
      } else {
        throw new Error(data.error || "Có lỗi xảy ra khi tải thống kê");
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Có lỗi xảy ra khi tải thống kê";
      setError(errorMessage);
      console.error("Fetch admin stats error:", error);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchStats();
  }, [fetchStats]);

  useEffect(() => {
    if (options.autoRefresh && options.refreshInterval) {
      const interval = setInterval(fetchStats, options.refreshInterval);
      return () => clearInterval(interval);
    }
  }, [fetchStats, options.autoRefresh, options.refreshInterval]);

  return {
    stats,
    loading,
    error,
    fetchStats,
  };
}

export function useAdminDashboardOverview(timeRange: string = "30d") {
  const [overview, setOverview] = useState<DashboardOverview | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchOverview = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch(`/api/admin/dashboard/overview?range=${timeRange}`);
      const data = await response.json();

      if (response.ok) {
        setOverview(data);
      } else {
        throw new Error(data.error || "Có lỗi xảy ra khi tải tổng quan dashboard");
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Có lỗi xảy ra khi tải tổng quan dashboard";
      setError(errorMessage);
      console.error("Fetch dashboard overview error:", error);
    } finally {
      setLoading(false);
    }
  }, [timeRange]);

  useEffect(() => {
    fetchOverview();
  }, [fetchOverview]);

  return {
    overview,
    loading,
    error,
    fetchOverview,
  };
}

export function useAdminMetrics() {
  const [metrics, setMetrics] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [testing, setTesting] = useState(false);

  const fetchMetrics = useCallback(async () => {
    setLoading(true);
    try {
      const adminToken = localStorage.getItem('adminToken');
      const response = await fetch('/api/admin/metrics', {
        headers: {
          'Authorization': `Bearer ${adminToken}`,
          'Content-Type': 'application/json',
        },
      });
      
      const data = await response.json();
      
      if (response.ok) {
        setMetrics(data);
      } else {
        throw new Error(data.error || "Có lỗi xảy ra khi tải metrics");
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Có lỗi xảy ra khi tải metrics";
      toast.error(errorMessage);
      console.error("Fetch metrics error:", error);
    } finally {
      setLoading(false);
    }
  }, []);

  const testService = async (service: string, data: any = {}) => {
    setTesting(true);
    try {
      const adminToken = localStorage.getItem('adminToken');
      const response = await fetch('/api/admin/metrics', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${adminToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ service, data }),
      });

      const result = await response.json();

      if (response.ok) {
        toast.success(`${service} test completed successfully`);
        return { success: true, result };
      } else {
        throw new Error(result.error || `${service} test failed`);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : `${service} test failed`;
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setTesting(false);
    }
  };

  useEffect(() => {
    fetchMetrics();
  }, [fetchMetrics]);

  return {
    metrics,
    loading,
    testing,
    fetchMetrics,
    testService,
  };
}