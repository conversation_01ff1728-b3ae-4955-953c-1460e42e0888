import { useCallback } from 'react';
import { useAdminAuth } from '@/contexts/AdminAuthContext';
import { useAdmin } from '@/contexts/AdminContext';

interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  pagination?: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

interface ApiOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: any;
}

export function useAdminApi() {
  const { isAuthenticated, logout } = useAdminAuth();
  const { setOperationLoading, isOperationLoading } = useAdmin();

  const apiCall = useCallback(async <T = any>(
    endpoint: string,
    options: ApiOptions = {},
    operationKey?: string
  ): Promise<ApiResponse<T>> => {
    if (!isAuthenticated) {
      throw new Error('Not authenticated');
    }

    const { method = 'GET', headers = {}, body } = options;
    
    if (operationKey) {
      setOperationLoading(operationKey, true);
    }

    try {
      const config: RequestInit = {
        method,
        headers: {
          'Content-Type': 'application/json',
          ...headers,
        },
        credentials: 'include',
      };

      if (body && method !== 'GET') {
        config.body = JSON.stringify(body);
      }

      const response = await fetch(endpoint, config);
      
      // Handle authentication errors
      if (response.status === 401 || response.status === 403) {
        logout();
        throw new Error('Session expired');
      }

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || `HTTP ${response.status}`);
      }

      return data;
    } catch (error) {
      console.error('API call error:', error);
      throw error;
    } finally {
      if (operationKey) {
        setOperationLoading(operationKey, false);
      }
    }
  }, [isAuthenticated, logout, setOperationLoading]);

  return {
    apiCall,
    isOperationLoading,
  };
}