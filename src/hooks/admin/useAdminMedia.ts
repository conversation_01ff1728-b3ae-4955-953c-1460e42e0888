import { useCallback, useEffect } from "react";
import { useAdminState } from "@/contexts/AdminContext";
import { useAdminApi } from "./useAdminApi";

export interface MediaDto {
  id: string;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  url: string;
  path: string;
  alt?: string;
  description?: string;
  tags?: string[];
  uploadedBy?: string;
  createdAt: string;
  updatedAt: string;
}

export interface AdminMediaFilters {
  search?: string;
  mimeType?: string;
  tags?: string;
  dateFrom?: string;
  dateTo?: string;
}

export interface UploadProgressCallback {
  (progress: number): void;
}

export function useAdminMedia() {
  const {
    data: media,
    loading,
    error,
    pagination,
    filters,
    updateData,
    updatePagination,
    updateFilters,
    setLoading,
    setError,
    refresh,
  } = useAdminState("admin-media");

  const { apiCall, isOperationLoading } = useAdminApi();

  const fetchMedia = useCallback(
    async (
      page: number = 1,
      limit: number = 20,
      searchFilters: AdminMediaFilters = {}
    ) => {
      setLoading(true);
      setError(null);

      try {
        const params = new URLSearchParams();
        params.append('page', page.toString());
        params.append('limit', limit.toString());
        
        // Add filters with proper string conversion
        Object.entries(searchFilters).forEach(([key, value]) => {
          if (value !== undefined && value !== '' && value !== null) {
            params.append(key, value.toString());
          }
        });

        const response = await apiCall<MediaDto[]>(
          `/api/admin/media/list?${params}`,
          { method: "GET" }
        );

        if (response.success && response.data) {
          updateData(response.data);
          if (response.pagination) {
            updatePagination(response.pagination);
          }
          updateFilters(searchFilters);
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : "Failed to fetch media");
      }
    },
    [apiCall, updateData, updatePagination, updateFilters, setLoading, setError]
  );

  const uploadMedia = useCallback(
    async (
      files: FileList | File[],
      onProgress?: UploadProgressCallback
    ): Promise<MediaDto[]> => {
      const formData = new FormData();

      Array.from(files).forEach((file) => {
        formData.append("files", file);
      });

      try {
        const xhr = new XMLHttpRequest();

        return new Promise((resolve, reject) => {
          xhr.upload.addEventListener("progress", (e) => {
            if (e.lengthComputable && onProgress) {
              const progress = (e.loaded / e.total) * 100;
              onProgress(progress);
            }
          });

          xhr.addEventListener("load", () => {
            if (xhr.status === 200) {
              const response = JSON.parse(xhr.responseText);
              if (response.success && response.data) {
                refresh(); // Refresh the media list
                resolve(response.data);
              } else {
                reject(new Error(response.error || "Upload failed"));
              }
            } else {
              reject(new Error(`Upload failed with status ${xhr.status}`));
            }
          });

          xhr.addEventListener("error", () => {
            reject(new Error("Upload failed"));
          });

          xhr.open("POST", "/api/admin/media/upload");
          xhr.setRequestHeader("credentials", "include");
          xhr.send(formData);
        });
      } catch (err) {
        throw new Error(
          err instanceof Error ? err.message : "Failed to upload media"
        );
      }
    },
    [refresh]
  );

  const getMediaById = useCallback(
    async (id: string): Promise<MediaDto | null> => {
      try {
        const response = await apiCall<MediaDto>(
          `/api/admin/media/${id}`,
          { method: "GET" },
          `get-media-${id}`
        );

        return response.success && response.data ? response.data : null;
      } catch (err) {
        throw new Error(
          err instanceof Error ? err.message : "Failed to get media"
        );
      }
    },
    [apiCall]
  );

  const updateMedia = useCallback(
    async (
      id: string,
      updates: Partial<Pick<MediaDto, "alt" | "description" | "tags">>
    ): Promise<MediaDto | null> => {
      try {
        const response = await apiCall<MediaDto>(
          `/api/admin/media/${id}`,
          {
            method: "PUT",
            body: updates,
          },
          `update-media-${id}`
        );

        if (response.success && response.data) {
          const updatedMedia = media.map((item) =>
            item.id === id ? response.data! : item
          );
          updateData(updatedMedia);
          return response.data;
        }
        return null;
      } catch (err) {
        throw new Error(
          err instanceof Error ? err.message : "Failed to update media"
        );
      }
    },
    [apiCall, media, updateData]
  );

  const deleteMedia = useCallback(
    async (ids: string | string[]): Promise<boolean> => {
      const mediaIds = Array.isArray(ids) ? ids : [ids];

      try {
        const response = await apiCall(
          "/api/admin/media/delete",
          {
            method: "POST",
            body: { ids: mediaIds },
          },
          `delete-media-${mediaIds.join(",")}`
        );

        if (response.success) {
          const filteredMedia = media.filter(
            (item) => !mediaIds.includes(item.id)
          );
          updateData(filteredMedia);
          return true;
        }
        return false;
      } catch (err) {
        throw new Error(
          err instanceof Error ? err.message : "Failed to delete media"
        );
      }
    },
    [apiCall, media, updateData]
  );

  const getMediaUrl = useCallback(
    async (
      id: string,
      size?: "thumbnail" | "medium" | "large"
    ): Promise<string | null> => {
      try {
        const params = size ? `?size=${size}` : "";
        const response = await apiCall<{ url: string }>(
          `/api/admin/media/url/${id}${params}`,
          { method: "GET" },
          `get-media-url-${id}`
        );

        return response.success && response.data ? response.data.url : null;
      } catch (err) {
        throw new Error(
          err instanceof Error ? err.message : "Failed to get media URL"
        );
      }
    },
    [apiCall]
  );

  const fixMediaUrls = useCallback(async (): Promise<boolean> => {
    try {
      const response = await apiCall(
        "/api/admin/media/fix-urls",
        { method: "POST" },
        "fix-media-urls"
      );

      if (response.success) {
        refresh(); // Refresh to get updated URLs
        return true;
      }
      return false;
    } catch (err) {
      throw new Error(
        err instanceof Error ? err.message : "Failed to fix media URLs"
      );
    }
  }, [apiCall, refresh]);

  const searchMedia = useCallback(
    (searchFilters: AdminMediaFilters) => {
      fetchMedia(1, pagination.limit, searchFilters);
    },
    [fetchMedia, pagination.limit]
  );

  const changePage = useCallback(
    (page: number) => {
      fetchMedia(page, pagination.limit, filters);
    },
    [fetchMedia, pagination.limit, filters]
  );

  const changePageSize = useCallback(
    (limit: number) => {
      fetchMedia(1, limit, filters);
    },
    [fetchMedia, filters]
  );

  // Auto-fetch on mount if no data
  useEffect(() => {
    if (media.length === 0 && !loading && !error) {
      fetchMedia();
    }
  }, [media.length, loading, error, fetchMedia]);

  return {
    // Data
    media,
    loading,
    error,
    pagination,
    filters,

    // Actions
    fetchMedia,
    uploadMedia,
    getMediaById,
    updateMedia,
    deleteMedia,
    getMediaUrl,
    fixMediaUrls,
    searchMedia,
    changePage,
    changePageSize,
    refresh,

    // Loading states
    isGettingMedia: (id: string) => isOperationLoading(`get-media-${id}`),
    isUpdating: (id: string) => isOperationLoading(`update-media-${id}`),
    isDeleting: (ids: string | string[]) => {
      const mediaIds = Array.isArray(ids) ? ids : [ids];
      return isOperationLoading(`delete-media-${mediaIds.join(",")}`);
    },
    isGettingUrl: (id: string) => isOperationLoading(`get-media-url-${id}`),
    isFixingUrls: isOperationLoading("fix-media-urls"),
  };
}
