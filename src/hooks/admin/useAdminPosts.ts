import { useState, useCallback } from 'react';
import { toast } from 'sonner';

export interface Post {
  id: string;
  title: string;
  content: string;
  excerpt: string;
  featured: boolean;
  published: boolean;
  slug: string;
  image: string;
  createdAt: string;
  updatedAt: string;
}

export interface PostFilters {
  search?: string;
  published?: boolean;
  featured?: boolean;
}

export interface PostFormData {
  title: string;
  content: string;
  excerpt: string;
  image: string;
  featured: boolean;
  published: boolean;
}

export const useAdminPosts = () => {
  const [posts, setPosts] = useState<Post[]>([]);
  const [loading, setLoading] = useState(false);
  const [totalCount, setTotalCount] = useState(0);

  const fetchPosts = useCallback(async (filters: PostFilters = {}) => {
    setLoading(true);
    try {
      const params = new URLSearchParams();
      
      if (filters.search) params.append('search', filters.search);
      if (filters.published !== undefined) params.append('published', filters.published.toString());
      if (filters.featured !== undefined) params.append('featured', filters.featured.toString());

      const response = await fetch(`/api/admin/posts?${params.toString()}`);
      const data = await response.json();

      if (response.ok) {
        setPosts(data.posts || []);
        setTotalCount(data.totalCount || 0);
      } else {
        toast.error("Có lỗi xảy ra khi tải danh sách bài viết");
      }
    } catch {
      toast.error("Có lỗi xảy ra khi tải danh sách bài viết");
    } finally {
      setLoading(false);
    }
  }, []);

  const createPost = useCallback(async (postData: PostFormData) => {
    try {
      const response = await fetch("/api/admin/posts", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(postData),
      });

      const data = await response.json();

      if (response.ok) {
        toast.success("Bài viết đã được tạo thành công");
        return { success: true, data };
      } else {
        toast.error(data.error || "Có lỗi xảy ra khi tạo bài viết");
        return { success: false, error: data.error };
      }
    } catch {
      toast.error("Có lỗi xảy ra khi tạo bài viết");
      return { success: false, error: "Network error" };
    }
  }, []);

  const fetchPost = useCallback(async (id: string) => {
    try {
      const response = await fetch(`/api/admin/posts/${id}`);
      const result = await response.json();

      if (response.ok) {
        return { success: true, data: result };
      } else {
        toast.error(result.error || "Có lỗi xảy ra khi tải bài viết");
        return { success: false, error: result.error };
      }
    } catch {
      toast.error("Có lỗi xảy ra khi tải bài viết");
      return { success: false, error: "Network error" };
    }
  }, []);

  const updatePost = useCallback(async (id: string, postData: PostFormData) => {
    try {
      const response = await fetch(`/api/admin/posts/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(postData),
      });

      const data = await response.json();

      if (response.ok) {
        toast.success("Bài viết đã được cập nhật thành công");
        return { success: true, data };
      } else {
        toast.error(data.error || "Có lỗi xảy ra khi cập nhật bài viết");
        return { success: false, error: data.error };
      }
    } catch {
      toast.error("Có lỗi xảy ra khi cập nhật bài viết");
      return { success: false, error: "Network error" };
    }
  }, []);

  const toggleFeatured = useCallback(async (post: Post) => {
    try {
      const response = await fetch(
        `/api/admin/posts/${post.id}?action=toggle-featured`,
        {
          method: "PATCH",
        }
      );

      if (response.ok) {
        toast.success(
          post.featured ? "Đã bỏ đánh dấu nổi bật" : "Đã đánh dấu nổi bật"
        );
        return { success: true };
      } else {
        toast.error("Có lỗi xảy ra khi cập nhật");
        return { success: false };
      }
    } catch {
      toast.error("Có lỗi xảy ra khi cập nhật");
      return { success: false };
    }
  }, []);

  const bulkUpdatePosts = useCallback(async (action: 'publish' | 'unpublish', postIds: string[]) => {
    try {
      const response = await fetch("/api/admin/posts", {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          action,
          postIds,
        }),
      });

      if (response.ok) {
        const actionText = action === 'publish' ? 'xuất bản' : 'ẩn';
        toast.success(`Đã ${actionText} ${postIds.length} bài viết`);
        return { success: true };
      } else {
        toast.error("Có lỗi xảy ra khi cập nhật");
        return { success: false };
      }
    } catch {
      toast.error("Có lỗi xảy ra khi cập nhật");
      return { success: false };
    }
  }, []);

  return {
    posts,
    loading,
    totalCount,
    fetchPosts,
    createPost,
    fetchPost,
    updatePost,
    toggleFeatured,
    bulkUpdatePosts,
  };
};