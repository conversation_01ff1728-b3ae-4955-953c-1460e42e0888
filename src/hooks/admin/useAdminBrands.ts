import { useCallback, useEffect } from "react";
import { useAdminState } from "@/contexts/AdminContext";
import { useAdminApi } from "./useAdminApi";
import { BrandDto, CreateBrandDto, UpdateBrandDto } from "@/app/dto";

export interface AdminBrandsFilters {
  search?: string;
  status?: "ACTIVE" | "INACTIVE";
}

export function useAdminBrands() {
  const {
    data: brands,
    loading,
    error,
    pagination,
    filters,
    updateData,
    updatePagination,
    updateFilters,
    setLoading,
    setError,
    refresh,
  } = useAdminState("admin-brands");

  const { apiCall, isOperationLoading } = useAdminApi();

  const fetchBrands = useCallback(
    async (
      page: number = 1,
      limit: number = 50,
      searchFilters: AdminBrandsFilters = {}
    ) => {
      setLoading(true);
      setError(null);

      try {
        const params = new URLSearchParams();
        params.append('page', page.toString());
        params.append('limit', limit.toString());
        
        // Add filters with proper string conversion
        Object.entries(searchFilters).forEach(([key, value]) => {
          if (value !== undefined && value !== '' && value !== null) {
            params.append(key, value.toString());
          }
        });

        const response = await apiCall<BrandDto[]>(
          `/api/admin/brands?${params}`,
          { method: "GET" }
        );

        if (response.success && response.data) {
          updateData(response.data);
          if (response.pagination) {
            updatePagination(response.pagination);
          }
          updateFilters(searchFilters);
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : "Failed to fetch brands");
      }
    },
    [apiCall, updateData, updatePagination, updateFilters, setLoading, setError]
  );

  const createBrand = useCallback(
    async (brandData: CreateBrandDto): Promise<BrandDto | null> => {
      try {
        const response = await apiCall<BrandDto>(
          "/api/admin/brands",
          {
            method: "POST",
            body: brandData,
          },
          "create-brand"
        );

        if (response.success && response.data) {
          refresh();
          return response.data;
        }
        return null;
      } catch (err) {
        throw new Error(
          err instanceof Error ? err.message : "Failed to create brand"
        );
      }
    },
    [apiCall, refresh]
  );

  const updateBrand = useCallback(
    async (id: string, brandData: UpdateBrandDto): Promise<BrandDto | null> => {
      try {
        const response = await apiCall<BrandDto>(
          `/api/admin/brands/${id}`,
          {
            method: "PUT",
            body: brandData,
          },
          `update-brand-${id}`
        );

        if (response.success && response.data) {
          const updatedBrands = brands.map((brand) =>
            brand.id === id ? response.data! : brand
          );
          updateData(updatedBrands);
          return response.data;
        }
        return null;
      } catch (err) {
        throw new Error(
          err instanceof Error ? err.message : "Failed to update brand"
        );
      }
    },
    [apiCall, brands, updateData]
  );

  const deleteBrand = useCallback(
    async (id: string): Promise<boolean> => {
      try {
        const response = await apiCall(
          `/api/admin/brands/${id}`,
          { method: "DELETE" },
          `delete-brand-${id}`
        );

        if (response.success) {
          const filteredBrands = brands.filter((brand) => brand.id !== id);
          updateData(filteredBrands);
          return true;
        }
        return false;
      } catch (err) {
        throw new Error(
          err instanceof Error ? err.message : "Failed to delete brand"
        );
      }
    },
    [apiCall, brands, updateData]
  );

  const getBrandById = useCallback(
    async (id: string): Promise<BrandDto | null> => {
      try {
        const response = await apiCall<BrandDto>(
          `/api/admin/brands/${id}`,
          { method: "GET" },
          `get-brand-${id}`
        );

        return response.success && response.data ? response.data : null;
      } catch (err) {
        throw new Error(
          err instanceof Error ? err.message : "Failed to get brand"
        );
      }
    },
    [apiCall]
  );

  const searchBrands = useCallback(
    (searchFilters: AdminBrandsFilters) => {
      fetchBrands(1, pagination.limit, searchFilters);
    },
    [fetchBrands, pagination.limit]
  );

  const changePage = useCallback(
    (page: number) => {
      fetchBrands(page, pagination.limit, filters as AdminBrandsFilters);
    },
    [fetchBrands, pagination.limit, filters]
  );

  const changePageSize = useCallback(
    (limit: number) => {
      fetchBrands(1, limit, filters as AdminBrandsFilters);
    },
    [fetchBrands, filters]
  );

  // Auto-fetch on mount if no data
  useEffect(() => {
    if (brands.length === 0 && !loading && !error) {
      fetchBrands();
    }
  }, [brands.length, loading, error, fetchBrands]);

  return {
    // Data
    brands,
    loading,
    error,
    pagination,
    filters,

    // Actions
    fetchBrands,
    createBrand,
    updateBrand,
    deleteBrand,
    getBrandById,
    searchBrands,
    changePage,
    changePageSize,
    refresh,

    // Loading states
    isCreating: isOperationLoading("create-brand"),
    isUpdating: (id: string) => isOperationLoading(`update-brand-${id}`),
    isDeleting: (id: string) => isOperationLoading(`delete-brand-${id}`),
    isGettingBrand: (id: string) => isOperationLoading(`get-brand-${id}`),
  };
}
