import { useCallback, useEffect } from "react";
import { useAdminState } from "@/contexts/AdminContext";
import { useAdminApi } from "./useAdminApi";
import { CategoryDto, CreateCategoryDto, UpdateCategoryDto } from "@/app/dto";

export interface AdminCategoriesFilters {
  search?: string;
  parentId?: string;
  status?: "ACTIVE" | "INACTIVE";
}

export function useAdminCategories() {
  const {
    data: categories,
    loading,
    error,
    pagination,
    filters,
    updateData,
    updatePagination,
    updateFilters,
    setLoading,
    setError,
    refresh,
  } = useAdminState("admin-categories");

  const { apiCall, isOperationLoading } = useAdminApi();

  const fetchCategories = useCallback(
    async (
      page: number = 1,
      limit: number = 50,
      searchFilters: AdminCategoriesFilters = {}
    ) => {
      setLoading(true);
      setError(null);

      try {
        const params = new URLSearchParams();
        params.append('page', page.toString());
        params.append('limit', limit.toString());
        
        // Add filters with proper string conversion
        Object.entries(searchFilters).forEach(([key, value]) => {
          if (value !== undefined && value !== '' && value !== null) {
            params.append(key, value.toString());
          }
        });

        const response = await apiCall<CategoryDto[]>(
          `/api/admin/categories?${params}`,
          { method: "GET" }
        );

        if (response.success && response.data) {
          updateData(response.data);
          if (response.pagination) {
            updatePagination(response.pagination);
          }
          updateFilters(searchFilters);
        }
      } catch (err) {
        setError(
          err instanceof Error ? err.message : "Failed to fetch categories"
        );
      }
    },
    [apiCall, updateData, updatePagination, updateFilters, setLoading, setError]
  );

  const getAllCategories = useCallback(async (): Promise<CategoryDto[]> => {
    try {
      const response = await apiCall<CategoryDto[]>(
        "/api/admin/categories?limit=1000",
        { method: "GET" },
        "get-all-categories"
      );

      return response.success && response.data ? response.data : [];
    } catch (err) {
      throw new Error(
        err instanceof Error ? err.message : "Failed to get all categories"
      );
    }
  }, [apiCall]);

  const createCategory = useCallback(
    async (categoryData: CreateCategoryDto): Promise<CategoryDto | null> => {
      try {
        const response = await apiCall<CategoryDto>(
          "/api/admin/categories",
          {
            method: "POST",
            body: categoryData,
          },
          "create-category"
        );

        if (response.success && response.data) {
          refresh();
          return response.data;
        }
        return null;
      } catch (err) {
        throw new Error(
          err instanceof Error ? err.message : "Failed to create category"
        );
      }
    },
    [apiCall, refresh]
  );

  const updateCategory = useCallback(
    async (
      id: string,
      categoryData: UpdateCategoryDto
    ): Promise<CategoryDto | null> => {
      try {
        const response = await apiCall<CategoryDto>(
          `/api/admin/categories/${id}`,
          {
            method: "PUT",
            body: categoryData,
          },
          `update-category-${id}`
        );

        if (response.success && response.data) {
          const updatedCategories = categories.map((category) =>
            category.id === id ? response.data! : category
          );
          updateData(updatedCategories);
          return response.data;
        }
        return null;
      } catch (err) {
        throw new Error(
          err instanceof Error ? err.message : "Failed to update category"
        );
      }
    },
    [apiCall, categories, updateData]
  );

  const deleteCategory = useCallback(
    async (id: string): Promise<boolean> => {
      try {
        const response = await apiCall(
          `/api/admin/categories/${id}`,
          { method: "DELETE" },
          `delete-category-${id}`
        );

        if (response.success) {
          const filteredCategories = categories.filter(
            (category) => category.id !== id
          );
          updateData(filteredCategories);
          return true;
        }
        return false;
      } catch (err) {
        throw new Error(
          err instanceof Error ? err.message : "Failed to delete category"
        );
      }
    },
    [apiCall, categories, updateData]
  );

  const getCategoryById = useCallback(
    async (id: string): Promise<CategoryDto | null> => {
      try {
        const response = await apiCall<CategoryDto>(
          `/api/admin/categories/${id}`,
          { method: "GET" },
          `get-category-${id}`
        );

        return response.success && response.data ? response.data : null;
      } catch (err) {
        throw new Error(
          err instanceof Error ? err.message : "Failed to get category"
        );
      }
    },
    [apiCall]
  );

  const getCategoryTree = useCallback((): CategoryDto[] => {
    const buildTree = (parentId: string | null = null): CategoryDto[] => {
      return categories
        .filter((cat) => cat.parentId === parentId)
        .map((category) => ({
          ...category,
          children: buildTree(category.id),
        }));
    };

    return buildTree();
  }, [categories]);

  const getSubcategories = useCallback(
    (parentId: string): CategoryDto[] => {
      return categories.filter((cat) => cat.parentId === parentId);
    },
    [categories]
  );

  const getRootCategories = useCallback((): CategoryDto[] => {
    return categories.filter((cat) => !cat.parentId);
  }, [categories]);

  const searchCategories = useCallback(
    (searchFilters: AdminCategoriesFilters) => {
      fetchCategories(1, pagination.limit, searchFilters);
    },
    [fetchCategories, pagination.limit]
  );

  const changePage = useCallback(
    (page: number) => {
      fetchCategories(
        page,
        pagination.limit,
        filters as AdminCategoriesFilters
      );
    },
    [fetchCategories, pagination.limit, filters]
  );

  const changePageSize = useCallback(
    (limit: number) => {
      fetchCategories(1, limit, filters as AdminCategoriesFilters);
    },
    [fetchCategories, filters]
  );

  // Auto-fetch on mount if no data
  useEffect(() => {
    if (categories.length === 0 && !loading && !error) {
      fetchCategories();
    }
  }, [categories.length, loading, error, fetchCategories]);

  return {
    // Data
    categories,
    loading,
    error,
    pagination,
    filters,

    // Actions
    fetchCategories,
    getAllCategories,
    createCategory,
    updateCategory,
    deleteCategory,
    getCategoryById,
    searchCategories,
    changePage,
    changePageSize,
    refresh,

    // Utility functions
    getCategoryTree,
    getSubcategories,
    getRootCategories,

    // Loading states
    isCreating: isOperationLoading("create-category"),
    isUpdating: (id: string) => isOperationLoading(`update-category-${id}`),
    isDeleting: (id: string) => isOperationLoading(`delete-category-${id}`),
    isGettingCategory: (id: string) => isOperationLoading(`get-category-${id}`),
    isGettingAll: isOperationLoading("get-all-categories"),
  };
}
