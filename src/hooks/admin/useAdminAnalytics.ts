import { useCallback, useState } from 'react';
import { useAdminApi } from './useAdminApi';

export interface AnalyticsDataDto {
  period: {
    start: string;
    end: string;
  };
  summary: {
    totalRevenue: number;
    totalOrders: number;
    totalUsers: number;
    conversionRate: number;
    averageOrderValue: number;
  };
  charts: {
    revenue: Array<{
      date: string;
      value: number;
    }>;
    orders: Array<{
      date: string;
      count: number;
    }>;
    users: Array<{
      date: string;
      newUsers: number;
      activeUsers: number;
    }>;
    products: Array<{
      id: string;
      name: string;
      sales: number;
      revenue: number;
    }>;
    categories: Array<{
      id: string;
      name: string;
      sales: number;
      percentage: number;
    }>;
  };
  comparisons: {
    revenueGrowth: number;
    ordersGrowth: number;
    usersGrowth: number;
    conversionGrowth: number;
  };
}

export interface AnalyticsFilters {
  period: '7d' | '30d' | '90d' | '1y' | 'custom';
  startDate?: string;
  endDate?: string;
  comparison?: boolean;
}

export function useAdminAnalytics() {
  const [data, setData] = useState<AnalyticsDataDto | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { apiCall, isOperationLoading } = useAdminApi();

  const fetchAnalytics = useCallback(async (filters: AnalyticsFilters) => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams({
        period: filters.period,
        ...(filters.startDate && { startDate: filters.startDate }),
        ...(filters.endDate && { endDate: filters.endDate }),
        ...(filters.comparison !== undefined && { comparison: filters.comparison.toString() }),
      });

      const response = await apiCall<AnalyticsDataDto>(
        `/api/admin/analytics?${params}`,
        { method: 'GET' },
        'fetch-analytics'
      );

      if (response.success && response.data) {
        setData(response.data);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch analytics');
    } finally {
      setLoading(false);
    }
  }, [apiCall]);

  const exportAnalytics = useCallback(async (
    filters: AnalyticsFilters,
    format: 'csv' | 'excel' | 'pdf' = 'csv'
  ): Promise<Blob | null> => {
    try {
      const params = new URLSearchParams({
        period: filters.period,
        format,
        ...(filters.startDate && { startDate: filters.startDate }),
        ...(filters.endDate && { endDate: filters.endDate }),
        ...(filters.comparison !== undefined && { comparison: filters.comparison.toString() }),
      });

      const response = await fetch(`/api/admin/analytics/export?${params}`, {
        method: 'GET',
        credentials: 'include',
      });

      if (response.ok) {
        return await response.blob();
      }
      return null;
    } catch (err) {
      throw new Error(err instanceof Error ? err.message : 'Failed to export analytics');
    }
  }, []);

  const getRevenueData = useCallback(() => {
    return data?.charts.revenue || [];
  }, [data]);

  const getOrdersData = useCallback(() => {
    return data?.charts.orders || [];
  }, [data]);

  const getUsersData = useCallback(() => {
    return data?.charts.users || [];
  }, [data]);

  const getTopProducts = useCallback((limit: number = 10) => {
    return data?.charts.products.slice(0, limit) || [];
  }, [data]);

  const getTopCategories = useCallback((limit: number = 10) => {
    return data?.charts.categories.slice(0, limit) || [];
  }, [data]);

  const getGrowthPercentage = useCallback((metric: keyof AnalyticsDataDto['comparisons']) => {
    return data?.comparisons[metric] || 0;
  }, [data]);

  const formatCurrency = useCallback((amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(amount);
  }, []);

  const formatPercentage = useCallback((value: number, decimals: number = 1) => {
    return `${value.toFixed(decimals)}%`;
  }, []);

  const formatNumber = useCallback((value: number) => {
    return new Intl.NumberFormat('vi-VN').format(value);
  }, []);

  return {
    // Data
    data,
    loading,
    error,

    // Actions
    fetchAnalytics,
    exportAnalytics,

    // Data accessors
    getRevenueData,
    getOrdersData,
    getUsersData,
    getTopProducts,
    getTopCategories,
    getGrowthPercentage,

    // Formatters
    formatCurrency,
    formatPercentage,
    formatNumber,

    // Loading states
    isFetching: loading || isOperationLoading('fetch-analytics'),
  };
}