import { useState, useCallback } from 'react';
import { toast } from 'sonner';

export interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  htmlContent: string;
  textContent: string;
  variables: string[];
}

export interface EmailStatus {
  enabled: boolean;
  smtpConfigured: boolean;
  lastTestSent?: string;
}

export const useAdminEmailTemplates = () => {
  const [templates, setTemplates] = useState<EmailTemplate[]>([]);
  const [emailStatus, setEmailStatus] = useState<EmailStatus | null>(null);
  const [loading, setLoading] = useState(false);
  const [testEmailLoading, setTestEmailLoading] = useState(false);
  const [testAllLoading, setTestAllLoading] = useState(false);

  const fetchTemplates = useCallback(async () => {
    try {
      const response = await fetch("/api/admin/email-preview", {
        method: "POST",
      });

      if (response.ok) {
        const data = await response.json();
        setTemplates(data.templates || []);
      } else {
        toast.error("Có lỗi xảy ra khi tải templates");
      }
    } catch {
      toast.error("Có lỗi xảy ra khi tải templates");
    }
  }, []);

  const fetchEmailStatus = useCallback(async () => {
    try {
      const response = await fetch("/api/admin/notifications/email");

      if (response.ok) {
        const data = await response.json();
        setEmailStatus(data);
      } else {
        toast.error("Có lỗi xảy ra khi tải trạng thái email");
      }
    } catch {
      toast.error("Có lỗi xảy ra khi tải trạng thái email");
    }
  }, []);

  const sendTestEmail = useCallback(async (templateName: string, testEmail: string) => {
    setTestEmailLoading(true);
    try {
      const response = await fetch("/api/admin/notifications/email", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action: "test",
          template: templateName,
          email: testEmail,
        }),
      });

      if (response.ok) {
        toast.success(`Test email đã được gửi đến ${testEmail}`);
        return { success: true };
      } else {
        const data = await response.json();
        toast.error(data.error || "Có lỗi xảy ra khi gửi test email");
        return { success: false, error: data.error };
      }
    } catch {
      toast.error("Có lỗi xảy ra khi gửi test email");
      return { success: false, error: "Network error" };
    } finally {
      setTestEmailLoading(false);
    }
  }, []);

  const sendTestAllEmails = useCallback(async (testEmail: string) => {
    setTestAllLoading(true);
    try {
      const response = await fetch("/api/admin/notifications/email", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action: "test-all",
          email: testEmail,
        }),
      });

      if (response.ok) {
        toast.success(`Tất cả test emails đã được gửi đến ${testEmail}`);
        return { success: true };
      } else {
        const data = await response.json();
        toast.error(data.error || "Có lỗi xảy ra khi gửi test emails");
        return { success: false, error: data.error };
      }
    } catch {
      toast.error("Có lỗi xảy ra khi gửi test emails");
      return { success: false, error: "Network error" };
    } finally {
      setTestAllLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    setLoading(true);
    try {
      await Promise.all([fetchTemplates(), fetchEmailStatus()]);
    } finally {
      setLoading(false);
    }
  }, [fetchTemplates, fetchEmailStatus]);

  return {
    templates,
    emailStatus,
    loading,
    testEmailLoading,
    testAllLoading,
    fetchTemplates,
    fetchEmailStatus,
    sendTestEmail,
    sendTestAllEmails,
    refreshData,
  };
};