import { useCallback } from "react";
import { useAdmin, AdminState, PaginationParams, SearchFilters } from "@/contexts/AdminContext";

/**
 * Hook để quản lý state cho admin pages
 * Kết nối với AdminContext để có state management thống nhất
 */
export function useAdminState(stateKey: string) {
  const { getState, setState, resetState, refreshData } = useAdmin();

  const state = getState(stateKey) || {
    data: [],
    loading: false,
    error: null,
    pagination: {
      page: 1,
      limit: 20,
      total: 0,
      totalPages: 0,
      hasNext: false,
      hasPrev: false,
    },
    filters: {},
  };

  const updateData = useCallback(
    (data: any[]) => {
      setState(stateKey, { data });
    },
    [stateKey, setState]
  );

  const updatePagination = useCallback(
    (pagination: Partial<PaginationParams>) => {
      setState(stateKey, {
        pagination: { ...state.pagination, ...pagination },
      });
    },
    [stateKey, setState, state.pagination]
  );

  const updateFilters = useCallback(
    (filters: Partial<SearchFilters>) => {
      setState(stateKey, {
        filters: { ...state.filters, ...filters },
      });
    },
    [stateKey, setState, state.filters]
  );

  const setLoading = useCallback(
    (loading: boolean) => {
      setState(stateKey, { loading });
    },
    [stateKey, setState]
  );

  const setError = useCallback(
    (error: string | null) => {
      setState(stateKey, { error });
    },
    [stateKey, setState]
  );

  const reset = useCallback(() => {
    resetState(stateKey);
  }, [stateKey, resetState]);

  const refresh = useCallback(() => {
    refreshData(stateKey);
  }, [stateKey, refreshData]);

  const changePage = useCallback(
    (page: number) => {
      updatePagination({ page });
    },
    [updatePagination]
  );

  const changePageSize = useCallback(
    (limit: number) => {
      updatePagination({ limit, page: 1 });
    },
    [updatePagination]
  );

  const clearFilters = useCallback(() => {
    setState(stateKey, {
      filters: {},
      pagination: { ...state.pagination, page: 1 },
    });
  }, [stateKey, setState, state.pagination]);

  const updateFilter = useCallback(
    (key: string, value: any) => {
      const newFilters = { ...state.filters, [key]: value };
      setState(stateKey, {
        filters: newFilters,
        pagination: { ...state.pagination, page: 1 },
      });
    },
    [stateKey, setState, state.filters, state.pagination]
  );

  return {
    // State
    data: state.data,
    loading: state.loading,
    error: state.error,
    pagination: state.pagination,
    filters: state.filters,

    // Actions
    updateData,
    updatePagination,
    updateFilters,
    setLoading,
    setError,
    reset,
    refresh,
    changePage,
    changePageSize,
    clearFilters,
    updateFilter,
  };
}
