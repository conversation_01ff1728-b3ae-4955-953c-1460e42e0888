import { useSettingsContext } from "@/contexts/SettingsContext";

export interface ContactInfo {
  phone: string;
  email: string;
  address: string;
  district: string;
  city: string;
  province: string;
  postalCode: string;
  country: string;
  googleMapsUrl: string;
  googleMapsEmbed: string;
  latitude: number | null;
  longitude: number | null;
  businessHours: Record<string, any>;
  whatsappNumber: string;
  telegramUsername: string;
  skypeId: string;
  socialMedia: {
    facebook: string;
    instagram: string;
    twitter: string;
    youtube: string;
    linkedin: string;
    tiktok: string;
    zalo: string;
  };
}

export function useContactInfo(): ContactInfo {
  const { settings } = useSettingsContext();

  return {
    phone: settings.contactPhone || "0796 59 78 78",
    email: settings.contactEmail || "<EMAIL>",
    address: settings.address || "TP. Hồ Chí Minh",
    district: settings.contactInfo.district || "",
    city: settings.contactInfo.city || "TP. <PERSON><PERSON>",
    province: settings.contactInfo.province || "TP. <PERSON><PERSON>",
    postalCode: settings.contactInfo.postalCode || "",
    country: settings.contactInfo.country || "Vietnam",
    googleMapsUrl: settings.contactInfo.googleMapsUrl || "",
    googleMapsEmbed: settings.contactInfo.googleMapsEmbed || "",
    latitude: settings.contactInfo.latitude || null,
    longitude: settings.contactInfo.longitude || null,
    businessHours: settings.contactInfo.businessHours || {
      monday: { open: "08:00", close: "18:00", closed: false },
      tuesday: { open: "08:00", close: "18:00", closed: false },
      wednesday: { open: "08:00", close: "18:00", closed: false },
      thursday: { open: "08:00", close: "18:00", closed: false },
      friday: { open: "08:00", close: "18:00", closed: false },
      saturday: { open: "08:00", close: "17:00", closed: false },
      sunday: { open: "09:00", close: "17:00", closed: false },
    },
    whatsappNumber: settings.contactInfo.whatsappNumber || "",
    telegramUsername: settings.contactInfo.telegramUsername || "",
    skypeId: settings.contactInfo.skypeId || "",
    socialMedia: {
      facebook: settings.socialMedia?.facebook || "",
      instagram: settings.socialMedia?.instagram || "",
      twitter: settings.socialMedia?.twitter || "",
      youtube: settings.socialMedia?.youtube || "",
      linkedin: settings.socialMedia?.linkedin || "",
      tiktok: settings.socialMedia?.tiktok || "",
      zalo: settings.socialMedia?.zalo || "https://zalo.me/0796597878",
    },
  };
}

// Helper functions for common contact actions
export function useContactActions() {
  const contactInfo = useContactInfo();

  return {
    callPhone: () => {
      if (contactInfo.phone) {
        window.open(`tel:${contactInfo.phone.replace(/\s/g, "")}`, "_self");
      }
    },
    sendEmail: (subject?: string) => {
      if (contactInfo.email) {
        const emailUrl = subject
          ? `mailto:${contactInfo.email}?subject=${encodeURIComponent(subject)}`
          : `mailto:${contactInfo.email}`;
        window.open(emailUrl, "_self");
      }
    },
    openZalo: (message?: string) => {
      const zaloUrl =
        contactInfo.socialMedia.zalo ||
        `https://zalo.me/${contactInfo.phone.replace(/\s/g, "")}`;
      const finalUrl = message
        ? `${zaloUrl}?message=${encodeURIComponent(message)}`
        : zaloUrl;
      window.open(finalUrl, "_blank");
    },
    openWhatsApp: (message?: string) => {
      if (contactInfo.whatsappNumber) {
        const whatsappUrl = message
          ? `https://wa.me/${contactInfo.whatsappNumber.replace(/\s/g, "")}?text=${encodeURIComponent(message)}`
          : `https://wa.me/${contactInfo.whatsappNumber.replace(/\s/g, "")}`;
        window.open(whatsappUrl, "_blank");
      }
    },
    openTelegram: () => {
      if (contactInfo.telegramUsername) {
        window.open(
          `https://t.me/${contactInfo.telegramUsername.replace("@", "")}`,
          "_blank"
        );
      }
    },
    openGoogleMaps: () => {
      if (contactInfo.googleMapsUrl) {
        window.open(contactInfo.googleMapsUrl, "_blank");
      } else if (contactInfo.latitude && contactInfo.longitude) {
        window.open(
          `https://maps.google.com/?q=${contactInfo.latitude},${contactInfo.longitude}`,
          "_blank"
        );
      }
    },
    openSocialMedia: (platform: keyof ContactInfo["socialMedia"]) => {
      const url = contactInfo.socialMedia[platform];
      if (url) {
        window.open(url, "_blank");
      }
    },
  };
}

// Helper function to format address
export function useFormattedAddress() {
  const contactInfo = useContactInfo();

  const getFullAddress = () => {
    const parts = [
      contactInfo.address,
      contactInfo.district,
      contactInfo.city,
      contactInfo.province,
      contactInfo.country,
    ].filter(Boolean);

    return parts.join(", ");
  };

  const getShortAddress = () => {
    const parts = [contactInfo.city, contactInfo.province].filter(Boolean);

    return parts.join(", ") || contactInfo.address;
  };

  return {
    fullAddress: getFullAddress(),
    shortAddress: getShortAddress(),
    streetAddress: contactInfo.address,
    district: contactInfo.district,
    city: contactInfo.city,
    province: contactInfo.province,
    postalCode: contactInfo.postalCode,
    country: contactInfo.country,
  };
}

// Helper function to format business hours
export function useBusinessHours() {
  const contactInfo = useContactInfo();

  const formatBusinessHours = () => {
    const hours = contactInfo.businessHours;
    if (!hours) return "8:00 - 18:00";

    // Find the most common hours
    const weekdayHours = [
      hours.monday,
      hours.tuesday,
      hours.wednesday,
      hours.thursday,
      hours.friday,
    ].filter((day) => day && !day.closed);

    if (weekdayHours.length > 0) {
      const firstDay = weekdayHours[0];
      return `${firstDay.open} - ${firstDay.close}`;
    }

    return "8:00 - 18:00";
  };

  const getDetailedHours = () => {
    const hours = contactInfo.businessHours;
    if (!hours) return null;

    const dayNames = {
      monday: "Thứ 2",
      tuesday: "Thứ 3",
      wednesday: "Thứ 4",
      thursday: "Thứ 5",
      friday: "Thứ 6",
      saturday: "Thứ 7",
      sunday: "Chủ nhật",
    };

    return Object.entries(hours).map(([day, info]: [string, any]) => ({
      day: dayNames[day as keyof typeof dayNames] || day,
      open: info?.open || "08:00",
      close: info?.close || "18:00",
      closed: info?.closed || false,
    }));
  };

  return {
    formatted: formatBusinessHours(),
    detailed: getDetailedHours(),
  };
}
