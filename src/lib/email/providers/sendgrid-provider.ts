import sgMail from '@sendgrid/mail';
import { IEmailProvider, BulkEmailResult } from './interface';
import {
  generateWelcomeEmail,
  generateOrderConfirmationEmail,
  generateContactFormEmail,
  generatePasswordResetEmail,
  generateAdminNotificationEmail,
  type WelcomeEmailData,
  type OrderConfirmationEmailData,
  type ContactFormEmailData,
  type PasswordResetEmailData,
  type AdminNotificationEmailData,
} from '../templates';

interface SendEmailOptions {
  to: string;
  toName?: string;
  subject: string;
  html: string;
  text: string;
  from?: string;
  fromName?: string;
}

export class SendGridProvider implements IEmailProvider {
  private config: {
    apiKey: string;
    from: {
      name: string;
      address: string;
    };
  } | null = null;
  private initialized = false;

  async initialize(): Promise<boolean> {
    try {
      const apiKey = process.env.SENDGRID_API_KEY;
      
      if (!apiKey) {
        console.warn("SendGrid API key not configured");
        return false;
      }

      this.config = {
        apiKey,
        from: {
          name: process.env.EMAIL_FROM_NAME || "NS Shop",
          address: process.env.EMAIL_FROM || "<EMAIL>",
        },
      };

      // Set the API key
      sgMail.setApiKey(apiKey);
      
      this.initialized = true;
      console.log("SendGrid email provider initialized successfully");
      return true;
    } catch (error) {
      console.error("Failed to initialize SendGrid email provider:", error);
      return false;
    }
  }

  private async sendEmail(options: SendEmailOptions): Promise<boolean> {
    if (!this.initialized || !this.config) {
      console.warn("SendGrid email provider not initialized");
      return false;
    }

    try {
      const msg = {
        to: {
          email: options.to,
          name: options.toName || '',
        },
        from: {
          email: options.from || this.config.from.address,
          name: options.fromName || this.config.from.name,
        },
        subject: options.subject,
        text: options.text,
        html: options.html,
      };

      const result = await sgMail.send(msg);
      console.log("SendGrid email sent successfully:", result[0].statusCode);
      return true;
    } catch (error) {
      console.error("Failed to send email via SendGrid:", error);
      return false;
    }
  }

  async sendWelcomeEmail(data: WelcomeEmailData): Promise<boolean> {
    const template = generateWelcomeEmail(data);
    
    return this.sendEmail({
      to: data.recipientEmail,
      toName: data.recipientName,
      subject: template.subject,
      html: template.html,
      text: template.text,
    });
  }

  async sendOrderConfirmationEmail(data: OrderConfirmationEmailData): Promise<boolean> {
    const template = generateOrderConfirmationEmail(data);
    
    return this.sendEmail({
      to: data.recipientEmail,
      toName: data.recipientName,
      subject: template.subject,
      html: template.html,
      text: template.text,
    });
  }

  async sendContactFormEmail(data: ContactFormEmailData): Promise<boolean> {
    const template = generateContactFormEmail(data);
    
    // Send to admin/support email
    const adminEmail = process.env.ADMIN_EMAIL || process.env.EMAIL_FROM || "<EMAIL>";
    
    return this.sendEmail({
      to: adminEmail,
      toName: "NS Shop Admin",
      subject: template.subject,
      html: template.html,
      text: template.text,
    });
  }

  async sendPasswordResetEmail(data: PasswordResetEmailData): Promise<boolean> {
    const template = generatePasswordResetEmail(data);
    
    return this.sendEmail({
      to: data.recipientEmail,
      toName: data.recipientName,
      subject: template.subject,
      html: template.html,
      text: template.text,
    });
  }

  async sendAdminNotificationEmail(data: AdminNotificationEmailData): Promise<boolean> {
    const template = generateAdminNotificationEmail(data);
    
    return this.sendEmail({
      to: data.recipientEmail,
      toName: data.recipientName,
      subject: template.subject,
      html: template.html,
      text: template.text,
    });
  }

  async sendBulkAdminNotificationEmails(
    notifications: AdminNotificationEmailData[]
  ): Promise<BulkEmailResult> {
    const results: BulkEmailResult = {
      sent: 0,
      failed: 0,
      errors: [],
    };

    for (const notification of notifications) {
      try {
        const success = await this.sendAdminNotificationEmail(notification);
        if (success) {
          results.sent++;
        } else {
          results.failed++;
          results.errors.push(
            `Failed to send to ${notification.recipientEmail}`
          );
        }
      } catch (error) {
        results.failed++;
        results.errors.push(
          `Error sending to ${notification.recipientEmail}: ${error}`
        );
      }

      // Add small delay to avoid overwhelming SendGrid
      await new Promise((resolve) => setTimeout(resolve, 100));
    }

    return results;
  }

  async testConnection(): Promise<boolean> {
    if (!this.initialized || !this.config) {
      return false;
    }

    try {
      // SendGrid doesn't have a direct test connection method
      // We'll just verify the API key is set and service is initialized
      return true;
    } catch (error) {
      console.error("SendGrid connection test failed:", error);
      return false;
    }
  }

  async sendTestEmail(recipientEmail: string, recipientName: string = "Test User"): Promise<boolean> {
    const testEmailData: AdminNotificationEmailData = {
      recipientName,
      recipientEmail,
      notification: {
        id: "test",
        title: "Test Email từ NS Shop (SendGrid)",
        message: "Đây là email test để kiểm tra cấu hình SendGrid. Nếu bạn nhận được email này, SendGrid đã hoạt động bình thường.",
        type: "INFO",
        priority: "NORMAL",
        createdAt: new Date().toISOString(),
      },
    };

    return this.sendAdminNotificationEmail(testEmailData);
  }

  getProviderName(): string {
    return "SendGrid";
  }

  async getStatus(): Promise<{
    connected: boolean;
    configured: boolean;
    message: string;
    provider: string;
  }> {
    const configured = !!process.env.SENDGRID_API_KEY;
    const connected = this.initialized && configured;

    return {
      connected,
      configured,
      message: connected 
        ? "SendGrid is connected and ready" 
        : configured 
          ? "SendGrid is configured but not connected"
          : "SendGrid is not configured",
      provider: "SendGrid",
    };
  }
}
