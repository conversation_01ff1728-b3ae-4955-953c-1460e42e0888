import type {
  Welcome<PERSON>mailD<PERSON>,
  OrderCon<PERSON>rmation<PERSON>mailD<PERSON>,
  ContactFormEmailData,
  PasswordResetEmailData,
  AdminNotificationEmailData,
} from '../templates';

export interface EmailProviderConfig {
  from: {
    name: string;
    address: string;
  };
  [key: string]: any;
}

export interface BulkEmailResult {
  sent: number;
  failed: number;
  errors: string[];
}

export interface IEmailProvider {
  /**
   * Initialize the email provider with configuration
   * @returns Promise<boolean> - true if initialization successful
   */
  initialize(): Promise<boolean>;

  /**
   * Send welcome email to new users
   * @param data - Welcome email data
   * @returns Promise<boolean> - true if email sent successfully
   */
  sendWelcomeEmail(data: WelcomeEmailData): Promise<boolean>;

  /**
   * Send order confirmation email
   * @param data - Order confirmation email data
   * @returns Promise<boolean> - true if email sent successfully
   */
  sendOrderConfirmationEmail(data: OrderConfirmationEmailData): Promise<boolean>;

  /**
   * Send contact form email to admin
   * @param data - Contact form email data
   * @returns Promise<boolean> - true if email sent successfully
   */
  sendContactFormEmail(data: ContactFormEmailData): Promise<boolean>;

  /**
   * Send password reset email
   * @param data - Password reset email data
   * @returns Promise<boolean> - true if email sent successfully
   */
  sendPasswordResetEmail(data: PasswordResetEmailData): Promise<boolean>;

  /**
   * Send admin notification email
   * @param data - Admin notification email data
   * @returns Promise<boolean> - true if email sent successfully
   */
  sendAdminNotificationEmail(data: AdminNotificationEmailData): Promise<boolean>;

  /**
   * Send bulk admin notification emails
   * @param notifications - Array of admin notification email data
   * @returns Promise<BulkEmailResult> - Result of bulk email operation
   */
  sendBulkAdminNotificationEmails(
    notifications: AdminNotificationEmailData[]
  ): Promise<BulkEmailResult>;

  /**
   * Test connection to email service
   * @returns Promise<boolean> - true if connection successful
   */
  testConnection(): Promise<boolean>;

  /**
   * Send test email
   * @param recipientEmail - Email address to send test email to
   * @param recipientName - Name of recipient (optional)
   * @returns Promise<boolean> - true if test email sent successfully
   */
  sendTestEmail(recipientEmail: string, recipientName?: string): Promise<boolean>;

  /**
   * Get provider name
   * @returns string - Name of the email provider
   */
  getProviderName(): string;

  /**
   * Get provider status information
   * @returns Promise<{
   *   connected: boolean;
   *   configured: boolean;
   *   message: string;
   *   provider: string;
   * }>
   */
  getStatus(): Promise<{
    connected: boolean;
    configured: boolean;
    message: string;
    provider: string;
  }>;
}

export type EmailProviderType = 'sendgrid' | 'ses' | 'smtp' | 'auto';

export interface EmailProviderOptions {
  type: EmailProviderType;
  config?: EmailProviderConfig;
  fallbackProviders?: EmailProviderType[];
}
