import { Client } from "minio";

// MinIO client configuration
const minioClient = new Client({
  endPoint: process.env.MINIO_ENDPOINT || "localhost",
  port: parseInt(process.env.MINIO_PORT || "9000"),
  useSSL: process.env.MINIO_USE_SSL === "true",
  accessKey: process.env.MINIO_ACCESS_KEY || "minioadmin",
  secretKey: process.env.MINIO_SECRET_KEY || "minioadmin123",
});

const BUCKET_NAME = process.env.MINIO_BUCKET_NAME || "ns-shop-media";

// Initialize bucket if it doesn't exist
export async function initializeBucket() {
  try {
    const bucketExists = await minioClient.bucketExists(BUCKET_NAME);
    if (!bucketExists) {
      await minioClient.makeBucket(BUCKET_NAME, "us-east-1");
      console.log(`Bucket ${BUCKET_NAME} created successfully`);

      // Set bucket policy to allow public read access for images
      const policy = {
        Version: "2012-10-17",
        Statement: [
          {
            Effect: "Allow",
            Principal: { AWS: ["*"] },
            Action: ["s3:GetObject"],
            Resource: [`arn:aws:s3:::${BUCKET_NAME}/*`],
          },
        ],
      };

      await minioClient.setBucketPolicy(BUCKET_NAME, JSON.stringify(policy));
      console.log(`Bucket policy set for ${BUCKET_NAME}`);
    }
  } catch (error) {
    console.error("Error initializing bucket:", error);
  }
}

// Upload file to MinIO
export async function uploadFile(
  file: Buffer,
  fileName: string,
  contentType: string,
  folder: string = "uploads"
): Promise<{ success: boolean; url?: string; error?: string }> {
  try {
    const objectName = `${folder}/${Date.now()}-${fileName}`;

    await minioClient.putObject(BUCKET_NAME, objectName, file, file.length, {
      "Content-Type": contentType,
    });

    // Generate public URL with fallback
    const endpoint = process.env.MINIO_ENDPOINT || "localhost";
    const port = process.env.MINIO_PORT || "9000";
    const url = `http://${endpoint}:${port}/${BUCKET_NAME}/${objectName}`;

    return {
      success: true,
      url,
    };
  } catch (error) {
    console.error("Error uploading file:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Upload failed",
    };
  }
}

// Delete file from MinIO
export async function deleteFile(
  objectName: string
): Promise<{ success: boolean; error?: string }> {
  try {
    // Remove bucket name and leading slash from URL if present
    const cleanObjectName = objectName
      .replace(
        `http://${process.env.MINIO_ENDPOINT}:${process.env.MINIO_PORT}/${BUCKET_NAME}/`,
        ""
      )
      .replace(`/${BUCKET_NAME}/`, "")
      .replace(/^\//, "");

    await minioClient.removeObject(BUCKET_NAME, cleanObjectName);

    return {
      success: true,
    };
  } catch (error) {
    console.error("Error deleting file:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Delete failed",
    };
  }
}

// List files in a folder
export async function listFiles(
  folder: string = "uploads",
  limit: number = 50
): Promise<{ success: boolean; files?: any[]; error?: string }> {
  try {
    const files: any[] = [];
    const stream = minioClient.listObjects(BUCKET_NAME, folder, true);

    let count = 0;
    for await (const obj of stream) {
      if (count >= limit) break;

      files.push({
        name: obj.name,
        size: obj.size,
        lastModified: obj.lastModified,
        url: `http://${process.env.MINIO_ENDPOINT || "localhost"}:${process.env.MINIO_PORT || "9000"}/${BUCKET_NAME}/${obj.name}`,
      });
      count++;
    }

    return {
      success: true,
      files,
    };
  } catch (error) {
    console.error("Error listing files:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "List failed",
    };
  }
}

// Get file info
export async function getFileInfo(
  objectName: string
): Promise<{ success: boolean; info?: any; error?: string }> {
  try {
    const endpoint = process.env.MINIO_ENDPOINT || "localhost";
    const port = process.env.MINIO_PORT || "9000";
    const cleanObjectName = objectName
      .replace(`http://${endpoint}:${port}/${BUCKET_NAME}/`, "")
      .replace(`/${BUCKET_NAME}/`, "")
      .replace(/^\//, "");

    const stat = await minioClient.statObject(BUCKET_NAME, cleanObjectName);

    return {
      success: true,
      info: {
        size: stat.size,
        lastModified: stat.lastModified,
        contentType: stat.metaData["content-type"],
        url: `http://${endpoint}:${port}/${BUCKET_NAME}/${cleanObjectName}`,
      },
    };
  } catch (error) {
    console.error("Error getting file info:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Get info failed",
    };
  }
}

export { minioClient, BUCKET_NAME };
