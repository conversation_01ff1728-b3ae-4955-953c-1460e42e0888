import { prisma } from "@/lib/prisma";

/**
 * Performance Optimization utilities for Audit Logs & Notification System
 */

// Database optimization
export class DatabaseOptimizer {
  /**
   * Archive old audit logs to improve query performance
   */
  static async archiveOldAuditLogs(olderThanDays: number = 365) {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

      // Count logs to be archived
      const logsToArchive = await prisma.auditLog.count({
        where: {
          createdAt: { lt: cutoffDate },
        },
      });

      if (logsToArchive === 0) {
        console.log("No audit logs to archive");
        return { archived: 0 };
      }

      // In a real implementation, you would move these to an archive table
      // For now, we'll just delete very old logs (older than 2 years)
      const veryOldCutoff = new Date();
      veryOldCutoff.setDate(veryOldCutoff.getDate() - (olderThanDays * 2));

      const deletedLogs = await prisma.auditLog.deleteMany({
        where: {
          createdAt: { lt: veryOld<PERSON>utoff },
        },
      });

      console.log(`Archived/deleted ${deletedLogs.count} old audit logs`);
      return { archived: deletedLogs.count };
    } catch (error) {
      console.error("Error archiving audit logs:", error);
      throw error;
    }
  }

  /**
   * Clean up old notifications
   */
  static async cleanupOldNotifications(olderThanDays: number = 90) {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

      // Delete read notifications older than cutoff
      const deletedNotifications = await prisma.notification.deleteMany({
        where: {
          isRead: true,
          createdAt: { lt: cutoffDate },
        },
      });

      console.log(`Cleaned up ${deletedNotifications.count} old notifications`);
      return { cleaned: deletedNotifications.count };
    } catch (error) {
      console.error("Error cleaning up notifications:", error);
      throw error;
    }
  }

  /**
   * Optimize database indexes
   */
  static async optimizeIndexes() {
    try {
      // This would typically be done via database migrations
      // Here we just log the recommended indexes
      const recommendedIndexes = [
        "CREATE INDEX IF NOT EXISTS idx_audit_logs_admin_id ON AuditLog(adminId);",
        "CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON AuditLog(createdAt);",
        "CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON AuditLog(action);",
        "CREATE INDEX IF NOT EXISTS idx_audit_logs_resource ON AuditLog(resource);",
        "CREATE INDEX IF NOT EXISTS idx_notifications_target ON Notification(targetType, targetId);",
        "CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON Notification(createdAt);",
        "CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON Notification(isRead);",
        "CREATE INDEX IF NOT EXISTS idx_notifications_priority ON Notification(priority);",
      ];

      console.log("Recommended database indexes:", recommendedIndexes);
      return { indexes: recommendedIndexes };
    } catch (error) {
      console.error("Error optimizing indexes:", error);
      throw error;
    }
  }
}

// Caching utilities
export class CacheManager {
  private static cache = new Map<string, { data: any; expiry: number }>();

  /**
   * Get cached data
   */
  static get<T>(key: string): T | null {
    const cached = this.cache.get(key);
    if (!cached) return null;

    if (Date.now() > cached.expiry) {
      this.cache.delete(key);
      return null;
    }

    return cached.data as T;
  }

  /**
   * Set cached data
   */
  static set(key: string, data: any, ttlSeconds: number = 300) {
    const expiry = Date.now() + (ttlSeconds * 1000);
    this.cache.set(key, { data, expiry });
  }

  /**
   * Clear cache
   */
  static clear(pattern?: string) {
    if (pattern) {
      for (const key of this.cache.keys()) {
        if (key.includes(pattern)) {
          this.cache.delete(key);
        }
      }
    } else {
      this.cache.clear();
    }
  }

  /**
   * Get cache stats
   */
  static getStats() {
    const now = Date.now();
    let validEntries = 0;
    let expiredEntries = 0;

    for (const [key, value] of this.cache.entries()) {
      if (now > value.expiry) {
        expiredEntries++;
      } else {
        validEntries++;
      }
    }

    return {
      totalEntries: this.cache.size,
      validEntries,
      expiredEntries,
      memoryUsage: JSON.stringify([...this.cache.entries()]).length,
    };
  }
}

// Query optimization
export class QueryOptimizer {
  /**
   * Optimized audit logs query with pagination and filtering
   */
  static async getAuditLogs(params: {
    page?: number;
    limit?: number;
    adminId?: string;
    action?: string;
    resource?: string;
    startDate?: Date;
    endDate?: Date;
  }) {
    const {
      page = 1,
      limit = 20,
      adminId,
      action,
      resource,
      startDate,
      endDate,
    } = params;

    // Build cache key
    const cacheKey = `audit_logs_${JSON.stringify(params)}`;
    
    // Check cache first
    const cached = CacheManager.get(cacheKey);
    if (cached) {
      return cached;
    }

    const where: any = {};

    if (adminId) where.adminId = adminId;
    if (action) where.action = { contains: action, mode: "insensitive" };
    if (resource) where.resource = { contains: resource, mode: "insensitive" };
    if (startDate || endDate) {
      where.createdAt = {};
      if (startDate) where.createdAt.gte = startDate;
      if (endDate) where.createdAt.lte = endDate;
    }

    const [logs, total] = await Promise.all([
      prisma.auditLog.findMany({
        where,
        include: {
          admin: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true,
              role: true,
            },
          },
        },
        orderBy: { createdAt: "desc" },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.auditLog.count({ where }),
    ]);

    const result = {
      data: logs,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };

    // Cache for 5 minutes
    CacheManager.set(cacheKey, result, 300);

    return result;
  }

  /**
   * Optimized notifications query
   */
  static async getNotifications(params: {
    adminId: string;
    page?: number;
    limit?: number;
    isRead?: boolean;
    priority?: string;
  }) {
    const { adminId, page = 1, limit = 20, isRead, priority } = params;

    const cacheKey = `notifications_${JSON.stringify(params)}`;
    const cached = CacheManager.get(cacheKey);
    if (cached) {
      return cached;
    }

    const where: any = {
      OR: [
        { targetType: "ALL_ADMINS" },
        { targetType: "SPECIFIC_ADMIN", targetId: adminId },
      ],
    };

    if (isRead !== undefined) where.isRead = isRead;
    if (priority) where.priority = priority;

    const [notifications, total] = await Promise.all([
      prisma.notification.findMany({
        where,
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: [
          { priority: "desc" },
          { createdAt: "desc" },
        ],
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.notification.count({ where }),
    ]);

    const result = {
      data: notifications,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };

    // Cache for 2 minutes (shorter for notifications)
    CacheManager.set(cacheKey, result, 120);

    return result;
  }
}

// Batch processing utilities
export class BatchProcessor {
  /**
   * Process audit logs in batches
   */
  static async processBatch<T>(
    items: T[],
    processor: (batch: T[]) => Promise<void>,
    batchSize: number = 100
  ) {
    const batches = [];
    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize));
    }

    for (const batch of batches) {
      await processor(batch);
      // Small delay to prevent overwhelming the database
      await new Promise(resolve => setTimeout(resolve, 10));
    }
  }

  /**
   * Bulk create audit logs
   */
  static async bulkCreateAuditLogs(logs: any[]) {
    await this.processBatch(logs, async (batch) => {
      await prisma.auditLog.createMany({
        data: batch,
        skipDuplicates: true,
      });
    });
  }

  /**
   * Bulk update notifications
   */
  static async bulkUpdateNotifications(
    notificationIds: string[],
    data: any
  ) {
    await this.processBatch(notificationIds, async (batch) => {
      await prisma.notification.updateMany({
        where: { id: { in: batch } },
        data,
      });
    });
  }
}

// Performance monitoring
export class PerformanceMonitor {
  private static metrics = new Map<string, number[]>();

  /**
   * Record operation timing
   */
  static recordTiming(operation: string, duration: number) {
    if (!this.metrics.has(operation)) {
      this.metrics.set(operation, []);
    }
    
    const timings = this.metrics.get(operation)!;
    timings.push(duration);
    
    // Keep only last 100 measurements
    if (timings.length > 100) {
      timings.shift();
    }
  }

  /**
   * Get performance stats
   */
  static getStats(operation?: string) {
    if (operation) {
      const timings = this.metrics.get(operation) || [];
      if (timings.length === 0) return null;

      const avg = timings.reduce((a, b) => a + b, 0) / timings.length;
      const min = Math.min(...timings);
      const max = Math.max(...timings);

      return { operation, avg, min, max, count: timings.length };
    }

    const stats: any = {};
    for (const [op, timings] of this.metrics.entries()) {
      if (timings.length > 0) {
        const avg = timings.reduce((a, b) => a + b, 0) / timings.length;
        const min = Math.min(...timings);
        const max = Math.max(...timings);
        stats[op] = { avg, min, max, count: timings.length };
      }
    }

    return stats;
  }

  /**
   * Measure function execution time
   */
  static async measure<T>(
    operation: string,
    fn: () => Promise<T>
  ): Promise<T> {
    const start = Date.now();
    try {
      const result = await fn();
      const duration = Date.now() - start;
      this.recordTiming(operation, duration);
      return result;
    } catch (error) {
      const duration = Date.now() - start;
      this.recordTiming(`${operation}_error`, duration);
      throw error;
    }
  }
}

// Scheduled maintenance
export class MaintenanceScheduler {
  private static intervals = new Map<string, NodeJS.Timeout>();

  /**
   * Schedule regular maintenance tasks
   */
  static scheduleMaintenanceTasks() {
    // Archive old audit logs daily at 2 AM
    this.scheduleTask("archive_audit_logs", "0 2 * * *", async () => {
      await DatabaseOptimizer.archiveOldAuditLogs(365);
    });

    // Clean up old notifications daily at 3 AM
    this.scheduleTask("cleanup_notifications", "0 3 * * *", async () => {
      await DatabaseOptimizer.cleanupOldNotifications(90);
    });

    // Clear expired cache entries every hour
    this.scheduleTask("clear_cache", "0 * * * *", async () => {
      const stats = CacheManager.getStats();
      if (stats.expiredEntries > 0) {
        CacheManager.clear();
        console.log(`Cleared ${stats.expiredEntries} expired cache entries`);
      }
    });

    console.log("Maintenance tasks scheduled");
  }

  /**
   * Schedule a task (simplified cron-like scheduling)
   */
  private static scheduleTask(
    name: string,
    schedule: string,
    task: () => Promise<void>
  ) {
    // In a real implementation, you'd use a proper cron library
    // For now, we'll just run tasks every hour as an example
    const interval = setInterval(async () => {
      try {
        console.log(`Running maintenance task: ${name}`);
        await task();
        console.log(`Completed maintenance task: ${name}`);
      } catch (error) {
        console.error(`Error in maintenance task ${name}:`, error);
      }
    }, 60 * 60 * 1000); // Every hour

    this.intervals.set(name, interval);
  }

  /**
   * Stop all scheduled tasks
   */
  static stopAllTasks() {
    for (const [name, interval] of this.intervals.entries()) {
      clearInterval(interval);
      console.log(`Stopped maintenance task: ${name}`);
    }
    this.intervals.clear();
  }
}

// Export all optimizers
export const PerformanceOptimization = {
  DatabaseOptimizer,
  CacheManager,
  QueryOptimizer,
  BatchProcessor,
  PerformanceMonitor,
  MaintenanceScheduler,
};
