import { toast } from "@/lib/toast";

export const addToWishlist = async (productId: string): Promise<boolean> => {
  try {
    const response = await fetch("/api/wishlist", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ productId }),
    });

    const data = await response.json();

    if (response.ok) {
      toast.success("Đã thêm vào danh sách yêu thích");
      return true;
    } else {
      toast.error(
        data.error || "Có lỗi xảy ra khi thêm vào danh sách yêu thích"
      );
      return false;
    }
  } catch (error) {
    console.error("Add to wishlist error:", error);
    toast.error("Có lỗi xảy ra khi thêm vào danh sách yêu thích");
    return false;
  }
};

export const removeFromWishlist = async (itemId: string): Promise<boolean> => {
  try {
    const response = await fetch(`/api/wishlist/${itemId}`, {
      method: "DELETE",
    });

    const data = await response.json();

    if (response.ok) {
      toast.success("Đã xóa khỏi danh sách yêu thích");
      return true;
    } else {
      toast.error(
        data.error || "Có lỗi xảy ra khi xóa khỏi danh sách yêu thích"
      );
      return false;
    }
  } catch (error) {
    console.error("Remove from wishlist error:", error);
    toast.error("Có lỗi xảy ra khi xóa khỏi danh sách yêu thích");
    return false;
  }
};

export const checkIfInWishlist = async (
  productId: string
): Promise<boolean> => {
  try {
    const response = await fetch("/api/wishlist");
    const data = await response.json();

    if (response.ok) {
      return data.some((item: any) => item.product.id === productId);
    }
    return false;
  } catch (error) {
    console.error("Check wishlist error:", error);
    return false;
  }
};
