import { getServerSession } from "next-auth";
import { adminAuthOptions } from "@/lib/admin-auth";

export async function verifyAdminAuth() {
  const session = await getServerSession(adminAuthOptions);

  if (!session || session.user.role !== "ADMIN") {
    return null;
  }

  return session;
}

export async function requireAdminAuth() {
  const session = await verifyAdminAuth();

  if (!session) {
    throw new Error("Unauthorized: Admin access required");
  }

  return session;
}
