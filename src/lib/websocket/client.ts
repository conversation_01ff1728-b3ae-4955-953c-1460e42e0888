/**
 * WebSocket Client Utilities
 * Client-side WebSocket connection management
 */

export interface WebSocketMessage {
  type: string;
  data: any;
  timestamp: string;
  id: string;
}

export interface NotificationData {
  id: string;
  title: string;
  message: string;
  category: 'info' | 'success' | 'warning' | 'error';
  userId?: string;
  data?: any;
}

export interface WebSocketClientOptions {
  url?: string;
  token?: string;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
  onConnect?: () => void;
  onDisconnect?: () => void;
  onMessage?: (message: WebSocketMessage) => void;
  onNotification?: (notification: NotificationData) => void;
  onError?: (error: Event) => void;
}

export class WebSocketClient {
  private ws: WebSocket | null = null;
  private url: string;
  private token: string | null;
  private reconnectInterval: number;
  private maxReconnectAttempts: number;
  private reconnectAttempts = 0;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private isConnecting = false;
  private isManuallyDisconnected = false;

  // Event handlers
  private onConnect?: () => void;
  private onDisconnect?: () => void;
  private onMessage?: (message: WebSocketMessage) => void;
  private onNotification?: (notification: NotificationData) => void;
  private onError?: (error: Event) => void;

  constructor(options: WebSocketClientOptions = {}) {
    this.url = options.url || 'ws://localhost:8080/api/websocket';
    this.token = options.token || null;
    this.reconnectInterval = options.reconnectInterval || 3000;
    this.maxReconnectAttempts = options.maxReconnectAttempts || 10;
    
    this.onConnect = options.onConnect;
    this.onDisconnect = options.onDisconnect;
    this.onMessage = options.onMessage;
    this.onNotification = options.onNotification;
    this.onError = options.onError;
  }

  /**
   * Connect to WebSocket server
   */
  connect(token?: string): void {
    if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {
      return;
    }

    this.isConnecting = true;
    this.isManuallyDisconnected = false;

    if (token) {
      this.token = token;
    }

    const wsUrl = this.token ? `${this.url}?token=${this.token}` : this.url;

    try {
      this.ws = new WebSocket(wsUrl);
      this.setupEventHandlers();
    } catch (error) {
      console.error('WebSocket connection error:', error);
      this.isConnecting = false;
      this.scheduleReconnect();
    }
  }

  /**
   * Disconnect from WebSocket server
   */
  disconnect(): void {
    this.isManuallyDisconnected = true;
    this.clearReconnectTimer();
    
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }

  /**
   * Send message to server
   */
  send(message: any): boolean {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      console.warn('WebSocket is not connected');
      return false;
    }

    try {
      this.ws.send(JSON.stringify(message));
      return true;
    } catch (error) {
      console.error('Failed to send WebSocket message:', error);
      return false;
    }
  }

  /**
   * Send ping to server
   */
  ping(): boolean {
    return this.send({
      type: 'ping',
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Get connection status
   */
  getStatus(): {
    connected: boolean;
    connecting: boolean;
    readyState: number | null;
    reconnectAttempts: number;
  } {
    return {
      connected: this.ws?.readyState === WebSocket.OPEN,
      connecting: this.isConnecting,
      readyState: this.ws?.readyState || null,
      reconnectAttempts: this.reconnectAttempts
    };
  }

  /**
   * Update authentication token
   */
  updateToken(token: string): void {
    this.token = token;
    
    // Reconnect with new token if currently connected
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.disconnect();
      setTimeout(() => this.connect(), 100);
    }
  }

  /**
   * Setup WebSocket event handlers
   */
  private setupEventHandlers(): void {
    if (!this.ws) return;

    this.ws.onopen = () => {
      console.log('WebSocket connected');
      this.isConnecting = false;
      this.reconnectAttempts = 0;
      this.clearReconnectTimer();
      
      if (this.onConnect) {
        this.onConnect();
      }

      // Start heartbeat
      this.startHeartbeat();
    };

    this.ws.onclose = (event) => {
      console.log('WebSocket disconnected:', event.code, event.reason);
      this.isConnecting = false;
      
      if (this.onDisconnect) {
        this.onDisconnect();
      }

      // Attempt to reconnect if not manually disconnected
      if (!this.isManuallyDisconnected) {
        this.scheduleReconnect();
      }
    };

    this.ws.onerror = (error) => {
      console.error('WebSocket error:', error);
      this.isConnecting = false;
      
      if (this.onError) {
        this.onError(error);
      }
    };

    this.ws.onmessage = (event) => {
      try {
        const message: WebSocketMessage = JSON.parse(event.data);
        
        // Handle different message types
        switch (message.type) {
          case 'notification':
            if (this.onNotification) {
              this.onNotification(message.data);
            }
            break;
          
          case 'system':
            this.handleSystemMessage(message);
            break;
          
          default:
            if (this.onMessage) {
              this.onMessage(message);
            }
        }
      } catch (error) {
        console.error('Failed to parse WebSocket message:', error);
      }
    };
  }

  /**
   * Handle system messages
   */
  private handleSystemMessage(message: WebSocketMessage): void {
    const { action } = message.data;
    
    switch (action) {
      case 'pong':
        // Heartbeat response received
        break;
      
      case 'auth_required':
        console.warn('WebSocket authentication required');
        break;
      
      case 'auth_failed':
        console.error('WebSocket authentication failed');
        this.disconnect();
        break;
      
      default:
        console.log('Unknown system message:', action);
    }
  }

  /**
   * Schedule reconnection attempt
   */
  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached');
      return;
    }

    this.clearReconnectTimer();
    
    const delay = Math.min(
      this.reconnectInterval * Math.pow(2, this.reconnectAttempts),
      30000 // Max 30 seconds
    );

    console.log(`Scheduling WebSocket reconnection in ${delay}ms (attempt ${this.reconnectAttempts + 1})`);
    
    this.reconnectTimer = setTimeout(() => {
      this.reconnectAttempts++;
      this.connect();
    }, delay);
  }

  /**
   * Clear reconnection timer
   */
  private clearReconnectTimer(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
  }

  /**
   * Start heartbeat to keep connection alive
   */
  private startHeartbeat(): void {
    const heartbeatInterval = setInterval(() => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        this.ping();
      } else {
        clearInterval(heartbeatInterval);
      }
    }, 30000); // Ping every 30 seconds
  }
}

/**
 * Create WebSocket client instance
 */
export function createWebSocketClient(options: WebSocketClientOptions = {}): WebSocketClient {
  return new WebSocketClient(options);
}

/**
 * React hook for WebSocket connection
 */
export function useWebSocket(options: WebSocketClientOptions = {}) {
  if (typeof window === 'undefined') {
    // Server-side rendering
    return {
      client: null,
      connected: false,
      connecting: false,
      connect: () => {},
      disconnect: () => {},
      send: () => false
    };
  }

  const client = new WebSocketClient(options);
  
  return {
    client,
    connected: client.getStatus().connected,
    connecting: client.getStatus().connecting,
    connect: (token?: string) => client.connect(token),
    disconnect: () => client.disconnect(),
    send: (message: any) => client.send(message)
  };
}
