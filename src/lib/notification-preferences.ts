import { prisma } from "@/lib/prisma";

export interface NotificationPreferences {
  emailNotifications: {
    enabled: boolean;
    types: {
      INFO: boolean;
      SUCCESS: boolean;
      WARNING: boolean;
      ERROR: boolean;
      SYSTEM: boolean;
    };
    priorities: {
      LOW: boolean;
      NORMAL: boolean;
      HIGH: boolean;
      URGENT: boolean;
    };
    frequency: "immediate" | "hourly" | "daily" | "weekly";
    quietHours: {
      enabled: boolean;
      start: string; // HH:mm format
      end: string; // HH:mm format
      timezone: string;
    };
  };
  browserNotifications: {
    enabled: boolean;
    types: {
      INFO: boolean;
      SUCCESS: boolean;
      WARNING: boolean;
      ERROR: boolean;
      SYSTEM: boolean;
    };
    priorities: {
      LOW: boolean;
      NORMAL: boolean;
      HIGH: boolean;
      URGENT: boolean;
    };
  };
  autoMarkAsRead: {
    enabled: boolean;
    afterDays: number;
  };
}

export const DEFAULT_PREFERENCES: NotificationPreferences = {
  emailNotifications: {
    enabled: true,
    types: {
      INFO: false,
      SUCCESS: false,
      WARNING: true,
      ERROR: true,
      SYSTEM: true,
    },
    priorities: {
      LOW: false,
      NORMAL: false,
      HIGH: true,
      URGENT: true,
    },
    frequency: "immediate",
    quietHours: {
      enabled: false,
      start: "22:00",
      end: "08:00",
      timezone: "Asia/Ho_Chi_Minh",
    },
  },
  browserNotifications: {
    enabled: true,
    types: {
      INFO: true,
      SUCCESS: true,
      WARNING: true,
      ERROR: true,
      SYSTEM: true,
    },
    priorities: {
      LOW: true,
      NORMAL: true,
      HIGH: true,
      URGENT: true,
    },
  },
  autoMarkAsRead: {
    enabled: true,
    afterDays: 30,
  },
};

/**
 * Get notification preferences for an admin user
 */
export async function getNotificationPreferences(
  adminId: string
): Promise<NotificationPreferences> {
  try {
    const admin = await prisma.adminUser.findUnique({
      where: { id: adminId },
      select: { permissions: true },
    });

    if (!admin || !admin.permissions) {
      return DEFAULT_PREFERENCES;
    }

    // Preferences are stored in the permissions JSON field
    const preferences = (admin.permissions as any)?.notificationPreferences;
    
    if (!preferences) {
      return DEFAULT_PREFERENCES;
    }

    // Merge with defaults to ensure all fields are present
    return mergeWithDefaults(preferences);
  } catch (error) {
    console.error("Error getting notification preferences:", error);
    return DEFAULT_PREFERENCES;
  }
}

/**
 * Update notification preferences for an admin user
 */
export async function updateNotificationPreferences(
  adminId: string,
  preferences: Partial<NotificationPreferences>
): Promise<NotificationPreferences> {
  try {
    // Get current preferences
    const currentPreferences = await getNotificationPreferences(adminId);
    
    // Merge with new preferences
    const updatedPreferences = deepMerge(currentPreferences, preferences);

    // Get current permissions
    const admin = await prisma.adminUser.findUnique({
      where: { id: adminId },
      select: { permissions: true },
    });

    const currentPermissions = (admin?.permissions as any) || {};

    // Update permissions with new notification preferences
    const updatedPermissions = {
      ...currentPermissions,
      notificationPreferences: updatedPreferences,
    };

    await prisma.adminUser.update({
      where: { id: adminId },
      data: { permissions: updatedPermissions },
    });

    return updatedPreferences;
  } catch (error) {
    console.error("Error updating notification preferences:", error);
    throw error;
  }
}

/**
 * Check if a notification should be sent via email based on preferences
 */
export async function shouldSendEmailNotification(
  adminId: string,
  notification: {
    type: string;
    priority: string;
    createdAt: Date;
  }
): Promise<boolean> {
  try {
    const preferences = await getNotificationPreferences(adminId);

    // Check if email notifications are enabled
    if (!preferences.emailNotifications.enabled) {
      return false;
    }

    // Check type filter
    const typeEnabled = preferences.emailNotifications.types[
      notification.type as keyof typeof preferences.emailNotifications.types
    ];
    if (!typeEnabled) {
      return false;
    }

    // Check priority filter
    const priorityEnabled = preferences.emailNotifications.priorities[
      notification.priority as keyof typeof preferences.emailNotifications.priorities
    ];
    if (!priorityEnabled) {
      return false;
    }

    // Check quiet hours
    if (preferences.emailNotifications.quietHours.enabled) {
      const isQuietTime = isInQuietHours(
        notification.createdAt,
        preferences.emailNotifications.quietHours
      );
      if (isQuietTime) {
        return false;
      }
    }

    // Check frequency (for batching - not implemented in this basic version)
    // This would require a more complex queuing system

    return true;
  } catch (error) {
    console.error("Error checking email notification preferences:", error);
    // Default to sending if there's an error
    return true;
  }
}

/**
 * Check if a notification should be shown in browser based on preferences
 */
export async function shouldShowBrowserNotification(
  adminId: string,
  notification: {
    type: string;
    priority: string;
  }
): Promise<boolean> {
  try {
    const preferences = await getNotificationPreferences(adminId);

    // Check if browser notifications are enabled
    if (!preferences.browserNotifications.enabled) {
      return false;
    }

    // Check type filter
    const typeEnabled = preferences.browserNotifications.types[
      notification.type as keyof typeof preferences.browserNotifications.types
    ];
    if (!typeEnabled) {
      return false;
    }

    // Check priority filter
    const priorityEnabled = preferences.browserNotifications.priorities[
      notification.priority as keyof typeof preferences.browserNotifications.priorities
    ];
    if (!priorityEnabled) {
      return false;
    }

    return true;
  } catch (error) {
    console.error("Error checking browser notification preferences:", error);
    // Default to showing if there's an error
    return true;
  }
}

/**
 * Get all admin users who should receive email notifications for a given notification
 */
export async function getEmailNotificationRecipients(
  notification: {
    type: string;
    priority: string;
    targetType: string;
    targetId?: string;
    createdAt: Date;
  }
): Promise<string[]> {
  try {
    // First, get all potential recipients based on target type
    let adminQuery: any = { isActive: true };

    switch (notification.targetType) {
      case "ALL_ADMINS":
        // No additional filter
        break;
      case "SPECIFIC_ADMIN":
        if (notification.targetId) {
          adminQuery.id = notification.targetId;
        } else {
          return [];
        }
        break;
      case "ROLE_ADMIN":
        adminQuery.role = "ADMIN";
        break;
      case "ROLE_MODERATOR":
        adminQuery.role = "MODERATOR";
        break;
      default:
        return [];
    }

    const admins = await prisma.adminUser.findMany({
      where: adminQuery,
      select: { id: true },
    });

    // Filter based on individual preferences
    const recipients: string[] = [];
    
    for (const admin of admins) {
      const shouldSend = await shouldSendEmailNotification(admin.id, notification);
      if (shouldSend) {
        recipients.push(admin.id);
      }
    }

    return recipients;
  } catch (error) {
    console.error("Error getting email notification recipients:", error);
    return [];
  }
}

// Helper functions

function mergeWithDefaults(preferences: any): NotificationPreferences {
  return deepMerge(DEFAULT_PREFERENCES, preferences);
}

function deepMerge(target: any, source: any): any {
  const result = { ...target };
  
  for (const key in source) {
    if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
      result[key] = deepMerge(target[key] || {}, source[key]);
    } else {
      result[key] = source[key];
    }
  }
  
  return result;
}

function isInQuietHours(
  date: Date,
  quietHours: {
    start: string;
    end: string;
    timezone: string;
  }
): boolean {
  try {
    // Convert date to the specified timezone
    const timeInTimezone = new Intl.DateTimeFormat('en-US', {
      timeZone: quietHours.timezone,
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);

    const [hours, minutes] = timeInTimezone.split(':').map(Number);
    const currentMinutes = hours * 60 + minutes;

    const [startHours, startMinutes] = quietHours.start.split(':').map(Number);
    const startTotalMinutes = startHours * 60 + startMinutes;

    const [endHours, endMinutes] = quietHours.end.split(':').map(Number);
    const endTotalMinutes = endHours * 60 + endMinutes;

    // Handle overnight quiet hours (e.g., 22:00 to 08:00)
    if (startTotalMinutes > endTotalMinutes) {
      return currentMinutes >= startTotalMinutes || currentMinutes <= endTotalMinutes;
    } else {
      return currentMinutes >= startTotalMinutes && currentMinutes <= endTotalMinutes;
    }
  } catch (error) {
    console.error("Error checking quiet hours:", error);
    return false;
  }
}
