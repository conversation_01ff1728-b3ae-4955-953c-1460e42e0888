import { NextResponse } from 'next/server';
import { ApiResponse, PaginatedResponse } from '../types';

/**
 * Success response formatter
 */
export function successResponse<T>(
  data: T,
  message?: string,
  status: number = 200
): NextResponse {
  const response: ApiResponse<T> = {
    success: true,
    data,
    message,
  };

  return NextResponse.json(response, { status });
}

/**
 * Error response formatter
 */
export function errorResponse(
  error: string,
  status: number = 400,
  details?: any
): NextResponse {
  const response: ApiResponse = {
    success: false,
    error,
    ...(details && { details }),
  };

  return NextResponse.json(response, { status });
}

/**
 * Paginated response formatter
 */
export function paginatedResponse<T>(
  data: T[],
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  },
  message?: string
): NextResponse {
  const response: PaginatedResponse<T> = {
    success: true,
    data,
    pagination,
    message,
  };

  return NextResponse.json(response);
}

/**
 * Created response formatter
 */
export function createdResponse<T>(
  data: T,
  message: string = 'Tạo thành công'
): NextResponse {
  return successResponse(data, message, 201);
}

/**
 * Updated response formatter
 */
export function updatedResponse<T>(
  data: T,
  message: string = 'Cập nhật thành công'
): NextResponse {
  return successResponse(data, message, 200);
}

/**
 * Deleted response formatter
 */
export function deletedResponse(
  message: string = 'Xóa thành công'
): NextResponse {
  return successResponse(true, message, 200);
}

/**
 * Not found response formatter
 */
export function notFoundResponse(
  message: string = 'Không tìm thấy dữ liệu'
): NextResponse {
  return errorResponse(message, 404);
}

/**
 * Unauthorized response formatter
 */
export function unauthorizedResponse(
  message: string = 'Chưa đăng nhập'
): NextResponse {
  return errorResponse(message, 401);
}

/**
 * Forbidden response formatter
 */
export function forbiddenResponse(
  message: string = 'Không có quyền truy cập'
): NextResponse {
  return errorResponse(message, 403);
}

/**
 * Validation error response formatter
 */
export function validationErrorResponse(
  errors: Record<string, string> | string,
  message: string = 'Dữ liệu không hợp lệ'
): NextResponse {
  if (typeof errors === 'string') {
    return errorResponse(errors, 400);
  }

  return errorResponse(message, 400, { validationErrors: errors });
}

/**
 * Server error response formatter
 */
export function serverErrorResponse(
  message: string = 'Lỗi máy chủ',
  error?: any
): NextResponse {
  console.error('Server error:', error);
  
  return errorResponse(message, 500, {
    ...(process.env.NODE_ENV === 'development' && error && { error: error.message }),
  });
}

/**
 * Rate limit response formatter
 */
export function rateLimitResponse(
  message: string = 'Quá nhiều yêu cầu, vui lòng thử lại sau'
): NextResponse {
  return errorResponse(message, 429);
}

/**
 * Conflict response formatter
 */
export function conflictResponse(
  message: string = 'Dữ liệu đã tồn tại'
): NextResponse {
  return errorResponse(message, 409);
}

/**
 * Bad request response formatter
 */
export function badRequestResponse(
  message: string = 'Yêu cầu không hợp lệ'
): NextResponse {
  return errorResponse(message, 400);
}

/**
 * No content response formatter
 */
export function noContentResponse(): NextResponse {
  return new NextResponse(null, { status: 204 });
}

/**
 * Generic response handler for common patterns
 */
export class ResponseHandler {
  static success<T>(data: T, message?: string, status?: number) {
    return successResponse(data, message, status);
  }

  static error(error: string, status?: number, details?: any) {
    return errorResponse(error, status, details);
  }

  static paginated<T>(
    data: T[],
    pagination: PaginatedResponse<T>['pagination'],
    message?: string
  ) {
    return paginatedResponse(data, pagination, message);
  }

  static created<T>(data: T, message?: string) {
    return createdResponse(data, message);
  }

  static updated<T>(data: T, message?: string) {
    return updatedResponse(data, message);
  }

  static deleted(message?: string) {
    return deletedResponse(message);
  }

  static notFound(message?: string) {
    return notFoundResponse(message);
  }

  static unauthorized(message?: string) {
    return unauthorizedResponse(message);
  }

  static forbidden(message?: string) {
    return forbiddenResponse(message);
  }

  static validationError(errors: Record<string, string> | string, message?: string) {
    return validationErrorResponse(errors, message);
  }

  static serverError(message?: string, error?: any) {
    return serverErrorResponse(message, error);
  }

  static conflict(message?: string) {
    return conflictResponse(message);
  }

  static badRequest(message?: string) {
    return badRequestResponse(message);
  }

  static noContent() {
    return noContentResponse();
  }
}
