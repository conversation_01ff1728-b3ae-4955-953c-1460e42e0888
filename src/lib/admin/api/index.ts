// Export all API utilities
export * from "./middleware";
export * from "./responses";
export * from "./pagination";
export * from "./errors";
// export * from './base-controller'; // TODO: Fix type issues

// Re-export types
export * from "../types";

// Convenience exports
export { ResponseHandler } from "./responses";
export { PaginationHelper } from "./pagination";
export { <PERSON>rrorHelper } from "./errors";
// export { BaseAdminController } from "./base-controller"; // TODO: Fix type issues
export {
  withAdminAuth,
  withPermissions,
  withRateLimit,
  withCors,
  withValidation,
  withLogging,
  combineMiddlewares,
} from "./middleware";
