// Interface for products with image data (legacy)
interface ProductWithImages {
  images?: string[];
  imageTypes?: string[];
  externalImages?: string[];
}

// Interface for products with media data (new)
interface ProductWithMedia {
  media?: Array<{
    media: {
      id: string;
      url: string;
      alt?: string;
      title?: string;
      width?: number;
      height?: number;
    };
    order: number;
    isPrimary: boolean;
  }>;
}

// Interface for brands with logo
interface BrandWithLogo {
  logo?: {
    id: string;
    url: string;
    alt?: string;
  } | null;
}

/**
 * Helper function to get the first available image from a product
 * Handles both internal and external images properly
 */
export function getFirstAvailableImage(
  product: ProductWithImages
): string | null {
  if (!product.images || product.images.length === 0) return null;

  for (let i = 0; i < product.images.length; i++) {
    const imageType = product.imageTypes?.[i] || "INTERNAL";
    const internalImage = product.images[i];
    const externalImage = product.externalImages?.[i];

    if (imageType === "INTERNAL" && internalImage) {
      return internalImage;
    } else if (imageType === "EXTERNAL" && externalImage) {
      return externalImage;
    }
  }

  return null;
}

/**
 * Helper function to get all available images from a product
 * Returns array of image URLs (both internal and external)
 */
export function getAllAvailableImages(product: ProductWithImages): string[] {
  if (!product.images || product.images.length === 0) return [];

  const availableImages: string[] = [];

  for (let i = 0; i < product.images.length; i++) {
    const imageType = product.imageTypes?.[i] || "INTERNAL";
    const internalImage = product.images[i];
    const externalImage = product.externalImages?.[i];

    if (imageType === "INTERNAL" && internalImage) {
      availableImages.push(internalImage);
    } else if (imageType === "EXTERNAL" && externalImage) {
      availableImages.push(externalImage);
    }
  }

  return availableImages;
}

/**
 * Helper function to get the first available image from a product with media
 * Returns the primary image or the first image if no primary is set
 */
export function getFirstAvailableImageFromMedia(
  product: ProductWithMedia
): string | null {
  if (!product.media || product.media.length === 0) return null;

  // First try to find primary image
  const primaryImage = product.media.find((m) => m.isPrimary);
  if (primaryImage) {
    return primaryImage.media.url;
  }

  // If no primary, return first image
  const sortedMedia = product.media.sort((a, b) => a.order - b.order);
  return sortedMedia[0]?.media.url || null;
}

/**
 * Helper function to get all available images from a product with media
 * Returns array of image URLs sorted by order
 */
export function getAllAvailableImagesFromMedia(
  product: ProductWithMedia
): string[] {
  if (!product.media || product.media.length === 0) return [];

  return product.media
    .sort((a, b) => a.order - b.order)
    .map((m) => m.media.url);
}

/**
 * Helper function to get brand logo URL
 */
export function getBrandLogoUrl(brand: BrandWithLogo): string | null {
  return brand.logo?.url || null;
}

/**
 * Universal function that works with both legacy and new media format
 */
export function getProductImage(
  product: ProductWithImages | ProductWithMedia
): string | null {
  // Check if it's the new media format
  if ("media" in product && product.media) {
    return getFirstAvailableImageFromMedia(product as ProductWithMedia);
  }

  // Fall back to legacy format
  return getFirstAvailableImage(product as ProductWithImages);
}
