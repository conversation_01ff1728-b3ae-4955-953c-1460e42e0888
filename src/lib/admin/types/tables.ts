import { ReactNode } from "react";

// Table Column Definition
export interface TableColumn<T = any> {
  key: string;
  title: string;
  dataIndex?: string;
  width?: string | number;
  align?: "left" | "center" | "right";
  sortable?: boolean;
  filterable?: boolean;
  searchable?: boolean;
  render?: (record: T, index: number) => ReactNode;
  className?: string;
}

// Table Configuration
export interface TableConfig<T = any> {
  columns: TableColumn<T>[];
  rowKey: string;
  pagination?: {
    enabled: boolean;
    pageSize: number;
    showSizeChanger?: boolean;
    showQuickJumper?: boolean;
    showTotal?: boolean;
  };
  selection?: {
    enabled: boolean;
    type: "checkbox" | "radio";
    onSelectionChange?: (selectedRows: T[]) => void;
  };
  actions?: {
    enabled: boolean;
    items: TableAction<T>[];
  };
  search?: {
    enabled: boolean;
    placeholder?: string;
    fields?: string[];
  };
  filters?: TableFilter[];
  sorting?: {
    enabled: boolean;
    defaultSort?: {
      field: string;
      order: "asc" | "desc";
    };
  };
  loading?: boolean;
  emptyText?: string;
  className?: string;
}

// Table Action
export interface TableAction<T = any> {
  key: string;
  label: string | ((record: T) => string);
  icon?: ReactNode | React.ComponentType<any>;
  type?: "primary" | "secondary" | "danger";
  onClick: (record: T) => void;
  disabled?: (record: T) => boolean;
  visible?: (record: T) => boolean;
}

// Table Filter
export interface TableFilter {
  key: string;
  label: string;
  type: "select" | "date" | "dateRange" | "number" | "text";
  options?: Array<{ value: string; label: string }>;
  placeholder?: string;
}

// Table State
export interface TableState<T = any> {
  data: T[];
  loading: boolean;
  pagination: {
    current: number;
    pageSize: number;
    total: number;
  };
  filters: Record<string, any>;
  sorting: {
    field?: string;
    order?: "asc" | "desc";
  };
  selection: {
    selectedRowKeys: string[];
    selectedRows: T[];
  };
  search: string;
}

// Table Actions
export type TableAction_Type =
  | { type: "SET_DATA"; data: any[] }
  | { type: "SET_LOADING"; loading: boolean }
  | { type: "SET_PAGINATION"; pagination: Partial<TableState["pagination"]> }
  | { type: "SET_FILTERS"; filters: Record<string, any> }
  | { type: "SET_SORTING"; sorting: TableState["sorting"] }
  | { type: "SET_SELECTION"; selection: Partial<TableState["selection"]> }
  | { type: "SET_SEARCH"; search: string }
  | { type: "RESET_TABLE" };

// Table Hook Return Type
export interface UseTableReturn<T = any> {
  state: TableState<T>;
  setData: (data: T[]) => void;
  setLoading: (loading: boolean) => void;
  setPagination: (pagination: Partial<TableState<T>["pagination"]>) => void;
  setFilters: (filters: Record<string, any>) => void;
  setSorting: (sorting: TableState<T>["sorting"]) => void;
  setSelection: (selection: Partial<TableState<T>["selection"]>) => void;
  setSearch: (search: string) => void;
  reset: () => void;
  refresh: () => Promise<void>;
}

// Admin Table Props
export interface AdminTableProps<T = any> {
  config: TableConfig<T>;
  dataSource: T[];
  loading?: boolean;
  onRefresh?: () => Promise<void>;
  className?: string;
}

// Bulk Actions
export interface BulkAction<T = any> {
  key: string;
  label: string;
  icon?: ReactNode | React.ComponentType<any>;
  type?: "primary" | "secondary" | "danger";
  onClick: (selectedRows: T[]) => void;
  disabled?: (selectedRows: T[]) => boolean;
  confirmMessage?: string;
}

// Export Configuration
export interface ExportConfig {
  enabled: boolean;
  formats: Array<"csv" | "excel" | "pdf">;
  filename?: string;
  columns?: string[];
}

// Import Configuration
export interface ImportConfig {
  enabled: boolean;
  template?: string;
  onImport: (data: any[]) => Promise<void>;
  validation?: (data: any[]) => string[];
}
