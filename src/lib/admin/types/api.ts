import { NextRequest } from 'next/server';

// Base API Response Types
export interface ApiResponse<T = any> {
  data?: T;
  error?: string;
  message?: string;
  success: boolean;
}

export interface PaginatedResponse<T = any> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

// Pagination Parameters
export interface PaginationParams {
  page: number;
  limit: number;
  skip: number;
}

// Search and Filter Parameters
export interface SearchParams {
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  [key: string]: any;
}

// CRUD Operation Types
export interface CrudOperations<T = any> {
  list: (params: SearchParams & PaginationParams) => Promise<PaginatedResponse<T>>;
  get: (id: string) => Promise<ApiResponse<T>>;
  create: (data: Partial<T>) => Promise<ApiResponse<T>>;
  update: (id: string, data: Partial<T>) => Promise<ApiResponse<T>>;
  delete: (id: string) => Promise<ApiResponse<boolean>>;
}

// Admin Controller Interface
export interface AdminController<T = any> extends CrudOperations<T> {
  validateAccess: (request: NextRequest) => Promise<boolean>;
  handleError: (error: any) => ApiResponse;
}

// Common Entity Fields
export interface BaseEntity {
  id: string;
  createdAt: Date;
  updatedAt: Date;
}

// Admin User Session
export interface AdminSession {
  user: {
    id: string;
    email: string;
    name: string;
    role: 'ADMIN' | 'USER';
  };
}

// Validation Schema Types
export interface ValidationRule {
  required?: boolean;
  min?: number;
  max?: number;
  pattern?: RegExp;
  custom?: (value: any) => boolean | string;
}

export interface ValidationSchema {
  [field: string]: ValidationRule;
}

// Error Types
export interface AdminError {
  code: string;
  message: string;
  field?: string;
  details?: any;
}

// Bulk Operations
export interface BulkOperation<T = any> {
  action: 'create' | 'update' | 'delete';
  data: T[];
}

export interface BulkResponse<T = any> {
  success: T[];
  errors: Array<{
    index: number;
    error: string;
    data: T;
  }>;
}
