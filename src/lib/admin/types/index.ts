// Export all types from sub-modules
export * from './api';
export * from './forms';
export * from './tables';

// Common Admin Types
export interface AdminConfig {
  apiPrefix: string;
  defaultPageSize: number;
  maxPageSize: number;
  allowedRoles: string[];
  features: {
    bulkOperations: boolean;
    export: boolean;
    import: boolean;
    audit: boolean;
  };
}

// Admin Module Definition
export interface AdminModule {
  name: string;
  path: string;
  icon?: string;
  permissions: string[];
  features: {
    list: boolean;
    create: boolean;
    read: boolean;
    update: boolean;
    delete: boolean;
    bulk: boolean;
    export: boolean;
    import: boolean;
  };
}

// Admin Navigation Item
export interface AdminNavItem {
  title: string;
  href: string;
  icon?: any;
  children?: AdminNavItem[];
  permissions?: string[];
}

// Admin Dashboard Widget
export interface AdminWidget {
  id: string;
  title: string;
  type: 'stat' | 'chart' | 'table' | 'custom';
  size: 'small' | 'medium' | 'large';
  data?: any;
  component?: React.ComponentType<any>;
  permissions?: string[];
}

// Admin Notification
export interface AdminNotification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  actions?: Array<{
    label: string;
    action: () => void;
  }>;
}

// Admin Activity Log
export interface AdminActivity {
  id: string;
  userId: string;
  userName: string;
  action: string;
  resource: string;
  resourceId?: string;
  details?: any;
  timestamp: Date;
  ip?: string;
  userAgent?: string;
}

// Admin Settings
export interface AdminSettings {
  general: {
    siteName: string;
    siteDescription: string;
    siteUrl: string;
    logo?: string;
    favicon?: string;
  };
  contact: {
    contactEmail: string;
    contactPhone: string;
    address: string;
    socialMedia?: Record<string, string>;
  };
  payment: {
    paymentMethods: string[];
  };
  shipping: {
    shippingSettings: any;
  };
  email: {
    emailSettings: any;
  };
  notifications: {
    notifications: any;
  };
  seo: {
    seoSettings: any;
  };
  security: {
    securitySettings: any;
  };
}

// Admin Permission
export interface AdminPermission {
  id: string;
  name: string;
  description: string;
  resource: string;
  action: string;
}

// Admin Role
export interface AdminRole {
  id: string;
  name: string;
  description: string;
  permissions: AdminPermission[];
  isDefault: boolean;
}

// Admin User
export interface AdminUser {
  id: string;
  name: string;
  email: string;
  phone?: string;
  avatar?: string;
  role: AdminRole;
  isActive: boolean;
  lastLoginAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}
