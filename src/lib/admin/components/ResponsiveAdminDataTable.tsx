"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { ResponsiveTable } from "@/components/ui/responsive-table";
import { cn } from "@/lib/utils";
import {
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  Filter,
  RefreshCw,
  Search,
} from "lucide-react";
import React, { useState } from "react";
import { AdminTableProps, BulkAction, TableColumn } from "../types";

interface ResponsiveAdminDataTableProps<T> extends AdminTableProps<T> {
  onSearch?: (search: string) => void;
  onFilter?: (filters: Record<string, any>) => void;
  onSort?: (field: string, order: "asc" | "desc") => void;
  onPageChange?: (page: number) => void;
  onPageSizeChange?: (pageSize: number) => void;
  onSelectionChange?: (selectedRows: T[]) => void;
  bulkActions?: BulkAction<T>[];
  searchValue?: string;
  selectedRows?: T[];
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
  };
  mobileBreakpoint?: number;
  stickyHeader?: boolean;
  maxHeight?: string;
}

export function ResponsiveAdminDataTable<T = any>({
  config,
  dataSource,
  loading = false,
  onRefresh,
  onSearch,
  onFilter,
  onSort,
  onPageChange,
  onPageSizeChange,
  onSelectionChange,
  bulkActions = [],
  searchValue = "",
  selectedRows = [],
  pagination,
  className,
  mobileBreakpoint = 768,
  stickyHeader = true,
  maxHeight = "600px",
}: ResponsiveAdminDataTableProps<T>) {
  const [searchInput, setSearchInput] = useState(searchValue);

  const handleSearch = (value: string) => {
    setSearchInput(value);
    onSearch?.(value);
  };

  const handleSort = (column: TableColumn<T>) => {
    if (!column.sortable || !onSort) return;
    // Toggle sort order logic would go here
    onSort(column.key, "asc");
  };

  // Convert AdminDataTable columns to ResponsiveTable columns
  const responsiveColumns = config.columns.map((column) => ({
    key: column.key,
    title: column.title,
    sortable: column.sortable,
    className: column.className,
    width: column.width,
    align: column.align,
    priority: getPriorityFromColumn(column),
    render: column.render
      ? (record: T, index: number) => column.render!(record, index)
      : undefined,
    mobileRender: getMobileRender(column),
  }));

  // Add selection column if enabled
  if (config.selection?.enabled) {
    responsiveColumns.unshift({
      key: "__selection",
      title: "",
      sortable: false,
      className: undefined,
      align: undefined,
      priority: 1,
      width: 48,
      render: (record: T) => (
        <Checkbox
          checked={isRowSelected(record)}
          onCheckedChange={(checked) => handleRowSelect(record, !!checked)}
        />
      ),
      mobileRender: () => null, // Hide selection in mobile
    });
  }

  // Convert actions to ResponsiveTable actions
  const responsiveActions = config.actions?.enabled
    ? config.actions.items.map((action) => ({
        key: action.key,
        label: typeof action.label === "function" ? "Action" : action.label,
        icon: action.icon as
          | React.ComponentType<{ className?: string }>
          | undefined,
        onClick: action.onClick,
        variant: (action.type === "danger" ? "destructive" : "default") as
          | "default"
          | "destructive"
          | "outline"
          | "secondary"
          | "ghost"
          | "link",
        disabled: action.disabled,
      }))
    : [];

  const isRowSelected = (record: T): boolean => {
    const recordKey = record[config.rowKey as keyof T]?.toString();
    return selectedRows.some(
      (row) => row[config.rowKey as keyof T]?.toString() === recordKey
    );
  };

  const handleRowSelect = (record: T, checked: boolean) => {
    if (!onSelectionChange) return;

    const recordKey = record[config.rowKey as keyof T]?.toString();
    let newSelection = [...selectedRows];

    if (checked) {
      if (!isRowSelected(record)) {
        newSelection.push(record);
      }
    } else {
      newSelection = newSelection.filter(
        (row) => row[config.rowKey as keyof T]?.toString() !== recordKey
      );
    }

    onSelectionChange(newSelection);
  };

  const handleSelectAll = (checked: boolean) => {
    if (!onSelectionChange || !dataSource) return;

    if (checked) {
      onSelectionChange(dataSource);
    } else {
      onSelectionChange([]);
    }
  };

  const isAllSelected =
    dataSource &&
    dataSource.length > 0 &&
    selectedRows.length === dataSource.length;
  const isIndeterminate =
    selectedRows.length > 0 && selectedRows.length < (dataSource?.length || 0);

  return (
    <div className={cn("space-y-4", className)}>
      {/* Header with search and actions */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex flex-1 items-center space-x-2">
          {config.search?.enabled && (
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder={config.search.placeholder || "Tìm kiếm..."}
                value={searchInput}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-9"
              />
            </div>
          )}

          {config.selection?.enabled && selectedRows.length > 0 && (
            <div className="flex items-center space-x-2">
              <span className="text-sm text-muted-foreground">
                Đã chọn {selectedRows.length} mục
              </span>
              {bulkActions.map((action) => (
                <Button
                  key={action.key}
                  variant="outline"
                  size="sm"
                  onClick={() => action.onClick(selectedRows)}
                  disabled={action.disabled?.(selectedRows)}
                >
                  {action.icon &&
                    (React.isValidElement(action.icon)
                      ? action.icon
                      : React.createElement(action.icon as any, {
                          className: "h-4 w-4 mr-2",
                        }))}
                  {action.label}
                </Button>
              ))}
            </div>
          )}
        </div>

        <div className="flex items-center space-x-2">
          {onRefresh && (
            <Button
              variant="outline"
              size="sm"
              onClick={onRefresh}
              disabled={loading}
            >
              <RefreshCw className={cn("h-4 w-4", loading && "animate-spin")} />
            </Button>
          )}
        </div>
      </div>

      {/* Bulk selection header for desktop */}
      {config.selection?.enabled && (
        <div className="hidden md:flex items-center space-x-2 p-2 bg-muted/50 rounded">
          <Checkbox
            checked={isAllSelected}
            ref={(el) => {
              if (el) {
                const input = el.querySelector(
                  'input[type="checkbox"]'
                ) as HTMLInputElement;
                if (input) input.indeterminate = isIndeterminate;
              }
            }}
            onCheckedChange={handleSelectAll}
          />
          <span className="text-sm">
            {isAllSelected
              ? `Đã chọn tất cả ${dataSource?.length || 0} mục`
              : selectedRows.length > 0
                ? `Đã chọn ${selectedRows.length} mục`
                : "Chọn tất cả"}
          </span>
        </div>
      )}

      {/* Responsive Table */}
      <ResponsiveTable
        columns={responsiveColumns}
        data={dataSource || []}
        rowKey={config.rowKey}
        loading={loading}
        emptyText={config.emptyText}
        actions={responsiveActions}
        mobileBreakpoint={mobileBreakpoint}
        stickyHeader={stickyHeader}
        maxHeight={maxHeight}
      />

      {/* Pagination */}
      {pagination && (
        <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
          <div className="text-sm text-muted-foreground">
            Hiển thị {(pagination.current - 1) * pagination.pageSize + 1} đến{" "}
            {Math.min(
              pagination.current * pagination.pageSize,
              pagination.total
            )}{" "}
            của {pagination.total} kết quả
          </div>

          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange?.(1)}
              disabled={pagination.current <= 1}
            >
              <ChevronsLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange?.(pagination.current - 1)}
              disabled={pagination.current <= 1}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>

            <span className="text-sm">
              Trang {pagination.current} /{" "}
              {Math.ceil(pagination.total / pagination.pageSize)}
            </span>

            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange?.(pagination.current + 1)}
              disabled={
                pagination.current >=
                Math.ceil(pagination.total / pagination.pageSize)
              }
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() =>
                onPageChange?.(
                  Math.ceil(pagination.total / pagination.pageSize)
                )
              }
              disabled={
                pagination.current >=
                Math.ceil(pagination.total / pagination.pageSize)
              }
            >
              <ChevronsRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}

// Helper functions
function getPriorityFromColumn<T>(column: TableColumn<T>): number {
  // Assign priority based on column characteristics
  if (column.key === "name" || column.key === "title") return 1;
  if (column.key === "status" || column.key === "type") return 2;
  if (column.key === "createdAt" || column.key === "updatedAt") return 5;
  if (column.key.includes("description") || column.key.includes("content"))
    return 4;
  return 3;
}

function getMobileRender<T>(column: TableColumn<T>) {
  // Provide mobile-optimized rendering for certain column types
  if (column.key === "status" || column.key === "type") {
    return column.render; // Keep badges and status indicators
  }
  if (column.key.includes("description") || column.key.includes("content")) {
    return (record: T, index: number) => {
      const content = column.render
        ? column.render(record, index)
        : record[column.key as keyof T];
      // Truncate long text for mobile
      const text = content?.toString() || "";
      return text.length > 100 ? `${text.substring(0, 100)}...` : text;
    };
  }
  return column.render;
}
