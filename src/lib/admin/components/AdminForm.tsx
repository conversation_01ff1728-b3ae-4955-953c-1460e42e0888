"use client";

import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { cn } from "@/lib/utils";
import { AdminFormProps, FormField } from "../types";
import { useAdminForm } from "../hooks";

interface AdminFormFieldProps {
  field: FormField;
  value: any;
  error?: string;
  touched?: boolean;
  onChange: (value: any) => void;
  onBlur: () => void;
  disabled?: boolean;
}

function AdminFormField({
  field,
  value,
  error,
  touched,
  onChange,
  onBlur,
  disabled,
}: AdminFormFieldProps) {
  const hasError = touched && error;

  const renderField = () => {
    switch (field.type) {
      case "text":
      case "email":
      case "password":
      case "number":
        return (
          <Input
            type={field.type}
            placeholder={field.placeholder}
            value={value || ""}
            onChange={(e) =>
              onChange(
                field.type === "number"
                  ? Number(e.target.value)
                  : e.target.value
              )
            }
            onBlur={onBlur}
            disabled={disabled || field.disabled}
            className={cn(hasError && "border-destructive")}
          />
        );

      case "textarea":
        return (
          <Textarea
            placeholder={field.placeholder}
            value={value || ""}
            onChange={(e) => onChange(e.target.value)}
            onBlur={onBlur}
            disabled={disabled || field.disabled}
            className={cn(hasError && "border-destructive")}
            rows={4}
          />
        );

      case "select":
        return (
          <Select
            value={value || ""}
            onValueChange={onChange}
            disabled={disabled || field.disabled}
          >
            <SelectTrigger className={cn(hasError && "border-destructive")}>
              <SelectValue placeholder={field.placeholder} />
            </SelectTrigger>
            <SelectContent>
              {field.options?.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );

      case "checkbox":
        return (
          <div className="flex items-center space-x-2">
            <Checkbox
              checked={!!value}
              onCheckedChange={onChange}
              disabled={disabled || field.disabled}
            />
            <Label className="text-sm font-normal">
              {field.placeholder || field.label}
            </Label>
          </div>
        );

      case "radio":
        return (
          <RadioGroup
            value={value || ""}
            onValueChange={onChange}
            disabled={disabled || field.disabled}
          >
            {field.options?.map((option) => (
              <div key={option.value} className="flex items-center space-x-2">
                <RadioGroupItem value={option.value} />
                <Label className="text-sm font-normal">{option.label}</Label>
              </div>
            ))}
          </RadioGroup>
        );

      case "date":
      case "datetime-local":
        return (
          <Input
            type={field.type}
            value={value || ""}
            onChange={(e) => onChange(e.target.value)}
            onBlur={onBlur}
            disabled={disabled || field.disabled}
            className={cn(hasError && "border-destructive")}
          />
        );

      case "file":
        return (
          <Input
            type="file"
            onChange={(e) => {
              const file = e.target.files?.[0];
              if (file) {
                // Handle file upload - you might want to implement file upload logic here
                onChange(file);
              }
            }}
            onBlur={onBlur}
            disabled={disabled || field.disabled}
            className={cn(hasError && "border-destructive")}
          />
        );

      default:
        return (
          <Input
            type="text"
            placeholder={field.placeholder}
            value={value || ""}
            onChange={(e) => onChange(e.target.value)}
            onBlur={onBlur}
            disabled={disabled || field.disabled}
            className={cn(hasError && "border-destructive")}
          />
        );
    }
  };

  return (
    <div className={cn("space-y-2", field.className)}>
      {field.type !== "checkbox" && (
        <Label htmlFor={field.name} className="text-sm font-medium">
          {field.label}
          {field.required && <span className="text-destructive ml-1">*</span>}
        </Label>
      )}

      {renderField()}

      {field.description && (
        <p className="text-xs text-muted-foreground">{field.description}</p>
      )}

      {hasError && <p className="text-xs text-destructive">{error}</p>}
    </div>
  );
}

export function AdminForm({ config, className, children }: AdminFormProps) {
  const form = useAdminForm({
    fields: config.fields,
    initialData: config.initialData,
    onSubmit: config.onSubmit,
  });

  const getGridColumns = () => {
    if (config.layout === "grid" && config.columns) {
      return `grid-cols-${config.columns}`;
    }
    return "grid-cols-1";
  };

  return (
    <form onSubmit={form.handleSubmit} className={cn("space-y-6", className)}>
      <div
        className={cn(
          "gap-4",
          config.layout === "grid" && `grid ${getGridColumns()}`,
          config.layout === "horizontal" && "space-y-4",
          config.layout === "vertical" && "space-y-4"
        )}
      >
        {config.fields.map((field) => (
          <AdminFormField
            key={field.name}
            field={field}
            value={form.state.data[field.name]}
            error={form.state.errors[field.name]}
            touched={form.state.touched[field.name]}
            onChange={(value) => form.setValue(field.name, value)}
            onBlur={() => form.setTouched(field.name)}
            disabled={config.disabled || form.state.submitting}
          />
        ))}
      </div>

      {children}

      <div className="flex items-center gap-2 pt-4">
        <Button
          type="submit"
          disabled={config.disabled || form.state.submitting || !form.isValid}
        >
          {form.state.submitting ? "Đang lưu..." : config.submitText || "Lưu"}
        </Button>

        {config.onCancel && (
          <Button
            type="button"
            variant="outline"
            onClick={config.onCancel}
            disabled={form.state.submitting}
          >
            {config.cancelText || "Hủy"}
          </Button>
        )}
      </div>
    </form>
  );
}
