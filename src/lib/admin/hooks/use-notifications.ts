"use client";

import { useState, useCallback } from "react";
import { toast } from "sonner";

export interface Notification {
  id: string;
  title: string;
  message: string;
  type: "info" | "success" | "warning" | "error";
  status: "unread" | "read" | "archived";
  userId?: string;
  actionUrl?: string;
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export interface NotificationFilters {
  search?: string;
  type?: string;
  status?: string;
  userId?: string;
  page: number;
  limit: number;
}

export interface EmailPreviewData {
  to: string;
  subject: string;
  template: string;
  data?: Record<string, any>;
}

export function useNotifications() {
  const [loading, setLoading] = useState(false);

  const getNotifications = useCallback(async (filters: NotificationFilters) => {
    setLoading(true);
    try {
      const params = new URLSearchParams();
      if (filters.search) params.append("search", String(filters.search));
      if (filters.type) params.append("type", String(filters.type));
      if (filters.status) params.append("status", String(filters.status));
      if (filters.userId) params.append("userId", String(filters.userId));
      params.append("page", String(filters.page));
      params.append("limit", String(filters.limit));

      const response = await fetch(`/api/admin/notifications?${params.toString()}`);
      if (!response.ok) throw new Error("Failed to fetch notifications");

      const data = await response.json();
      return data;
    } catch (err) {
      const errorMessage = "Không thể tải danh sách thông báo";
      toast.error(errorMessage);
      return { notifications: [], total: 0, pagination: null };
    } finally {
      setLoading(false);
    }
  }, []);

  const markAsRead = useCallback(async (notificationId: string): Promise<boolean> => {
    try {
      const response = await fetch(`/api/admin/notifications/${notificationId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ status: "read" }),
      });

      if (!response.ok) throw new Error("Failed to mark notification as read");

      return true;
    } catch (err) {
      const errorMessage = "Không thể đánh dấu thông báo đã đọc";
      toast.error(errorMessage);
      return false;
    }
  }, []);

  const bulkMarkAsRead = useCallback(async (notificationIds: string[]): Promise<boolean> => {
    setLoading(true);
    try {
      const response = await fetch("/api/admin/notifications/bulk", {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ 
          ids: notificationIds,
          action: "mark-read"
        }),
      });

      if (!response.ok) throw new Error("Failed to bulk mark notifications as read");

      toast.success("Đánh dấu thông báo hàng loạt thành công");
      return true;
    } catch (err) {
      const errorMessage = "Không thể đánh dấu thông báo hàng loạt";
      toast.error(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  const deleteNotification = useCallback(async (notificationId: string): Promise<boolean> => {
    try {
      const response = await fetch(`/api/admin/notifications/${notificationId}`, {
        method: "DELETE",
      });

      if (!response.ok) throw new Error("Failed to delete notification");

      return true;
    } catch (err) {
      const errorMessage = "Không thể xóa thông báo";
      toast.error(errorMessage);
      return false;
    }
  }, []);

  const sendTestEmail = useCallback(async (emailData: EmailPreviewData): Promise<boolean> => {
    setLoading(true);
    try {
      const response = await fetch("/api/admin/email-preview", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(emailData),
      });

      if (!response.ok) throw new Error("Failed to send test email");

      toast.success("Gửi email thử nghiệm thành công");
      return true;
    } catch (err: any) {
      const errorMessage = err.message || "Không thể gửi email thử nghiệm";
      toast.error(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  const getNotificationTemplates = useCallback(async () => {
    try {
      const response = await fetch("/api/admin/notifications/email");
      if (!response.ok) throw new Error("Failed to fetch notification templates");

      const data = await response.json();
      return data.templates || [];
    } catch (err) {
      const errorMessage = "Không thể tải danh sách template";
      toast.error(errorMessage);
      return [];
    }
  }, []);

  const updateNotificationTemplate = useCallback(async (templateId: string, templateData: any): Promise<boolean> => {
    setLoading(true);
    try {
      const response = await fetch("/api/admin/notifications/email", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ templateId, ...templateData }),
      });

      if (!response.ok) throw new Error("Failed to update notification template");

      toast.success("Cập nhật template thành công");
      return true;
    } catch (err) {
      const errorMessage = "Không thể cập nhật template";
      toast.error(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  const deleteNotificationTemplate = useCallback(async (templateId: string): Promise<boolean> => {
    setLoading(true);
    try {
      const response = await fetch("/api/admin/notifications/email", {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ templateId }),
      });

      if (!response.ok) throw new Error("Failed to delete notification template");

      toast.success("Xóa template thành công");
      return true;
    } catch (err) {
      const errorMessage = "Không thể xóa template";
      toast.error(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    loading,
    getNotifications,
    markAsRead,
    bulkMarkAsRead,
    deleteNotification,
    sendTestEmail,
    getNotificationTemplates,
    updateNotificationTemplate,
    deleteNotificationTemplate,
  };
}