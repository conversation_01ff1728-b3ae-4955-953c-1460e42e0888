"use client";

import { useState, useCallback } from "react";
import { toast } from "sonner";

export interface MediaFile {
  id: string;
  name: string;
  originalName: string;
  mimeType: string;
  size: number;
  url: string;
  thumbnailUrl?: string;
  alt?: string;
  caption?: string;
  folder?: string;
  tags?: string[];
  createdAt: string;
  updatedAt: string;
}

export interface MediaFilters {
  search?: string;
  type?: string;
  folder?: string;
  tags?: string[];
  page: number;
  limit: number;
}

export interface UploadResponse {
  success: boolean;
  files?: MediaFile[];
  error?: string;
}

export function useMedia() {
  const [loading, setLoading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  const getMediaList = useCallback(async (filters: MediaFilters) => {
    setLoading(true);
    try {
      const params = new URLSearchParams();
      if (filters.search) params.append("search", String(filters.search));
      if (filters.type) params.append("type", String(filters.type));
      if (filters.folder) params.append("folder", String(filters.folder));
      if (filters.tags && filters.tags.length > 0) {
        params.append("tags", filters.tags.join(","));
      }
      params.append("page", String(filters.page));
      params.append("limit", String(filters.limit));

      const response = await fetch(`/api/admin/media/list?${params.toString()}`);
      if (!response.ok) throw new Error("Failed to fetch media files");

      const data = await response.json();
      return data;
    } catch (err) {
      const errorMessage = "Không thể tải danh sách media";
      toast.error(errorMessage);
      return { files: [], total: 0, pagination: null };
    } finally {
      setLoading(false);
    }
  }, []);

  const uploadFiles = useCallback(async (files: FileList, folder?: string): Promise<MediaFile[]> => {
    setLoading(true);
    setUploadProgress(0);

    try {
      const formData = new FormData();
      Array.from(files).forEach(file => {
        formData.append("files", file);
      });
      if (folder) {
        formData.append("folder", folder);
      }

      const response = await fetch("/api/admin/media/upload", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) throw new Error("Failed to upload files");

      const data: UploadResponse = await response.json();
      
      if (data.success && data.files) {
        toast.success(`Tải lên ${data.files.length} file thành công`);
        return data.files;
      } else {
        throw new Error(data.error || "Upload failed");
      }
    } catch (err) {
      const errorMessage = "Không thể tải lên file";
      toast.error(errorMessage);
      return [];
    } finally {
      setLoading(false);
      setUploadProgress(0);
    }
  }, []);

  const updateMedia = useCallback(async (fileId: string, updates: Partial<Pick<MediaFile, "alt" | "caption" | "tags" | "folder">>): Promise<MediaFile | null> => {
    setLoading(true);
    try {
      const response = await fetch(`/api/admin/media/${fileId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(updates),
      });

      if (!response.ok) throw new Error("Failed to update media file");

      const data = await response.json();
      toast.success("Cập nhật file thành công");
      return data.file || null;
    } catch (err) {
      const errorMessage = "Không thể cập nhật file";
      toast.error(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const deleteMedia = useCallback(async (fileId: string): Promise<boolean> => {
    setLoading(true);
    try {
      const response = await fetch(`/api/admin/media/${fileId}`, {
        method: "DELETE",
      });

      if (!response.ok) throw new Error("Failed to delete media file");

      toast.success("Xóa file thành công");
      return true;
    } catch (err) {
      const errorMessage = "Không thể xóa file";
      toast.error(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  const bulkDeleteMedia = useCallback(async (fileIds: string[]): Promise<boolean> => {
    setLoading(true);
    try {
      await Promise.all(
        fileIds.map(fileId => 
          fetch(`/api/admin/media/${fileId}`, { method: "DELETE" })
        )
      );

      toast.success("Xóa file hàng loạt thành công");
      return true;
    } catch (err) {
      const errorMessage = "Không thể xóa file hàng loạt";
      toast.error(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    loading,
    uploadProgress,
    getMediaList,
    uploadFiles,
    updateMedia,
    deleteMedia,
    bulkDeleteMedia,
  };
}