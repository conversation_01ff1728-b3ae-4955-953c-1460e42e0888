"use client";

import { useState, useCallback } from "react";
import { toast } from "sonner";

export interface AdminUser {
  id: string;
  username: string;
  email: string;
  name: string;
  avatar?: string;
  role: "super_admin" | "admin" | "moderator";
  status: "active" | "inactive" | "banned";
  permissions?: string[];
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface AdminFilters {
  search?: string;
  role?: string;
  status?: string;
  page: number;
  limit: number;
}

export interface LoginData {
  username: string;
  password: string;
}

export function useAdmins() {
  const [loading, setLoading] = useState(false);

  const getAdmins = useCallback(async (filters: AdminFilters) => {
    setLoading(true);
    try {
      const params = new URLSearchParams();
      if (filters.search) params.append("search", String(filters.search));
      if (filters.role) params.append("role", String(filters.role));
      if (filters.status) params.append("status", String(filters.status));
      params.append("page", String(filters.page));
      params.append("limit", String(filters.limit));

      const response = await fetch(`/api/admin/admins?${params.toString()}`);
      if (!response.ok) throw new Error("Failed to fetch admins");

      const data = await response.json();
      return data;
    } catch (err) {
      const errorMessage = "Không thể tải danh sách admin";
      toast.error(errorMessage);
      return { admins: [], total: 0, pagination: null };
    } finally {
      setLoading(false);
    }
  }, []);

  const deleteAdmin = useCallback(async (adminId: string): Promise<boolean> => {
    setLoading(true);
    try {
      const response = await fetch(`/api/admin/admins/${adminId}`, {
        method: "DELETE",
      });

      if (!response.ok) throw new Error("Failed to delete admin");

      toast.success("Xóa admin thành công");
      return true;
    } catch (err) {
      const errorMessage = "Không thể xóa admin";
      toast.error(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  const login = useCallback(async (loginData: LoginData): Promise<boolean> => {
    setLoading(true);
    try {
      const response = await fetch("/api/admin-login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(loginData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Login failed");
      }

      const data = await response.json();
      
      if (data.success) {
        toast.success("Đăng nhập thành công");
        return true;
      } else {
        throw new Error(data.error || "Login failed");
      }
    } catch (err: any) {
      const errorMessage = err.message || "Đăng nhập thất bại";
      toast.error(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    loading,
    getAdmins,
    deleteAdmin,
    login,
  };
}