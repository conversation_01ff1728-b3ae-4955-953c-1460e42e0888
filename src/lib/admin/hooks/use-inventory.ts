"use client";

import { useState, useCallback } from "react";
import { toast } from "sonner";

export interface InventoryMovement {
  id: string;
  productId: string;
  productName: string;
  type: "in" | "out" | "adjustment";
  quantity: number;
  previousStock: number;
  newStock: number;
  reason?: string;
  reference?: string;
  createdBy: string;
  createdAt: string;
}

export interface InventoryFilters {
  search?: string;
  productId?: string;
  type?: string;
  dateFrom?: string;
  dateTo?: string;
  page: number;
  limit: number;
}

export interface CreateInventoryData {
  productId: string;
  type: "in" | "out" | "adjustment";
  quantity: number;
  reason?: string;
  reference?: string;
}

export function useInventory() {
  const [loading, setLoading] = useState(false);

  const getInventoryMovements = useCallback(async (filters: InventoryFilters) => {
    setLoading(true);
    try {
      const params = new URLSearchParams();
      if (filters.search) params.append("search", String(filters.search));
      if (filters.productId) params.append("productId", String(filters.productId));
      if (filters.type) params.append("type", String(filters.type));
      if (filters.dateFrom) params.append("dateFrom", String(filters.dateFrom));
      if (filters.dateTo) params.append("dateTo", String(filters.dateTo));
      params.append("page", String(filters.page));
      params.append("limit", String(filters.limit));

      const response = await fetch(
        `/api/admin/inventory/movements?${params.toString()}`
      );
      if (!response.ok) throw new Error("Failed to fetch inventory movements");

      const data = await response.json();
      return data;
    } catch (err) {
      const errorMessage = "Không thể tải lịch sử xuất nhập kho";
      toast.error(errorMessage);
      return { movements: [], total: 0, pagination: null };
    } finally {
      setLoading(false);
    }
  }, []);

  const getProducts = useCallback(async () => {
    try {
      const response = await fetch("/api/admin/products");
      if (!response.ok) throw new Error("Failed to fetch products");

      const data = await response.json();
      return data.products || [];
    } catch (err) {
      const errorMessage = "Không thể tải danh sách sản phẩm";
      toast.error(errorMessage);
      return [];
    }
  }, []);

  const createInventoryMovement = useCallback(async (inventoryData: CreateInventoryData): Promise<InventoryMovement | null> => {
    setLoading(true);
    try {
      const response = await fetch("/api/admin/inventory", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(inventoryData),
      });

      if (!response.ok) throw new Error("Failed to create inventory movement");

      const data = await response.json();
      toast.success("Tạo phiếu xuất nhập kho thành công");
      return data.movement || null;
    } catch (err) {
      const errorMessage = "Không thể tạo phiếu xuất nhập kho";
      toast.error(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    loading,
    getInventoryMovements,
    getProducts,
    createInventoryMovement,
  };
}