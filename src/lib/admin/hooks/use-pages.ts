"use client";

import { useState, useCallback } from "react";
import { toast } from "sonner";

export interface Page {
  id: string;
  title: string;
  content: string;
  slug: string;
  status: "draft" | "published" | "archived";
  template?: string;
  seo?: {
    metaTitle?: string;
    metaDescription?: string;
    keywords?: string[];
  };
  createdAt: string;
  updatedAt: string;
}

export interface CreatePageData {
  title: string;
  content: string;
  slug: string;
  status: Page["status"];
  template?: string;
  seo?: Page["seo"];
}

export function usePages() {
  const [loading, setLoading] = useState(false);

  const getPage = useCallback(async (id: string): Promise<Page | null> => {
    setLoading(true);
    try {
      const response = await fetch(`/api/admin/pages/${id}`);
      if (!response.ok) throw new Error("Failed to fetch page");

      const data = await response.json();
      return data.page || null;
    } catch (err) {
      const errorMessage = "Không thể tải trang";
      toast.error(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const createPage = useCallback(async (pageData: CreatePageData): Promise<Page | null> => {
    setLoading(true);
    try {
      const response = await fetch("/api/admin/pages", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(pageData),
      });

      if (!response.ok) throw new Error("Failed to create page");

      const data = await response.json();
      toast.success("Tạo trang thành công");
      return data.page || null;
    } catch (err) {
      const errorMessage = "Không thể tạo trang";
      toast.error(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const updatePage = useCallback(async (id: string, pageData: Partial<CreatePageData>): Promise<Page | null> => {
    setLoading(true);
    try {
      const response = await fetch(`/api/admin/pages/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(pageData),
      });

      if (!response.ok) throw new Error("Failed to update page");

      const data = await response.json();
      toast.success("Cập nhật trang thành công");
      return data.page || null;
    } catch (err) {
      const errorMessage = "Không thể cập nhật trang";
      toast.error(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const deletePage = useCallback(async (id: string): Promise<boolean> => {
    setLoading(true);
    try {
      const response = await fetch(`/api/admin/pages/${id}`, {
        method: "DELETE",
      });

      if (!response.ok) throw new Error("Failed to delete page");

      toast.success("Xóa trang thành công");
      return true;
    } catch (err) {
      const errorMessage = "Không thể xóa trang";
      toast.error(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    loading,
    getPage,
    createPage,
    updatePage,
    deletePage,
  };
}