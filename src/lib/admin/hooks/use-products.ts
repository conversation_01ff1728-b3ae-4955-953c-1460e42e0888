"use client";

import { useState, useCallback } from "react";
import { toast } from "sonner";

export interface Product {
  id: string;
  name: string;
  description?: string;
  shortDescription?: string;
  slug: string;
  sku: string;
  price: number;
  salePrice?: number;
  status: "active" | "inactive" | "draft";
  stock: number;
  images?: string[];
  featuredImage?: string;
  categories?: Array<{ id: string; name: string }>;
  brand?: { id: string; name: string };
  attributes?: Array<{
    id: string;
    name: string;
    value: string;
  }>;
  seo?: {
    metaTitle?: string;
    metaDescription?: string;
    keywords?: string[];
  };
  createdAt: string;
  updatedAt: string;
}

export interface CreateProductData {
  name: string;
  description?: string;
  shortDescription?: string;
  slug: string;
  sku: string;
  price: number;
  salePrice?: number;
  status: Product["status"];
  stock: number;
  images?: string[];
  featuredImage?: string;
  categoryIds?: string[];
  brandId?: string;
  attributes?: Array<{
    attributeId: string;
    value: string;
  }>;
  seo?: Product["seo"];
}

export function useProducts() {
  const [loading, setLoading] = useState(false);

  const getProduct = useCallback(async (id: string): Promise<Product | null> => {
    setLoading(true);
    try {
      const response = await fetch(`/api/admin/products/${id}`);
      if (!response.ok) throw new Error("Failed to fetch product");

      const data = await response.json();
      return data.product || null;
    } catch (err) {
      const errorMessage = "Không thể tải sản phẩm";
      toast.error(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const createProduct = useCallback(async (productData: CreateProductData): Promise<Product | null> => {
    setLoading(true);
    try {
      const response = await fetch("/api/admin/products", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(productData),
      });

      if (!response.ok) throw new Error("Failed to create product");

      const data = await response.json();
      toast.success("Tạo sản phẩm thành công");
      return data.product || null;
    } catch (err) {
      const errorMessage = "Không thể tạo sản phẩm";
      toast.error(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const updateProduct = useCallback(async (id: string, productData: Partial<CreateProductData>): Promise<Product | null> => {
    setLoading(true);
    try {
      const response = await fetch(`/api/admin/products/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(productData),
      });

      if (!response.ok) throw new Error("Failed to update product");

      const data = await response.json();
      toast.success("Cập nhật sản phẩm thành công");
      return data.product || null;
    } catch (err) {
      const errorMessage = "Không thể cập nhật sản phẩm";
      toast.error(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const deleteProduct = useCallback(async (id: string): Promise<boolean> => {
    setLoading(true);
    try {
      const response = await fetch(`/api/admin/products/${id}`, {
        method: "DELETE",
      });

      if (!response.ok) throw new Error("Failed to delete product");

      toast.success("Xóa sản phẩm thành công");
      return true;
    } catch (err) {
      const errorMessage = "Không thể xóa sản phẩm";
      toast.error(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  const getCategories = useCallback(async () => {
    try {
      const response = await fetch("/api/admin/categories");
      if (!response.ok) throw new Error("Failed to fetch categories");

      const data = await response.json();
      return data.categories || [];
    } catch (err) {
      const errorMessage = "Không thể tải danh mục";
      toast.error(errorMessage);
      return [];
    }
  }, []);

  return {
    loading,
    getProduct,
    createProduct,
    updateProduct,
    deleteProduct,
    getCategories,
  };
}