"use client";

import { useState, useCallback } from "react";
import { toast } from "sonner";

export interface AnalyticsData {
  totalSales: number;
  totalOrders: number;
  totalCustomers: number;
  totalProducts: number;
  salesGrowth: number;
  ordersGrowth: number;
  customersGrowth: number;
  productsGrowth: number;
  chartData: Array<{
    date: string;
    sales: number;
    orders: number;
    customers: number;
  }>;
  topProducts: Array<{
    id: string;
    name: string;
    sales: number;
    orders: number;
  }>;
  topCategories: Array<{
    id: string;
    name: string;
    sales: number;
    orders: number;
  }>;
  recentOrders: Array<{
    id: string;
    customerName: string;
    total: number;
    status: string;
    createdAt: string;
  }>;
}

export type TimeRange = "7d" | "30d" | "90d" | "1y" | "all";

export function useAnalytics() {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<AnalyticsData | null>(null);

  const fetchAnalytics = useCallback(async (timeRange: TimeRange = "30d") => {
    setLoading(true);
    try {
      const response = await fetch(`/api/admin/analytics?range=${timeRange}`, {
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) throw new Error("Failed to fetch analytics data");

      const analyticsData = await response.json();
      setData(analyticsData);
      return analyticsData;
    } catch (err) {
      const errorMessage = "Không thể tải dữ liệu phân tích";
      toast.error(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const refreshAnalytics = useCallback(async (timeRange: TimeRange = "30d") => {
    return await fetchAnalytics(timeRange);
  }, [fetchAnalytics]);

  return {
    loading,
    data,
    fetchAnalytics,
    refreshAnalytics,
  };
}