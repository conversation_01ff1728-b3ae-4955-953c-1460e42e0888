'use client';

import { useReducer, useCallback, useEffect } from 'react';
import { FormState, FormAction, FormField, UseFormReturn } from '../types';

/**
 * Form reducer
 */
function formReducer(state: FormState, action: FormAction): FormState {
  switch (action.type) {
    case 'SET_FIELD':
      return {
        ...state,
        data: {
          ...state.data,
          [action.field]: action.value,
        },
        // Clear error when field is updated
        errors: {
          ...state.errors,
          [action.field]: '',
        },
      };

    case 'SET_ERROR':
      return {
        ...state,
        errors: {
          ...state.errors,
          [action.field]: action.error,
        },
      };

    case 'CLEAR_ERROR':
      return {
        ...state,
        errors: {
          ...state.errors,
          [action.field]: '',
        },
      };

    case 'SET_TOUCHED':
      return {
        ...state,
        touched: {
          ...state.touched,
          [action.field]: true,
        },
      };

    case 'SET_LOADING':
      return {
        ...state,
        loading: action.loading,
      };

    case 'SET_SUBMITTING':
      return {
        ...state,
        submitting: action.submitting,
      };

    case 'RESET_FORM':
      return {
        data: action.initialData || {},
        errors: {},
        touched: {},
        loading: false,
        submitting: false,
      };

    case 'SET_ERRORS':
      return {
        ...state,
        errors: action.errors,
      };

    default:
      return state;
  }
}

interface UseAdminFormOptions {
  fields: FormField[];
  initialData?: any;
  onSubmit: (data: any) => Promise<void>;
  validateOnChange?: boolean;
  validateOnBlur?: boolean;
}

/**
 * Hook for admin form management
 */
export function useAdminForm(options: UseAdminFormOptions): UseFormReturn {
  const {
    fields,
    initialData = {},
    onSubmit,
    validateOnChange = true,
    validateOnBlur = true,
  } = options;

  const [state, dispatch] = useReducer(formReducer, {
    data: initialData,
    errors: {},
    touched: {},
    loading: false,
    submitting: false,
  });

  /**
   * Validate a single field
   */
  const validateField = useCallback((field: FormField, value: any): string => {
    const { validation } = field;
    if (!validation) return '';

    // Required validation
    if (validation.required && (!value || (typeof value === 'string' && !value.trim()))) {
      return `${field.label} là bắt buộc`;
    }

    // Skip other validations if field is empty and not required
    if (!value && !validation.required) return '';

    // Min length validation
    if (validation.min && typeof value === 'string' && value.length < validation.min) {
      return `${field.label} phải có ít nhất ${validation.min} ký tự`;
    }

    // Max length validation
    if (validation.max && typeof value === 'string' && value.length > validation.max) {
      return `${field.label} không được vượt quá ${validation.max} ký tự`;
    }

    // Min value validation (for numbers)
    if (validation.min && typeof value === 'number' && value < validation.min) {
      return `${field.label} phải lớn hơn hoặc bằng ${validation.min}`;
    }

    // Max value validation (for numbers)
    if (validation.max && typeof value === 'number' && value > validation.max) {
      return `${field.label} phải nhỏ hơn hoặc bằng ${validation.max}`;
    }

    // Pattern validation
    if (validation.pattern && typeof value === 'string' && !validation.pattern.test(value)) {
      return `${field.label} không đúng định dạng`;
    }

    // Custom validation
    if (validation.custom) {
      const result = validation.custom(value);
      if (typeof result === 'string') return result;
      if (result === false) return `${field.label} không hợp lệ`;
    }

    return '';
  }, []);

  /**
   * Validate all fields
   */
  const validate = useCallback((): boolean => {
    const errors: Record<string, string> = {};
    let isValid = true;

    fields.forEach(field => {
      const error = validateField(field, state.data[field.name]);
      if (error) {
        errors[field.name] = error;
        isValid = false;
      }
    });

    dispatch({ type: 'SET_ERRORS', errors });
    return isValid;
  }, [fields, state.data, validateField]);

  /**
   * Set field value
   */
  const setValue = useCallback((field: string, value: any) => {
    dispatch({ type: 'SET_FIELD', field, value });

    // Validate on change if enabled
    if (validateOnChange) {
      const fieldConfig = fields.find(f => f.name === field);
      if (fieldConfig) {
        const error = validateField(fieldConfig, value);
        if (error) {
          dispatch({ type: 'SET_ERROR', field, error });
        } else {
          dispatch({ type: 'CLEAR_ERROR', field });
        }
      }
    }
  }, [fields, validateField, validateOnChange]);

  /**
   * Set field error
   */
  const setError = useCallback((field: string, error: string) => {
    dispatch({ type: 'SET_ERROR', field, error });
  }, []);

  /**
   * Clear field error
   */
  const clearError = useCallback((field: string) => {
    dispatch({ type: 'CLEAR_ERROR', field });
  }, []);

  /**
   * Set field as touched
   */
  const setTouched = useCallback((field: string) => {
    dispatch({ type: 'SET_TOUCHED', field });

    // Validate on blur if enabled
    if (validateOnBlur) {
      const fieldConfig = fields.find(f => f.name === field);
      if (fieldConfig) {
        const error = validateField(fieldConfig, state.data[field]);
        if (error) {
          dispatch({ type: 'SET_ERROR', field, error });
        } else {
          dispatch({ type: 'CLEAR_ERROR', field });
        }
      }
    }
  }, [fields, state.data, validateField, validateOnBlur]);

  /**
   * Handle form submission
   */
  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();

    dispatch({ type: 'SET_SUBMITTING', submitting: true });

    try {
      // Validate all fields
      const isValid = validate();
      if (!isValid) {
        return;
      }

      // Call onSubmit
      await onSubmit(state.data);
    } catch (error) {
      console.error('Form submission error:', error);
    } finally {
      dispatch({ type: 'SET_SUBMITTING', submitting: false });
    }
  }, [state.data, validate, onSubmit]);

  /**
   * Reset form
   */
  const reset = useCallback((newInitialData?: any) => {
    dispatch({ type: 'RESET_FORM', initialData: newInitialData || initialData });
  }, [initialData]);

  /**
   * Check if form is valid
   */
  const isValid = Object.keys(state.errors).length === 0 || 
    Object.values(state.errors).every(error => !error);

  /**
   * Check if form is dirty (has changes)
   */
  const isDirty = JSON.stringify(state.data) !== JSON.stringify(initialData);

  // Initialize form data with default values
  useEffect(() => {
    const defaultData: any = {};
    fields.forEach(field => {
      if (field.defaultValue !== undefined) {
        defaultData[field.name] = field.defaultValue;
      }
    });

    if (Object.keys(defaultData).length > 0) {
      dispatch({ type: 'RESET_FORM', initialData: { ...defaultData, ...initialData } });
    }
  }, [fields, initialData]);

  return {
    state,
    setValue,
    setError,
    clearError,
    setTouched,
    handleSubmit,
    reset,
    validate,
    isValid,
    isDirty,
  };
}
