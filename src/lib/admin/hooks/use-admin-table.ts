'use client';

import { useReducer, useCallback } from 'react';
import { TableState, TableAction_Type, UseTableReturn } from '../types';

/**
 * Table reducer
 */
function tableReducer<T>(state: TableState<T>, action: TableAction_Type): TableState<T> {
  switch (action.type) {
    case 'SET_DATA':
      return {
        ...state,
        data: action.data,
      };

    case 'SET_LOADING':
      return {
        ...state,
        loading: action.loading,
      };

    case 'SET_PAGINATION':
      return {
        ...state,
        pagination: {
          ...state.pagination,
          ...action.pagination,
        },
      };

    case 'SET_FILTERS':
      return {
        ...state,
        filters: action.filters,
      };

    case 'SET_SORTING':
      return {
        ...state,
        sorting: action.sorting,
      };

    case 'SET_SELECTION':
      return {
        ...state,
        selection: {
          ...state.selection,
          ...action.selection,
        },
      };

    case 'SET_SEARCH':
      return {
        ...state,
        search: action.search,
      };

    case 'RESET_TABLE':
      return {
        data: [],
        loading: false,
        pagination: {
          current: 1,
          pageSize: 20,
          total: 0,
        },
        filters: {},
        sorting: {},
        selection: {
          selectedRowKeys: [],
          selectedRows: [],
        },
        search: '',
      };

    default:
      return state;
  }
}

interface UseAdminTableOptions<T> {
  initialPageSize?: number;
  onRefresh?: () => Promise<void>;
  rowKey?: keyof T;
}

/**
 * Hook for admin table management
 */
export function useAdminTable<T = any>(
  options: UseAdminTableOptions<T> = {}
): UseTableReturn<T> {
  const {
    initialPageSize = 20,
    onRefresh,
    rowKey = 'id' as keyof T,
  } = options;

  const [state, dispatch] = useReducer(tableReducer<T>, {
    data: [],
    loading: false,
    pagination: {
      current: 1,
      pageSize: initialPageSize,
      total: 0,
    },
    filters: {},
    sorting: {},
    selection: {
      selectedRowKeys: [],
      selectedRows: [],
    },
    search: '',
  });

  /**
   * Set table data
   */
  const setData = useCallback((data: T[]) => {
    dispatch({ type: 'SET_DATA', data });
  }, []);

  /**
   * Set loading state
   */
  const setLoading = useCallback((loading: boolean) => {
    dispatch({ type: 'SET_LOADING', loading });
  }, []);

  /**
   * Set pagination
   */
  const setPagination = useCallback((pagination: Partial<TableState<T>['pagination']>) => {
    dispatch({ type: 'SET_PAGINATION', pagination });
  }, []);

  /**
   * Set filters
   */
  const setFilters = useCallback((filters: Record<string, any>) => {
    dispatch({ type: 'SET_FILTERS', filters });
    // Reset to first page when filters change
    dispatch({ type: 'SET_PAGINATION', pagination: { current: 1 } });
  }, []);

  /**
   * Set sorting
   */
  const setSorting = useCallback((sorting: TableState<T>['sorting']) => {
    dispatch({ type: 'SET_SORTING', sorting });
  }, []);

  /**
   * Set selection
   */
  const setSelection = useCallback((selection: Partial<TableState<T>['selection']>) => {
    dispatch({ type: 'SET_SELECTION', selection });
  }, []);

  /**
   * Set search
   */
  const setSearch = useCallback((search: string) => {
    dispatch({ type: 'SET_SEARCH', search });
    // Reset to first page when search changes
    dispatch({ type: 'SET_PAGINATION', pagination: { current: 1 } });
  }, []);

  /**
   * Reset table state
   */
  const reset = useCallback(() => {
    dispatch({ type: 'RESET_TABLE' });
  }, []);

  /**
   * Refresh table data
   */
  const refresh = useCallback(async () => {
    if (onRefresh) {
      setLoading(true);
      try {
        await onRefresh();
      } finally {
        setLoading(false);
      }
    }
  }, [onRefresh, setLoading]);

  /**
   * Handle row selection
   */
  const handleRowSelect = useCallback((record: T, selected: boolean) => {
    const key = String(record[rowKey]);
    const { selectedRowKeys, selectedRows } = state.selection;

    if (selected) {
      // Add to selection
      setSelection({
        selectedRowKeys: [...selectedRowKeys, key],
        selectedRows: [...selectedRows, record],
      });
    } else {
      // Remove from selection
      setSelection({
        selectedRowKeys: selectedRowKeys.filter(k => k !== key),
        selectedRows: selectedRows.filter(r => String(r[rowKey]) !== key),
      });
    }
  }, [state.selection, setSelection, rowKey]);

  /**
   * Handle select all rows
   */
  const handleSelectAll = useCallback((selected: boolean) => {
    if (selected) {
      // Select all visible rows
      const allKeys = state.data.map(record => String(record[rowKey]));
      setSelection({
        selectedRowKeys: allKeys,
        selectedRows: [...state.data],
      });
    } else {
      // Clear selection
      setSelection({
        selectedRowKeys: [],
        selectedRows: [],
      });
    }
  }, [state.data, setSelection, rowKey]);

  /**
   * Clear selection
   */
  const clearSelection = useCallback(() => {
    setSelection({
      selectedRowKeys: [],
      selectedRows: [],
    });
  }, [setSelection]);

  /**
   * Get current query parameters
   */
  const getQueryParams = useCallback(() => {
    const params: Record<string, any> = {
      page: state.pagination.current,
      limit: state.pagination.pageSize,
    };

    if (state.search) {
      params.search = state.search;
    }

    if (state.sorting.field) {
      params.sortBy = state.sorting.field;
      params.sortOrder = state.sorting.order || 'asc';
    }

    // Add filters
    Object.entries(state.filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params[key] = value;
      }
    });

    return params;
  }, [state]);

  /**
   * Check if a row is selected
   */
  const isRowSelected = useCallback((record: T): boolean => {
    const key = String(record[rowKey]);
    return state.selection.selectedRowKeys.includes(key);
  }, [state.selection.selectedRowKeys, rowKey]);

  /**
   * Check if all rows are selected
   */
  const isAllSelected = useCallback((): boolean => {
    return state.data.length > 0 && 
           state.data.every(record => isRowSelected(record));
  }, [state.data, isRowSelected]);

  /**
   * Check if some rows are selected (for indeterminate state)
   */
  const isSomeSelected = useCallback((): boolean => {
    return state.selection.selectedRowKeys.length > 0 && 
           !isAllSelected();
  }, [state.selection.selectedRowKeys.length, isAllSelected]);

  return {
    state,
    setData,
    setLoading,
    setPagination,
    setFilters,
    setSorting,
    setSelection,
    setSearch,
    reset,
    refresh,
    handleRowSelect,
    handleSelectAll,
    clearSelection,
    getQueryParams,
    isRowSelected,
    isAllSelected,
    isSomeSelected,
  } as UseTableReturn<T> & {
    handleRowSelect: (record: T, selected: boolean) => void;
    handleSelectAll: (selected: boolean) => void;
    clearSelection: () => void;
    getQueryParams: () => Record<string, any>;
    isRowSelected: (record: T) => boolean;
    isAllSelected: () => boolean;
    isSomeSelected: () => boolean;
  };
}
