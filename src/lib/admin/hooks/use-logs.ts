"use client";

import { useState, useCallback } from "react";
import { toast } from "sonner";

export interface LogEntry {
  id: string;
  level: "info" | "warn" | "error" | "debug";
  message: string;
  userId?: string;
  userEmail?: string;
  action?: string;
  resource?: string;
  resourceId?: string;
  metadata?: Record<string, any>;
  ip?: string;
  userAgent?: string;
  createdAt: string;
}

export interface LogFilters {
  search?: string;
  level?: string;
  action?: string;
  resource?: string;
  userId?: string;
  dateFrom?: string;
  dateTo?: string;
  page: number;
  limit: number;
}

export function useLogs() {
  const [loading, setLoading] = useState(false);

  const getLogs = useCallback(async (filters: LogFilters) => {
    setLoading(true);
    try {
      const params = new URLSearchParams();
      if (filters.search) params.append("search", String(filters.search));
      if (filters.level) params.append("level", String(filters.level));
      if (filters.action) params.append("action", String(filters.action));
      if (filters.resource) params.append("resource", String(filters.resource));
      if (filters.userId) params.append("userId", String(filters.userId));
      if (filters.dateFrom) params.append("dateFrom", String(filters.dateFrom));
      if (filters.dateTo) params.append("dateTo", String(filters.dateTo));
      params.append("page", String(filters.page));
      params.append("limit", String(filters.limit));

      const response = await fetch(`/api/admin/logs?${params.toString()}`);
      if (!response.ok) throw new Error("Failed to fetch logs");

      const data = await response.json();
      return data;
    } catch (err) {
      const errorMessage = "Không thể tải danh sách log";
      toast.error(errorMessage);
      return { logs: [], total: 0, pagination: null };
    } finally {
      setLoading(false);
    }
  }, []);

  const exportLogs = useCallback(async (filters: Omit<LogFilters, "page" | "limit">) => {
    setLoading(true);
    try {
      const params = new URLSearchParams();
      if (filters.search) params.append("search", String(filters.search));
      if (filters.level) params.append("level", String(filters.level));
      if (filters.action) params.append("action", String(filters.action));
      if (filters.resource) params.append("resource", String(filters.resource));
      if (filters.userId) params.append("userId", String(filters.userId));
      if (filters.dateFrom) params.append("dateFrom", String(filters.dateFrom));
      if (filters.dateTo) params.append("dateTo", String(filters.dateTo));

      const response = await fetch(`/api/admin/logs/export?${params.toString()}`);
      if (!response.ok) throw new Error("Failed to export logs");

      // Create download
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.style.display = "none";
      a.href = url;
      a.download = `logs_${new Date().toISOString().split("T")[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast.success("Xuất log thành công");
      return true;
    } catch (err) {
      const errorMessage = "Không thể xuất log";
      toast.error(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    loading,
    getLogs,
    exportLogs,
  };
}