"use client";

import { useState, useCallback } from "react";
import { toast } from "sonner";

export interface Brand {
  id: string;
  name: string;
  description?: string;
  slug: string;
  logo?: string;
  website?: string;
  status: "active" | "inactive";
  seo?: {
    metaTitle?: string;
    metaDescription?: string;
    keywords?: string[];
  };
  createdAt: string;
  updatedAt: string;
}

export interface CreateBrandData {
  name: string;
  description?: string;
  slug: string;
  logo?: string;
  website?: string;
  status: Brand["status"];
  seo?: Brand["seo"];
}

export function useBrands() {
  const [loading, setLoading] = useState(false);

  const getBrand = useCallback(async (id: string): Promise<Brand | null> => {
    setLoading(true);
    try {
      const response = await fetch(`/api/admin/brands/${id}`);
      if (!response.ok) throw new Error("Failed to fetch brand");

      const data = await response.json();
      return data.brand || null;
    } catch (err) {
      const errorMessage = "<PERSON>hông thể tải thương hiệu";
      toast.error(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const createBrand = useCallback(async (brandData: CreateBrandData): Promise<Brand | null> => {
    setLoading(true);
    try {
      const response = await fetch("/api/admin/brands", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(brandData),
      });

      if (!response.ok) throw new Error("Failed to create brand");

      const data = await response.json();
      toast.success("Tạo thương hiệu thành công");
      return data.brand || null;
    } catch (err) {
      const errorMessage = "Không thể tạo thương hiệu";
      toast.error(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const updateBrand = useCallback(async (id: string, brandData: Partial<CreateBrandData>): Promise<Brand | null> => {
    setLoading(true);
    try {
      const response = await fetch(`/api/admin/brands/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(brandData),
      });

      if (!response.ok) throw new Error("Failed to update brand");

      const data = await response.json();
      toast.success("Cập nhật thương hiệu thành công");
      return data.brand || null;
    } catch (err) {
      const errorMessage = "Không thể cập nhật thương hiệu";
      toast.error(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const deleteBrand = useCallback(async (id: string): Promise<boolean> => {
    setLoading(true);
    try {
      const response = await fetch(`/api/admin/brands/${id}`, {
        method: "DELETE",
      });

      if (!response.ok) throw new Error("Failed to delete brand");

      toast.success("Xóa thương hiệu thành công");
      return true;
    } catch (err) {
      const errorMessage = "Không thể xóa thương hiệu";
      toast.error(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    loading,
    getBrand,
    createBrand,
    updateBrand,
    deleteBrand,
  };
}