"use client";

import { useState, useCallback } from "react";
import { toast } from "sonner";

export interface Attribute {
  id: string;
  name: string;
  type: "text" | "number" | "select" | "multiselect" | "boolean" | "color" | "date";
  required: boolean;
  searchable: boolean;
  options?: string[];
  defaultValue?: string;
  description?: string;
  sort: number;
  status: "active" | "inactive";
  createdAt: string;
  updatedAt: string;
}

export interface CreateAttributeData {
  name: string;
  type: Attribute["type"];
  required?: boolean;
  searchable?: boolean;
  options?: string[];
  defaultValue?: string;
  description?: string;
  sort?: number;
  status: Attribute["status"];
}

export function useAttributes() {
  const [loading, setLoading] = useState(false);

  const getAttribute = useCallback(async (id: string): Promise<Attribute | null> => {
    setLoading(true);
    try {
      const response = await fetch(`/api/admin/attributes/${id}`);
      if (!response.ok) throw new Error("Failed to fetch attribute");

      const data = await response.json();
      return data.attribute || null;
    } catch (err) {
      const errorMessage = "Không thể tải thuộc tính";
      toast.error(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const createAttribute = useCallback(async (attributeData: CreateAttributeData): Promise<Attribute | null> => {
    setLoading(true);
    try {
      const response = await fetch("/api/admin/attributes", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(attributeData),
      });

      if (!response.ok) throw new Error("Failed to create attribute");

      const data = await response.json();
      toast.success("Tạo thuộc tính thành công");
      return data.attribute || null;
    } catch (err) {
      const errorMessage = "Không thể tạo thuộc tính";
      toast.error(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const updateAttribute = useCallback(async (id: string, attributeData: Partial<CreateAttributeData>): Promise<Attribute | null> => {
    setLoading(true);
    try {
      const response = await fetch(`/api/admin/attributes/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(attributeData),
      });

      if (!response.ok) throw new Error("Failed to update attribute");

      const data = await response.json();
      toast.success("Cập nhật thuộc tính thành công");
      return data.attribute || null;
    } catch (err) {
      const errorMessage = "Không thể cập nhật thuộc tính";
      toast.error(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const deleteAttribute = useCallback(async (id: string): Promise<boolean> => {
    setLoading(true);
    try {
      const response = await fetch(`/api/admin/attributes/${id}`, {
        method: "DELETE",
      });

      if (!response.ok) throw new Error("Failed to delete attribute");

      toast.success("Xóa thuộc tính thành công");
      return true;
    } catch (err) {
      const errorMessage = "Không thể xóa thuộc tính";
      toast.error(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  const toggleStatus = useCallback(async (id: string): Promise<boolean> => {
    setLoading(true);
    try {
      const response = await fetch(`/api/admin/attributes/${id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ action: "toggle-status" }),
      });

      if (!response.ok) throw new Error("Failed to toggle attribute status");

      toast.success("Cập nhật trạng thái thuộc tính thành công");
      return true;
    } catch (err) {
      const errorMessage = "Không thể cập nhật trạng thái thuộc tính";
      toast.error(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    loading,
    getAttribute,
    createAttribute,
    updateAttribute,
    deleteAttribute,
    toggleStatus,
  };
}