"use client";

import { useState, useCallback } from "react";
import { toast } from "sonner";

export interface Post {
  id: string;
  title: string;
  content: string;
  excerpt?: string;
  slug: string;
  status: "draft" | "published" | "archived";
  featuredImage?: string;
  tags?: string[];
  categories?: string[];
  seo?: {
    metaTitle?: string;
    metaDescription?: string;
    keywords?: string[];
  };
  publishedAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreatePostData {
  title: string;
  content: string;
  excerpt?: string;
  slug: string;
  status: Post["status"];
  featuredImage?: string;
  tags?: string[];
  categories?: string[];
  seo?: Post["seo"];
  publishedAt?: string;
}

export function usePosts() {
  const [loading, setLoading] = useState(false);

  const getPost = useCallback(async (id: string): Promise<Post | null> => {
    setLoading(true);
    try {
      const response = await fetch(`/api/admin/posts/${id}`);
      if (!response.ok) throw new Error("Failed to fetch post");

      const data = await response.json();
      return data.post || null;
    } catch (err) {
      const errorMessage = "Không thể tải bài viết";
      toast.error(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const createPost = useCallback(async (postData: CreatePostData): Promise<Post | null> => {
    setLoading(true);
    try {
      const response = await fetch("/api/admin/posts", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(postData),
      });

      if (!response.ok) throw new Error("Failed to create post");

      const data = await response.json();
      toast.success("Tạo bài viết thành công");
      return data.post || null;
    } catch (err) {
      const errorMessage = "Không thể tạo bài viết";
      toast.error(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const updatePost = useCallback(async (id: string, postData: Partial<CreatePostData>): Promise<Post | null> => {
    setLoading(true);
    try {
      const response = await fetch(`/api/admin/posts/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(postData),
      });

      if (!response.ok) throw new Error("Failed to update post");

      const data = await response.json();
      toast.success("Cập nhật bài viết thành công");
      return data.post || null;
    } catch (err) {
      const errorMessage = "Không thể cập nhật bài viết";
      toast.error(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const deletePost = useCallback(async (id: string): Promise<boolean> => {
    setLoading(true);
    try {
      const response = await fetch(`/api/admin/posts/${id}`, {
        method: "DELETE",
      });

      if (!response.ok) throw new Error("Failed to delete post");

      toast.success("Xóa bài viết thành công");
      return true;
    } catch (err) {
      const errorMessage = "Không thể xóa bài viết";
      toast.error(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  const bulkDeletePosts = useCallback(async (ids: string[]): Promise<boolean> => {
    setLoading(true);
    try {
      await Promise.all(
        ids.map(id => 
          fetch(`/api/admin/posts/${id}`, { method: "DELETE" })
        )
      );

      toast.success("Xóa bài viết hàng loạt thành công");
      return true;
    } catch (err) {
      const errorMessage = "Không thể xóa bài viết hàng loạt";
      toast.error(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    loading,
    getPost,
    createPost,
    updatePost,
    deletePost,
    bulkDeletePosts,
  };
}