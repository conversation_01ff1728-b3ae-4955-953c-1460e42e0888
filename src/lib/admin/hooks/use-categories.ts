"use client";

import { useState, useCallback } from "react";
import { toast } from "sonner";

export interface Category {
  id: string;
  name: string;
  description?: string;
  slug: string;
  image?: string;
  parentId?: string;
  parent?: { id: string; name: string };
  children?: Category[];
  status: "active" | "inactive";
  sort: number;
  seo?: {
    metaTitle?: string;
    metaDescription?: string;
    keywords?: string[];
  };
  createdAt: string;
  updatedAt: string;
}

export interface CreateCategoryData {
  name: string;
  description?: string;
  slug: string;
  image?: string;
  parentId?: string;
  status: Category["status"];
  sort?: number;
  seo?: Category["seo"];
}

export function useCategories() {
  const [loading, setLoading] = useState(false);

  const getCategory = useCallback(async (id: string): Promise<Category | null> => {
    setLoading(true);
    try {
      const response = await fetch(`/api/admin/categories/${id}`);
      if (!response.ok) throw new Error("Failed to fetch category");

      const data = await response.json();
      return data.category || null;
    } catch (err) {
      const errorMessage = "Không thể tải danh mục";
      toast.error(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const getCategories = useCallback(async () => {
    try {
      const response = await fetch("/api/admin/categories");
      if (!response.ok) throw new Error("Failed to fetch categories");

      const data = await response.json();
      return data.categories || [];
    } catch (err) {
      const errorMessage = "Không thể tải danh sách danh mục";
      toast.error(errorMessage);
      return [];
    }
  }, []);

  const getPublicCategories = useCallback(async () => {
    try {
      const response = await fetch("/api/categories");
      if (!response.ok) throw new Error("Failed to fetch public categories");

      const data = await response.json();
      return data.categories || [];
    } catch (err) {
      const errorMessage = "Không thể tải danh sách danh mục công khai";
      toast.error(errorMessage);
      return [];
    }
  }, []);

  const createCategory = useCallback(async (categoryData: CreateCategoryData): Promise<Category | null> => {
    setLoading(true);
    try {
      const response = await fetch("/api/admin/categories", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(categoryData),
      });

      if (!response.ok) throw new Error("Failed to create category");

      const data = await response.json();
      toast.success("Tạo danh mục thành công");
      return data.category || null;
    } catch (err) {
      const errorMessage = "Không thể tạo danh mục";
      toast.error(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const updateCategory = useCallback(async (id: string, categoryData: Partial<CreateCategoryData>): Promise<Category | null> => {
    setLoading(true);
    try {
      const response = await fetch(`/api/admin/categories/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(categoryData),
      });

      if (!response.ok) throw new Error("Failed to update category");

      const data = await response.json();
      toast.success("Cập nhật danh mục thành công");
      return data.category || null;
    } catch (err) {
      const errorMessage = "Không thể cập nhật danh mục";
      toast.error(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const deleteCategory = useCallback(async (id: string): Promise<boolean> => {
    setLoading(true);
    try {
      const response = await fetch(`/api/admin/categories/${id}`, {
        method: "DELETE",
      });

      if (!response.ok) throw new Error("Failed to delete category");

      toast.success("Xóa danh mục thành công");
      return true;
    } catch (err) {
      const errorMessage = "Không thể xóa danh mục";
      toast.error(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    loading,
    getCategory,
    getCategories,
    getPublicCategories,
    createCategory,
    updateCategory,
    deleteCategory,
  };
}