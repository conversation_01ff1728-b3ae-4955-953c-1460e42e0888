"use client";

import { useState, useEffect, useCallback } from "react";
import { toast } from "sonner";

export interface Contact {
  id: string;
  name: string;
  email: string;
  phone?: string;
  company?: string;
  message: string;
  status: "pending" | "in_progress" | "resolved" | "rejected";
  priority: "low" | "medium" | "high" | "urgent";
  createdAt: string;
  updatedAt: string;
}

export interface ContactStats {
  total: number;
  pending: number;
  inProgress: number;
  resolved: number;
  rejected: number;
}

export interface ContactFilters {
  search?: string;
  status?: string;
  priority?: string;
  page: number;
  limit: number;
}

export function useContacts() {
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [stats, setStats] = useState<ContactStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchContacts = useCallback(async (filters: ContactFilters) => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams();
      if (filters.search) params.append("search", String(filters.search));
      if (filters.status) params.append("status", String(filters.status));
      if (filters.priority) params.append("priority", String(filters.priority));
      params.append("page", String(filters.page));
      params.append("limit", String(filters.limit));

      const response = await fetch(`/api/admin/contacts?${params.toString()}`);
      if (!response.ok) throw new Error("Failed to fetch contacts");

      const data = await response.json();
      setContacts(data.contacts || []);
      return data;
    } catch (err) {
      const errorMessage = "Không thể tải danh sách liên hệ";
      setError(errorMessage);
      toast.error(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchStats = useCallback(async () => {
    try {
      const response = await fetch("/api/admin/contacts/stats");
      if (!response.ok) throw new Error("Failed to fetch stats");

      const data = await response.json();
      setStats(data);
      return data;
    } catch (err) {
      const errorMessage = "Không thể tải thống kê liên hệ";
      setError(errorMessage);
      toast.error(errorMessage);
      throw err;
    }
  }, []);

  const updateContactStatus = useCallback(async (contactId: string, status: Contact["status"]) => {
    try {
      const response = await fetch(`/api/admin/contacts/${contactId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ status }),
      });

      if (!response.ok) throw new Error("Failed to update contact status");

      const data = await response.json();
      
      // Update local state
      setContacts(prev => 
        prev.map(contact => 
          contact.id === contactId 
            ? { ...contact, status, updatedAt: new Date().toISOString() }
            : contact
        )
      );

      toast.success("Cập nhật trạng thái thành công");
      return data;
    } catch (err) {
      const errorMessage = "Không thể cập nhật trạng thái liên hệ";
      toast.error(errorMessage);
      throw err;
    }
  }, []);

  return {
    contacts,
    stats,
    loading,
    error,
    fetchContacts,
    fetchStats,
    updateContactStatus,
  };
}