"use client";

import { getSession } from "next-auth/react";

interface AuthState {
  isAuthenticated: boolean;
  user: any | null;
  isLoading: boolean;
  error: string | null;
}

interface QueuedRequest {
  resolve: (value: any) => void;
  reject: (error: any) => void;
  fetch: () => Promise<any>;
}

class AuthenticationManager {
  private static instance: AuthenticationManager;
  private authState: AuthState = {
    isAuthenticated: false,
    user: null,
    isLoading: true,
    error: null,
  };
  private authPromise: Promise<AuthState> | null = null;
  private listeners: Set<(state: AuthState) => void> = new Set();
  private requestQueue: QueuedRequest[] = [];
  private initialized = false;

  private constructor() {}

  static getInstance(): AuthenticationManager {
    if (!AuthenticationManager.instance) {
      AuthenticationManager.instance = new AuthenticationManager();
    }
    return AuthenticationManager.instance;
  }

  // Subscribe to auth state changes
  subscribe(listener: (state: AuthState) => void): () => void {
    this.listeners.add(listener);

    // Immediately notify with current state
    listener(this.authState);

    // Initialize auth check if not done yet
    if (!this.initialized) {
      this.initialize();
    }

    return () => {
      this.listeners.delete(listener);
    };
  }

  // Initialize authentication check
  private async initialize(): Promise<void> {
    if (this.initialized) return;
    this.initialized = true;

    await this.checkAuth();
  }

  // Check authentication status
  async checkAuth(force = false): Promise<AuthState> {
    // If we have a pending auth check and not forced, return that promise
    if (this.authPromise && !force) {
      return this.authPromise;
    }

    // Create new auth check promise
    this.authPromise = this.performAuthCheck();

    try {
      const result = await this.authPromise;
      this.authPromise = null;
      return result;
    } catch (error) {
      this.authPromise = null;
      throw error;
    }
  }

  private async performAuthCheck(): Promise<AuthState> {
    try {
      this.updateState({ isLoading: true, error: null });

      const session = await getSession();

      const newState: AuthState = {
        isAuthenticated: !!session,
        user: session?.user || null,
        isLoading: false,
        error: null,
      };

      this.updateState(newState);

      // Process queued requests after auth is resolved
      this.processQueue();

      return newState;
    } catch (error) {
      const errorState: AuthState = {
        isAuthenticated: false,
        user: null,
        isLoading: false,
        error: error instanceof Error ? error.message : "Authentication failed",
      };

      this.updateState(errorState);

      // Reject all queued requests
      this.rejectQueue(error);

      return errorState;
    }
  }

  private updateState(updates: Partial<AuthState>): void {
    this.authState = { ...this.authState, ...updates };

    // Notify all listeners
    this.listeners.forEach((listener) => {
      try {
        listener(this.authState);
      } catch (error) {
        console.error("Error in auth state listener:", error);
      }
    });
  }

  // Queue API request until authentication is resolved
  async queueRequest<T>(fetchFn: () => Promise<T>): Promise<T> {
    // If already authenticated, execute immediately
    if (this.authState.isAuthenticated && !this.authState.isLoading) {
      return fetchFn();
    }

    // If not authenticated and not loading, reject immediately
    if (!this.authState.isLoading && !this.authState.isAuthenticated) {
      throw new Error("Not authenticated");
    }

    // Otherwise, queue the request
    return new Promise<T>((resolve, reject) => {
      this.requestQueue.push({
        resolve,
        reject,
        fetch: fetchFn,
      });
    });
  }

  private async processQueue(): void {
    if (!this.authState.isAuthenticated) {
      return;
    }

    const queue = [...this.requestQueue];
    this.requestQueue = [];

    for (const request of queue) {
      try {
        const result = await request.fetch();
        request.resolve(result);
      } catch (error) {
        request.reject(error);
      }
    }
  }

  private rejectQueue(error: any): void {
    const queue = [...this.requestQueue];
    this.requestQueue = [];

    queue.forEach((request) => {
      request.reject(error);
    });
  }

  // Get current auth state
  getAuthState(): AuthState {
    return { ...this.authState };
  }

  // Force refresh authentication
  async refresh(): Promise<AuthState> {
    return this.checkAuth(true);
  }

  // Clear authentication state
  clearAuth(): void {
    this.updateState({
      isAuthenticated: false,
      user: null,
      isLoading: false,
      error: null,
    });

    // Reject any pending requests
    this.rejectQueue(new Error("Authentication cleared"));
  }
}

export const authManager = AuthenticationManager.getInstance();
