"use client";

import { api } from "@/lib/api-client";
import { Category, ApiResponse, PaginatedResponse } from "@/types";

export interface CategoryFilters {
  parentId?: string;
  isActive?: boolean;
  search?: string;
  sort?: "name" | "order" | "createdAt";
  order?: "asc" | "desc";
}

export interface CategoryListParams extends CategoryFilters {
  page?: number;
  limit?: number;
}

export interface CategoryWithProductCount extends Category {
  productCount: number;
}

export class CategoryService {
  private static readonly ENDPOINT = "/categories";

  // Get paginated categories list
  static async getCategories(
    params: CategoryListParams = {}
  ): Promise<PaginatedResponse<Category>> {
    return api.getList<Category>(this.ENDPOINT, params);
  }

  // Get single category by ID
  static async getCategory(id: string): Promise<ApiResponse<Category>> {
    return api.getById<Category>(this.ENDPOINT, id);
  }

  // Get category by slug
  static async getCategoryBySlug(slug: string): Promise<ApiResponse<Category>> {
    return api.getById<Category>(this.ENDPOINT, slug);
  }

  // Get root categories (no parent)
  static async getRootCategories(): Promise<PaginatedResponse<Category>> {
    return api.getList<Category>(this.ENDPOINT, {
      filter: { parentId: null, isActive: true },
      sort: "order",
    });
  }

  // Get child categories
  static async getChildCategories(
    parentId: string
  ): Promise<PaginatedResponse<Category>> {
    return api.getList<Category>(this.ENDPOINT, {
      filter: { parentId, isActive: true },
      sort: "order",
    });
  }

  // Get category tree (hierarchical structure)
  static async getCategoryTree(): Promise<ApiResponse<Category[]>> {
    return api.getById<Category[]>(`${this.ENDPOINT}/tree`, "");
  }

  // Get categories with product counts
  static async getCategoriesWithCounts(): Promise<
    ApiResponse<CategoryWithProductCount[]>
  > {
    return api.getById<CategoryWithProductCount[]>(
      `${this.ENDPOINT}/with-counts`,
      ""
    );
  }

  // Get featured categories for homepage
  static async getFeaturedCategories(
    limit: number = 6
  ): Promise<PaginatedResponse<CategoryWithProductCount>> {
    return api.getList<CategoryWithProductCount>(`${this.ENDPOINT}/featured`, {
      limit,
      sort: "order",
    });
  }

  // Search categories
  static async searchCategories(
    query: string,
    filters: CategoryFilters = {}
  ): Promise<PaginatedResponse<Category>> {
    return api.getList<Category>(this.ENDPOINT, {
      search: query,
      ...filters,
    });
  }

  // Get category breadcrumb
  static async getCategoryBreadcrumb(
    categoryId: string
  ): Promise<ApiResponse<Category[]>> {
    return api.getById<Category[]>(
      `${this.ENDPOINT}/${categoryId}/breadcrumb`,
      ""
    );
  }

  // Admin methods
  static async createCategory(
    data: Partial<Category>
  ): Promise<ApiResponse<Category>> {
    return api.create<Category>(this.ENDPOINT, data);
  }

  static async updateCategory(
    id: string,
    data: Partial<Category>
  ): Promise<ApiResponse<Category>> {
    return api.update<Category>(this.ENDPOINT, id, data);
  }

  static async deleteCategory(id: string): Promise<ApiResponse<void>> {
    return api.remove<void>(this.ENDPOINT, id);
  }

  // Utility methods
  static buildCategoryTree(categories: Category[]): Category[] {
    const categoryMap = new Map<string, Category & { children: Category[] }>();
    const rootCategories: (Category & { children: Category[] })[] = [];

    // First pass: create map and initialize children arrays
    categories.forEach((category) => {
      categoryMap.set(category.id, { ...category, children: [] });
    });

    // Second pass: build tree structure
    categories.forEach((category) => {
      const categoryWithChildren = categoryMap.get(category.id)!;

      if ((category as any).parentId) {
        const parent = categoryMap.get((category as any).parentId);
        if (parent) {
          parent.children.push(categoryWithChildren);
        }
      } else {
        rootCategories.push(categoryWithChildren);
      }
    });

    // Sort by order
    const sortByOrder = (cats: (Category & { children: Category[] })[]) => {
      cats.sort((a, b) => ((a as any).order || 0) - ((b as any).order || 0));
      cats.forEach((cat) => {
        if (cat.children.length > 0) {
          sortByOrder(cat.children as any);
        }
      });
    };

    sortByOrder(rootCategories);
    return rootCategories;
  }

  static getCategoryPath(
    category: Category,
    allCategories: Category[]
  ): Category[] {
    const path: Category[] = [category];
    let current = category;

    while (current.parentId) {
      const parent = allCategories.find((c) => c.id === current.parentId);
      if (parent) {
        path.unshift(parent);
        current = parent;
      } else {
        break;
      }
    }

    return path;
  }

  static getCategoryDepth(
    category: Category,
    allCategories: Category[]
  ): number {
    let depth = 0;
    let current = category;

    while (current.parentId) {
      const parent = allCategories.find((c) => c.id === current.parentId);
      if (parent) {
        depth++;
        current = parent;
      } else {
        break;
      }
    }

    return depth;
  }

  static getDescendantIds(
    categoryId: string,
    allCategories: Category[]
  ): string[] {
    const descendants: string[] = [];
    const children = allCategories.filter((c) => c.parentId === categoryId);

    children.forEach((child) => {
      descendants.push(child.id);
      descendants.push(...this.getDescendantIds(child.id, allCategories));
    });

    return descendants;
  }

  static getCategoryImage(category: Category): string | null {
    return (category as any).image?.url || category.image || null;
  }

  static isCategoryActive(category: Category): boolean {
    return (category as any).isActive ?? true;
  }

  static hasChildren(categoryId: string, allCategories: Category[]): boolean {
    return allCategories.some((c) => c.parentId === categoryId);
  }
}

export default CategoryService;
