// Export all services
export { default as ProductService } from "./product-service";
export { default as CategoryService } from "./category-service";
export { default as CartService } from "./cart-service";
export { default as UserService } from "./user-service";
export { default as OrderService } from "./order-service";

// Export service types
export type {
  ProductFilters,
  ProductListParams,
} from "./product-service";

export type {
  CategoryFilters,
  CategoryListParams,
  CategoryWithProductCount,
} from "./category-service";

export type {
  AddToCartData,
  UpdateCartItemData,
  CartSummary,
} from "./cart-service";

export type {
  UserProfileData,
  AddressData,
  ChangePasswordData,
} from "./user-service";

export type {
  CreateOrderData,
  OrderFilters,
  OrderListParams,
  OrderStats,
} from "./order-service";

// Re-export API client
export { api, apiClient, ApiError } from "../api-client";
export type { RequestConfig, CacheConfig } from "../api-client";
