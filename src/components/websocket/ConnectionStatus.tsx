/**
 * WebSocket Connection Status Component
 * Shows real-time connection status with reconnection controls
 */

'use client';

import React, { useState } from 'react';
import { useWebSocketStatus } from '@/hooks/useWebSocket';

interface ConnectionStatusProps {
  token?: string;
  className?: string;
  showDetails?: boolean;
  showControls?: boolean;
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
}

const ConnectionStatus: React.FC<ConnectionStatusProps> = ({
  token,
  className = '',
  showDetails = false,
  showControls = false,
  position = 'bottom-right'
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const status = useWebSocketStatus({
    token,
    autoConnect: !!token
  });

  const getStatusColor = () => {
    if (status.connected) return 'bg-green-500';
    if (status.connecting) return 'bg-yellow-500';
    if (status.error) return 'bg-red-500';
    return 'bg-gray-500';
  };

  const getStatusText = () => {
    if (status.connected) return 'Connected';
    if (status.connecting) return 'Connecting...';
    if (status.error) return 'Disconnected';
    return 'Offline';
  };

  const getPositionClasses = () => {
    switch (position) {
      case 'top-left':
        return 'top-4 left-4';
      case 'top-right':
        return 'top-4 right-4';
      case 'bottom-left':
        return 'bottom-4 left-4';
      case 'bottom-right':
      default:
        return 'bottom-4 right-4';
    }
  };

  const handleToggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <div className={`fixed ${getPositionClasses()} z-40 ${className}`}>
      {/* Compact Status Indicator */}
      <div
        className={`
          flex items-center space-x-2 bg-white rounded-lg shadow-lg border px-3 py-2 cursor-pointer
          transition-all duration-200 hover:shadow-xl
          ${isExpanded ? 'rounded-b-none' : ''}
        `}
        onClick={handleToggleExpanded}
      >
        {/* Status Dot */}
        <div className={`w-3 h-3 rounded-full ${getStatusColor()} animate-pulse`}></div>
        
        {/* Status Text */}
        <span className="text-sm font-medium text-gray-700">
          {getStatusText()}
        </span>

        {/* Expand Arrow */}
        {(showDetails || showControls) && (
          <svg
            className={`w-4 h-4 text-gray-400 transition-transform ${isExpanded ? 'rotate-180' : ''}`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        )}
      </div>

      {/* Expanded Details */}
      {isExpanded && (showDetails || showControls) && (
        <div className="bg-white border border-t-0 rounded-b-lg shadow-lg p-4 min-w-64">
          {/* Connection Details */}
          {showDetails && (
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Status:</span>
                <span className={`font-medium ${status.connected ? 'text-green-600' : status.error ? 'text-red-600' : 'text-yellow-600'}`}>
                  {getStatusText()}
                </span>
              </div>
              
              {status.reconnectAttempts > 0 && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Reconnect attempts:</span>
                  <span className="font-medium text-orange-600">{status.reconnectAttempts}</span>
                </div>
              )}
              
              {status.error && (
                <div className="mt-2">
                  <span className="text-gray-600 block mb-1">Error:</span>
                  <span className="text-red-600 text-xs bg-red-50 p-2 rounded block">
                    {status.error}
                  </span>
                </div>
              )}
              
              <div className="flex justify-between">
                <span className="text-gray-600">Last updated:</span>
                <span className="text-gray-500 text-xs">
                  {new Date().toLocaleTimeString()}
                </span>
              </div>
            </div>
          )}

          {/* Connection Controls */}
          {showControls && (
            <div className="mt-4 pt-4 border-t space-y-2">
              <button
                className="w-full px-3 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors text-sm"
                onClick={() => window.location.reload()}
              >
                Reconnect
              </button>
              
              <button
                className="w-full px-3 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors text-sm"
                onClick={() => setIsExpanded(false)}
              >
                Close
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ConnectionStatus;

/**
 * Simple status badge component
 */
export const ConnectionBadge: React.FC<{ token?: string; className?: string }> = ({
  token,
  className = ''
}) => {
  const status = useWebSocketStatus({ token, autoConnect: !!token });

  const getStatusColor = () => {
    if (status.connected) return 'bg-green-500';
    if (status.connecting) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  return (
    <div className={`inline-flex items-center space-x-2 ${className}`}>
      <div className={`w-2 h-2 rounded-full ${getStatusColor()}`}></div>
      <span className="text-xs text-gray-600">
        {status.connected ? 'Live' : status.connecting ? 'Connecting' : 'Offline'}
      </span>
    </div>
  );
};

/**
 * Notification count badge
 */
export const NotificationBadge: React.FC<{ 
  count: number; 
  className?: string;
  onClick?: () => void;
}> = ({ count, className = '', onClick }) => {
  if (count === 0) return null;

  return (
    <button
      onClick={onClick}
      className={`
        relative inline-flex items-center justify-center w-6 h-6 text-xs font-bold text-white 
        bg-red-500 rounded-full hover:bg-red-600 transition-colors
        ${className}
      `}
    >
      {count > 99 ? '99+' : count}
      
      {/* Pulse animation for new notifications */}
      <div className="absolute inset-0 rounded-full bg-red-500 animate-ping opacity-75"></div>
    </button>
  );
};
