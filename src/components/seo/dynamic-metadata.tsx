"use client";

import { useSettings } from "@/contexts";
import { useEffect } from "react";

interface DynamicMetadataProps {
  title?: string;
  description?: string;
  keywords?: string[];
  image?: string;
  url?: string;
  type?: "website" | "article" | "product";
}

export function DynamicMetadata({
  title,
  description,
  keywords = [],
  image,
  url,
  type = "website",
}: DynamicMetadataProps) {
  const { settings } = useSettings();

  useEffect(() => {
    // Update document title
    if (title) {
      document.title = `${title} | ${settings.siteInfo.name}`;
    }

    // Update meta description
    if (description) {
      const metaDescription = document.querySelector(
        'meta[name="description"]'
      );
      if (metaDescription) {
        metaDescription.setAttribute("content", description);
      }
    }

    // Update meta keywords
    if (keywords.length > 0) {
      const metaKeywords = document.querySelector('meta[name="keywords"]');
      if (metaKeywords) {
        metaKeywords.setAttribute("content", keywords.join(", "));
      }
    }

    // Update Open Graph tags
    const ogTitle = document.querySelector('meta[property="og:title"]');
    if (ogTitle && title) {
      ogTitle.setAttribute("content", title);
    }

    const ogDescription = document.querySelector(
      'meta[property="og:description"]'
    );
    if (ogDescription && description) {
      ogDescription.setAttribute("content", description);
    }

    const ogImage = document.querySelector('meta[property="og:image"]');
    if (ogImage && image) {
      ogImage.setAttribute("content", image);
    }

    const ogUrl = document.querySelector('meta[property="og:url"]');
    if (ogUrl && url) {
      ogUrl.setAttribute("content", url);
    }

    const ogType = document.querySelector('meta[property="og:type"]');
    if (ogType) {
      ogType.setAttribute("content", type);
    }

    // Update Twitter Card tags
    const twitterTitle = document.querySelector('meta[name="twitter:title"]');
    if (twitterTitle && title) {
      twitterTitle.setAttribute("content", title);
    }

    const twitterDescription = document.querySelector(
      'meta[name="twitter:description"]'
    );
    if (twitterDescription && description) {
      twitterDescription.setAttribute("content", description);
    }

    const twitterImage = document.querySelector('meta[name="twitter:image"]');
    if (twitterImage && image) {
      twitterImage.setAttribute("content", image);
    }

    // Update canonical URL
    const canonical = document.querySelector('link[rel="canonical"]');
    if (canonical && url) {
      canonical.setAttribute("href", url);
    }

    // Add structured data for products
    if (type === "product") {
      const structuredData = {
        "@context": "https://schema.org",
        "@type": "Product",
        name: title,
        description: description,
        image: image,
        url: url,
        brand: {
          "@type": "Brand",
          name: settings.siteInfo.name,
        },
        offers: {
          "@type": "Offer",
          availability: "https://schema.org/InStock",
          priceCurrency: "VND",
        },
      };

      // Remove existing product structured data
      const existingScript = document.querySelector(
        'script[type="application/ld+json"][data-type="product"]'
      );
      if (existingScript) {
        existingScript.remove();
      }

      // Add new structured data
      const script = document.createElement("script");
      script.type = "application/ld+json";
      script.setAttribute("data-type", "product");
      script.textContent = JSON.stringify(structuredData);
      document.head.appendChild(script);
    }
  }, [title, description, keywords, image, url, type, settings]);

  return null; // This component doesn't render anything
}

// Hook for easy metadata updates
export function useMetadata() {
  const { settings } = useSettings();

  const updateMetadata = (metadata: DynamicMetadataProps) => {
    // This would trigger the DynamicMetadata component to update
    // In a real implementation, you might use a context or state management
    console.log("Updating metadata:", metadata);
  };

  const getDefaultMetadata = () => ({
    siteName: settings.siteInfo.name,
    siteDescription: settings.siteInfo.description,
    siteUrl: settings.siteInfo.url,
    contactEmail: settings.contactInfo.email,
    contactPhone: settings.contactInfo.phone,
    socialMedia: settings.socialMedia,
  });

  return {
    updateMetadata,
    getDefaultMetadata,
    settings,
  };
}
