"use client";

import { motion } from "framer-motion";
import { useState } from "react";
import { ChevronDown, ChevronUp, HelpCircle, Phone, Mail, MapPin, Clock } from "lucide-react";

export function AboutFAQSection() {
  const [openIndex, setOpenIndex] = useState<number | null>(0);

  const faqs = [
    {
      question: "Phí vận chuyển thế nào?",
      answer: "Sau khi bạn đặt hàng tại xưởng may NS Shop, bạn sẽ được báo giá tổng phí ship. Tổng tiền phí ship sẽ phụ thuộc vào vị trí và hình thức nhận hàng của khách. Chúng tôi hỗ trợ giao hàng toàn quốc với nhiều hình thức vận chuyển khác nhau để phù hợp với nhu cầu của khách hàng."
    },
    {
      question: "<PERSON>ách đặt hàng may gia công đơn giản nhất tại NS Shop?",
      answer: "<PERSON><PERSON> rất nhiều cách đặt hàng may gia công tại NS Shop:\n• Đặt trực tiếp tại website\n• Qua email: <EMAIL>\n• Qua Fanpage: NS Shop\n• Số điện thoại hotline: 0796 597 878\n\nChúng tôi luôn sẵn sàng hỗ trợ khách hàng 24/7 qua các kênh liên lạc trên."
    },
    {
      question: "Tôi ở xa, làm sao tôi tin tưởng khi chuyển tiền rồi mới nhận hàng?",
      answer: "Xưởng may NS Shop có địa chỉ rõ ràng tại TP. Hồ Chí Minh và được công khai tại website https://nsshop.com/. Xưởng may gia công hàng thiết kế số lượng ít NS Shop cam kết hoàn thành nhanh chóng và giao hàng như thỏa thuận với khách hàng. Chúng tôi có hợp đồng rõ ràng và chính sách bảo hành cho từng đơn hàng."
    },
    {
      question: "Sản xuất 1 đơn hàng số lượng ít trong vòng bao lâu?",
      answer: "Thời gian sản xuất hàng từ 7 – 10 ngày sau khi duyệt mẫu xong. Đối với những đơn hàng phức tạp hoặc số lượng lớn, thời gian có thể kéo dài hơn và chúng tôi sẽ thông báo cụ thể cho khách hàng ngay từ đầu."
    },
    {
      question: "NS Shop có cung cấp mẫu thiết kế của chúng tôi cho các khách hàng không?",
      answer: "Không. 100% mẫu thiết kế của khách hàng mang đến NS Shop đều được bảo vệ bản quyền. Chúng tôi sẽ không cung cấp mẫu của khách hàng với bất kỳ đối tác nào khác. Đây là cam kết nghiêm túc và là một trong những giá trị cốt lõi của NS Shop."
    },
    {
      question: "Số lượng đặt hàng tối thiểu là bao nhiêu?",
      answer: "NS Shop chuyên nhận đặt may số lượng ít từ 30 sản phẩm/mẫu. Đây là một trong những ưu điểm lớn của chúng tôi so với các xưởng may khác, giúp các thương hiệu nhỏ và khởi nghiệp có thể dễ dàng sản xuất sản phẩm của mình."
    },
    {
      question: "NS Shop có hỗ trợ thiết kế không?",
      answer: "Có, chúng tôi có đội ngũ thiết kế chuyên nghiệp sẵn sàng hỗ trợ khách hàng từ khâu ý tưởng ban đầu đến sản phẩm hoàn thiện. Chúng tôi cũng có thể góp ý và hoàn thiện thiết kế dựa trên kinh nghiệm 10 năm trong ngành."
    },
    {
      question: "Chính sách thanh toán như thế nào?",
      answer: "NS Shop hỗ trợ nhiều hình thức thanh toán linh hoạt:\n• Thanh toán trước 50% khi ký hợp đồng\n• Thanh toán 50% còn lại khi giao hàng\n• Chuyển khoản ngân hàng\n• Thanh toán tiền mặt khi nhận hàng (với khách hàng tại TP.HCM)\n• Thanh toán qua ví điện tử"
    }
  ];

  const contactMethods = [
    {
      icon: Phone,
      title: "Hotline",
      content: "0796 59 78 78",
      description: "Hỗ trợ 24/7",
      color: "green"
    },
    {
      icon: Mail,
      title: "Email",
      content: "<EMAIL>",
      description: "Phản hồi trong 2h",
      color: "blue"
    },
    {
      icon: MapPin,
      title: "Địa chỉ",
      content: "TP. Hồ Chí Minh",
      description: "Ghé thăm showroom",
      color: "purple"
    },
    {
      icon: Clock,
      title: "Giờ làm việc",
      content: "8:00 - 18:00",
      description: "Thứ 2 - Chủ nhật",
      color: "orange"
    }
  ];

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <section id="cau-hoi-thuong-gap" className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            CÂU HỎI THƯỜNG GẶP
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-pink-500 to-purple-600 mx-auto mb-8"></div>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            NS Shop biết bạn có rất nhiều băn khoăn. Bạn thật sự muốn tìm cho mình 1 đơn vị để hợp tác lâu dài. 
            Hi vọng những câu hỏi dưới đây có thể giúp bạn hiểu hơn về NS Shop.
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-3 gap-12">
          {/* FAQ List */}
          <div className="lg:col-span-2">
            <div className="space-y-4">
              {faqs.map((faq, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100"
                >
                  <button
                    onClick={() => toggleFAQ(index)}
                    className="w-full px-6 py-6 text-left flex items-center justify-between hover:bg-gray-50 transition-colors duration-200"
                  >
                    <div className="flex items-center gap-4">
                      <div className="flex-shrink-0 w-10 h-10 bg-pink-100 rounded-full flex items-center justify-center">
                        <HelpCircle className="h-5 w-5 text-pink-600" />
                      </div>
                      <h3 className="text-lg font-semibold text-gray-900 pr-4">
                        {faq.question}
                      </h3>
                    </div>
                    <div className="flex-shrink-0">
                      {openIndex === index ? (
                        <ChevronUp className="h-5 w-5 text-gray-500" />
                      ) : (
                        <ChevronDown className="h-5 w-5 text-gray-500" />
                      )}
                    </div>
                  </button>
                  
                  <motion.div
                    initial={false}
                    animate={{
                      height: openIndex === index ? "auto" : 0,
                      opacity: openIndex === index ? 1 : 0
                    }}
                    transition={{ duration: 0.3, ease: "easeInOut" }}
                    className="overflow-hidden"
                  >
                    <div className="px-6 pb-6">
                      <div className="pl-14">
                        <p className="text-gray-600 leading-relaxed whitespace-pre-line">
                          {faq.answer}
                        </p>
                      </div>
                    </div>
                  </motion.div>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Contact Info Sidebar */}
          <div className="lg:col-span-1">
            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="bg-white rounded-3xl p-8 shadow-lg border border-gray-100 sticky top-8"
            >
              <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">
                Cần hỗ trợ thêm?
              </h3>
              
              <div className="space-y-6">
                {contactMethods.map((method, index) => {
                  const Icon = method.icon;
                  return (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      viewport={{ once: true }}
                      className="flex items-center gap-4 p-4 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors duration-200"
                    >
                      <div className={`flex-shrink-0 w-12 h-12 bg-${method.color}-100 rounded-full flex items-center justify-center`}>
                        <Icon className={`h-6 w-6 text-${method.color}-600`} />
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900">{method.title}</h4>
                        <p className="text-gray-700 font-medium">{method.content}</p>
                        <p className="text-sm text-gray-500">{method.description}</p>
                      </div>
                    </motion.div>
                  );
                })}
              </div>

              {/* Quick Contact Form */}
              <div className="mt-8 p-6 bg-gradient-to-br from-pink-50 to-purple-50 rounded-2xl">
                <h4 className="font-bold text-gray-900 mb-4 text-center">
                  Gửi câu hỏi nhanh
                </h4>
                <form className="space-y-4">
                  <input
                    type="text"
                    placeholder="Họ tên của bạn"
                    className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-pink-300 focus:outline-none focus:ring-2 focus:ring-pink-100"
                  />
                  <input
                    type="email"
                    placeholder="Email của bạn"
                    className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-pink-300 focus:outline-none focus:ring-2 focus:ring-pink-100"
                  />
                  <textarea
                    placeholder="Câu hỏi của bạn..."
                    rows={3}
                    className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-pink-300 focus:outline-none focus:ring-2 focus:ring-pink-100 resize-none"
                  ></textarea>
                  <button
                    type="submit"
                    className="w-full bg-gradient-to-r from-pink-500 to-purple-600 text-white py-3 rounded-xl font-semibold hover:from-pink-600 hover:to-purple-700 transition-all duration-300"
                  >
                    Gửi câu hỏi
                  </button>
                </form>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  );
}
