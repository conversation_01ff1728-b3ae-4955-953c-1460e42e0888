"use client";

import { motion } from "framer-motion";
import { Check, Star, Zap, Crown, Shield } from "lucide-react";
import { Button } from "@/components/ui/button";

export function AboutPricingSection() {
  const pricingPlans = [
    {
      name: "<PERSON><PERSON><PERSON>",
      icon: Star,
      price: "<PERSON>ên hệ",
      originalPrice: null,
      description: "Phù hợp cho các shop nhỏ và khởi nghiệp",
      minQuantity: "30-100 sản phẩm",
      features: [
        "Tư vấn thiết kế cơ bản",
        "May mẫu thử miễn phí",
        "Chọn vải từ kho có sẵn",
        "Thời gian sản xuất: 10-15 ngày",
        "Bảo hành 30 ngày",
        "Hỗ trợ qua email"
      ],
      color: "blue",
      popular: false
    },
    {
      name: "<PERSON><PERSON><PERSON> Ti<PERSON>u <PERSON>",
      icon: Zap,
      price: "<PERSON>ên hệ",
      originalPrice: null,
      description: "Lựa chọn phổ biến cho các thương hiệu vừa",
      minQuantity: "100-500 sản phẩm",
      features: [
        "Tư vấn thiết kế chuyên sâu",
        "May 2 mẫu thử miễn phí",
        "Tự do chọn vải theo yêu cầu",
        "Thời gian sản xuất: 7-10 ngày",
        "Bảo hành 60 ngày",
        "Hỗ trợ qua điện thoại",
        "Theo dõi tiến độ online",
        "Giảm 5% cho đơn hàng tiếp theo"
      ],
      color: "pink",
      popular: true
    },
    {
      name: "Gói Cao Cấp",
      icon: Crown,
      price: "Liên hệ",
      originalPrice: null,
      description: "Dành cho các thương hiệu lớn và yêu cầu cao",
      minQuantity: "500+ sản phẩm",
      features: [
        "Tư vấn thiết kế độc quyền",
        "May 3 mẫu thử miễn phí",
        "Nhập vải cao cấp theo yêu cầu",
        "Thời gian sản xuất: 5-7 ngày",
        "Bảo hành 90 ngày",
        "Hỗ trợ 24/7",
        "Quản lý dự án riêng",
        "Giảm 10% cho đơn hàng tiếp theo",
        "Ưu tiên sản xuất",
        "Giao hàng miễn phí toàn quốc"
      ],
      color: "purple",
      popular: false
    }
  ];

  const additionalServices = [
    {
      name: "Thiết kế logo & nhãn mác",
      price: "500.000đ - 2.000.000đ",
      description: "Thiết kế nhận diện thương hiệu chuyên nghiệp"
    },
    {
      name: "Chụp ảnh sản phẩm",
      price: "200.000đ - 500.000đ/sản phẩm",
      description: "Chụp ảnh chuyên nghiệp cho catalog và website"
    },
    {
      name: "In thêu logo",
      price: "10.000đ - 50.000đ/sản phẩm",
      description: "In và thêu logo theo yêu cầu khách hàng"
    },
    {
      name: "Đóng gói cao cấp",
      price: "20.000đ - 100.000đ/sản phẩm",
      description: "Đóng gói sản phẩm với hộp và túi branded"
    }
  ];

  return (
    <section id="bang-gia" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            BẢNG GIÁ
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-pink-500 to-purple-600 mx-auto mb-8"></div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Minh bạch với khách hàng trong mọi phương diện chính là cách giúp NS Shop luôn phát triển vững chắc.
          </p>
        </motion.div>

        {/* Pricing Cards */}
        <div className="grid md:grid-cols-3 gap-8 mb-16">
          {pricingPlans.map((plan, index) => {
            const Icon = plan.icon;
            return (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                viewport={{ once: true }}
                className={`relative bg-white rounded-3xl p-8 shadow-lg border-2 transition-all duration-300 hover:shadow-xl ${
                  plan.popular 
                    ? "border-pink-300 transform scale-105" 
                    : "border-gray-200 hover:border-pink-200"
                }`}
              >
                {/* Popular Badge */}
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <div className="bg-gradient-to-r from-pink-500 to-purple-600 text-white px-6 py-2 rounded-full text-sm font-semibold">
                      Phổ biến nhất
                    </div>
                  </div>
                )}

                {/* Plan Header */}
                <div className="text-center mb-8">
                  <div className={`inline-flex items-center justify-center w-16 h-16 bg-${plan.color}-100 rounded-2xl mb-4`}>
                    <Icon className={`h-8 w-8 text-${plan.color}-600`} />
                  </div>
                  
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">
                    {plan.name}
                  </h3>
                  
                  <p className="text-gray-600 mb-4">
                    {plan.description}
                  </p>

                  <div className="mb-4">
                    <span className="text-4xl font-bold text-gray-900">
                      {plan.price}
                    </span>
                    {plan.originalPrice && (
                      <span className="text-lg text-gray-500 line-through ml-2">
                        {plan.originalPrice}
                      </span>
                    )}
                  </div>

                  <div className={`inline-block px-4 py-2 bg-${plan.color}-50 text-${plan.color}-700 rounded-full text-sm font-medium`}>
                    {plan.minQuantity}
                  </div>
                </div>

                {/* Features List */}
                <div className="space-y-4 mb-8">
                  {plan.features.map((feature, featureIndex) => (
                    <div key={featureIndex} className="flex items-start gap-3">
                      <div className={`flex-shrink-0 w-5 h-5 bg-${plan.color}-100 rounded-full flex items-center justify-center mt-0.5`}>
                        <Check className={`h-3 w-3 text-${plan.color}-600`} />
                      </div>
                      <span className="text-gray-600 text-sm">{feature}</span>
                    </div>
                  ))}
                </div>

                {/* CTA Button */}
                <Button
                  className={`w-full py-3 rounded-xl font-semibold transition-all duration-300 ${
                    plan.popular
                      ? "bg-gradient-to-r from-pink-500 to-purple-600 text-white hover:from-pink-600 hover:to-purple-700"
                      : `border-2 border-${plan.color}-300 text-${plan.color}-600 hover:bg-${plan.color}-50`
                  }`}
                  variant={plan.popular ? "default" : "outline"}
                >
                  Liên hệ báo giá
                </Button>
              </motion.div>
            );
          })}
        </div>

        {/* Additional Services */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="bg-gradient-to-br from-gray-50 to-white rounded-3xl p-8 md:p-12"
        >
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">
              DỊCH VỤ BỔ SUNG
            </h3>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Ngoài dịch vụ may gia công, NS Shop còn cung cấp các dịch vụ bổ sung 
              để hỗ trợ toàn diện cho thương hiệu của bạn.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-6">
            {additionalServices.map((service, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: index % 2 === 0 ? -30 : 30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300"
              >
                <div className="flex items-start justify-between mb-4">
                  <h4 className="text-xl font-bold text-gray-900">
                    {service.name}
                  </h4>
                  <div className="text-right">
                    <span className="text-lg font-bold text-pink-600">
                      {service.price}
                    </span>
                  </div>
                </div>
                <p className="text-gray-600 text-sm">
                  {service.description}
                </p>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Pricing Notes */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="mt-16 bg-blue-50 rounded-3xl p-8 border border-blue-100"
        >
          <div className="flex items-start gap-4">
            <div className="flex-shrink-0 w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
              <Shield className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h4 className="text-xl font-bold text-gray-900 mb-4">
                Lưu ý về giá cả
              </h4>
              <div className="space-y-2 text-gray-600">
                <p>• Giá cả có thể thay đổi tùy thuộc vào độ phức tạp của thiết kế và chất liệu sử dụng</p>
                <p>• Báo giá chi tiết sẽ được cung cấp sau khi tư vấn cụ thể về yêu cầu</p>
                <p>• Khách hàng lâu dài sẽ được hưởng chính sách giá ưu đãi đặc biệt</p>
                <p>• Tất cả giá đã bao gồm VAT, không phát sinh chi phí ẩn</p>
                <p>• Hỗ trợ thanh toán linh hoạt theo từng giai đoạn sản xuất</p>
              </div>
            </div>
          </div>
        </motion.div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="mt-16 text-center"
        >
          <div className="bg-gradient-to-r from-pink-500 to-purple-600 rounded-3xl p-8 md:p-12 text-white">
            <h3 className="text-3xl font-bold mb-4">
              Cần tư vấn chi tiết về giá cả?
            </h3>
            <p className="text-pink-100 mb-8 max-w-2xl mx-auto">
              Liên hệ với chúng tôi ngay để nhận báo giá chi tiết và tư vấn miễn phí 
              về dự án của bạn. Đội ngũ chuyên gia sẽ hỗ trợ bạn 24/7.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                size="lg"
                variant="secondary"
                className="bg-white text-pink-600 hover:bg-gray-100 px-8 py-3 rounded-full font-semibold"
              >
                Nhận báo giá miễn phí
              </Button>
              
              <Button
                size="lg"
                variant="outline"
                className="border-white text-white hover:bg-white hover:text-pink-600 px-8 py-3 rounded-full font-semibold"
              >
                Gọi ngay: 0796 59 78 78
              </Button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
