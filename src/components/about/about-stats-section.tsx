"use client";

import { motion } from "framer-motion";
import { useEffect, useState } from "react";
import { Calendar, Users, Palette, Package } from "lucide-react";

export function AboutStatsSection() {
  const [isVisible, setIsVisible] = useState(false);

  const stats = [
    {
      icon: Calendar,
      number: 10,
      suffix: "+",
      label: "NĂM KINH NGHIỆM",
      description: "Hơn 10 năm hoạt động trong ngành may mặc",
      color: "pink"
    },
    {
      icon: Users,
      number: 50,
      suffix: "+",
      label: "TỔNG NHÂN SỰ",
      description: "<PERSON><PERSON><PERSON> ngũ thợ may và nhân viên chuyên nghiệp",
      color: "blue"
    },
    {
      icon: Palette,
      number: 1000,
      suffix: "+",
      label: "MẪU THIẾT KẾ",
      description: "Số lượng mẫu thiết kế đã thực hiện thành công",
      color: "purple"
    },
    {
      icon: Package,
      number: 50000,
      suffix: "+",
      label: "SẢN PHẨM ĐÃ SẢN XUẤT",
      description: "Tổng số sản phẩm đã giao đến tay kh<PERSON>ch hàng",
      color: "green"
    }
  ];

  // Counter animation hook
  const useCounter = (end: number, duration: number = 2000) => {
    const [count, setCount] = useState(0);

    useEffect(() => {
      if (!isVisible) return;

      let startTime: number;
      let animationFrame: number;

      const animate = (timestamp: number) => {
        if (!startTime) startTime = timestamp;
        const progress = Math.min((timestamp - startTime) / duration, 1);
        
        setCount(Math.floor(progress * end));

        if (progress < 1) {
          animationFrame = requestAnimationFrame(animate);
        }
      };

      animationFrame = requestAnimationFrame(animate);

      return () => {
        if (animationFrame) {
          cancelAnimationFrame(animationFrame);
        }
      };
    }, [end, duration, isVisible]);

    return count;
  };

  return (
    <section className="py-20 bg-gradient-to-br from-gray-900 via-purple-900 to-pink-900 text-white relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0 bg-[url('/patterns/geometric-pattern.svg')] bg-repeat"></div>
      </div>

      {/* Floating Elements */}
      <div className="absolute top-20 left-10 w-32 h-32 bg-pink-500/20 rounded-full blur-xl"></div>
      <div className="absolute bottom-20 right-10 w-40 h-40 bg-purple-500/20 rounded-full blur-xl"></div>
      <div className="absolute top-1/2 left-1/4 w-24 h-24 bg-blue-500/20 rounded-full blur-xl"></div>

      <div className="container mx-auto px-4 relative z-10">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          onViewportEnter={() => setIsVisible(true)}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            NHỮNG CON SỐ BIẾT NÓI
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-pink-400 to-purple-400 mx-auto mb-8"></div>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Những con số này là minh chứng cho sự tin tưởng và hài lòng của khách hàng 
            đối với chất lượng dịch vụ của NS Shop.
          </p>
        </motion.div>

        {/* Stats Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {stats.map((stat, index) => {
            const Icon = stat.icon;
            const count = useCounter(stat.number, 2500);
            
            return (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 50, scale: 0.8 }}
                whileInView={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                viewport={{ once: true }}
                className="text-center group"
              >
                <div className="relative">
                  {/* Background Glow */}
                  <div className={`absolute inset-0 bg-${stat.color}-500/20 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-500`}></div>
                  
                  {/* Card Content */}
                  <div className="relative bg-white/10 backdrop-blur-sm rounded-3xl p-8 border border-white/20 hover:border-white/40 transition-all duration-500 group-hover:transform group-hover:scale-105">
                    {/* Icon */}
                    <div className={`inline-flex items-center justify-center w-20 h-20 bg-${stat.color}-500/20 rounded-2xl mb-6 group-hover:scale-110 transition-transform duration-300`}>
                      <Icon className={`h-10 w-10 text-${stat.color}-400`} />
                    </div>
                    
                    {/* Number */}
                    <div className="mb-4">
                      <span className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                        {isVisible ? count.toLocaleString() : 0}
                      </span>
                      <span className={`text-3xl font-bold text-${stat.color}-400 ml-1`}>
                        {stat.suffix}
                      </span>
                    </div>
                    
                    {/* Label */}
                    <h3 className="text-lg font-bold text-white mb-3 tracking-wide">
                      {stat.label}
                    </h3>
                    
                    {/* Description */}
                    <p className="text-gray-300 text-sm leading-relaxed">
                      {stat.description}
                    </p>
                  </div>
                </div>
              </motion.div>
            );
          })}
        </div>

        {/* Additional Info */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.5 }}
          viewport={{ once: true }}
          className="mt-16 text-center"
        >
          <div className="bg-white/5 backdrop-blur-sm rounded-3xl p-8 border border-white/10">
            <h3 className="text-2xl font-bold text-white mb-4">
              Cam kết chất lượng từ những con số thực tế
            </h3>
            <p className="text-gray-300 max-w-3xl mx-auto leading-relaxed">
              Mỗi con số đều phản ánh sự nỗ lực không ngừng của NS Shop trong việc mang đến 
              những sản phẩm chất lượng cao và dịch vụ tốt nhất cho khách hàng. 
              Chúng tôi tự hào về những thành tựu đã đạt được và cam kết tiếp tục phát triển.
            </p>
          </div>
        </motion.div>

        {/* Achievement Badges */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.7 }}
          viewport={{ once: true }}
          className="mt-12 flex flex-wrap justify-center gap-6"
        >
          {[
            { label: "Uy tín", icon: "🏆" },
            { label: "Chất lượng", icon: "⭐" },
            { label: "Nhanh chóng", icon: "⚡" },
            { label: "Chuyên nghiệp", icon: "💼" }
          ].map((badge, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.8 + index * 0.1 }}
              viewport={{ once: true }}
              className="flex items-center gap-2 bg-white/10 backdrop-blur-sm rounded-full px-6 py-3 border border-white/20"
            >
              <span className="text-2xl">{badge.icon}</span>
              <span className="text-white font-medium">{badge.label}</span>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
}
