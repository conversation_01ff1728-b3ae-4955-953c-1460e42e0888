"use client";

import { motion } from "framer-motion";
import { Target, Eye, Heart } from "lucide-react";

export function AboutIntroSection() {
  const values = [
    {
      icon: Target,
      title: "S<PERSON> mệnh",
      description: "Chúng tôi tự hào là đối tác tin cậy của các thương hiệu thời trang thiết kế, luôn đồng hành phát triển và cam kết mang đến chất lượng vượt trội cùng dịch vụ tốt nhất.",
      color: "pink"
    },
    {
      icon: Eye,
      title: "Tầm nhìn", 
      description: "Trở thành xưởng may hàng đầu được các thương hiệu thời trang thiết kế tin tưởng lựa chọn, mở rộng quy mô sản xuất hiện đại và dẫn đầu về chất lượng cùng dịch vụ trong ngành thời trang nội địa.",
      color: "purple"
    },
    {
      icon: Heart,
      title: "<PERSON><PERSON><PERSON> trị cốt lõi",
      description: "<PERSON><PERSON> tín - Chất lượng - Tr<PERSON><PERSON> nhiệm - <PERSON><PERSON><PERSON> mới - <PERSON><PERSON><PERSON> hành. Những giá trị này là nền tảng cho mọi hoạt động của chúng tôi.",
      color: "indigo"
    }
  ];

  return (
    <section id="gioi-thieu" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            GIỚI THIỆU
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-pink-500 to-purple-600 mx-auto mb-8"></div>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            NS Shop là xưởng may gia công hàng thiết kế số lượng ít dành riêng cho các thương hiệu thời trang 
            tại TPHCM nói riêng và Việt Nam nói chung. Được thành lập bởi đội ngũ với hơn 10 năm kinh nghiệm 
            trong ngành thời trang.
          </p>
        </motion.div>

        {/* Values Grid */}
        <div className="grid md:grid-cols-3 gap-8 mb-16">
          {values.map((value, index) => {
            const Icon = value.icon;
            return (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                viewport={{ once: true }}
                className="text-center group"
              >
                <div className={`inline-flex items-center justify-center w-20 h-20 rounded-full bg-${value.color}-100 mb-6 group-hover:scale-110 transition-transform duration-300`}>
                  <Icon className={`h-10 w-10 text-${value.color}-600`} />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">{value.title}</h3>
                <p className="text-gray-600 leading-relaxed">{value.description}</p>
              </motion.div>
            );
          })}
        </div>

        {/* Company Story */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="bg-gradient-to-br from-pink-50 to-purple-50 rounded-3xl p-8 md:p-12"
        >
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h3 className="text-3xl font-bold text-gray-900 mb-6">
                Câu chuyện của chúng tôi
              </h3>
              <div className="space-y-4 text-gray-600 leading-relaxed">
                <p>
                  Bắt đầu từ một xưởng may nhỏ với niềm đam mê thời trang, NS Shop đã không ngừng 
                  phát triển và hoàn thiện để trở thành đối tác tin cậy của hàng trăm thương hiệu 
                  thời trang trên toàn quốc.
                </p>
                <p>
                  Với đội ngũ thợ may lành nghề và hệ thống máy móc hiện đại, chúng tôi chuyên 
                  sản xuất các sản phẩm thời trang cao cấp với số lượng linh hoạt từ 30 sản phẩm/mẫu.
                </p>
                <p>
                  Chúng tôi hiểu rằng mỗi thiết kế đều mang trong mình câu chuyện riêng, và sứ mệnh 
                  của chúng tôi là biến những ý tưởng sáng tạo thành hiện thực với chất lượng hoàn hảo.
                </p>
              </div>
            </div>
            
            <div className="relative">
              <div className="relative rounded-2xl overflow-hidden shadow-xl">
                <img
                  src="/images/about-story.jpg"
                  alt="Câu chuyện NS Shop"
                  className="w-full h-[400px] object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = "https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80";
                  }}
                />
                
                {/* Decorative Elements */}
                <div className="absolute -top-4 -right-4 w-24 h-24 bg-pink-200 rounded-full opacity-60"></div>
                <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-purple-200 rounded-full opacity-40"></div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Key Features */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="mt-16 text-center"
        >
          <h3 className="text-3xl font-bold text-gray-900 mb-8">
            Điểm nổi bật của NS Shop
          </h3>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[
              { number: "10+", label: "Năm kinh nghiệm", color: "pink" },
              { number: "30+", label: "Sản phẩm tối thiểu", color: "purple" },
              { number: "500+", label: "Khách hàng tin tưởng", color: "indigo" },
              { number: "24/7", label: "Hỗ trợ khách hàng", color: "blue" }
            ].map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className={`bg-${stat.color}-50 rounded-2xl p-6 border border-${stat.color}-100`}
              >
                <div className={`text-4xl font-bold text-${stat.color}-600 mb-2`}>
                  {stat.number}
                </div>
                <div className="text-gray-600 font-medium">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
}
