"use client";

import { motion } from "framer-motion";
import { CheckCircle, Clock, Shield, Zap, Award, Heart } from "lucide-react";

export function AboutWhyChooseUsSection() {
  const reasons = [
    {
      icon: Zap,
      title: "Tốc độ nhanh chóng",
      description: "Ra mẫu từ file ảnh nhanh chóng, thời gian sản xuất từ 7-10 ngày sau khi duyệt mẫu.",
      color: "yellow"
    },
    {
      icon: Award,
      title: "Chất lượng vượt trội",
      description: "Thợ may mẫu có kinh nghiệm lâu năm, máy móc hiện đại, đảm bảo chất lượng sản phẩm.",
      color: "blue"
    },
    {
      icon: Shield,
      title: "Bảo mật tuyệt đối",
      description: "100% mẫu thiết kế của khách hàng được bảo vệ bản quyền, không cung cấp cho bên thứ ba.",
      color: "green"
    },
    {
      icon: Clock,
      title: "<PERSON>h hoạt số lượng",
      description: "Sản xuất số lượng ít chỉ từ 30c/mẫu với giá sản xuất cạnh tranh, phù hợp mọi quy mô.",
      color: "purple"
    },
    {
      icon: CheckCircle,
      title: "Dịch vụ chuyên nghiệp",
      description: "Đội ngũ tư vấn chuyên nghiệp, cập nhật thông tin hàng hóa nhanh chóng cho khách hàng.",
      color: "pink"
    },
    {
      icon: Heart,
      title: "Tận tâm chu đáo",
      description: "Chu đáo – nhiệt tình – tin cậy và uy tín trong mọi giao dịch với khách hàng.",
      color: "red"
    }
  ];

  const processSteps = [
    {
      step: "01",
      title: "Tư vấn & Báo giá",
      description: "Nhận yêu cầu từ khách hàng, tư vấn chi tiết và báo giá minh bạch"
    },
    {
      step: "02", 
      title: "Thiết kế & Mẫu thử",
      description: "Tạo mẫu thử từ ý tưởng hoặc hình ảnh, hoàn thiện thiết kế theo yêu cầu"
    },
    {
      step: "03",
      title: "Sản xuất",
      description: "Tiến hành sản xuất với quy trình chuyên nghiệp, kiểm soát chất lượng nghiêm ngặt"
    },
    {
      step: "04",
      title: "Giao hàng",
      description: "Đóng gói cẩn thận và giao hàng đúng thời gian cam kết"
    }
  ];

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            TẠI SAO CHỌN NS SHOP?
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-pink-500 to-purple-600 mx-auto mb-8"></div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Với hơn 10 năm kinh nghiệm trong ngành may mặc, chúng tôi tự hào mang đến những giá trị 
            vượt trội cho khách hàng.
          </p>
        </motion.div>

        {/* Reasons Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
          {reasons.map((reason, index) => {
            const Icon = reason.icon;
            return (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="group"
              >
                <div className="bg-white rounded-3xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 h-full">
                  <div className={`inline-flex items-center justify-center w-16 h-16 bg-${reason.color}-100 rounded-2xl mb-6 group-hover:scale-110 transition-transform duration-300`}>
                    <Icon className={`h-8 w-8 text-${reason.color}-600`} />
                  </div>
                  
                  <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-pink-600 transition-colors">
                    {reason.title}
                  </h3>
                  
                  <p className="text-gray-600 leading-relaxed">
                    {reason.description}
                  </p>
                </div>
              </motion.div>
            );
          })}
        </div>

        {/* Process Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="bg-gradient-to-br from-pink-50 to-purple-50 rounded-3xl p-8 md:p-12"
        >
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">
              QUY TRÌNH LÀM VIỆC
            </h3>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Quy trình làm việc chuyên nghiệp, minh bạch giúp khách hàng yên tâm và theo dõi tiến độ dễ dàng.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {processSteps.map((step, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                viewport={{ once: true }}
                className="text-center relative"
              >
                {/* Step Number */}
                <div className="relative mb-6">
                  <div className="w-20 h-20 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full flex items-center justify-center mx-auto shadow-lg">
                    <span className="text-2xl font-bold text-white">{step.step}</span>
                  </div>
                  
                  {/* Connector Line */}
                  {index < processSteps.length - 1 && (
                    <div className="hidden lg:block absolute top-10 left-full w-full h-0.5 bg-gradient-to-r from-pink-300 to-purple-300 transform -translate-y-1/2"></div>
                  )}
                </div>
                
                <h4 className="text-xl font-bold text-gray-900 mb-3">
                  {step.title}
                </h4>
                
                <p className="text-gray-600 text-sm leading-relaxed">
                  {step.description}
                </p>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Commitment Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="mt-16 text-center"
        >
          <div className="bg-white rounded-3xl p-8 md:p-12 shadow-lg border border-gray-100">
            <h3 className="text-3xl font-bold text-gray-900 mb-6">
              CAM KẾT CỦA CHÚNG TÔI
            </h3>
            
            <div className="grid md:grid-cols-3 gap-8">
              <div className="space-y-4">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                  <CheckCircle className="h-8 w-8 text-green-600" />
                </div>
                <h4 className="text-xl font-bold text-gray-900">Chất lượng đảm bảo</h4>
                <p className="text-gray-600">100% sản phẩm được kiểm tra chất lượng trước khi giao hàng</p>
              </div>
              
              <div className="space-y-4">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto">
                  <Clock className="h-8 w-8 text-blue-600" />
                </div>
                <h4 className="text-xl font-bold text-gray-900">Đúng thời gian</h4>
                <p className="text-gray-600">Giao hàng đúng thời gian cam kết, cập nhật tiến độ liên tục</p>
              </div>
              
              <div className="space-y-4">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto">
                  <Shield className="h-8 w-8 text-purple-600" />
                </div>
                <h4 className="text-xl font-bold text-gray-900">Bảo mật thiết kế</h4>
                <p className="text-gray-600">Cam kết bảo mật tuyệt đối mọi thiết kế của khách hàng</p>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
