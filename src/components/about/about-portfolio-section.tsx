"use client";

import { motion } from "framer-motion";
import { useState } from "react";
import { <PERSON>, Heart, Star } from "lucide-react";
import { Button } from "@/components/ui/button";

export function AboutPortfolioSection() {
  const [selectedCategory, setSelectedCategory] = useState("all");

  const categories = [
    { id: "all", label: "Tất cả" },
    { id: "dress", label: "<PERSON><PERSON>y đầm" },
    { id: "shirt", label: "Áo sơ mi" },
    { id: "pants", label: "Quần" },
    { id: "jacket", label: "<PERSON>o khoác" }
  ];

  const portfolioItems = [
    {
      id: 1,
      title: "<PERSON><PERSON><PERSON> đầm công sở",
      category: "dress",
      image: "https://images.unsplash.com/photo-1595777457583-95e059d581b8?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
      description: "<PERSON><PERSON><PERSON><PERSON> kế váy đầm công sở thanh lịch, ph<PERSON> hợp với phụ nữ hiện đại",
      tags: ["<PERSON><PERSON><PERSON> sở", "<PERSON><PERSON> lịch", "<PERSON><PERSON><PERSON><PERSON> nghiệp"]
    },
    {
      id: 2,
      title: "<PERSON>o sơ mi nam",
      category: "shirt", 
      image: "https://images.unsplash.com/photo-1602810318383-e386cc2a3ccf?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
      description: "Áo sơ mi nam cao cấp với chất liệu cotton premium",
      tags: ["Nam giới", "Cao cấp", "Cotton"]
    },
    {
      id: 3,
      title: "Quần âu nữ",
      category: "pants",
      image: "https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
      description: "Quần âu nữ form dáng chuẩn, tôn dáng người mặc",
      tags: ["Nữ giới", "Form chuẩn", "Tôn dáng"]
    },
    {
      id: 4,
      title: "Áo khoác blazer",
      category: "jacket",
      image: "https://images.unsplash.com/photo-1551698618-1dfe5d97d256?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
      description: "Blazer nữ phong cách Hàn Quốc, trẻ trung và năng động",
      tags: ["Blazer", "Hàn Quốc", "Trẻ trung"]
    },
    {
      id: 5,
      title: "Đầm dự tiệc",
      category: "dress",
      image: "https://images.unsplash.com/photo-1566479179817-c0b5b4b8b1a8?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
      description: "Đầm dự tiệc sang trọng với chi tiết thêu tay tinh xảo",
      tags: ["Dự tiệc", "Sang trọng", "Thêu tay"]
    },
    {
      id: 6,
      title: "Áo thun polo",
      category: "shirt",
      image: "https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
      description: "Áo polo unisex chất liệu cotton mềm mại, thoáng mát",
      tags: ["Unisex", "Cotton", "Thoáng mát"]
    },
    {
      id: 7,
      title: "Quần jeans",
      category: "pants",
      image: "https://images.unsplash.com/photo-1542272604-787c3835535d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
      description: "Quần jeans skinny fit với công nghệ co giãn 4 chiều",
      tags: ["Jeans", "Skinny fit", "Co giãn"]
    },
    {
      id: 8,
      title: "Áo khoác bomber",
      category: "jacket",
      image: "https://images.unsplash.com/photo-1551028719-00167b16eac5?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
      description: "Bomber jacket phong cách streetwear cho giới trẻ",
      tags: ["Bomber", "Streetwear", "Giới trẻ"]
    },
    {
      id: 9,
      title: "Váy maxi",
      category: "dress",
      image: "https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
      description: "Váy maxi bohemian với họa tiết hoa lá độc đáo",
      tags: ["Maxi", "Bohemian", "Họa tiết"]
    }
  ];

  const filteredItems = selectedCategory === "all" 
    ? portfolioItems 
    : portfolioItems.filter(item => item.category === selectedCategory);

  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            SẢN PHẨM ĐÃ THỰC HIỆN
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-pink-500 to-purple-600 mx-auto mb-8"></div>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            Hiểu rõ sứ mệnh của mình cũng như đạo đức làm nghề. NS Shop nói không với việc rò rỉ ý tưởng thiết kế. 
            Chúng tôi bảo mật tuyệt đối các mẫu thời trang được đối tác/khách hàng mang tới. 
            Dưới đây chỉ là số ít các mẫu của chính NS Shop.
          </p>
        </motion.div>

        {/* Category Filter */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="flex flex-wrap justify-center gap-4 mb-12"
        >
          {categories.map((category) => (
            <Button
              key={category.id}
              variant={selectedCategory === category.id ? "default" : "outline"}
              className={`px-6 py-2 rounded-full transition-all duration-300 ${
                selectedCategory === category.id
                  ? "bg-gradient-to-r from-pink-500 to-purple-600 text-white"
                  : "border-gray-300 text-gray-600 hover:border-pink-300 hover:text-pink-600"
              }`}
              onClick={() => setSelectedCategory(category.id)}
            >
              {category.label}
            </Button>
          ))}
        </motion.div>

        {/* Portfolio Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredItems.map((item, index) => (
            <motion.div
              key={item.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              layout
              className="group cursor-pointer"
            >
              <div className="bg-white rounded-3xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-500 group-hover:transform group-hover:scale-105">
                {/* Image */}
                <div className="relative overflow-hidden">
                  <img
                    src={item.image}
                    alt={item.title}
                    className="w-full h-64 object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  
                  {/* Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="absolute bottom-4 left-4 right-4">
                      <div className="flex items-center gap-2 text-white">
                        <Eye className="h-4 w-4" />
                        <span className="text-sm">Xem chi tiết</span>
                      </div>
                    </div>
                  </div>

                  {/* Floating Action */}
                  <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <button className="w-10 h-10 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white transition-colors">
                      <Heart className="h-5 w-5 text-pink-500" />
                    </button>
                  </div>
                </div>

                {/* Content */}
                <div className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-2 group-hover:text-pink-600 transition-colors">
                    {item.title}
                  </h3>
                  
                  <p className="text-gray-600 text-sm mb-4 leading-relaxed">
                    {item.description}
                  </p>

                  {/* Tags */}
                  <div className="flex flex-wrap gap-2 mb-4">
                    {item.tags.map((tag, tagIndex) => (
                      <span
                        key={tagIndex}
                        className="px-3 py-1 bg-pink-50 text-pink-600 text-xs font-medium rounded-full"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>

                  {/* Rating */}
                  <div className="flex items-center gap-1">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className="h-4 w-4 text-yellow-400 fill-current"
                      />
                    ))}
                    <span className="text-sm text-gray-500 ml-2">5.0</span>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="mt-16 text-center"
        >
          <div className="bg-white rounded-3xl p-8 md:p-12 shadow-lg">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">
              Bạn có ý tưởng thiết kế riêng?
            </h3>
            <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
              Hãy để NS Shop biến ý tưởng của bạn thành hiện thực. Chúng tôi cam kết bảo mật 
              tuyệt đối và mang đến chất lượng tốt nhất cho sản phẩm của bạn.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                size="lg"
                className="bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white px-8 py-3 rounded-full"
              >
                Tư vấn thiết kế miễn phí
              </Button>
              
              <Button
                variant="outline"
                size="lg"
                className="border-pink-300 text-pink-600 hover:bg-pink-50 px-8 py-3 rounded-full"
              >
                Xem thêm mẫu thiết kế
              </Button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
