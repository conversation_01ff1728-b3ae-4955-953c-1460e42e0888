"use client";

import { motion } from "framer-motion";
import { useState, useEffect } from "react";
import { <PERSON>uo<PERSON>, Star, ChevronLeft, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";

export function AboutTestimonialsSection() {
  const [currentIndex, setCurrentIndex] = useState(0);

  const testimonials = [
    {
      id: 1,
      name: "<PERSON>ơ<PERSON>",
      role: "Chủ Shop",
      company: "Sương Fashion",
      avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80",
      content: "Đ<PERSON>i ngũ NS Shop rất nhiệt tình, từ khâu tư vấn đến hỗ trợ sau khi hoàn tất đơn hàng. Mặc dù còn rất trẻ nhưng kinh nghiệm trong ngành may là thực sự đáng nể. <PERSON><PERSON> khâ<PERSON> vận chuyển chưa đ<PERSON>, nhưng vì lý do khách quan nên vẫn sẽ tiếp tục hợp tác!",
      rating: 5,
      project: "Bộ sưu tập Thu Đông 2024"
    },
    {
      id: 2,
      name: "Ms Thi",
      role: "Chủ Shop",
      company: "Thi Boutique",
      avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80",
      content: "Thực sự ban đầu mình rất lo lắng khi phải gia công những mẫu thiết kế của mình bên ngoài vì sợ bị sao chép. Nhưng NS Shop đã không làm mình thất vọng, 2 năm hợp tác chưa 1 lần NS Shop để lộ mẫu thiết kế của mình cho bên khác. Hi vọng các bạn tiếp tục duy trì và phát triển.",
      rating: 5,
      project: "Dòng sản phẩm Exclusive"
    },
    {
      id: 3,
      name: "Ms Hoan",
      role: "Sinh viên khởi nghiệp",
      company: "Hoan's Brand",
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80",
      content: "Mặc dù đam mê thời trang đã thấm vào trong máu, nhưng kiến thức của mình trong ngành này thật sự hạn chế. Rất may mắn khi mình tìm được NS Shop, ngoài gia công, xưởng đã tư vấn cho mình rất nhiều kiến thức bổ ích về kinh doanh thời trang.",
      rating: 5,
      project: "Thương hiệu khởi nghiệp"
    },
    {
      id: 4,
      name: "Mr Nam",
      role: "Giám đốc",
      company: "Nam Fashion Co.",
      avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80",
      content: "Chất lượng sản phẩm của NS Shop luôn đảm bảo, đúng tiến độ cam kết. Đặc biệt ấn tượng với khả năng sản xuất số lượng ít mà vẫn giữ được giá cả cạnh tranh. Đây chính là điều mà các xưởng may khác khó có thể làm được.",
      rating: 5,
      project: "Đồng phục công ty"
    },
    {
      id: 5,
      name: "Ms Linh",
      role: "Nhà thiết kế",
      company: "Linh Design Studio",
      avatar: "https://images.unsplash.com/photo-1534528741775-53994a69daeb?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80",
      content: "Là một nhà thiết kế, tôi rất khó tính về chất lượng sản phẩm. NS Shop đã làm tôi hài lòng từ khâu tư vấn chọn vải, hoàn thiện thiết kế cho đến sản phẩm cuối cùng. Họ thực sự hiểu được ý tưởng của tôi và biến nó thành hiện thực một cách hoàn hảo.",
      rating: 5,
      project: "Bộ sưu tập Haute Couture"
    }
  ];

  const teamMembers = [
    {
      name: "MS HIỀN",
      role: "CEO & FOUNDER",
      quote: "Khởi nghiệp từ 1 sào đồ lề đường đến chuỗi 40 shop thời trang. Tôi thực sự hiểu rõ những khó khăn, những mong muốn của khách hàng khi đến với NS Shop.",
      avatar: "https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80"
    },
    {
      name: "MR NAM",
      role: "CEO & FOUNDER", 
      quote: "Cuộc cách mạng công nghiệp lần thứ tư xuất hiện, mỗi doanh nghiệp đều phải ứng dụng các công nghệ để 4.0 hóa. Đến với NS Shop, bạn sẽ nhận được sự khác biệt đến từ quy trình quản lý cập nhật thời gian thực.",
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80"
    },
    {
      name: "MS DIỄM",
      role: "QUẢN LÝ",
      quote: "Mỗi sản phẩm, mỗi đơn hàng chính là niềm tin khách hàng gửi gắm tại NS Shop. Tôi sẽ dùng 100% năng lực và kỹ năng của mình để làm ra đúng thời gian cam kết những sản phẩm chất lượng.",
      avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80"
    }
  ];

  // Auto-slide functionality
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentIndex((prevIndex) => 
        prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1
      );
    }, 5000);

    return () => clearInterval(timer);
  }, [testimonials.length]);

  const nextTestimonial = () => {
    setCurrentIndex(currentIndex === testimonials.length - 1 ? 0 : currentIndex + 1);
  };

  const prevTestimonial = () => {
    setCurrentIndex(currentIndex === 0 ? testimonials.length - 1 : currentIndex - 1);
  };

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            KHÁCH HÀNG NÓI GÌ
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-pink-500 to-purple-600 mx-auto mb-8"></div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            NS Shop luôn đặt mình vào vị trí của khách hàng, để có thể lắng nghe và thấu hiểu 
            những mong muốn của khách hàng.
          </p>
        </motion.div>

        {/* Main Testimonial Slider */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="relative mb-16"
        >
          <div className="bg-gradient-to-br from-pink-50 to-purple-50 rounded-3xl p-8 md:p-12 relative overflow-hidden">
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-5">
              <Quote className="absolute top-8 left-8 h-24 w-24 text-pink-500" />
              <Quote className="absolute bottom-8 right-8 h-16 w-16 text-purple-500 rotate-180" />
            </div>

            <div className="relative z-10">
              <div className="grid lg:grid-cols-3 gap-8 items-center">
                {/* Testimonial Content */}
                <div className="lg:col-span-2">
                  <motion.div
                    key={currentIndex}
                    initial={{ opacity: 0, x: 50 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -50 }}
                    transition={{ duration: 0.5 }}
                  >
                    {/* Rating */}
                    <div className="flex items-center gap-1 mb-4">
                      {[...Array(testimonials[currentIndex].rating)].map((_, i) => (
                        <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                      ))}
                    </div>

                    {/* Quote */}
                    <blockquote className="text-xl md:text-2xl text-gray-700 leading-relaxed mb-6 italic">
                      "{testimonials[currentIndex].content}"
                    </blockquote>

                    {/* Project */}
                    <div className="inline-block px-4 py-2 bg-pink-100 text-pink-700 rounded-full text-sm font-medium mb-6">
                      Dự án: {testimonials[currentIndex].project}
                    </div>

                    {/* Author Info */}
                    <div className="flex items-center gap-4">
                      <img
                        src={testimonials[currentIndex].avatar}
                        alt={testimonials[currentIndex].name}
                        className="w-16 h-16 rounded-full object-cover border-4 border-white shadow-lg"
                      />
                      <div>
                        <h4 className="text-xl font-bold text-gray-900">
                          {testimonials[currentIndex].name}
                        </h4>
                        <p className="text-gray-600">
                          {testimonials[currentIndex].role} - {testimonials[currentIndex].company}
                        </p>
                      </div>
                    </div>
                  </motion.div>
                </div>

                {/* Navigation */}
                <div className="lg:col-span-1 flex lg:flex-col items-center justify-center gap-4">
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={prevTestimonial}
                    className="w-12 h-12 rounded-full border-pink-300 text-pink-600 hover:bg-pink-50"
                  >
                    <ChevronLeft className="h-6 w-6" />
                  </Button>

                  {/* Dots Indicator */}
                  <div className="flex lg:flex-col gap-2">
                    {testimonials.map((_, index) => (
                      <button
                        key={index}
                        onClick={() => setCurrentIndex(index)}
                        className={`w-3 h-3 rounded-full transition-all duration-300 ${
                          index === currentIndex
                            ? "bg-gradient-to-r from-pink-500 to-purple-600 scale-125"
                            : "bg-gray-300 hover:bg-gray-400"
                        }`}
                      />
                    ))}
                  </div>

                  <Button
                    variant="outline"
                    size="icon"
                    onClick={nextTestimonial}
                    className="w-12 h-12 rounded-full border-pink-300 text-pink-600 hover:bg-pink-50"
                  >
                    <ChevronRight className="h-6 w-6" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Team Quotes */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="grid md:grid-cols-3 gap-8"
        >
          {teamMembers.map((member, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.2 }}
              viewport={{ once: true }}
              className="bg-white rounded-3xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100"
            >
              <div className="text-center">
                <img
                  src={member.avatar}
                  alt={member.name}
                  className="w-20 h-20 rounded-full object-cover mx-auto mb-6 border-4 border-pink-100"
                />
                
                <h4 className="text-xl font-bold text-gray-900 mb-2">
                  {member.name}
                </h4>
                
                <p className="text-pink-600 font-medium mb-4">
                  {member.role}
                </p>
                
                <blockquote className="text-gray-600 italic leading-relaxed">
                  "{member.quote}"
                </blockquote>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="mt-16 text-center"
        >
          <div className="bg-gradient-to-r from-pink-500 to-purple-600 rounded-3xl p-8 md:p-12 text-white">
            <h3 className="text-3xl font-bold mb-4">
              Bạn cũng muốn trở thành khách hàng hài lòng của chúng tôi?
            </h3>
            <p className="text-pink-100 mb-8 max-w-2xl mx-auto">
              Hãy liên hệ với NS Shop ngay hôm nay để được tư vấn miễn phí và trải nghiệm 
              dịch vụ chất lượng cao mà hàng trăm khách hàng đã tin tưởng.
            </p>
            
            <Button
              size="lg"
              variant="secondary"
              className="bg-white text-pink-600 hover:bg-gray-100 px-8 py-3 rounded-full font-semibold"
            >
              Liên hệ tư vấn miễn phí
            </Button>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
