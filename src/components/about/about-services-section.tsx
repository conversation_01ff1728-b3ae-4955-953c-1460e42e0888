"use client";

import { motion } from "framer-motion";
import { Lightbulb, Palette, Package, Scissors, Star, Users } from "lucide-react";
import { Button } from "@/components/ui/button";

export function AboutServicesSection() {
  const services = [
    {
      icon: Lightbulb,
      title: "Tư vấn định hướng thương hiệu",
      description: "Giúp quý khách xây dựng vững chắc thương hiệu thời trang của mình trong lòng khách hàng. Lựa chọn được dòng sản phẩm và phong cách phù hợp với mục tiêu của thương hiệu.",
      color: "yellow",
      category: "THƯƠNG HIỆU"
    },
    {
      icon: Palette,
      title: "Gó<PERSON> ý, hoàn thiện thiết kế",
      description: "Bằng thẩm mỹ được trau dồi qua 10 năm kinh nghiệm, NS Shop sẽ giúp hoàn thiện thiết kế tuyệt vời nhất dựa trên ý tưởng của quý khách.",
      color: "pink",
      category: "THIẾT KẾ"
    },
    {
      icon: Package,
      title: "<PERSON><PERSON> vấn, nhập nguyên liệu sản xuất",
      description: "Mối quan hệ thân thiết của NS Shop với các đối tác cung ứng nguyên vật liệu, chính là đòn bẩy giúp cho khách hàng có được những sản phẩm chất lượng mà giá cả vẫn cạnh tranh.",
      color: "green",
      category: "NGUYÊN LIỆU"
    },
    {
      icon: Scissors,
      title: "Sản xuất sản phẩm với dây chuyền chuyên nghiệp",
      description: "Đội ngũ NS Shop được đào tạo chuyên nghiệp, trải nghiệm sâu sắc trong ngành may. Đảm bảo quý khách có hàng đúng như mong muốn, trong đúng khoảng thời gian đã thống nhất.",
      color: "blue",
      category: "CẮT MAY"
    }
  ];

  const features = [
    "Ra mẫu từ file ảnh nhanh chóng",
    "Thợ may mẫu có kinh nghiệm lâu năm", 
    "Sản xuất sản phẩm cho nhiều shop thời trang thiết kế",
    "Đội ngũ tư vấn, quản lí hàng hoá chuyên nghiệp",
    "Sản xuất số lượng ít chỉ từ 30c/mẫu với giá sản xuất cạnh tranh",
    "Khả năng thích ứng và sản xuất theo trend nhanh",
    "Chi phí gia công rẻ",
    "Máy móc được đầu tư bài bản, hiện đại",
    "Khả năng nhận biết xu hướng, thẩm mỹ sản phẩm tốt",
    "Chu đáo – nhiệt tình – tin cậy và uy tín"
  ];

  return (
    <section className="py-20 bg-gradient-to-br from-gray-50 to-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            LĨNH VỰC
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-pink-500 to-purple-600 mx-auto mb-8"></div>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            NS Shop chuyên nhận đặt may số lượng ít từ 30c/mẫu, lên rập, lên mẫu từ sản phẩm thực tế hoặc hình ảnh, bản vẽ. 
            Nhận IN và THÊU theo yêu cầu, tư vấn về nguồn vải, nguyên phụ liệu phù hợp với mẫu may. 
            Nhận thiết kế và sản xuất nhãn mác theo thương hiệu riêng.
          </p>
        </motion.div>

        {/* Services Grid */}
        <div className="grid md:grid-cols-2 gap-8 mb-16">
          {services.map((service, index) => {
            const Icon = service.icon;
            return (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                viewport={{ once: true }}
                className="bg-white rounded-3xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 group border border-gray-100"
              >
                <div className="flex items-start gap-6">
                  <div className={`flex-shrink-0 w-16 h-16 bg-${service.color}-100 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                    <Icon className={`h-8 w-8 text-${service.color}-600`} />
                  </div>
                  
                  <div className="flex-1">
                    <div className={`inline-block px-3 py-1 bg-${service.color}-50 text-${service.color}-700 text-xs font-semibold rounded-full mb-3`}>
                      {service.category}
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-pink-600 transition-colors">
                      {service.title}
                    </h3>
                    <p className="text-gray-600 leading-relaxed">
                      {service.description}
                    </p>
                  </div>
                </div>
              </motion.div>
            );
          })}
        </div>

        {/* Why Choose Us */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="bg-white rounded-3xl p-8 md:p-12 shadow-lg"
        >
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">
              LÝ DO KHÁCH HÀNG CHỌN XƯỞNG MAY NS SHOP
            </h3>
            <p className="text-gray-600 max-w-3xl mx-auto">
              Có hàng trăm xưởng may gia công ngoài kia, nhưng NS Shop luôn được tin tưởng lựa chọn. 
              Và dưới đây là những lý do cho lựa chọn ấy:
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="flex items-center gap-3 p-4 bg-gradient-to-r from-pink-50 to-purple-50 rounded-xl hover:from-pink-100 hover:to-purple-100 transition-all duration-300"
              >
                <div className="flex-shrink-0 w-6 h-6 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full flex items-center justify-center">
                  <Star className="h-3 w-3 text-white fill-current" />
                </div>
                <span className="text-gray-700 text-sm font-medium">{feature}</span>
              </motion.div>
            ))}
          </div>

          {/* CTA */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
            viewport={{ once: true }}
            className="text-center mt-12"
          >
            <Button
              size="lg"
              className="bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white px-8 py-3 rounded-full"
            >
              <Users className="mr-2 h-5 w-5" />
              Tham gia cùng 500+ khách hàng tin tưởng
            </Button>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
