"use client";

import { motion } from "framer-motion";
import { useState } from "react";
import { ChevronDown, ChevronUp, HelpCircle, DollarSign } from "lucide-react";
import { Button } from "@/components/ui/button";

export function PricingFAQSection() {
  const [openIndex, setOpenIndex] = useState<number | null>(null);

  const pricingFAQs = [
    {
      question: "Gi<PERSON> cả có bao gồm vật liệu không?",
      answer: "Gi<PERSON> cả được báo tùy thuộc vào yêu cầu cụ thể:\n\n• Nếu khách hàng cung cấp vật liệu: Chỉ tính phí gia công\n• Nếu NS Shop cung cấp vật liệu: Bao gồm cả vật liệu và gia công\n• Chúng tôi sẽ báo giá chi tiết và minh bạch cho từng hạng mục\n• Kh<PERSON>ch hàng có thể lựa chọn phương án phù hợp với ngân sách",
    },
    {
      question: "C<PERSON> giảm giá cho đơn hàng số lượng lớn không?",
      answer: "Có! Chúng tôi có chính sách giảm giá hấp dẫn cho đơn hàng số lượng lớn:\n\n• 100-300 sản phẩm: Giảm 5-10%\n• 300-500 sản phẩm: Giảm 10-15%\n• 500+ sản phẩm: Giảm 15-20%\n• Đơn hàng thường xuyên: Ưu đãi đặc biệt\n\nMức giảm giá cụ thể sẽ được tính dựa trên độ phức tạp và loại sản phẩm.",
    },
    {
      question: "Làm thế nào để nhận báo giá chính xác?",
      answer: "Để nhận báo giá chính xác nhất, vui lòng cung cấp:\n\n• Hình ảnh hoặc mô tả chi tiết sản phẩm\n• Số lượng cần sản xuất\n• Chất liệu mong muốn\n• Thời gian giao hàng\n• Yêu cầu đặc biệt (nếu có)\n\nChúng tôi sẽ báo giá trong vòng 2-4 giờ làm việc.",
    },
    {
      question: "Có phí phát sinh nào khác không?",
      answer: "NS Shop cam kết minh bạch về giá cả:\n\n• Không có phí ẩn\n• Mọi chi phí đều được báo trước\n• Phí vận chuyển được tính riêng (nếu có)\n• Phí thay đổi thiết kế (nếu vượt quá 2 lần sửa)\n• Phí gấp đơn hàng (nếu yêu cầu giao trước thời hạn)\n\nTất cả đều được thông báo và thỏa thuận trước khi thực hiện.",
    },
    {
      question: "Chính sách thanh toán như thế nào?",
      answer: "Chúng tôi hỗ trợ nhiều hình thức thanh toán linh hoạt:\n\n• Thanh toán khi nhận hàng (COD)\n• Chuyển khoản ngân hàng\n• Ví điện tử (MoMo, ZaloPay)\n• Thanh toán theo đợt cho đơn lớn:\n  - 50% khi bắt đầu sản xuất\n  - 50% khi hoàn thành\n\nKhách hàng thân thiết có thể được hỗ trợ thanh toán sau.",
    },
    {
      question: "Có bảo hành cho sản phẩm không?",
      answer: "Có! NS Shop cung cấp chế độ bảo hành toàn diện:\n\n• Bảo hành lỗi kỹ thuật: 30-90 ngày (tùy gói)\n• Sửa chữa miễn phí nếu lỗi do sản xuất\n• Đổi trả trong 7 ngày nếu không đúng yêu cầu\n• Hỗ trợ tư vấn sử dụng và bảo quản\n• Ưu tiên sửa chữa cho khách hàng VIP",
    },
  ];

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          {/* Section Header */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <div className="flex items-center justify-center gap-3 mb-6">
              <div className="w-16 h-16 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full flex items-center justify-center">
                <DollarSign className="h-8 w-8 text-white" />
              </div>
            </div>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Câu Hỏi Về{" "}
              <span className="bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent">
                Giá Cả
              </span>
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Giải đáp các thắc mắc phổ biến về giá cả, thanh toán và chính sách của chúng tôi
            </p>
          </motion.div>

          {/* FAQ List */}
          <div className="space-y-4">
            {pricingFAQs.map((faq, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-gray-50 rounded-2xl shadow-lg overflow-hidden border border-gray-100 hover:shadow-xl transition-all duration-300"
              >
                <button
                  onClick={() => toggleFAQ(index)}
                  className="w-full px-6 py-6 text-left flex items-center justify-between hover:bg-gray-100 transition-colors duration-200"
                >
                  <div className="flex items-start gap-4 flex-1">
                    <div className="flex-shrink-0 w-10 h-10 bg-pink-100 rounded-full flex items-center justify-center">
                      <HelpCircle className="h-5 w-5 text-pink-600" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900">
                        {faq.question}
                      </h3>
                    </div>
                  </div>
                  <div className="flex-shrink-0 ml-4">
                    {openIndex === index ? (
                      <ChevronUp className="h-5 w-5 text-gray-400" />
                    ) : (
                      <ChevronDown className="h-5 w-5 text-gray-400" />
                    )}
                  </div>
                </button>

                {openIndex === index && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: "auto" }}
                    exit={{ opacity: 0, height: 0 }}
                    transition={{ duration: 0.3 }}
                    className="px-6 pb-6"
                  >
                    <div className="pl-14">
                      <div className="prose prose-gray max-w-none">
                        <div className="text-gray-700 leading-relaxed whitespace-pre-line">
                          {faq.answer}
                        </div>
                      </div>
                    </div>
                  </motion.div>
                )}
              </motion.div>
            ))}
          </div>

          {/* CTA Section */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="mt-16 text-center bg-gradient-to-r from-pink-50 to-purple-50 rounded-3xl p-8 border border-pink-100"
          >
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Vẫn Có Thắc Mắc Về Giá Cả?
            </h3>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              Đội ngũ tư vấn của chúng tôi sẵn sàng giải đáp mọi câu hỏi và cung cấp 
              báo giá chi tiết miễn phí cho dự án của bạn.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                onClick={() => window.open("tel:0796597878", "_self")}
                className="bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white px-8 py-3 rounded-xl font-semibold"
              >
                Gọi Tư Vấn Miễn Phí
              </Button>
              <Button
                onClick={() => {
                  const message = "Xin chào! Tôi muốn được tư vấn về giá cả dịch vụ may gia công.";
                  const encodedMessage = encodeURIComponent(message);
                  window.open(`https://zalo.me/0796597878?message=${encodedMessage}`, "_blank");
                }}
                variant="outline"
                className="border-2 border-pink-300 text-pink-600 hover:bg-pink-50 px-8 py-3 rounded-xl font-semibold"
              >
                Chat Zalo
              </Button>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
