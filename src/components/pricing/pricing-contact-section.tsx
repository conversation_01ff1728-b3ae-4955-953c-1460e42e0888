"use client";

import { motion } from "framer-motion";
import {
  Phone,
  Mail,
  MessageCircle,
  Calculator,
  Send,
  User,
  Package,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useState } from "react";
import { useContactInfo, useContactActions } from "@/hooks/use-contact-info";
import { useContactForm } from "@/hooks/use-contact-form";

export function PricingContactSection() {
  const contactData = useContactInfo();
  const contactActions = useContactActions();

  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    company: "",
    service: "",
    quantity: "",
    description: "",
    timeline: "",
  });

  const { submitForm, isSubmitting } = useContactForm({
    onSuccess: () => {
      setFormData({
        name: "",
        email: "",
        phone: "",
        company: "",
        service: "",
        quantity: "",
        description: "",
        timeline: "",
      });
    },
  });

  const contactMethods = [
    {
      icon: Phone,
      title: "Hotline Báo Giá",
      content: contactData.phone,
      description: "Tư vấn trực tiếp 24/7",
      color: "green",
      action: contactActions.callPhone,
    },
    {
      icon: MessageCircle,
      title: "Chat Zalo",
      content: contactData.phone,
      description: "Nhận báo giá nhanh",
      color: "blue",
      action: () =>
        contactActions.openZalo(
          "Xin chào! Tôi muốn nhận báo giá dịch vụ may gia công."
        ),
    },
    {
      icon: Mail,
      title: "Email",
      content: contactData.email,
      description: "Gửi yêu cầu chi tiết",
      color: "purple",
      action: () =>
        contactActions.sendEmail("Yêu cầu báo giá dịch vụ may gia công"),
    },
  ];

  const serviceOptions = [
    "May gia công số lượng ít",
    "May gia công số lượng lớn",
    "Thiết kế + May gia công",
    "Chụp ảnh sản phẩm",
    "Thiết kế bao bì",
    "Dịch vụ khác",
  ];

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await submitForm({
      name: formData.name,
      email: formData.email,
      phone: formData.phone,
      company: formData.company,
      service: formData.service,
      subject: "Yêu cầu báo giá dịch vụ may gia công",
      message: `Thông tin yêu cầu báo giá:
          
Dịch vụ: ${formData.service}
Số lượng: ${formData.quantity}
Thời gian mong muốn: ${formData.timeline}

Mô tả chi tiết:
${formData.description}`,
      source: "pricing-page",
      quantity: formData.quantity,
      timeline: formData.timeline,
      description: formData.description,
    });
  };

  return (
    <section className="py-20 bg-gradient-to-br from-pink-50 via-purple-50 to-blue-50">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <div className="flex items-center justify-center gap-3 mb-6">
              <div className="w-16 h-16 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full flex items-center justify-center">
                <Calculator className="h-8 w-8 text-white" />
              </div>
            </div>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Nhận Báo Giá{" "}
              <span className="bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent">
                Miễn Phí
              </span>
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Gửi thông tin dự án của bạn để nhận báo giá chi tiết và tư vấn
              chuyên nghiệp từ đội ngũ NS Shop
            </p>
          </motion.div>

          <div className="grid lg:grid-cols-2 gap-12">
            {/* Contact Methods */}
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h3 className="text-2xl font-bold text-gray-900 mb-8">
                Liên Hệ Nhanh
              </h3>

              <div className="space-y-6 mb-8">
                {contactMethods.map((method, index) => {
                  const Icon = method.icon;
                  return (
                    <motion.button
                      key={index}
                      onClick={method.action}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      className="w-full p-6 bg-white rounded-2xl border border-gray-100 hover:shadow-lg transition-all duration-300 text-left group"
                    >
                      <div className="flex items-center gap-4">
                        <div
                          className={`w-14 h-14 rounded-full flex items-center justify-center transition-colors duration-300 ${
                            method.color === "green"
                              ? "bg-green-100 text-green-600 group-hover:bg-green-200"
                              : method.color === "blue"
                                ? "bg-blue-100 text-blue-600 group-hover:bg-blue-200"
                                : "bg-purple-100 text-purple-600 group-hover:bg-purple-200"
                          }`}
                        >
                          <Icon className="h-7 w-7" />
                        </div>
                        <div className="flex-1">
                          <h4 className="font-semibold text-gray-900 mb-1">
                            {method.title}
                          </h4>
                          <p className="text-lg font-medium text-gray-800 mb-1">
                            {method.content}
                          </p>
                          <p className="text-sm text-gray-600">
                            {method.description}
                          </p>
                        </div>
                      </div>
                    </motion.button>
                  );
                })}
              </div>

              {/* Quick Info */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                viewport={{ once: true }}
                className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100"
              >
                <h4 className="font-bold text-gray-900 mb-4">
                  ⚡ Cam Kết Của Chúng Tôi
                </h4>
                <div className="space-y-3 text-sm text-gray-700">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    <span>Báo giá trong vòng 2-4 giờ làm việc</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                    <span>Tư vấn miễn phí không giới hạn</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                    <span>Giá cả cạnh tranh nhất thị trường</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-pink-400 rounded-full"></div>
                    <span>Hỗ trợ 24/7 qua mọi kênh</span>
                  </div>
                </div>
              </motion.div>
            </motion.div>

            {/* Quote Request Form */}
            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="bg-white rounded-3xl p-8 shadow-lg border border-gray-100"
            >
              <h3 className="text-2xl font-bold text-gray-900 mb-8">
                Yêu Cầu Báo Giá Chi Tiết
              </h3>

              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Họ và tên *
                    </label>
                    <div className="relative">
                      <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                      <Input
                        type="text"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        required
                        placeholder="Nhập họ và tên"
                        className="pl-10 py-3 rounded-xl border-gray-200 focus:border-pink-400 focus:ring-pink-400"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Email *
                    </label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                      <Input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        required
                        placeholder="Nhập email"
                        className="pl-10 py-3 rounded-xl border-gray-200 focus:border-pink-400 focus:ring-pink-400"
                      />
                    </div>
                  </div>
                </div>

                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Số điện thoại *
                    </label>
                    <div className="relative">
                      <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                      <Input
                        type="tel"
                        name="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        required
                        placeholder="Nhập số điện thoại"
                        className="pl-10 py-3 rounded-xl border-gray-200 focus:border-pink-400 focus:ring-pink-400"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Tên công ty/shop
                    </label>
                    <Input
                      type="text"
                      name="company"
                      value={formData.company}
                      onChange={handleInputChange}
                      placeholder="Nhập tên công ty"
                      className="py-3 rounded-xl border-gray-200 focus:border-pink-400 focus:ring-pink-400"
                    />
                  </div>
                </div>

                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Dịch vụ quan tâm *
                    </label>
                    <Select
                      onValueChange={(value) =>
                        handleSelectChange("service", value)
                      }
                    >
                      <SelectTrigger className="py-3 rounded-xl border-gray-200 focus:border-pink-400 focus:ring-pink-400">
                        <SelectValue placeholder="Chọn dịch vụ" />
                      </SelectTrigger>
                      <SelectContent>
                        {serviceOptions.map((service) => (
                          <SelectItem key={service} value={service}>
                            {service}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Số lượng dự kiến
                    </label>
                    <div className="relative">
                      <Package className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                      <Input
                        type="text"
                        name="quantity"
                        value={formData.quantity}
                        onChange={handleInputChange}
                        placeholder="VD: 100 sản phẩm"
                        className="pl-10 py-3 rounded-xl border-gray-200 focus:border-pink-400 focus:ring-pink-400"
                      />
                    </div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Thời gian mong muốn
                  </label>
                  <Input
                    type="text"
                    name="timeline"
                    value={formData.timeline}
                    onChange={handleInputChange}
                    placeholder="VD: Trong vòng 2 tuần"
                    className="py-3 rounded-xl border-gray-200 focus:border-pink-400 focus:ring-pink-400"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Mô tả chi tiết dự án *
                  </label>
                  <Textarea
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    required
                    rows={4}
                    placeholder="Mô tả chi tiết về sản phẩm, chất liệu, thiết kế, yêu cầu đặc biệt..."
                    className="rounded-xl border-gray-200 focus:border-pink-400 focus:ring-pink-400 resize-none"
                  />
                </div>

                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white py-3 rounded-xl font-semibold transition-all duration-300 flex items-center justify-center gap-2"
                >
                  {isSubmitting ? (
                    <>
                      <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      Đang gửi...
                    </>
                  ) : (
                    <>
                      <Send className="h-5 w-5" />
                      Gửi Yêu Cầu Báo Giá
                    </>
                  )}
                </Button>
              </form>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  );
}
