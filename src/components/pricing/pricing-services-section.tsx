"use client";

import { motion } from "framer-motion";
import { Palette, Camera, Package, Truck, Scissors, Shirt } from "lucide-react";

export function PricingServicesSection() {
  const additionalServices = [
    {
      name: "Thiết Kế Chuyên Nghiệp",
      price: "500.000đ - 2.000.000đ",
      description: "Thiết kế từ ý tưởng, chỉnh sửa mẫu có sẵn, tư vấn xu hướng thời trang",
      icon: Palette,
      color: "pink",
      features: [
        "Thiết kế từ ý tưởng",
        "Chỉnh sửa mẫu có sẵn", 
        "Tư vấn xu hướng",
        "File thiết kế chất lượng cao"
      ]
    },
    {
      name: "Chụp Ảnh Sản Phẩm",
      price: "200.000đ - 800.000đ",
      description: "Chụp ảnh chuyên nghiệp cho sản phẩm, lookbook, catalog",
      icon: Camera,
      color: "blue",
      features: [
        "Chụ<PERSON> ảnh sản phẩm đơn",
        "<PERSON><PERSON><PERSON> ảnh model",
        "Chỉnh sửa ảnh chuyên nghiệp",
        "Giao file đa định dạng"
      ]
    },
    {
      name: "<PERSON><PERSON><PERSON><PERSON> Kế Bao Bì",
      price: "300.000đ - 1.500.000đ", 
      description: "Thiết kế túi, hộp, nhãn mác, tag sản phẩm theo thương hiệu",
      icon: Package,
      color: "green",
      features: [
        "Thiết kế túi đựng",
        "Thiết kế hộp sản phẩm",
        "Nhãn mác, tag",
        "In ấn chất lượng cao"
      ]
    },
    {
      name: "Vận Chuyển Express",
      price: "100.000đ - 300.000đ",
      description: "Giao hàng nhanh trong 24h nội thành, 48h toàn quốc",
      icon: Truck,
      color: "orange",
      features: [
        "Giao hàng trong 24h",
        "Đóng gói cẩn thận",
        "Bảo hiểm hàng hóa",
        "Theo dõi đơn hàng"
      ]
    },
    {
      name: "May Mẫu Thử Nhanh",
      price: "200.000đ - 500.000đ",
      description: "May mẫu thử trong 24-48h để kiểm tra fit và chất lượng",
      icon: Scissors,
      color: "purple",
      features: [
        "May mẫu trong 24-48h",
        "Kiểm tra fit và form",
        "Chỉnh sửa theo yêu cầu",
        "Tư vấn cải tiến"
      ]
    },
    {
      name: "In Thêu Logo",
      price: "50.000đ - 200.000đ",
      description: "In/thêu logo, slogan lên sản phẩm với nhiều kỹ thuật khác nhau",
      icon: Shirt,
      color: "indigo",
      features: [
        "In lụa, in chuyển nhiệt",
        "Thêu máy chuyên nghiệp",
        "Nhiều màu sắc",
        "Bền màu, không phai"
      ]
    },
  ];

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Dịch Vụ{" "}
              <span className="bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent">
                Bổ Sung
              </span>
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Các dịch vụ hỗ trợ chuyên nghiệp giúp hoàn thiện sản phẩm và thương hiệu của bạn
            </p>
          </motion.div>

          {/* Services Grid */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {additionalServices.map((service, index) => {
              const Icon = service.icon;
              const colorClasses = {
                pink: "bg-pink-100 text-pink-600 border-pink-200",
                blue: "bg-blue-100 text-blue-600 border-blue-200", 
                green: "bg-green-100 text-green-600 border-green-200",
                orange: "bg-orange-100 text-orange-600 border-orange-200",
                purple: "bg-purple-100 text-purple-600 border-purple-200",
                indigo: "bg-indigo-100 text-indigo-600 border-indigo-200",
              };

              return (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 group"
                >
                  {/* Service Header */}
                  <div className="flex items-start justify-between mb-4">
                    <div className={`w-12 h-12 rounded-xl flex items-center justify-center ${colorClasses[service.color as keyof typeof colorClasses]} group-hover:scale-110 transition-transform duration-300`}>
                      <Icon className="h-6 w-6" />
                    </div>
                    <div className="text-right">
                      <span className="text-lg font-bold text-pink-600">
                        {service.price}
                      </span>
                    </div>
                  </div>

                  <h3 className="text-xl font-bold text-gray-900 mb-3">
                    {service.name}
                  </h3>

                  <p className="text-gray-600 text-sm mb-4 leading-relaxed">
                    {service.description}
                  </p>

                  {/* Features List */}
                  <div className="space-y-2">
                    {service.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center gap-2">
                        <div className="w-1.5 h-1.5 bg-pink-400 rounded-full"></div>
                        <span className="text-sm text-gray-700">{feature}</span>
                      </div>
                    ))}
                  </div>
                </motion.div>
              );
            })}
          </div>

          {/* Pricing Notes */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="mt-16 bg-gradient-to-r from-blue-50 to-purple-50 rounded-3xl p-8 border border-blue-100"
          >
            <div className="text-center mb-6">
              <h3 className="text-2xl font-bold text-gray-900 mb-2">
                💡 Lưu Ý Về Giá Cả
              </h3>
            </div>
            
            <div className="grid md:grid-cols-2 gap-6 text-gray-700">
              <div className="space-y-3">
                <h4 className="font-semibold text-gray-900">Yếu tố ảnh hưởng giá:</h4>
                <ul className="space-y-2 text-sm">
                  <li>• Độ phức tạp của thiết kế</li>
                  <li>• Chất liệu và phụ kiện sử dụng</li>
                  <li>• Số lượng sản xuất</li>
                  <li>• Thời gian giao hàng yêu cầu</li>
                </ul>
              </div>
              
              <div className="space-y-3">
                <h4 className="font-semibold text-gray-900">Cam kết của chúng tôi:</h4>
                <ul className="space-y-2 text-sm">
                  <li>• Báo giá chi tiết và minh bạch</li>
                  <li>• Không phát sinh chi phí ẩn</li>
                  <li>• Tư vấn tối ưu chi phí</li>
                  <li>• Hỗ trợ thanh toán linh hoạt</li>
                </ul>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
