"use client";

import { motion } from "framer-motion";
import { Check, Star, Zap, Crown, Shield, Phone, MessageCircle } from "lucide-react";
import { Button } from "@/components/ui/button";

export function PricingPlansSection() {
  const pricingPlans = [
    {
      name: "<PERSON><PERSON><PERSON>",
      icon: Star,
      price: "Từ 80.000đ",
      originalPrice: null,
      description: "Phù hợp cho các shop nhỏ và khởi nghiệp",
      minQuantity: "30-100 sản phẩm",
      features: [
        "Tư vấn thiết kế cơ bản",
        "May mẫu thử miễn phí",
        "Chọn vải từ kho có sẵn",
        "Thời gian sản xuất: 10-15 ngày",
        "Bảo hành 30 ngày",
        "Hỗ trợ qua email",
      ],
      color: "blue",
      popular: false,
    },
    {
      name: "<PERSON><PERSON><PERSON>i<PERSON>",
      icon: Zap,
      price: "Từ 120.000đ",
      originalPrice: "150.000đ",
      description: "<PERSON><PERSON><PERSON> chọn phổ biến nhất cho các doanh nghiệp",
      minQuantity: "50-300 sản phẩm",
      features: [
        "Tư vấn thiết kế chuyên nghiệp",
        "May 2 mẫu thử miễn phí",
        "Tự do chọn chất liệu",
        "Thời gian sản xuất: 7-10 ngày",
        "Bảo hành 60 ngày",
        "Hỗ trợ qua điện thoại",
        "Chụp ảnh sản phẩm miễn phí",
        "Thiết kế logo/nhãn mác",
      ],
      color: "pink",
      popular: true,
    },
    {
      name: "Gói Premium",
      icon: Crown,
      price: "Từ 200.000đ",
      originalPrice: null,
      description: "Dành cho các thương hiệu cao cấp",
      minQuantity: "100+ sản phẩm",
      features: [
        "Tư vấn thiết kế cao cấp",
        "May 3 mẫu thử miễn phí",
        "Vải cao cấp, nhập khẩu",
        "Thời gian sản xuất: 5-7 ngày",
        "Bảo hành 90 ngày",
        "Hỗ trợ 24/7",
        "Chụp ảnh chuyên nghiệp",
        "Thiết kế bao bì",
        "Tư vấn marketing",
        "Ưu tiên sản xuất",
      ],
      color: "purple",
      popular: false,
    },
  ];

  const handleContactForPlan = (planName: string) => {
    const message = `Xin chào! Tôi quan tâm đến ${planName} và muốn được tư vấn chi tiết về giá cả và dịch vụ.`;
    const encodedMessage = encodeURIComponent(message);
    window.open(`https://zalo.me/0796597878?message=${encodedMessage}`, "_blank");
  };

  return (
    <section id="pricing-plans" className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Gói Dịch Vụ{" "}
              <span className="bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent">
                May Gia Công
              </span>
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Chọn gói dịch vụ phù hợp với nhu cầu và ngân sách của bạn. 
              Tất cả gói đều đảm bảo chất lượng cao và giao hàng đúng hẹn.
            </p>
          </motion.div>

          {/* Pricing Cards */}
          <div className="grid md:grid-cols-3 gap-8 mb-16">
            {pricingPlans.map((plan, index) => {
              const Icon = plan.icon;
              return (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.2 }}
                  viewport={{ once: true }}
                  className={`relative bg-white rounded-3xl p-8 shadow-lg border-2 transition-all duration-300 hover:shadow-xl ${
                    plan.popular 
                      ? "border-pink-300 transform scale-105" 
                      : "border-gray-200 hover:border-pink-200"
                  }`}
                >
                  {/* Popular Badge */}
                  {plan.popular && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <div className="bg-gradient-to-r from-pink-500 to-purple-600 text-white px-6 py-2 rounded-full text-sm font-semibold">
                        Phổ biến nhất
                      </div>
                    </div>
                  )}

                  {/* Plan Header */}
                  <div className="text-center mb-8">
                    <div className={`w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center ${
                      plan.color === "blue" ? "bg-blue-100" :
                      plan.color === "pink" ? "bg-pink-100" : "bg-purple-100"
                    }`}>
                      <Icon className={`h-8 w-8 ${
                        plan.color === "blue" ? "text-blue-600" :
                        plan.color === "pink" ? "text-pink-600" : "text-purple-600"
                      }`} />
                    </div>

                    <h3 className="text-2xl font-bold text-gray-900 mb-2">
                      {plan.name}
                    </h3>

                    <p className="text-gray-600 mb-4">
                      {plan.description}
                    </p>

                    <div className="mb-4">
                      <div className="flex items-center justify-center gap-2">
                        <span className="text-3xl font-bold text-gray-900">
                          {plan.price}
                        </span>
                        {plan.originalPrice && (
                          <span className="text-lg text-gray-400 line-through">
                            {plan.originalPrice}
                          </span>
                        )}
                      </div>
                      <p className="text-sm text-gray-500 mt-1">
                        {plan.minQuantity}
                      </p>
                    </div>
                  </div>

                  {/* Features List */}
                  <div className="space-y-4 mb-8">
                    {plan.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-start gap-3">
                        <div className="flex-shrink-0 w-5 h-5 bg-green-100 rounded-full flex items-center justify-center mt-0.5">
                          <Check className="h-3 w-3 text-green-600" />
                        </div>
                        <span className="text-gray-700 text-sm leading-relaxed">
                          {feature}
                        </span>
                      </div>
                    ))}
                  </div>

                  {/* CTA Button */}
                  <Button
                    onClick={() => handleContactForPlan(plan.name)}
                    className={`w-full py-3 rounded-xl font-semibold transition-all duration-300 ${
                      plan.popular
                        ? "bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white"
                        : "bg-gray-100 hover:bg-gray-200 text-gray-900"
                    }`}
                  >
                    Liên Hệ Báo Giá
                  </Button>
                </motion.div>
              );
            })}
          </div>

          {/* Bottom CTA */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center bg-white rounded-3xl p-8 shadow-lg border border-gray-100"
          >
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Cần Tư Vấn Thêm?
            </h3>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              Đội ngũ chuyên gia của chúng tôi sẵn sàng tư vấn miễn phí để giúp bạn 
              chọn gói dịch vụ phù hợp nhất với nhu cầu và ngân sách.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                onClick={() => window.open("tel:0796597878", "_self")}
                className="bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-xl font-semibold flex items-center gap-2"
              >
                <Phone className="h-5 w-5" />
                Gọi Ngay: 0796 59 78 78
              </Button>
              <Button
                onClick={() => window.open("https://zalo.me/0796597878", "_blank")}
                variant="outline"
                className="border-2 border-blue-300 text-blue-600 hover:bg-blue-50 px-6 py-3 rounded-xl font-semibold flex items-center gap-2"
              >
                <MessageCircle className="h-5 w-5" />
                Chat Zalo
              </Button>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
