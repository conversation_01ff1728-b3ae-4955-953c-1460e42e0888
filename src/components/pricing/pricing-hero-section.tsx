"use client";

import { motion } from "framer-motion";
import { DollarSign, Calculator, Phone, MessageCircle, CheckCircle, Star } from "lucide-react";
import { Button } from "@/components/ui/button";

export function PricingHeroSection() {
  const features = [
    "Báo giá miễn phí 24/7",
    "Hỗ trợ số lượng ít từ 30 sản phẩm",
    "<PERSON>i<PERSON> cả cạnh tranh nhất thị trường",
    "Chất lượng đảm bảo 100%",
  ];

  const quickActions = [
    {
      icon: Calculator,
      title: "Tính giá nhanh",
      description: "Ước tính chi phí ngay",
      action: () => {
        // Scroll to pricing calculator or open modal
        const pricingSection = document.getElementById("pricing-plans");
        if (pricingSection) {
          pricingSection.scrollIntoView({ behavior: "smooth" });
        }
      },
      color: "blue",
    },
    {
      icon: Phone,
      title: "<PERSON><PERSON>i báo gi<PERSON>",
      description: "0796 59 78 78",
      action: () => {
        window.open("tel:0796597878", "_self");
      },
      color: "green",
    },
    {
      icon: MessageCircle,
      title: "Chat <PERSON>alo",
      description: "T<PERSON> vấn trực tiếp",
      action: () => {
        window.open("https://zalo.me/0796597878", "_blank");
      },
      color: "blue",
    },
  ];

  return (
    <section className="relative bg-gradient-to-br from-pink-50 via-purple-50 to-blue-50 py-20 overflow-hidden">
      {/* Background decorations */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-20 h-20 bg-pink-200 rounded-full opacity-20 animate-pulse"></div>
        <div className="absolute top-40 right-20 w-16 h-16 bg-purple-200 rounded-full opacity-20 animate-pulse delay-1000"></div>
        <div className="absolute bottom-20 left-1/4 w-12 h-12 bg-blue-200 rounded-full opacity-20 animate-pulse delay-2000"></div>
        <div className="absolute top-1/2 right-1/4 w-8 h-8 bg-green-200 rounded-full opacity-20 animate-pulse delay-3000"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-6xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
            >
              <div className="flex items-center gap-3 mb-6">
                <div className="w-16 h-16 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full flex items-center justify-center">
                  <DollarSign className="h-8 w-8 text-white" />
                </div>
                <div className="flex items-center gap-2">
                  <Star className="h-5 w-5 text-yellow-400 fill-current" />
                  <Star className="h-5 w-5 text-yellow-400 fill-current" />
                  <Star className="h-5 w-5 text-yellow-400 fill-current" />
                  <Star className="h-5 w-5 text-yellow-400 fill-current" />
                  <Star className="h-5 w-5 text-yellow-400 fill-current" />
                  <span className="text-sm text-gray-600 ml-2">5.0 (200+ đánh giá)</span>
                </div>
              </div>

              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
                Bảng Giá{" "}
                <span className="bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent">
                  Dịch Vụ
                </span>
              </h1>

              <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                Giá cả minh bạch, cạnh tranh nhất thị trường. Hỗ trợ may gia công số lượng ít 
                với chất lượng cao và thời gian giao hàng nhanh chóng.
              </p>

              {/* Features List */}
              <div className="space-y-4 mb-8">
                {features.map((feature, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.6, delay: 0.2 + index * 0.1 }}
                    className="flex items-center gap-3"
                  >
                    <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    </div>
                    <span className="text-gray-700 font-medium">{feature}</span>
                  </motion.div>
                ))}
              </div>

              {/* CTA Buttons */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.6 }}
                className="flex flex-col sm:flex-row gap-4"
              >
                <Button
                  onClick={() => {
                    const pricingSection = document.getElementById("pricing-plans");
                    if (pricingSection) {
                      pricingSection.scrollIntoView({ behavior: "smooth" });
                    }
                  }}
                  className="bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white px-8 py-3 rounded-xl font-semibold text-lg"
                >
                  Xem Bảng Giá Chi Tiết
                </Button>
                <Button
                  variant="outline"
                  onClick={() => window.open("tel:0796597878", "_self")}
                  className="border-2 border-pink-300 text-pink-600 hover:bg-pink-50 px-8 py-3 rounded-xl font-semibold text-lg"
                >
                  Báo Giá Miễn Phí
                </Button>
              </motion.div>
            </motion.div>

            {/* Right Content - Quick Actions */}
            <motion.div
              initial={{ opacity: 0, x: 30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="space-y-6"
            >
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-2">
                  Nhận Báo Giá Ngay
                </h3>
                <p className="text-gray-600">
                  Chọn cách liên hệ phù hợp với bạn
                </p>
              </div>

              {quickActions.map((action, index) => {
                const Icon = action.icon;
                return (
                  <motion.button
                    key={index}
                    onClick={action.action}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.4 + index * 0.1 }}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="w-full p-6 bg-white rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 text-left group"
                  >
                    <div className="flex items-center gap-4">
                      <div
                        className={`w-14 h-14 rounded-full flex items-center justify-center transition-colors duration-300 ${
                          action.color === "blue"
                            ? "bg-blue-100 text-blue-600 group-hover:bg-blue-200"
                            : "bg-green-100 text-green-600 group-hover:bg-green-200"
                        }`}
                      >
                        <Icon className="h-7 w-7" />
                      </div>
                      <div className="flex-1">
                        <h4 className="font-semibold text-gray-900 mb-1 text-lg">
                          {action.title}
                        </h4>
                        <p className="text-gray-600">{action.description}</p>
                      </div>
                    </div>
                  </motion.button>
                );
              })}

              {/* Special Offer */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.8 }}
                className="p-6 bg-gradient-to-r from-pink-500 to-purple-600 rounded-2xl text-white"
              >
                <h4 className="font-bold text-lg mb-2">🎉 Ưu Đãi Đặc Biệt</h4>
                <p className="text-pink-100 mb-3">
                  Giảm 10% cho đơn hàng đầu tiên và miễn phí thiết kế mẫu
                </p>
                <p className="text-sm text-pink-200">
                  *Áp dụng cho đơn hàng từ 50 sản phẩm trở lên
                </p>
              </motion.div>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  );
}
