"use client";

import { motion } from "framer-motion";
import { Check, X, Star } from "lucide-react";

export function PricingComparisonSection() {
  const comparisonData = [
    {
      feature: "Tư vấn thiết kế",
      basic: "Cơ bản",
      standard: "Chuyên nghiệp", 
      premium: "Cao cấp",
    },
    {
      feature: "Số mẫu thử miễn phí",
      basic: "1 mẫu",
      standard: "2 mẫu",
      premium: "3 mẫu",
    },
    {
      feature: "Thời gian sản xuất",
      basic: "10-15 ngày",
      standard: "7-10 ngày",
      premium: "5-7 ngày",
    },
    {
      feature: "Chọn chất liệu",
      basic: "Kho có sẵn",
      standard: "Tự do chọn",
      premium: "<PERSON> cấp, nhập khẩu",
    },
    {
      feature: "Bảo hành",
      basic: "30 ngày",
      standard: "60 ngày", 
      premium: "90 ngày",
    },
    {
      feature: "Hỗ trợ khách hàng",
      basic: "Email",
      standard: "<PERSON><PERSON><PERSON><PERSON> thoại",
      premium: "24/7",
    },
    {
      feature: "Chụp ảnh sản phẩm",
      basic: false,
      standard: true,
      premium: true,
    },
    {
      feature: "Thiết kế logo/nhãn mác",
      basic: false,
      standard: true,
      premium: true,
    },
    {
      feature: "Thiết kế bao bì",
      basic: false,
      standard: false,
      premium: true,
    },
    {
      feature: "Tư vấn marketing",
      basic: false,
      standard: false,
      premium: true,
    },
    {
      feature: "Ưu tiên sản xuất",
      basic: false,
      standard: false,
      premium: true,
    },
  ];

  const plans = [
    {
      name: "Gói Cơ Bản",
      price: "Từ 80.000đ",
      color: "blue",
      popular: false,
    },
    {
      name: "Gói Tiêu Chuẩn", 
      price: "Từ 120.000đ",
      color: "pink",
      popular: true,
    },
    {
      name: "Gói Premium",
      price: "Từ 200.000đ", 
      color: "purple",
      popular: false,
    },
  ];

  const renderFeatureValue = (value: any, planIndex: number) => {
    if (typeof value === "boolean") {
      return value ? (
        <Check className="h-5 w-5 text-green-600 mx-auto" />
      ) : (
        <X className="h-5 w-5 text-gray-400 mx-auto" />
      );
    }
    
    return (
      <span className={`text-sm font-medium ${
        planIndex === 1 ? "text-pink-600" : "text-gray-700"
      }`}>
        {value}
      </span>
    );
  };

  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              So Sánh{" "}
              <span className="bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent">
                Gói Dịch Vụ
              </span>
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Xem chi tiết sự khác biệt giữa các gói dịch vụ để chọn lựa phù hợp nhất
            </p>
          </motion.div>

          {/* Comparison Table */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="bg-white rounded-3xl shadow-lg overflow-hidden border border-gray-100"
          >
            {/* Table Header */}
            <div className="grid grid-cols-4 gap-4 p-6 bg-gray-50 border-b border-gray-200">
              <div className="font-semibold text-gray-900">Tính năng</div>
              {plans.map((plan, index) => (
                <div key={index} className="text-center">
                  <div className={`inline-flex items-center gap-2 px-3 py-1 rounded-full text-sm font-semibold ${
                    plan.popular 
                      ? "bg-pink-100 text-pink-600" 
                      : "bg-gray-100 text-gray-600"
                  }`}>
                    {plan.popular && <Star className="h-4 w-4" />}
                    {plan.name}
                  </div>
                  <div className="text-lg font-bold text-gray-900 mt-2">
                    {plan.price}
                  </div>
                </div>
              ))}
            </div>

            {/* Table Body */}
            <div className="divide-y divide-gray-100">
              {comparisonData.map((row, rowIndex) => (
                <motion.div
                  key={rowIndex}
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: rowIndex * 0.05 }}
                  viewport={{ once: true }}
                  className="grid grid-cols-4 gap-4 p-6 hover:bg-gray-50 transition-colors duration-200"
                >
                  <div className="font-medium text-gray-900">
                    {row.feature}
                  </div>
                  <div className="text-center">
                    {renderFeatureValue(row.basic, 0)}
                  </div>
                  <div className="text-center">
                    {renderFeatureValue(row.standard, 1)}
                  </div>
                  <div className="text-center">
                    {renderFeatureValue(row.premium, 2)}
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Bottom Note */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="mt-12 text-center"
          >
            <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 max-w-4xl mx-auto">
              <h3 className="text-xl font-bold text-gray-900 mb-3">
                🎯 Gói nào phù hợp với bạn?
              </h3>
              <div className="grid md:grid-cols-3 gap-6 text-sm">
                <div className="text-center">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <span className="text-blue-600 font-bold">1</span>
                  </div>
                  <h4 className="font-semibold text-gray-900 mb-2">Gói Cơ Bản</h4>
                  <p className="text-gray-600">
                    Phù hợp cho shop nhỏ, khởi nghiệp với ngân sách hạn chế
                  </p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-pink-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <span className="text-pink-600 font-bold">2</span>
                  </div>
                  <h4 className="font-semibold text-gray-900 mb-2">Gói Tiêu Chuẩn</h4>
                  <p className="text-gray-600">
                    Lựa chọn tối ưu cho các doanh nghiệp vừa và nhỏ
                  </p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <span className="text-purple-600 font-bold">3</span>
                  </div>
                  <h4 className="font-semibold text-gray-900 mb-2">Gói Premium</h4>
                  <p className="text-gray-600">
                    Dành cho thương hiệu cao cấp, yêu cầu chất lượng tối đa
                  </p>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
