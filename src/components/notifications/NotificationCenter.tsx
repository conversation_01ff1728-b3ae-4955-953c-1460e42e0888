/**
 * Notification Center Component
 * Real-time notification display with polling integration
 */

"use client";

import React, { useState, useEffect } from "react";
import { useNotifications } from "@/contexts/NotificationContext";

interface NotificationCenterProps {
  className?: string;
  maxNotifications?: number;
  autoHide?: boolean;
  autoHideDelay?: number;
}

interface NotificationItemProps {
  notification: any & { read?: boolean };
  onMarkAsRead: (id: string) => void;
  onRemove: (id: string) => void;
  autoHide?: boolean;
  autoHideDelay?: number;
}

const NotificationItem: React.FC<NotificationItemProps> = ({
  notification,
  onMarkAsRead,
  onRemove,
  autoHide = true,
  autoHideDelay = 5000,
}) => {
  const [isVisible, setIsVisible] = useState(true);
  const [isRemoving, setIsRemoving] = useState(false);

  useEffect(() => {
    if (autoHide && !notification.read && !notification.isRead) {
      const timer = setTimeout(() => {
        handleRemove();
      }, autoHideDelay);

      return () => clearTimeout(timer);
    }
  }, [autoHide, autoHideDelay, notification.read, notification.isRead]);

  const handleMarkAsRead = () => {
    onMarkAsRead(notification.id);
  };

  const handleRemove = () => {
    setIsRemoving(true);
    setTimeout(() => {
      setIsVisible(false);
      onRemove(notification.id);
    }, 300);
  };

  const getCategoryStyles = () => {
    const type = notification.type || notification.category;
    switch (type?.toLowerCase()) {
      case "success":
        return "bg-green-50 border-green-200 text-green-800";
      case "warning":
        return "bg-yellow-50 border-yellow-200 text-yellow-800";
      case "error":
        return "bg-red-50 border-red-200 text-red-800";
      default:
        return "bg-blue-50 border-blue-200 text-blue-800";
    }
  };

  const getCategoryIcon = () => {
    const type = notification.type || notification.category;
    switch (type?.toLowerCase()) {
      case "success":
        return "✓";
      case "warning":
        return "⚠";
      case "error":
        return "✕";
      default:
        return "ℹ";
    }
  };

  if (!isVisible) return null;

  const isRead = notification.read || notification.isRead;

  return (
    <div
      className={`
        relative p-4 mb-3 border rounded-lg shadow-sm transition-all duration-300
        ${getCategoryStyles()}
        ${isRemoving ? "opacity-0 transform translate-x-full" : "opacity-100 transform translate-x-0"}
        ${isRead ? "opacity-60" : ""}
      `}
    >
      {/* Category Icon */}
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0 w-6 h-6 flex items-center justify-center rounded-full bg-white">
          <span className="text-sm font-bold">{getCategoryIcon()}</span>
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          <h4 className="text-sm font-medium mb-1">{notification.title}</h4>
          <p className="text-sm opacity-90">{notification.message}</p>

          {/* Timestamp */}
          <p className="text-xs opacity-60 mt-2">
            {notification.createdAt
              ? new Date(notification.createdAt).toLocaleTimeString()
              : new Date().toLocaleTimeString()}
          </p>
        </div>

        {/* Actions */}
        <div className="flex-shrink-0 flex space-x-2">
          {!isRead && (
            <button
              onClick={handleMarkAsRead}
              className="text-xs px-2 py-1 bg-white bg-opacity-50 rounded hover:bg-opacity-75 transition-colors"
              title="Mark as read"
            >
              ✓
            </button>
          )}

          <button
            onClick={handleRemove}
            className="text-xs px-2 py-1 bg-white bg-opacity-50 rounded hover:bg-opacity-75 transition-colors"
            title="Remove"
          >
            ✕
          </button>
        </div>
      </div>

      {/* Unread indicator */}
      {!isRead && (
        <div className="absolute left-0 top-1/2 transform -translate-y-1/2 w-1 h-8 bg-current rounded-r"></div>
      )}
    </div>
  );
};

const NotificationCenter: React.FC<NotificationCenterProps> = ({
  className = "",
  maxNotifications = 10,
  autoHide = true,
  autoHideDelay = 5000,
}) => {
  const {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    refreshNotifications,
  } = useNotifications();
  const [displayedNotifications, setDisplayedNotifications] = useState<any[]>(
    []
  );

  // Update displayed notifications
  useEffect(() => {
    const latest = notifications
      .slice(0, maxNotifications)
      .map((n) => ({ ...n, read: n.isRead }));

    setDisplayedNotifications(latest);
  }, [notifications, maxNotifications]);

  const handleMarkAsRead = async (notificationId: string) => {
    try {
      await markAsRead(notificationId);
      setDisplayedNotifications((prev) =>
        prev.map((n) =>
          n.id === notificationId ? { ...n, read: true, isRead: true } : n
        )
      );
    } catch (error) {
      console.error("Failed to mark notification as read:", error);
    }
  };

  const handleRemove = (notificationId: string) => {
    setDisplayedNotifications((prev) =>
      prev.filter((n) => n.id !== notificationId)
    );
  };

  const handleClearAll = () => {
    setDisplayedNotifications([]);
  };

  const handleMarkAllAsRead = async () => {
    try {
      await markAllAsRead();
      setDisplayedNotifications((prev) =>
        prev.map((n) => ({ ...n, read: true, isRead: true }))
      );
    } catch (error) {
      console.error("Failed to mark all notifications as read:", error);
    }
  };

  const handleRefresh = () => {
    refreshNotifications();
  };

  if (displayedNotifications.length === 0) {
    return null;
  }

  return (
    <div className={`fixed top-4 right-4 w-96 max-w-full z-50 ${className}`}>
      {/* Header */}
      <div className="bg-white rounded-t-lg border border-b-0 px-4 py-3 flex items-center justify-between shadow-sm">
        <div className="flex items-center space-x-2">
          <h3 className="font-medium text-gray-900">Notifications</h3>
          {unreadCount > 0 && (
            <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full">
              {unreadCount}
            </span>
          )}
        </div>

        <div className="flex space-x-2">
          <button
            onClick={handleRefresh}
            className="text-xs text-gray-500 hover:text-gray-700"
            title="Refresh"
          >
            Refresh
          </button>

          <button
            onClick={handleMarkAllAsRead}
            className="text-xs text-gray-500 hover:text-gray-700"
            title="Mark all as read"
          >
            Mark all read
          </button>

          <button
            onClick={handleClearAll}
            className="text-xs text-gray-500 hover:text-gray-700"
            title="Clear all"
          >
            Clear all
          </button>
        </div>
      </div>

      {/* Notifications List */}
      <div className="bg-white rounded-b-lg border shadow-lg max-h-96 overflow-y-auto">
        <div className="p-2">
          {displayedNotifications.map((notification) => (
            <NotificationItem
              key={notification.id}
              notification={notification}
              onMarkAsRead={handleMarkAsRead}
              onRemove={handleRemove}
              autoHide={autoHide}
              autoHideDelay={autoHideDelay}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default NotificationCenter;
