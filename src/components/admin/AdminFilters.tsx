"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Search, Filter, X } from "lucide-react";

interface FilterField {
  key: string;
  label: string;
  type: "text" | "select" | "date" | "boolean";
  placeholder?: string;
  options?: Array<{ value: string; label: string }>;
  value?: any;
}

interface AdminFiltersProps {
  fields: FilterField[];
  values: Record<string, any>;
  onChange: (key: string, value: any) => void;
  onSubmit: () => void;
  onReset: () => void;
  loading?: boolean;
  className?: string;
}

/**
 * Component filters chung cho admin pages
 */
export function AdminFilters({
  fields,
  values,
  onChange,
  onSubmit,
  onReset,
  loading = false,
  className = "",
}: AdminFiltersProps) {
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit();
  };

  const hasActiveFilters = Object.values(values).some(
    (value) => value !== "" && value !== undefined && value !== null
  );

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Filter className="h-5 w-5" />
          Bộ lọc
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {fields.map((field) => (
              <div key={field.key}>
                {field.type === "text" && (
                  <div className="relative">
                    {field.key === "search" && (
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    )}
                    <input
                      type="text"
                      placeholder={field.placeholder || field.label}
                      value={values[field.key] || ""}
                      onChange={(e) => onChange(field.key, e.target.value)}
                      className={`w-full ${
                        field.key === "search" ? "pl-10" : "pl-3"
                      } pr-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent`}
                    />
                  </div>
                )}

                {field.type === "select" && (
                  <select
                    value={values[field.key] || ""}
                    onChange={(e) => onChange(field.key, e.target.value)}
                    className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                  >
                    <option value="">{field.placeholder || `Tất cả ${field.label.toLowerCase()}`}</option>
                    {field.options?.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                )}

                {field.type === "date" && (
                  <input
                    type="date"
                    value={values[field.key] || ""}
                    onChange={(e) => onChange(field.key, e.target.value)}
                    className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                  />
                )}

                {field.type === "boolean" && (
                  <select
                    value={values[field.key] || ""}
                    onChange={(e) => onChange(field.key, e.target.value)}
                    className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                  >
                    <option value="">{field.placeholder || `Tất cả`}</option>
                    <option value="true">Có</option>
                    <option value="false">Không</option>
                  </select>
                )}
              </div>
            ))}
          </div>

          <div className="flex items-center gap-2">
            <Button
              type="submit"
              className="bg-pink-600 hover:bg-pink-700"
              disabled={loading}
            >
              <Filter className="h-4 w-4 mr-2" />
              Lọc
            </Button>

            {hasActiveFilters && (
              <Button
                type="button"
                variant="outline"
                onClick={onReset}
                disabled={loading}
              >
                <X className="h-4 w-4 mr-2" />
                Xóa bộ lọc
              </Button>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  );
}

/**
 * Quick filters cho các trạng thái phổ biến
 */
interface QuickFilter {
  label: string;
  value: string;
  count?: number;
}

interface AdminQuickFiltersProps {
  filters: QuickFilter[];
  activeFilter: string;
  onChange: (value: string) => void;
  className?: string;
}

export function AdminQuickFilters({
  filters,
  activeFilter,
  onChange,
  className = "",
}: AdminQuickFiltersProps) {
  return (
    <div className={`flex flex-wrap gap-2 ${className}`}>
      {filters.map((filter) => (
        <Button
          key={filter.value}
          variant={activeFilter === filter.value ? "default" : "outline"}
          size="sm"
          onClick={() => onChange(filter.value)}
          className={
            activeFilter === filter.value
              ? "bg-pink-600 hover:bg-pink-700"
              : ""
          }
        >
          {filter.label}
          {filter.count !== undefined && (
            <span className="ml-1 text-xs opacity-75">({filter.count})</span>
          )}
        </Button>
      ))}
    </div>
  );
}
