/**
 * Admin Metrics Dashboard Component
 * Real-time performance monitoring and metrics visualization
 */

'use client';

import React, { useState, useEffect } from 'react';
import { useAdminWebSocket } from '@/hooks/useWebSocket';

interface MetricData {
  name: string;
  value: number;
  type: 'counter' | 'gauge' | 'histogram' | 'timer';
  tags?: Record<string, string>;
  timestamp: string;
}

interface DashboardStats {
  totalRequests: number;
  errorRate: number;
  avgResponseTime: number;
  activeConnections: number;
  cacheHitRate: number;
  memoryUsage: number;
  uptime: number;
}

interface MetricsDashboardProps {
  adminToken?: string;
  refreshInterval?: number;
  className?: string;
}

const MetricCard: React.FC<{
  title: string;
  value: string | number;
  unit?: string;
  trend?: 'up' | 'down' | 'stable';
  color?: 'green' | 'red' | 'blue' | 'yellow';
  className?: string;
}> = ({ title, value, unit = '', trend, color = 'blue', className = '' }) => {
  const getColorClasses = () => {
    switch (color) {
      case 'green':
        return 'bg-green-50 border-green-200 text-green-800';
      case 'red':
        return 'bg-red-50 border-red-200 text-red-800';
      case 'yellow':
        return 'bg-yellow-50 border-yellow-200 text-yellow-800';
      default:
        return 'bg-blue-50 border-blue-200 text-blue-800';
    }
  };

  const getTrendIcon = () => {
    switch (trend) {
      case 'up':
        return '↗';
      case 'down':
        return '↘';
      default:
        return '→';
    }
  };

  return (
    <div className={`p-4 rounded-lg border ${getColorClasses()} ${className}`}>
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-medium opacity-80">{title}</h3>
        {trend && (
          <span className="text-lg opacity-60">{getTrendIcon()}</span>
        )}
      </div>
      <div className="mt-2">
        <span className="text-2xl font-bold">{value}</span>
        {unit && <span className="text-sm opacity-60 ml-1">{unit}</span>}
      </div>
    </div>
  );
};

const MetricsDashboard: React.FC<MetricsDashboardProps> = ({
  adminToken,
  refreshInterval = 5000,
  className = ''
}) => {
  const [stats, setStats] = useState<DashboardStats>({
    totalRequests: 0,
    errorRate: 0,
    avgResponseTime: 0,
    activeConnections: 0,
    cacheHitRate: 0,
    memoryUsage: 0,
    uptime: 0
  });

  const [recentMetrics, setRecentMetrics] = useState<MetricData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const { connected, send } = useAdminWebSocket(adminToken);

  // Fetch metrics data
  const fetchMetrics = async () => {
    try {
      const response = await fetch('/api/admin/metrics', {
        headers: {
          'Authorization': `Bearer ${adminToken}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch metrics');
      }

      const data = await response.json();
      
      if (data.success) {
        setStats(data.data.summary);
        setRecentMetrics(data.data.recentMetrics || []);
        setError(null);
      } else {
        setError(data.error || 'Failed to load metrics');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setIsLoading(false);
    }
  };

  // Test services
  const testService = async (service: string, data: any = {}) => {
    try {
      const response = await fetch('/api/admin/metrics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${adminToken}`
        },
        body: JSON.stringify({
          action: `test_${service}`,
          data
        })
      });

      const result = await response.json();
      
      if (result.success) {
        alert(`${service} test successful!`);
      } else {
        alert(`${service} test failed: ${result.error}`);
      }
    } catch (err) {
      alert(`${service} test error: ${err}`);
    }
  };

  // Auto-refresh metrics
  useEffect(() => {
    fetchMetrics();
    
    const interval = setInterval(fetchMetrics, refreshInterval);
    return () => clearInterval(interval);
  }, [adminToken, refreshInterval]);

  // Format uptime
  const formatUptime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  };

  // Format percentage
  const formatPercentage = (value: number) => {
    return `${(value * 100).toFixed(1)}%`;
  };

  // Format memory
  const formatMemory = (bytes: number) => {
    const mb = bytes / (1024 * 1024);
    return `${mb.toFixed(1)} MB`;
  };

  if (isLoading) {
    return (
      <div className={`p-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {[...Array(8)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-gray-900">Performance Metrics</h2>
        
        <div className="flex items-center space-x-4">
          {/* Connection Status */}
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${connected ? 'bg-green-500' : 'bg-red-500'}`}></div>
            <span className="text-sm text-gray-600">
              {connected ? 'Live Updates' : 'Offline'}
            </span>
          </div>
          
          {/* Refresh Button */}
          <button
            onClick={fetchMetrics}
            className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors text-sm"
          >
            Refresh
          </button>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
          Error: {error}
        </div>
      )}

      {/* Main Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <MetricCard
          title="Total Requests"
          value={stats.totalRequests.toLocaleString()}
          color="blue"
          trend="up"
        />
        
        <MetricCard
          title="Error Rate"
          value={formatPercentage(stats.errorRate)}
          color={stats.errorRate > 0.05 ? 'red' : 'green'}
          trend={stats.errorRate > 0.05 ? 'up' : 'down'}
        />
        
        <MetricCard
          title="Avg Response Time"
          value={stats.avgResponseTime}
          unit="ms"
          color={stats.avgResponseTime > 1000 ? 'red' : stats.avgResponseTime > 500 ? 'yellow' : 'green'}
        />
        
        <MetricCard
          title="Active Connections"
          value={stats.activeConnections}
          color="blue"
        />
        
        <MetricCard
          title="Cache Hit Rate"
          value={formatPercentage(stats.cacheHitRate)}
          color={stats.cacheHitRate > 0.8 ? 'green' : stats.cacheHitRate > 0.6 ? 'yellow' : 'red'}
        />
        
        <MetricCard
          title="Memory Usage"
          value={formatMemory(stats.memoryUsage)}
          color={stats.memoryUsage > 500 * 1024 * 1024 ? 'red' : 'green'}
        />
        
        <MetricCard
          title="Uptime"
          value={formatUptime(stats.uptime)}
          color="green"
        />
        
        <MetricCard
          title="System Status"
          value="Healthy"
          color="green"
        />
      </div>

      {/* Service Tests */}
      <div className="bg-white rounded-lg border p-6 mb-8">
        <h3 className="text-lg font-semibold mb-4">Service Tests</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <button
            onClick={() => testService('websocket')}
            className="p-3 bg-blue-50 border border-blue-200 rounded hover:bg-blue-100 transition-colors"
          >
            <div className="text-sm font-medium text-blue-800">Test WebSocket</div>
            <div className="text-xs text-blue-600">Send test notification</div>
          </button>
          
          <button
            onClick={() => testService('email', { email: '<EMAIL>' })}
            className="p-3 bg-green-50 border border-green-200 rounded hover:bg-green-100 transition-colors"
          >
            <div className="text-sm font-medium text-green-800">Test Email</div>
            <div className="text-xs text-green-600">Send test email</div>
          </button>
          
          <button
            onClick={() => testService('cache')}
            className="p-3 bg-purple-50 border border-purple-200 rounded hover:bg-purple-100 transition-colors"
          >
            <div className="text-sm font-medium text-purple-800">Test Cache</div>
            <div className="text-xs text-purple-600">Cache operations</div>
          </button>
          
          <button
            onClick={() => testService('rate_limit')}
            className="p-3 bg-orange-50 border border-orange-200 rounded hover:bg-orange-100 transition-colors"
          >
            <div className="text-sm font-medium text-orange-800">Test Rate Limit</div>
            <div className="text-xs text-orange-600">Rate limiting check</div>
          </button>
        </div>
      </div>

      {/* Recent Metrics */}
      <div className="bg-white rounded-lg border p-6">
        <h3 className="text-lg font-semibold mb-4">Recent Metrics</h3>
        
        {recentMetrics.length > 0 ? (
          <div className="space-y-2 max-h-64 overflow-y-auto">
            {recentMetrics.map((metric, index) => (
              <div key={index} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                <div className="flex items-center space-x-3">
                  <span className="text-sm font-medium">{metric.name}</span>
                  <span className="text-xs bg-gray-100 px-2 py-1 rounded">{metric.type}</span>
                </div>
                
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-mono">{metric.value}</span>
                  <span className="text-xs text-gray-500">
                    {new Date(metric.timestamp).toLocaleTimeString()}
                  </span>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-gray-500 text-sm">No recent metrics available</p>
        )}
      </div>
    </div>
  );
};

export default MetricsDashboard;
