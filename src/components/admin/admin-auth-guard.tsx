"use client";

import { useAdminAuth } from "@/contexts/AdminAuthContext";

interface AdminAuthGuardProps {
  children: React.ReactNode;
}

export default function AdminAuthGuard({ children }: AdminAuthGuardProps) {
  const { isAuthenticated, isLoading, error } = useAdminAuth();

  // Show loading spinner only when actually loading (first time or when cache is invalid)
  if (isLoading) {
    return (
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "100vh",
          backgroundColor: "#f8f9fa",
        }}
      >
        <div
          style={{
            padding: "20px",
            backgroundColor: "white",
            borderRadius: "8px",
            boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
          }}
        >
          <p>Đang kiểm tra quyền truy cập...</p>
          {error && (
            <p style={{ color: "red", fontSize: "14px", marginTop: "8px" }}>
              {error}
            </p>
          )}
        </div>
      </div>
    );
  }

  // Show nothing while redirecting (if not authenticated)
  if (!isAuthenticated) {
    return null;
  }

  // Render admin content if authenticated
  return <>{children}</>;
}
