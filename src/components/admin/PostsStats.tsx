'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  FileText, 
  Eye, 
  Edit, 
  Star,
  TrendingUp,
  Calendar,
  Users,
  MessageSquare
} from 'lucide-react';

interface PostsStatsData {
  total: number;
  published: number;
  draft: number;
  archived: number;
  featured: number;
}

interface PostsStatsProps {
  className?: string;
}

export function PostsStats({ className }: PostsStatsProps) {
  const [stats, setStats] = useState<PostsStatsData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await fetch('/api/admin/posts/stats');
        if (response.ok) {
          const result = await response.json();
          if (result.success) {
            setStats(result.data);
          }
        }
      } catch (error) {
        console.error('Error fetching posts stats:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  if (loading) {
    return (
      <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 ${className}`}>
        {Array.from({ length: 5 }).map((_, i) => (
          <Card key={i}>
            <CardContent className="p-4">
              <div className="animate-pulse">
                <div className="flex items-center gap-2">
                  <div className="w-5 h-5 bg-gray-200 rounded" />
                  <div className="space-y-2 flex-1">
                    <div className="h-3 bg-gray-200 rounded w-3/4" />
                    <div className="h-6 bg-gray-200 rounded w-1/2" />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (!stats) {
    return null;
  }

  const statsCards = [
    {
      title: 'Tổng bài viết',
      value: stats.total,
      icon: FileText,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
    },
    {
      title: 'Đã xuất bản',
      value: stats.published,
      icon: Eye,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
    },
    {
      title: 'Bản nháp',
      value: stats.draft,
      icon: Edit,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100',
    },
    {
      title: 'Đã lưu trữ',
      value: stats.archived,
      icon: Calendar,
      color: 'text-gray-600',
      bgColor: 'bg-gray-100',
    },
    {
      title: 'Nổi bật',
      value: stats.featured,
      icon: Star,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
    },
  ];

  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 ${className}`}>
      {statsCards.map((stat, index) => {
        const Icon = stat.icon;
        return (
          <Card key={index}>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${stat.bgColor}`}>
                  <Icon className={`h-5 w-5 ${stat.color}`} />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">{stat.title}</p>
                  <p className="text-2xl font-bold">{stat.value}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}

// Quick Actions Component
export function PostsQuickActions() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Hành động nhanh</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="p-4 border rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-pink-100 rounded-lg flex items-center justify-center">
                <FileText className="h-5 w-5 text-pink-600" />
              </div>
              <div>
                <h3 className="font-medium">Viết bài mới</h3>
                <p className="text-sm text-muted-foreground">Tạo bài viết mới</p>
              </div>
            </div>
          </div>

          <div className="p-4 border rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <TrendingUp className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <h3 className="font-medium">Phân tích</h3>
                <p className="text-sm text-muted-foreground">Xem thống kê chi tiết</p>
              </div>
            </div>
          </div>

          <div className="p-4 border rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                <Users className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <h3 className="font-medium">Quản lý tác giả</h3>
                <p className="text-sm text-muted-foreground">Phân quyền viết bài</p>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Recent Activity Component
export function PostsRecentActivity() {
  const [activities, setActivities] = useState([
    {
      id: 1,
      type: 'create',
      title: 'Tạo bài viết mới',
      description: 'Xu hướng thời trang mùa hè 2024',
      time: '2 phút trước',
      icon: FileText,
      color: 'text-green-600',
    },
    {
      id: 2,
      type: 'publish',
      title: 'Xuất bản bài viết',
      description: 'Cách phối đồ công sở thanh lịch',
      time: '1 giờ trước',
      icon: Eye,
      color: 'text-blue-600',
    },
    {
      id: 3,
      type: 'comment',
      title: 'Bình luận mới',
      description: 'Bí quyết chọn giày phù hợp',
      time: '3 giờ trước',
      icon: MessageSquare,
      color: 'text-purple-600',
    },
  ]);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Hoạt động gần đây</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {activities.map((activity) => {
            const Icon = activity.icon;
            return (
              <div key={activity.id} className="flex items-start gap-3">
                <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center flex-shrink-0">
                  <Icon className={`h-4 w-4 ${activity.color}`} />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium">{activity.title}</p>
                  <p className="text-sm text-muted-foreground truncate">
                    {activity.description}
                  </p>
                  <p className="text-xs text-muted-foreground mt-1">
                    {activity.time}
                  </p>
                </div>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}
