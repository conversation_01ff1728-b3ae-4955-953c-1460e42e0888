"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Plus,
  Edit,
  Trash2,
  MoreHorizontal,
  GripVertical,
  Save,
  X,
  AlertCircle,
  Package,
} from "lucide-react";
import { toast } from "sonner";
import { AttributeValue, AttributeValueFormData } from "@/types/attribute";

interface AttributeValueManagerProps {
  attributeId: string;
  attributeName: string;
  values: AttributeValue[];
  onValuesChange: (values: AttributeValue[]) => void;
  loading?: boolean;
}

interface ValueFormData {
  value: string;
  slug: string;
  sortOrder: number;
}

export function AttributeValueManager({
  attributeId,
  attributeName,
  values,
  onValuesChange,
  loading = false,
}: AttributeValueManagerProps) {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingValue, setEditingValue] = useState<AttributeValue | null>(null);
  const [deletingValue, setDeletingValue] = useState<AttributeValue | null>(null);
  const [formData, setFormData] = useState<ValueFormData>({
    value: "",
    slug: "",
    sortOrder: 0,
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);

  const resetForm = () => {
    setFormData({
      value: "",
      slug: "",
      sortOrder: values.length + 1,
    });
    setErrors({});
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.value.trim()) {
      newErrors.value = "Giá trị là bắt buộc";
    }

    if (!formData.slug.trim()) {
      newErrors.slug = "Slug là bắt buộc";
    }

    // Check for duplicate values
    const existingValue = values.find(
      (v) =>
        v.value.toLowerCase() === formData.value.toLowerCase() &&
        v.id !== editingValue?.id
    );
    if (existingValue) {
      newErrors.value = "Giá trị này đã tồn tại";
    }

    // Check for duplicate slugs
    const existingSlug = values.find(
      (v) =>
        v.slug.toLowerCase() === formData.slug.toLowerCase() &&
        v.id !== editingValue?.id
    );
    if (existingSlug) {
      newErrors.slug = "Slug này đã tồn tại";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleCreate = async () => {
    if (!validateForm()) return;

    try {
      const response = await fetch(`/api/admin/attributes/${attributeId}/values`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        const newValue = await response.json();
        onValuesChange([...values, newValue]);
        setIsCreateDialogOpen(false);
        resetForm();
        toast.success("Tạo giá trị thành công");
      } else {
        const error = await response.json();
        toast.error(error.error || "Có lỗi xảy ra khi tạo giá trị");
      }
    } catch (error) {
      console.error("Error creating value:", error);
      toast.error("Có lỗi xảy ra khi tạo giá trị");
    }
  };

  const handleEdit = async () => {
    if (!editingValue || !validateForm()) return;

    try {
      const response = await fetch(
        `/api/admin/attributes/${attributeId}/values/${editingValue.id}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(formData),
        }
      );

      if (response.ok) {
        const updatedValue = await response.json();
        onValuesChange(
          values.map((v) => (v.id === editingValue.id ? updatedValue : v))
        );
        setEditingValue(null);
        resetForm();
        toast.success("Cập nhật giá trị thành công");
      } else {
        const error = await response.json();
        toast.error(error.error || "Có lỗi xảy ra khi cập nhật giá trị");
      }
    } catch (error) {
      console.error("Error updating value:", error);
      toast.error("Có lỗi xảy ra khi cập nhật giá trị");
    }
  };

  const handleDelete = async () => {
    if (!deletingValue) return;

    try {
      const response = await fetch(
        `/api/admin/attributes/${attributeId}/values/${deletingValue.id}`,
        {
          method: "DELETE",
        }
      );

      if (response.ok) {
        onValuesChange(values.filter((v) => v.id !== deletingValue.id));
        setDeletingValue(null);
        toast.success("Xóa giá trị thành công");
      } else {
        const error = await response.json();
        toast.error(error.error || "Có lỗi xảy ra khi xóa giá trị");
      }
    } catch (error) {
      console.error("Error deleting value:", error);
      toast.error("Có lỗi xảy ra khi xóa giá trị");
    }
  };

  const handleReorder = async (newValues: AttributeValue[]) => {
    try {
      const response = await fetch(
        `/api/admin/attributes/${attributeId}/values/reorder`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            values: newValues.map((v, index) => ({
              id: v.id,
              sortOrder: index + 1,
            })),
          }),
        }
      );

      if (response.ok) {
        onValuesChange(newValues);
        toast.success("Cập nhật thứ tự thành công");
      } else {
        toast.error("Có lỗi xảy ra khi cập nhật thứ tự");
      }
    } catch (error) {
      console.error("Error reordering values:", error);
      toast.error("Có lỗi xảy ra khi cập nhật thứ tự");
    }
  };

  const openCreateDialog = () => {
    resetForm();
    setIsCreateDialogOpen(true);
  };

  const openEditDialog = (value: AttributeValue) => {
    setFormData({
      value: value.value,
      slug: value.slug,
      sortOrder: value.sortOrder,
    });
    setEditingValue(value);
    setErrors({});
  };

  const handleDragStart = (index: number) => {
    setDraggedIndex(index);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDrop = (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault();

    if (draggedIndex === null || draggedIndex === dropIndex) return;

    const newValues = [...values];
    const draggedItem = newValues[draggedIndex];

    newValues.splice(draggedIndex, 1);
    newValues.splice(dropIndex, 0, draggedItem);

    // Update sort orders
    const updatedValues = newValues.map((value, index) => ({
      ...value,
      sortOrder: index + 1,
    }));

    handleReorder(updatedValues);
    setDraggedIndex(null);
  };

  // Auto-generate slug from value
  const handleValueChange = (value: string) => {
    const slug = value
      .toLowerCase()
      .replace(/\s+/g, "-")
      .replace(/[àáạảãâầấậẩẫăằắặẳẵ]/g, "a")
      .replace(/[èéẹẻẽêềếệểễ]/g, "e")
      .replace(/[ìíịỉĩ]/g, "i")
      .replace(/[òóọỏõôồốộổỗơờớợởỡ]/g, "o")
      .replace(/[ùúụủũưừứựửữ]/g, "u")
      .replace(/[ỳýỵỷỹ]/g, "y")
      .replace(/[đ]/g, "d")
      .replace(/[^a-z0-9-]/g, "");

    setFormData((prev) => ({ ...prev, value, slug }));
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Quản lý giá trị</CardTitle>
          <p className="text-sm text-muted-foreground">
            Thuộc tính: {attributeName}
          </p>
        </div>
        <Button onClick={openCreateDialog} size="sm">
          <Plus className="h-4 w-4 mr-2" />
          Thêm giá trị
        </Button>
      </CardHeader>
      <CardContent>
        {values.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Package className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p className="text-lg font-medium mb-2">Chưa có giá trị nào</p>
            <p className="text-sm mb-4">
              Thêm giá trị đầu tiên cho thuộc tính này
            </p>
            <Button onClick={openCreateDialog} size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Thêm giá trị
            </Button>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-8"></TableHead>
                <TableHead>Giá trị</TableHead>
                <TableHead>Slug</TableHead>
                <TableHead>Thứ tự</TableHead>
                <TableHead>Sản phẩm</TableHead>
                <TableHead className="w-8"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {values
                .sort((a, b) => a.sortOrder - b.sortOrder)
                .map((value, index) => (
                  <TableRow
                    key={value.id}
                    draggable
                    onDragStart={() => handleDragStart(index)}
                    onDragOver={handleDragOver}
                    onDrop={(e) => handleDrop(e, index)}
                    className="cursor-move"
                  >
                    <TableCell>
                      <GripVertical className="h-4 w-4 text-gray-400" />
                    </TableCell>
                    <TableCell>
                      <div className="font-medium">{value.value}</div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{value.slug}</Badge>
                    </TableCell>
                    <TableCell>{value.sortOrder}</TableCell>
                    <TableCell>
                      <Badge variant="secondary">
                        {value._count?.products || 0}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => openEditDialog(value)}>
                            <Edit className="h-4 w-4 mr-2" />
                            Chỉnh sửa
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => setDeletingValue(value)}
                            className="text-red-600"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Xóa
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
            </TableBody>
          </Table>
        )}
      </CardContent>

      {/* Create Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Thêm giá trị mới</DialogTitle>
            <DialogDescription>
              Thêm giá trị mới cho thuộc tính "{attributeName}"
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="create-value">
                Giá trị <span className="text-red-500">*</span>
              </Label>
              <Input
                id="create-value"
                value={formData.value}
                onChange={(e) => handleValueChange(e.target.value)}
                placeholder="Nhập giá trị..."
                className={errors.value ? "border-red-500" : ""}
              />
              {errors.value && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertCircle className="h-4 w-4" />
                  {errors.value}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="create-slug">
                Slug <span className="text-red-500">*</span>
              </Label>
              <Input
                id="create-slug"
                value={formData.slug}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, slug: e.target.value }))
                }
                placeholder="slug-gia-tri"
                className={errors.slug ? "border-red-500" : ""}
              />
              {errors.slug && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertCircle className="h-4 w-4" />
                  {errors.slug}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="create-sortOrder">Thứ tự sắp xếp</Label>
              <Input
                id="create-sortOrder"
                type="number"
                value={formData.sortOrder}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    sortOrder: parseInt(e.target.value) || 0,
                  }))
                }
                min="0"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsCreateDialogOpen(false)}
            >
              Hủy
            </Button>
            <Button onClick={handleCreate} disabled={loading}>
              <Save className="h-4 w-4 mr-2" />
              Tạo giá trị
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Dialog */}
      <Dialog open={!!editingValue} onOpenChange={() => setEditingValue(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Chỉnh sửa giá trị</DialogTitle>
            <DialogDescription>
              Chỉnh sửa giá trị cho thuộc tính "{attributeName}"
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="edit-value">
                Giá trị <span className="text-red-500">*</span>
              </Label>
              <Input
                id="edit-value"
                value={formData.value}
                onChange={(e) => handleValueChange(e.target.value)}
                placeholder="Nhập giá trị..."
                className={errors.value ? "border-red-500" : ""}
              />
              {errors.value && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertCircle className="h-4 w-4" />
                  {errors.value}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-slug">
                Slug <span className="text-red-500">*</span>
              </Label>
              <Input
                id="edit-slug"
                value={formData.slug}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, slug: e.target.value }))
                }
                placeholder="slug-gia-tri"
                className={errors.slug ? "border-red-500" : ""}
              />
              {errors.slug && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertCircle className="h-4 w-4" />
                  {errors.slug}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-sortOrder">Thứ tự sắp xếp</Label>
              <Input
                id="edit-sortOrder"
                type="number"
                value={formData.sortOrder}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    sortOrder: parseInt(e.target.value) || 0,
                  }))
                }
                min="0"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setEditingValue(null)}
            >
              Hủy
            </Button>
            <Button onClick={handleEdit} disabled={loading}>
              <Save className="h-4 w-4 mr-2" />
              Cập nhật
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={!!deletingValue} onOpenChange={() => setDeletingValue(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Xác nhận xóa</DialogTitle>
            <DialogDescription>
              Bạn có chắc chắn muốn xóa giá trị "{deletingValue?.value}"?
              {deletingValue?._count?.products && deletingValue._count.products > 0 && (
                <span className="block mt-2 text-red-600">
                  Cảnh báo: Giá trị này đang được sử dụng bởi{" "}
                  {deletingValue._count.products} sản phẩm.
                </span>
              )}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setDeletingValue(null)}
            >
              Hủy
            </Button>
            <Button
              onClick={handleDelete}
              variant="destructive"
              disabled={loading}
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Xóa
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
}
