"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command";
import { Check, ChevronsUpDown, X, Palette, Ruler, Tag } from "lucide-react";
import { cn } from "@/lib/utils";
import {
  Attribute,
  AttributeValue,
  AttributeType,
  ATTRIBUTE_TYPE_LABELS,
} from "@/types/attribute";

interface AttributeSelectorProps {
  attributes: Attribute[];
  selectedValues: Record<string, string | string[]>;
  onSelectionChange: (attributeId: string, value: string | string[] | null) => void;
  mode?: "single" | "multiple";
  showRequired?: boolean;
  className?: string;
}

interface AttributeInputProps {
  attribute: Attribute;
  value: string | string[] | null;
  onChange: (value: string | string[] | null) => void;
  mode?: "single" | "multiple";
}

function AttributeInput({ attribute, value, onChange, mode = "single" }: AttributeInputProps) {
  const [open, setOpen] = useState(false);
  const [inputValue, setInputValue] = useState("");

  const renderColorValue = (colorValue: AttributeValue) => (
    <div className="flex items-center gap-2">
      <div
        className="w-4 h-4 rounded border border-gray-300"
        style={{
          backgroundColor: getColorCode(colorValue.value),
        }}
      />
      <span>{colorValue.value}</span>
    </div>
  );

  const getColorCode = (colorName: string): string => {
    const colorMap: Record<string, string> = {
      "đỏ": "#ef4444",
      "xanh dương": "#3b82f6",
      "xanh lá": "#22c55e",
      "vàng": "#eab308",
      "đen": "#000000",
      "trắng": "#ffffff",
      "hồng": "#ec4899",
      "tím": "#a855f7",
      "xám": "#6b7280",
      "nâu": "#a3a3a3",
      "cam": "#f97316",
      "be": "#f5f5dc",
      "navy": "#1e3a8a",
      "khaki": "#f0e68c",
    };
    return colorMap[colorName.toLowerCase()] || "#6b7280";
  };

  const renderSizeValue = (sizeValue: AttributeValue) => (
    <div className="flex items-center gap-2">
      <Ruler className="w-4 h-4 text-gray-500" />
      <span className="font-medium">{sizeValue.value}</span>
    </div>
  );

  const getSelectedValues = (): AttributeValue[] => {
    if (!value) return [];
    
    if (Array.isArray(value)) {
      return attribute.values?.filter(v => value.includes(v.id)) || [];
    } else {
      const selectedValue = attribute.values?.find(v => v.id === value);
      return selectedValue ? [selectedValue] : [];
    }
  };

  const isSelected = (valueId: string): boolean => {
    if (!value) return false;
    
    if (Array.isArray(value)) {
      return value.includes(valueId);
    } else {
      return value === valueId;
    }
  };

  const handleSelect = (valueId: string) => {
    if (mode === "multiple" || attribute.type === "MULTI_SELECT") {
      const currentValues = Array.isArray(value) ? value : [];
      
      if (currentValues.includes(valueId)) {
        const newValues = currentValues.filter(id => id !== valueId);
        onChange(newValues.length > 0 ? newValues : null);
      } else {
        onChange([...currentValues, valueId]);
      }
    } else {
      onChange(value === valueId ? null : valueId);
      setOpen(false);
    }
  };

  const handleRemove = (valueId: string) => {
    if (Array.isArray(value)) {
      const newValues = value.filter(id => id !== valueId);
      onChange(newValues.length > 0 ? newValues : null);
    } else {
      onChange(null);
    }
  };

  switch (attribute.type) {
    case "TEXT":
      return (
        <div className="space-y-2">
          <Label htmlFor={attribute.id}>
            {attribute.name}
            {attribute.isRequired && <span className="text-red-500 ml-1">*</span>}
          </Label>
          <Input
            id={attribute.id}
            value={typeof value === "string" ? value : ""}
            onChange={(e) => onChange(e.target.value || null)}
            placeholder={`Nhập ${attribute.name.toLowerCase()}...`}
          />
        </div>
      );

    case "NUMBER":
      return (
        <div className="space-y-2">
          <Label htmlFor={attribute.id}>
            {attribute.name}
            {attribute.isRequired && <span className="text-red-500 ml-1">*</span>}
          </Label>
          <Input
            id={attribute.id}
            type="number"
            value={typeof value === "string" ? value : ""}
            onChange={(e) => onChange(e.target.value || null)}
            placeholder={`Nhập ${attribute.name.toLowerCase()}...`}
          />
        </div>
      );

    case "BOOLEAN":
      return (
        <div className="flex items-center space-x-2">
          <Switch
            id={attribute.id}
            checked={value === "true"}
            onCheckedChange={(checked) => onChange(checked ? "true" : "false")}
          />
          <Label htmlFor={attribute.id}>
            {attribute.name}
            {attribute.isRequired && <span className="text-red-500 ml-1">*</span>}
          </Label>
        </div>
      );

    case "COLOR":
    case "SIZE":
    case "SELECT":
    case "MULTI_SELECT":
      const isMultiple = mode === "multiple" || attribute.type === "MULTI_SELECT";
      const selectedValues = getSelectedValues();

      return (
        <div className="space-y-2">
          <Label>
            {attribute.name}
            {attribute.isRequired && <span className="text-red-500 ml-1">*</span>}
          </Label>
          
          {/* Selected values display */}
          {selectedValues.length > 0 && (
            <div className="flex flex-wrap gap-2 mb-2">
              {selectedValues.map((selectedValue) => (
                <Badge
                  key={selectedValue.id}
                  variant="secondary"
                  className="flex items-center gap-1"
                >
                  {attribute.type === "COLOR" && renderColorValue(selectedValue)}
                  {attribute.type === "SIZE" && renderSizeValue(selectedValue)}
                  {(attribute.type === "SELECT" || attribute.type === "MULTI_SELECT") && (
                    <span>{selectedValue.value}</span>
                  )}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-4 w-4 p-0 hover:bg-transparent"
                    onClick={() => handleRemove(selectedValue.id)}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </Badge>
              ))}
            </div>
          )}

          <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                role="combobox"
                aria-expanded={open}
                className="w-full justify-between"
              >
                {selectedValues.length > 0
                  ? `${selectedValues.length} đã chọn`
                  : `Chọn ${attribute.name.toLowerCase()}...`}
                <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-full p-0">
              <Command>
                <CommandInput
                  placeholder={`Tìm ${attribute.name.toLowerCase()}...`}
                  value={inputValue}
                  onValueChange={setInputValue}
                />
                <CommandEmpty>Không tìm thấy kết quả.</CommandEmpty>
                <CommandGroup className="max-h-64 overflow-auto">
                  {attribute.values
                    ?.filter((attrValue) =>
                      attrValue.value.toLowerCase().includes(inputValue.toLowerCase())
                    )
                    .sort((a, b) => a.sortOrder - b.sortOrder)
                    .map((attrValue) => (
                      <CommandItem
                        key={attrValue.id}
                        value={attrValue.value}
                        onSelect={() => handleSelect(attrValue.id)}
                        className="flex items-center gap-2"
                      >
                        <div className="flex items-center gap-2 flex-1">
                          {attribute.type === "COLOR" && (
                            <div
                              className="w-4 h-4 rounded border border-gray-300"
                              style={{
                                backgroundColor: getColorCode(attrValue.value),
                              }}
                            />
                          )}
                          {attribute.type === "SIZE" && (
                            <Ruler className="w-4 h-4 text-gray-500" />
                          )}
                          <span>{attrValue.value}</span>
                        </div>
                        <Check
                          className={cn(
                            "h-4 w-4",
                            isSelected(attrValue.id) ? "opacity-100" : "opacity-0"
                          )}
                        />
                      </CommandItem>
                    ))}
                </CommandGroup>
              </Command>
            </PopoverContent>
          </Popover>

          {attribute.description && (
            <p className="text-sm text-muted-foreground">{attribute.description}</p>
          )}
        </div>
      );

    default:
      return null;
  }
}

export function AttributeSelector({
  attributes,
  selectedValues,
  onSelectionChange,
  mode = "single",
  showRequired = true,
  className,
}: AttributeSelectorProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [filterType, setFilterType] = useState<AttributeType | "all">("all");
  const [showOnlyRequired, setShowOnlyRequired] = useState(false);

  const filteredAttributes = attributes.filter((attribute) => {
    const matchesSearch = attribute.name
      .toLowerCase()
      .includes(searchTerm.toLowerCase());
    const matchesType = filterType === "all" || attribute.type === filterType;
    const matchesRequired = !showOnlyRequired || attribute.isRequired;

    return matchesSearch && matchesType && matchesRequired;
  });

  const requiredAttributes = attributes.filter((attr) => attr.isRequired);
  const optionalAttributes = attributes.filter((attr) => !attr.isRequired);

  return (
    <div className={cn("space-y-6", className)}>
      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Bộ lọc thuộc tính</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="search">Tìm kiếm</Label>
              <Input
                id="search"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Tìm thuộc tính..."
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="type-filter">Loại thuộc tính</Label>
              <Select
                value={filterType}
                onValueChange={(value: AttributeType | "all") => setFilterType(value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tất cả</SelectItem>
                  {Object.entries(ATTRIBUTE_TYPE_LABELS).map(([value, label]) => (
                    <SelectItem key={value} value={value}>
                      {label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center space-x-2 pt-6">
              <Switch
                id="required-only"
                checked={showOnlyRequired}
                onCheckedChange={setShowOnlyRequired}
              />
              <Label htmlFor="required-only">Chỉ thuộc tính bắt buộc</Label>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Required Attributes */}
      {showRequired && requiredAttributes.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <span className="text-red-500">*</span>
              Thuộc tính bắt buộc
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {requiredAttributes
              .filter((attr) => filteredAttributes.includes(attr))
              .map((attribute) => (
                <AttributeInput
                  key={attribute.id}
                  attribute={attribute}
                  value={selectedValues[attribute.id] || null}
                  onChange={(value) => onSelectionChange(attribute.id, value)}
                  mode={mode}
                />
              ))}
          </CardContent>
        </Card>
      )}

      {/* Optional Attributes */}
      {optionalAttributes.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Thuộc tính tùy chọn</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {optionalAttributes
              .filter((attr) => filteredAttributes.includes(attr))
              .map((attribute) => (
                <AttributeInput
                  key={attribute.id}
                  attribute={attribute}
                  value={selectedValues[attribute.id] || null}
                  onChange={(value) => onSelectionChange(attribute.id, value)}
                  mode={mode}
                />
              ))}
          </CardContent>
        </Card>
      )}

      {/* No results */}
      {filteredAttributes.length === 0 && (
        <Card>
          <CardContent className="text-center py-8">
            <Tag className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p className="text-lg font-medium mb-2">Không tìm thấy thuộc tính</p>
            <p className="text-sm text-muted-foreground">
              Thử thay đổi bộ lọc hoặc từ khóa tìm kiếm
            </p>
          </CardContent>
        </Card>
      )}

      {/* Summary */}
      {Object.keys(selectedValues).length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Tóm tắt lựa chọn</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {Object.entries(selectedValues).map(([attributeId, value]) => {
                const attribute = attributes.find((attr) => attr.id === attributeId);
                if (!attribute || !value) return null;

                const selectedValueObjects = Array.isArray(value)
                  ? attribute.values?.filter((v) => value.includes(v.id)) || []
                  : attribute.values?.filter((v) => v.id === value) || [];

                return (
                  <div key={attributeId} className="flex items-center justify-between">
                    <span className="font-medium">{attribute.name}:</span>
                    <div className="flex flex-wrap gap-1">
                      {attribute.type === "TEXT" || attribute.type === "NUMBER" ? (
                        <Badge variant="outline">{value}</Badge>
                      ) : attribute.type === "BOOLEAN" ? (
                        <Badge variant="outline">
                          {value === "true" ? "Có" : "Không"}
                        </Badge>
                      ) : (
                        selectedValueObjects.map((valueObj) => (
                          <Badge key={valueObj.id} variant="outline">
                            {valueObj.value}
                          </Badge>
                        ))
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
