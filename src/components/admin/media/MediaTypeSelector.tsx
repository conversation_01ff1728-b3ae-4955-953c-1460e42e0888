"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Image as ImageIcon,
  Link as LinkIcon,
  Upload,
  X,
  Eye,
  AlertCircle,
} from "lucide-react";
import { MediaSelector } from "@/components/admin/MediaManager";
import { MediaType, MediaField, isValidMediaUrl } from "@/types/media";

interface MediaTypeSelectorProps {
  label: string;
  value: MediaField;
  onChange: (value: MediaField) => void;
  placeholder?: string;
  folder?: string;
  allowedTypes?: string[];
  className?: string;
  required?: boolean;
}

export function MediaTypeSelector({
  label,
  value,
  onChange,
  placeholder = "Chọn media...",
  folder = "uploads",
  allowedTypes = ["image/jpeg", "image/png", "image/gif", "image/webp"],
  className,
  required = false,
}: MediaTypeSelectorProps) {
  const [showPreview, setShowPreview] = useState(false);

  const handleTypeChange = (type: MediaType) => {
    onChange({
      ...value,
      type,
      url: type === "INTERNAL" ? value.url : undefined,
      externalUrl: type === "EXTERNAL" ? value.externalUrl : undefined,
    });
  };

  const handleInternalMediaChange = (url: string) => {
    onChange({
      ...value,
      url,
    });
  };

  const handleExternalUrlChange = (externalUrl: string) => {
    onChange({
      ...value,
      externalUrl,
    });
  };

  const handleClear = () => {
    onChange({
      type: "INTERNAL",
      url: undefined,
      externalUrl: undefined,
    });
  };

  const getMediaUrl = () => {
    if (value.type === "EXTERNAL") {
      return value.externalUrl;
    }
    return value.url;
  };

  const isImage = (url: string) => {
    if (!url) return false;
    const imageExtensions = [".jpg", ".jpeg", ".png", ".gif", ".webp"];
    return imageExtensions.some((ext) => url.toLowerCase().includes(ext));
  };

  const hasValue = () => {
    return (value.type === "INTERNAL" && value.url) || 
           (value.type === "EXTERNAL" && value.externalUrl);
  };

  const isValidUrl = () => {
    const url = getMediaUrl();
    return url ? isValidMediaUrl(url, value.type) : true;
  };

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center">
              <ImageIcon className="h-5 w-5 mr-2" />
              {label}
              {required && <span className="text-red-500 ml-1">*</span>}
            </div>
            {hasValue() && (
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={handleClear}
                className="text-red-600 hover:text-red-700"
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Media Type Selection */}
          <div>
            <Label>Loại media</Label>
            <Select value={value.type} onValueChange={handleTypeChange}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="INTERNAL">
                  <div className="flex items-center gap-2">
                    <Upload className="h-4 w-4" />
                    <span>Tải lên (Internal)</span>
                  </div>
                </SelectItem>
                <SelectItem value="EXTERNAL">
                  <div className="flex items-center gap-2">
                    <LinkIcon className="h-4 w-4" />
                    <span>Liên kết ngoài (External)</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Separator />

          {/* Media Input */}
          {value.type === "INTERNAL" ? (
            <div>
              <Label>Chọn file từ thư viện</Label>
              <MediaSelector
                value={value.url || ""}
                onChange={handleInternalMediaChange}
                folder={folder}
                allowedTypes={allowedTypes}
                placeholder={placeholder}
              />
            </div>
          ) : (
            <div>
              <Label>URL liên kết ngoài</Label>
              <div className="space-y-2">
                <Input
                  type="url"
                  value={value.externalUrl || ""}
                  onChange={(e) => handleExternalUrlChange(e.target.value)}
                  placeholder="https://example.com/image.jpg"
                />
                {value.externalUrl && !isValidUrl() && (
                  <div className="flex items-center gap-2 text-red-600 text-sm">
                    <AlertCircle className="h-4 w-4" />
                    <span>URL không hợp lệ</span>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Preview */}
          {hasValue() && isValidUrl() && (
            <div>
              <div className="flex items-center justify-between mb-2">
                <Label>Xem trước</Label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => setShowPreview(!showPreview)}
                >
                  <Eye className="h-4 w-4 mr-2" />
                  {showPreview ? "Ẩn" : "Hiện"}
                </Button>
              </div>

              {showPreview && (
                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <Badge variant={value.type === "INTERNAL" ? "default" : "secondary"}>
                      {value.type === "INTERNAL" ? "Internal" : "External"}
                    </Badge>
                    <span className="text-sm text-muted-foreground">
                      {getMediaUrl()}
                    </span>
                  </div>

                  {isImage(getMediaUrl()!) ? (
                    <img
                      src={getMediaUrl()}
                      alt="Preview"
                      className="max-w-full h-auto max-h-48 rounded border"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.style.display = "none";
                      }}
                    />
                  ) : (
                    <div className="flex items-center justify-center h-24 bg-gray-100 rounded border">
                      <div className="text-center">
                        <ImageIcon className="h-8 w-8 mx-auto text-gray-400 mb-2" />
                        <p className="text-sm text-gray-500">Không thể xem trước</p>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}

          {/* Info */}
          <div className="text-xs text-muted-foreground">
            {value.type === "INTERNAL" ? (
              <p>
                <strong>Internal:</strong> File sẽ được tải lên và lưu trữ trên server.
                Hỗ trợ các định dạng: {allowedTypes.join(", ")}
              </p>
            ) : (
              <p>
                <strong>External:</strong> Sử dụng URL từ nguồn bên ngoài.
                Đảm bảo URL có thể truy cập công khai.
              </p>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
