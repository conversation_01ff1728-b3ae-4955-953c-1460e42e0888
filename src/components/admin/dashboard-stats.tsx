"use client";

import { useState, useEffect } from "react";
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  ShoppingCart,
  Users,
  Package,
  Loader2,
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { formatCurrency, formatNumber } from "@/lib/utils";
import { useAdminStats } from "@/hooks/admin/useAdminStats";

interface StatsData {
  overview: {
    totalRevenue: { value: number; growth: number };
    totalOrders: { value: number; growth: number };
    totalUsers: { value: number; growth: number };
    totalProducts: { value: number; growth: number };
  };
}

export function DashboardStats() {
  const { stats, loading, error } = useAdminStats();

  // Transform the hook data to match the component's expected format
  const transformedStats = stats ? {
    overview: {
      totalRevenue: { value: stats.revenue, growth: stats.growth.revenue },
      totalOrders: { value: stats.orders, growth: stats.growth.orders },
      totalUsers: { value: stats.users, growth: stats.growth.users },
      totalProducts: { value: stats.products, growth: stats.growth.products },
    }
  } : null;

  if (loading) {
    return (
      <div
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
        data-testid="stats-loading"
      >
        {Array.from({ length: 4 }, (_, i) => (
          <Card key={i} className="relative overflow-hidden">
            <CardContent className="p-6">
              <div className="flex items-center justify-center h-20">
                <Loader2 className="h-6 w-6 animate-spin" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (!transformedStats) {
    return (
      <div
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
        data-testid="stats-error"
      >
        {Array.from({ length: 4 }, (_, i) => (
          <Card key={i} className="relative overflow-hidden">
            <CardContent className="p-6">
              <div className="text-center text-muted-foreground">
                Không thể tải dữ liệu
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const statsConfig = [
    {
      title: "Tổng doanh thu",
      value: transformedStats?.overview?.totalRevenue?.value || 0,
      change: transformedStats?.overview?.totalRevenue?.growth || 0,
      icon: DollarSign,
      color: "text-green-600",
      bgColor: "bg-green-100",
      format: "currency",
      testId: "revenue-stat",
    },
    {
      title: "Đơn hàng",
      value: transformedStats?.overview?.totalOrders?.value || 0,
      change: transformedStats?.overview?.totalOrders?.growth || 0,
      icon: ShoppingCart,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
      format: "number",
      testId: "orders-stat",
    },
    {
      title: "Khách hàng",
      value: transformedStats?.overview?.totalUsers?.value || 0,
      change: transformedStats?.overview?.totalUsers?.growth || 0,
      icon: Users,
      color: "text-purple-600",
      bgColor: "bg-purple-100",
      format: "number",
      testId: "users-stat",
    },
    {
      title: "Sản phẩm",
      value: transformedStats?.overview?.totalProducts?.value || 0,
      change: transformedStats?.overview?.totalProducts?.growth || 0,
      icon: Package,
      color: "text-orange-600",
      bgColor: "bg-orange-100",
      format: "number",
      testId: "products-stat",
    },
  ];
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {statsConfig.map((stat) => (
        <Card
          key={stat.title}
          className="relative overflow-hidden"
          data-testid="stats-card"
        >
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              {stat.title}
            </CardTitle>
            <div className={`p-2 rounded-lg ${stat.bgColor}`}>
              <stat.icon className={`h-4 w-4 ${stat.color}`} />
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-2" data-testid={stat.testId}>
              <div className="text-2xl font-bold stat-value">
                {stat.format === "currency"
                  ? formatCurrency(stat.value)
                  : formatNumber(stat.value)}
              </div>
              <div className="flex items-center space-x-1 text-sm">
                {stat.change >= 0 ? (
                  <TrendingUp className="h-4 w-4 text-green-600" />
                ) : (
                  <TrendingDown className="h-4 w-4 text-red-600" />
                )}
                <span
                  className={
                    stat.change >= 0 ? "text-green-600" : "text-red-600"
                  }
                >
                  {Math.abs(stat.change)}%
                </span>
                <span className="text-muted-foreground">
                  so với tháng trước
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
