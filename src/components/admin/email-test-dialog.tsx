"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Mail, Send, CheckCircle, XCircle, Loader2, AlertCircle } from "lucide-react";
import { toast } from "sonner";
import { useAdminOperations } from "@/hooks/admin/useAdminOperations";

interface EmailStatus {
  configured: boolean;
  connected: boolean;
  message: string;
  error?: string;
}

export function EmailTestDialog() {
  const [isOpen, setIsOpen] = useState(false);
  const [testForm, setTestForm] = useState({
    recipientEmail: "",
    recipientName: "",
  });

  const { emailStatus, emailTesting, checkEmailStatus, testEmail } = useAdminOperations({
    onEmailTestSuccess: () => {
      setTestForm({ recipientEmail: "", recipientName: "" });
    },
  });

  const sendTestEmail = async () => {
    if (!testForm.recipientEmail || !testForm.recipientName) {
      toast.error("Vui lòng điền đầy đủ thông tin");
      return;
    }

    await testEmail('single');
  };

  const handleOpenChange = (open: boolean) => {
    setIsOpen(open);
    if (open) {
      checkEmailStatus();
    }
  };

  const getStatusIcon = () => {
    if (!emailStatus) return <Loader2 className="h-4 w-4 animate-spin" />;
    
    if (emailStatus.status === 'working') {
      return <CheckCircle className="h-4 w-4 text-green-600" />;
    } else if (emailStatus.status === 'error') {
      return <AlertCircle className="h-4 w-4 text-yellow-600" />;
    } else {
      return <XCircle className="h-4 w-4 text-red-600" />;
    }
  };

  const getStatusColor = () => {
    if (!emailStatus) return "bg-gray-100 text-gray-800";
    
    if (emailStatus.status === 'working') {
      return "bg-green-100 text-green-800";
    } else if (emailStatus.status === 'error') {
      return "bg-yellow-100 text-yellow-800";
    } else {
      return "bg-red-100 text-red-800";
    }
  };

  const getStatusText = () => {
    if (!emailStatus) return "Đang kiểm tra...";
    
    if (emailStatus.status === 'working') {
      return "Hoạt động bình thường";
    } else if (emailStatus.status === 'error') {
      return emailStatus.message || "Có lỗi xảy ra";
    } else {
      return emailStatus.message || "Chưa cấu hình";
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          <Mail className="h-4 w-4 mr-2" />
          Test Email
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Test Email Notification</DialogTitle>
          <DialogDescription>
            Kiểm tra và test hệ thống gửi email thông báo liên hệ
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Email Status */}
          <div className="p-4 border rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-medium">Trạng thái Email Service</h4>
              {getStatusIcon()}
            </div>
            <Badge className={getStatusColor()}>
              {getStatusText()}
            </Badge>
            {emailStatus && (
              <p className="text-sm text-muted-foreground mt-2">
                {emailStatus.message}
              </p>
            )}
            {emailStatus?.error && (
              <p className="text-sm text-red-600 mt-1">
                Lỗi: {emailStatus.error}
              </p>
            )}
          </div>

          {/* Test Email Form */}
          {emailStatus?.configured && (
            <div className="space-y-3">
              <div>
                <Label htmlFor="recipientName">Tên người nhận</Label>
                <Input
                  id="recipientName"
                  placeholder="Nhập tên người nhận"
                  value={testForm.recipientName}
                  onChange={(e) =>
                    setTestForm({ ...testForm, recipientName: e.target.value })
                  }
                />
              </div>
              <div>
                <Label htmlFor="recipientEmail">Email người nhận</Label>
                <Input
                  id="recipientEmail"
                  type="email"
                  placeholder="Nhập email người nhận"
                  value={testForm.recipientEmail}
                  onChange={(e) =>
                    setTestForm({ ...testForm, recipientEmail: e.target.value })
                  }
                />
              </div>
              <Button
                onClick={sendTestEmail}
                disabled={emailTesting || (emailStatus && emailStatus.status !== 'working')}
                className="w-full"
              >
                {emailTesting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Đang gửi...
                  </>
                ) : (
                  <>
                    <Send className="h-4 w-4 mr-2" />
                    Gửi Email Test
                  </>
                )}
              </Button>
            </div>
          )}

          {/* Help Text */}
          <div className="text-sm text-muted-foreground">
            <p>
              Email test sẽ gửi một tin nhắn liên hệ mẫu để kiểm tra template và
              hệ thống email notification.
            </p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
