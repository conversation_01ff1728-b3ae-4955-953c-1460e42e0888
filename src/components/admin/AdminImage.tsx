"use client";

import React, { useState } from "react";
import Image from "next/image";
import { cn } from "@/lib/utils";
import { ImageIcon, AlertCircle, Loader2, X } from "lucide-react";

interface AdminImageProps {
  src?: string | null;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  fallbackIcon?: React.ReactNode;
  showLoadingState?: boolean;
  rounded?: boolean;
  objectFit?: "cover" | "contain" | "fill" | "none" | "scale-down";
  priority?: boolean;
  sizes?: string;
}

export function AdminImage({
  src,
  alt,
  width,
  height,
  className,
  fallbackIcon,
  showLoadingState = true,
  rounded = true,
  objectFit = "cover",
  priority = false,
  sizes,
}: AdminImageProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  // Validate URL function
  const isValidUrl = (url: string | null | undefined): boolean => {
    if (!url || typeof url !== "string" || url.trim() === "") {
      return false;
    }

    try {
      // Check if it's a valid URL
      new URL(url);
      return true;
    } catch {
      // Check if it's a valid relative path
      return (
        url.startsWith("/") || url.startsWith("./") || url.startsWith("../")
      );
    }
  };

  const handleLoad = () => {
    setIsLoading(false);
    setHasError(false);
  };

  const handleError = () => {
    setIsLoading(false);
    setHasError(true);
  };

  const containerClasses = cn(
    "relative bg-muted flex items-center justify-center overflow-hidden",
    rounded && "rounded-lg",
    className
  );

  const imageClasses = cn(
    "transition-opacity duration-200",
    isLoading && "opacity-0",
    objectFit === "cover" && "object-cover",
    objectFit === "contain" && "object-contain",
    objectFit === "fill" && "object-fill",
    objectFit === "none" && "object-none",
    objectFit === "scale-down" && "object-scale-down"
  );

  // If no src provided, invalid URL, or has error, show fallback
  if (!src || !isValidUrl(src) || hasError) {
    return (
      <div className={containerClasses} style={{ width, height }}>
        {hasError ? (
          <div className="flex flex-col items-center justify-center text-muted-foreground">
            <AlertCircle className="h-4 w-4 mb-1" />
            <span className="text-xs">Lỗi tải ảnh</span>
          </div>
        ) : (
          fallbackIcon || (
            <ImageIcon className="h-6 w-6 text-muted-foreground" />
          )
        )}
      </div>
    );
  }

  return (
    <div className={containerClasses} style={{ width, height }}>
      {/* Loading state */}
      {isLoading && showLoadingState && (
        <div className="absolute inset-0 flex items-center justify-center">
          <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
        </div>
      )}

      {/* Image */}
      {width && height ? (
        <Image
          src={src}
          alt={alt}
          width={width}
          height={height}
          className={imageClasses}
          onLoad={handleLoad}
          onError={handleError}
          priority={priority}
          sizes={sizes}
        />
      ) : (
        <Image
          src={src}
          alt={alt}
          fill
          className={imageClasses}
          onLoad={handleLoad}
          onError={handleError}
          priority={priority}
          sizes={sizes}
        />
      )}
    </div>
  );
}

// Specialized variants for common use cases
export function AdminProductImage({
  src,
  alt,
  size = 48,
  className,
}: {
  src?: string | null;
  alt: string;
  size?: number;
  className?: string;
}) {
  return (
    <AdminImage
      src={src}
      alt={alt}
      width={size}
      height={size}
      className={cn("flex-shrink-0", className)}
      fallbackIcon={<ImageIcon className="h-6 w-6 text-muted-foreground" />}
    />
  );
}

export function AdminBrandLogo({
  src,
  alt,
  size = 48,
  className,
}: {
  src?: string | null;
  alt: string;
  size?: number;
  className?: string;
}) {
  return (
    <AdminImage
      src={src}
      alt={alt}
      width={size}
      height={size}
      className={cn("flex-shrink-0", className)}
      objectFit="contain"
      fallbackIcon={
        <div className="text-xs font-bold text-muted-foreground">LOGO</div>
      }
    />
  );
}

export function AdminCategoryImage({
  src,
  alt,
  size = 48,
  className,
}: {
  src?: string | null;
  alt: string;
  size?: number;
  className?: string;
}) {
  return (
    <AdminImage
      src={src}
      alt={alt}
      width={size}
      height={size}
      className={cn("flex-shrink-0", className)}
      fallbackIcon={<ImageIcon className="h-6 w-6 text-muted-foreground" />}
    />
  );
}

// Preview component for forms
export function AdminImagePreview({
  src,
  alt,
  onRemove,
  className,
  label,
}: {
  src: string;
  alt: string;
  onRemove?: () => void;
  className?: string;
  label?: string;
}) {
  return (
    <div className={cn("relative group", className)}>
      <AdminImage src={src} alt={alt} className="w-full h-32 border" rounded />

      {label && (
        <div className="absolute top-2 left-2 bg-black/50 text-white text-xs px-2 py-1 rounded">
          {label}
        </div>
      )}

      {onRemove && (
        <button
          type="button"
          onClick={onRemove}
          className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
        >
          <X className="h-3 w-3" />
        </button>
      )}
    </div>
  );
}
