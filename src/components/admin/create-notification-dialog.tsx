"use client";

import { <PERSON><PERSON>, AlertDescription } from "@/components/ui/alert";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Calendar, Loader2 } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";

interface CreateNotificationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

interface AdminUser {
  id: string;
  name: string;
  email: string;
}

export function CreateNotificationDialog({
  open,
  onOpenChange,
  onSuccess,
}: CreateNotificationDialogProps) {
  const [formData, setFormData] = useState({
    title: "",
    message: "",
    type: "INFO" as "INFO" | "SUCCESS" | "WARNING" | "ERROR" | "SYSTEM",
    priority: "NORMAL" as "LOW" | "NORMAL" | "HIGH" | "URGENT",
    targetType: "ALL_ADMINS" as
      | "ALL_ADMINS"
      | "SPECIFIC_ADMIN"
      | "ROLE_ADMIN"
      | "ROLE_MODERATOR",
    targetId: "",
    actionUrl: "",
    expiresAt: "",
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [adminUsers, setAdminUsers] = useState<AdminUser[]>([]);

  useEffect(() => {
    if (open) {
      fetchAdminUsers();
    }
  }, [open]);

  const fetchAdminUsers = async () => {
    try {
      const response = await fetch("/api/admin/admins");
      if (response.ok) {
        const data = await response.json();
        setAdminUsers(data.adminUsers);
      }
    } catch (error) {
      console.error("Error fetching admin users:", error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");

    if (!formData.title.trim()) {
      setError("Tiêu đề là bắt buộc");
      return;
    }

    if (!formData.message.trim()) {
      setError("Nội dung là bắt buộc");
      return;
    }

    if (formData.targetType === "SPECIFIC_ADMIN" && !formData.targetId) {
      setError("Vui lòng chọn admin cụ thể");
      return;
    }

    setLoading(true);

    try {
      const submitData = {
        title: formData.title.trim(),
        message: formData.message.trim(),
        type: formData.type,
        priority: formData.priority,
        targetType: formData.targetType,
        targetId:
          formData.targetType === "SPECIFIC_ADMIN"
            ? formData.targetId
            : undefined,
        actionUrl: formData.actionUrl.trim() || undefined,
        expiresAt: formData.expiresAt || undefined,
      };

      const response = await fetch("/api/admin/notifications", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(submitData),
      });

      if (response.ok) {
        onSuccess();
        onOpenChange(false);
        toast.success("Tạo thông báo thành công");
        // Reset form
        setFormData({
          title: "",
          message: "",
          type: "INFO",
          priority: "NORMAL",
          targetType: "ALL_ADMINS",
          targetId: "",
          actionUrl: "",
          expiresAt: "",
        });
      } else {
        const errorData = await response.json();
        setError(errorData.error || "Có lỗi xảy ra khi tạo thông báo");
      }
    } catch (error: unknown) {
      setError("Có lỗi xảy ra khi tạo thông báo");
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Tạo thông báo mới</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="title">Tiêu đề *</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => handleInputChange("title", e.target.value)}
              disabled={loading}
              placeholder="Nhập tiêu đề thông báo..."
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="message">Nội dung *</Label>
            <Textarea
              id="message"
              value={formData.message}
              onChange={(e) => handleInputChange("message", e.target.value)}
              disabled={loading}
              placeholder="Nhập nội dung thông báo..."
              rows={4}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="type">Loại thông báo</Label>
              <Select
                value={formData.type}
                onValueChange={(value) => handleInputChange("type", value)}
                disabled={loading}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="INFO">Thông tin</SelectItem>
                  <SelectItem value="SUCCESS">Thành công</SelectItem>
                  <SelectItem value="WARNING">Cảnh báo</SelectItem>
                  <SelectItem value="ERROR">Lỗi</SelectItem>
                  <SelectItem value="SYSTEM">Hệ thống</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="priority">Mức độ ưu tiên</Label>
              <Select
                value={formData.priority}
                onValueChange={(value) => handleInputChange("priority", value)}
                disabled={loading}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="LOW">Thấp</SelectItem>
                  <SelectItem value="NORMAL">Bình thường</SelectItem>
                  <SelectItem value="HIGH">Cao</SelectItem>
                  <SelectItem value="URGENT">Khẩn cấp</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="targetType">Đối tượng nhận</Label>
            <Select
              value={formData.targetType}
              onValueChange={(value) => handleInputChange("targetType", value)}
              disabled={loading}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL_ADMINS">Tất cả Admin</SelectItem>
                <SelectItem value="SPECIFIC_ADMIN">Admin cụ thể</SelectItem>
                <SelectItem value="ROLE_ADMIN">Chỉ Admin</SelectItem>
                <SelectItem value="ROLE_MODERATOR">Chỉ Moderator</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {formData.targetType === "SPECIFIC_ADMIN" && (
            <div className="space-y-2">
              <Label htmlFor="targetId">Chọn Admin</Label>
              <Select
                value={formData.targetId}
                onValueChange={(value) => handleInputChange("targetId", value)}
                disabled={loading}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Chọn admin..." />
                </SelectTrigger>
                <SelectContent>
                  {adminUsers.map((admin) => (
                    <SelectItem key={admin.id} value={admin.id}>
                      {admin.name} ({admin.email})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="actionUrl">URL hành động (tùy chọn)</Label>
            <Input
              id="actionUrl"
              type="url"
              value={formData.actionUrl}
              onChange={(e) => handleInputChange("actionUrl", e.target.value)}
              disabled={loading}
              placeholder="https://example.com/action"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="expiresAt">Ngày hết hạn (tùy chọn)</Label>
            <div className="relative">
              <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                id="expiresAt"
                type="datetime-local"
                value={formData.expiresAt}
                onChange={(e) => handleInputChange("expiresAt", e.target.value)}
                disabled={loading}
                className="pl-10"
                min={new Date().toISOString().slice(0, 16)}
              />
            </div>
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="flex justify-end space-x-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={loading}
            >
              Hủy
            </Button>
            <Button type="submit" disabled={loading}>
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Tạo thông báo
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
