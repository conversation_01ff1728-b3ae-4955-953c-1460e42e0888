"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { format } from "date-fns";
import { vi } from "date-fns/locale";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Activity,
  Bell,
  AlertTriangle,
  CheckCircle,
  Clock,
  Users,
  ShoppingCart,
  TrendingUp,
  AlertCircle,
  ExternalLink,
  RefreshCw,
  Eye,
} from "lucide-react";
import { useNotifications } from "@/contexts/NotificationContext";

interface DashboardStats {
  auditLogs: {
    total: number;
    today: number;
    thisWeek: number;
  };
  notifications: {
    total: number;
    unread: number;
    urgent: number;
  };
  systemHealth: {
    status: "healthy" | "warning" | "critical";
    uptime: string;
    lastCheck: string;
  };
  recentActivity: {
    totalActions: number;
    uniqueAdmins: number;
    topActions: Array<{
      action: string;
      count: number;
    }>;
  };
}

interface RecentAuditLog {
  id: string;
  action: string;
  resource: string;
  description: string;
  createdAt: string;
  admin: {
    name: string;
    avatar?: string;
  };
}

interface SystemAlert {
  id: string;
  type: "warning" | "error" | "info";
  title: string;
  message: string;
  createdAt: string;
  actionUrl?: string;
}

export function AuditNotificationDashboard() {
  const { notifications, unreadCount, isConnected } = useNotifications();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [recentAuditLogs, setRecentAuditLogs] = useState<RecentAuditLog[]>([]);
  const [systemAlerts, setSystemAlerts] = useState<SystemAlert[]>([]);
  const [loading, setLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  useEffect(() => {
    loadDashboardData();

    // Auto-refresh every 30 seconds
    const interval = setInterval(loadDashboardData, 30000);
    return () => clearInterval(interval);
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);

      const [statsResponse, auditLogsResponse, alertsResponse] =
        await Promise.all([
          fetch("/api/admin/dashboard/stats"),
          fetch("/api/admin/audit-logs?limit=5"),
          fetch("/api/admin/dashboard/alerts"),
        ]);

      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setStats(statsData);
      }

      if (auditLogsResponse.ok) {
        const auditData = await auditLogsResponse.json();
        setRecentAuditLogs(auditData.data || []);
      }

      if (alertsResponse.ok) {
        const alertsData = await alertsResponse.json();
        setSystemAlerts(alertsData.alerts || []);
      }

      setLastUpdated(new Date());
    } catch (error) {
      console.error("Error loading dashboard data:", error);
    } finally {
      setLoading(false);
    }
  };

  const getActionIcon = (action: string) => {
    if (action.includes("CREATE"))
      return <CheckCircle className="h-4 w-4 text-green-500" />;
    if (action.includes("UPDATE"))
      return <Activity className="h-4 w-4 text-blue-500" />;
    if (action.includes("DELETE"))
      return <AlertTriangle className="h-4 w-4 text-red-500" />;
    return <Activity className="h-4 w-4 text-gray-500" />;
  };

  const getAlertIcon = (type: string) => {
    switch (type) {
      case "error":
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case "warning":
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      default:
        return <CheckCircle className="h-4 w-4 text-blue-500" />;
    }
  };

  const getSystemHealthColor = (status: string) => {
    switch (status) {
      case "healthy":
        return "text-green-600";
      case "warning":
        return "text-yellow-600";
      case "critical":
        return "text-red-600";
      default:
        return "text-gray-600";
    }
  };

  const recentNotifications = notifications.slice(0, 5);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Audit & Notification Dashboard</h2>
          <p className="text-muted-foreground">
            Tổng quan về hoạt động hệ thống và thông báo
          </p>
        </div>
        <div className="flex items-center gap-2">
          <div className="text-sm text-muted-foreground">
            Cập nhật lần cuối: {format(lastUpdated, "HH:mm:ss", { locale: vi })}
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={loadDashboardData}
            disabled={loading}
          >
            <RefreshCw
              className={`h-4 w-4 mr-2 ${loading ? "animate-spin" : ""}`}
            />
            Làm mới
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Audit Logs Hôm nay
                </p>
                <p className="text-2xl font-bold">
                  {stats?.auditLogs.today || 0}
                </p>
              </div>
              <Activity className="h-8 w-8 text-blue-500" />
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              Tổng: {stats?.auditLogs.total || 0} logs
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Thông báo chưa đọc
                </p>
                <p className="text-2xl font-bold text-blue-600">
                  {unreadCount}
                </p>
              </div>
              <Bell className="h-8 w-8 text-blue-500" />
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              Tổng: {notifications.length} thông báo
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Thông báo khẩn cấp
                </p>
                <p className="text-2xl font-bold text-red-600">
                  {stats?.notifications.urgent || 0}
                </p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-500" />
            </div>
            <p className="text-xs text-muted-foreground mt-2">Cần xử lý ngay</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Trạng thái hệ thống
                </p>
                <p
                  className={`text-2xl font-bold ${getSystemHealthColor(stats?.systemHealth.status || "healthy")}`}
                >
                  {stats?.systemHealth.status === "healthy"
                    ? "Tốt"
                    : stats?.systemHealth.status === "warning"
                      ? "Cảnh báo"
                      : "Lỗi"}
                </p>
              </div>
              <div
                className={`h-8 w-8 rounded-full flex items-center justify-center ${
                  stats?.systemHealth.status === "healthy"
                    ? "bg-green-100"
                    : stats?.systemHealth.status === "warning"
                      ? "bg-yellow-100"
                      : "bg-red-100"
                }`}
              >
                <div
                  className={`h-4 w-4 rounded-full ${
                    stats?.systemHealth.status === "healthy"
                      ? "bg-green-500"
                      : stats?.systemHealth.status === "warning"
                        ? "bg-yellow-500"
                        : "bg-red-500"
                  }`}
                />
              </div>
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              Uptime: {stats?.systemHealth.uptime || "N/A"}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Connection Status */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div
                className={`h-3 w-3 rounded-full ${isConnected ? "bg-green-500" : "bg-red-500"}`}
              />
              <span className="text-sm font-medium">
                Real-time Connection: {isConnected ? "Kết nối" : "Mất kết nối"}
              </span>
            </div>
            <div className="text-xs text-muted-foreground">
              {isConnected
                ? "Nhận thông báo real-time"
                : "Không nhận được thông báo real-time"}
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Audit Logs */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
            <div>
              <CardTitle className="text-lg">Audit Logs Gần đây</CardTitle>
              <CardDescription>5 hoạt động admin gần nhất</CardDescription>
            </div>
            <Button variant="outline" size="sm" asChild>
              <Link href="/admin/audit-logs">
                <Eye className="h-4 w-4 mr-2" />
                Xem tất cả
              </Link>
            </Button>
          </CardHeader>
          <CardContent className="space-y-4">
            {recentAuditLogs.length === 0 ? (
              <p className="text-sm text-muted-foreground text-center py-4">
                Chưa có audit logs nào
              </p>
            ) : (
              recentAuditLogs.map((log) => (
                <div
                  key={log.id}
                  className="flex items-start gap-3 p-3 rounded-lg border"
                >
                  <div className="flex-shrink-0 mt-1">
                    {getActionIcon(log.action)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <Badge variant="outline" className="text-xs">
                        {log.action}
                      </Badge>
                      <Badge variant="secondary" className="text-xs">
                        {log.resource}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mb-2">
                      {log.description}
                    </p>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Avatar className="h-6 w-6">
                          <AvatarImage src={log.admin.avatar} />
                          <AvatarFallback className="text-xs">
                            {log.admin.name.charAt(0)}
                          </AvatarFallback>
                        </Avatar>
                        <span className="text-xs text-muted-foreground">
                          {log.admin.name}
                        </span>
                      </div>
                      <span className="text-xs text-muted-foreground">
                        {format(new Date(log.createdAt), "HH:mm", {
                          locale: vi,
                        })}
                      </span>
                    </div>
                  </div>
                </div>
              ))
            )}
          </CardContent>
        </Card>

        {/* Recent Notifications */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
            <div>
              <CardTitle className="text-lg">Thông báo Gần đây</CardTitle>
              <CardDescription>5 thông báo mới nhất</CardDescription>
            </div>
            <Button variant="outline" size="sm" asChild>
              <Link href="/admin/notifications">
                <Bell className="h-4 w-4 mr-2" />
                Xem tất cả
              </Link>
            </Button>
          </CardHeader>
          <CardContent className="space-y-4">
            {recentNotifications.length === 0 ? (
              <p className="text-sm text-muted-foreground text-center py-4">
                Chưa có thông báo nào
              </p>
            ) : (
              recentNotifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`flex items-start gap-3 p-3 rounded-lg border ${
                    !notification.isRead ? "bg-blue-50 border-blue-200" : ""
                  }`}
                >
                  <div className="flex-shrink-0 mt-1">
                    {notification.type === "SUCCESS" && (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    )}
                    {notification.type === "ERROR" && (
                      <AlertCircle className="h-4 w-4 text-red-500" />
                    )}
                    {notification.type === "WARNING" && (
                      <AlertTriangle className="h-4 w-4 text-yellow-500" />
                    )}
                    {notification.type === "INFO" && (
                      <Bell className="h-4 w-4 text-blue-500" />
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <Badge
                        variant={
                          notification.priority === "URGENT"
                            ? "destructive"
                            : notification.priority === "HIGH"
                              ? "secondary"
                              : "outline"
                        }
                        className="text-xs"
                      >
                        {notification.priority}
                      </Badge>
                      {!notification.isRead && (
                        <div className="h-2 w-2 bg-blue-500 rounded-full" />
                      )}
                    </div>
                    <p className="text-sm font-medium mb-1">
                      {notification.title}
                    </p>
                    <p className="text-sm text-muted-foreground mb-2 line-clamp-2">
                      {notification.message}
                    </p>
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-muted-foreground">
                        {format(new Date(notification.createdAt), "HH:mm", {
                          locale: vi,
                        })}
                      </span>
                      {notification.actionUrl && (
                        <Button variant="ghost" size="sm" asChild>
                          <Link href={notification.actionUrl}>
                            <ExternalLink className="h-3 w-3" />
                          </Link>
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              ))
            )}
          </CardContent>
        </Card>
      </div>

      {/* System Alerts */}
      {systemAlerts.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-yellow-500" />
              Cảnh báo Hệ thống
            </CardTitle>
            <CardDescription>
              Các cảnh báo và thông tin quan trọng về hệ thống
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            {systemAlerts.map((alert) => (
              <div
                key={alert.id}
                className="flex items-start gap-3 p-3 rounded-lg border border-yellow-200 bg-yellow-50"
              >
                <div className="flex-shrink-0 mt-1">
                  {getAlertIcon(alert.type)}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium mb-1">{alert.title}</p>
                  <p className="text-sm text-muted-foreground mb-2">
                    {alert.message}
                  </p>
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-muted-foreground">
                      {format(new Date(alert.createdAt), "dd/MM/yyyy HH:mm", {
                        locale: vi,
                      })}
                    </span>
                    {alert.actionUrl && (
                      <Button variant="outline" size="sm" asChild>
                        <Link href={alert.actionUrl}>
                          Xem chi tiết
                          <ExternalLink className="h-3 w-3 ml-1" />
                        </Link>
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      )}

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Thao tác Nhanh</CardTitle>
          <CardDescription>
            Các thao tác thường dùng cho audit và notification
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button variant="outline" asChild>
              <Link href="/admin/audit-logs">
                <Activity className="h-4 w-4 mr-2" />
                Xem Audit Logs
              </Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href="/admin/notifications">
                <Bell className="h-4 w-4 mr-2" />
                Quản lý Thông báo
              </Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href="/admin/notifications/settings">
                <Clock className="h-4 w-4 mr-2" />
                Cài đặt Thông báo
              </Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href="/admin/notifications/rules">
                <TrendingUp className="h-4 w-4 mr-2" />
                Quản lý Rules
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
