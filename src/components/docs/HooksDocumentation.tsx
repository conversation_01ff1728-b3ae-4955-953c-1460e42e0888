'use client';

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { CheckCircle, AlertCircle, Code, BookOpen } from 'lucide-react';

export function HooksDocumentation() {
  return (
    <div className="space-y-8">
      <div className="text-center">
        <h2 className="text-3xl font-bold mb-4">Hooks Documentation</h2>
        <p className="text-lg text-muted-foreground">
          Complete guide to using context hooks in the application
        </p>
      </div>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="hooks">Hooks API</TabsTrigger>
          <TabsTrigger value="examples">Examples</TabsTrigger>
          <TabsTrigger value="migration">Migration</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BookOpen className="h-5 w-5" />
                Architecture Overview
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="font-semibold mb-2">Provider Structure</h3>
                  <div className="text-sm space-y-1">
                    <div>ErrorBoundaryProvider</div>
                    <div className="ml-2">└─ ThemeProvider</div>
                    <div className="ml-4">└─ AuthProvider</div>
                    <div className="ml-6">└─ AppContextProvider</div>
                    <div className="ml-8">├─ SettingsProvider</div>
                    <div className="ml-8">├─ NotificationProvider</div>
                    <div className="ml-8">├─ UserProvider</div>
                    <div className="ml-8">├─ EnhancedCartProvider</div>
                    <div className="ml-8">└─ SearchProvider</div>
                  </div>
                </div>
                <div>
                  <h3 className="font-semibold mb-2">Hook Standardization</h3>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span className="text-sm">useSettings</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span className="text-sm">useUser</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span className="text-sm">useCart</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span className="text-sm">useSearch</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span className="text-sm">useNotifications</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span className="text-sm">useTheme</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="hooks" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>useSettings</CardTitle>
                <Badge variant="secondary">Site Configuration</Badge>
              </CardHeader>
              <CardContent>
                <pre className="text-sm bg-gray-100 p-3 rounded overflow-x-auto">
{`const { 
  settings, 
  loading, 
  updateSettings 
} = useSettings();

// Access site settings
settings.siteName
settings.contactInfo.phone
settings.shippingSettings`}
                </pre>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>useUser</CardTitle>
                <Badge variant="secondary">User Management</Badge>
              </CardHeader>
              <CardContent>
                <pre className="text-sm bg-gray-100 p-3 rounded overflow-x-auto">
{`const { 
  user, 
  loading, 
  updateProfile,
  wishlistItems,
  addToWishlist,
  isInWishlist 
} = useUser();`}
                </pre>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>useCart</CardTitle>
                <Badge variant="secondary">Shopping Cart</Badge>
              </CardHeader>
              <CardContent>
                <pre className="text-sm bg-gray-100 p-3 rounded overflow-x-auto">
{`const { 
  cart, 
  summary, 
  addToCart,
  removeFromCart,
  updateCartItem,
  quickAdd 
} = useCart();`}
                </pre>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>useSearch</CardTitle>
                <Badge variant="secondary">Search & Filter</Badge>
              </CardHeader>
              <CardContent>
                <pre className="text-sm bg-gray-100 p-3 rounded overflow-x-auto">
{`const { 
  query, 
  setQuery,
  results,
  loading,
  suggestions 
} = useSearch();`}
                </pre>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="examples" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Common Usage Patterns</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h3 className="font-semibold mb-2">1. Product Component with Cart & Wishlist</h3>
                <pre className="text-sm bg-gray-100 p-3 rounded overflow-x-auto">
{`function ProductCard({ product }) {
  const { addToCart } = useCart();
  const { addToWishlist, isInWishlist } = useUser();
  
  const handleAddToCart = async () => {
    try {
      await addToCart({
        productId: product.id,
        quantity: 1
      });
      toast.success('Added to cart!');
    } catch (error) {
      toast.error('Failed to add to cart');
    }
  };
  
  return (
    <div>
      <h3>{product.name}</h3>
      <button onClick={handleAddToCart}>
        Add to Cart
      </button>
      <button onClick={() => addToWishlist(product.id)}>
        {isInWishlist(product.id) ? '❤️' : '🤍'}
      </button>
    </div>
  );
}`}
                </pre>
              </div>

              <div>
                <h3 className="font-semibold mb-2">2. Header with Search & Cart</h3>
                <pre className="text-sm bg-gray-100 p-3 rounded overflow-x-auto">
{`function Header() {
  const { settings } = useSettings();
  const { summary } = useCart();
  const { setQuery } = useSearch();
  
  return (
    <header>
      <h1>{settings.siteName}</h1>
      <SearchBox onSearch={setQuery} />
      <CartIcon count={summary?.totalQuantity} />
    </header>
  );
}`}
                </pre>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="migration" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Migration Guide</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="font-semibold mb-2">Before (Old Imports)</h3>
                <pre className="text-sm bg-red-50 p-3 rounded">
{`import { useSettingsContext } from "@/contexts/SettingsContext";
import { useUserContext } from "@/contexts/user-context";
import { useEnhancedCart } from "@/contexts/enhanced-cart-context";
import { useSearchContext } from "@/contexts/search-context";`}
                </pre>
              </div>

              <div>
                <h3 className="font-semibold mb-2">After (Standardized)</h3>
                <pre className="text-sm bg-green-50 p-3 rounded">
{`import { 
  useSettings, 
  useUser, 
  useCart, 
  useSearch 
} from "@/contexts";`}
                </pre>
              </div>

              <div className="mt-4">
                <h3 className="font-semibold mb-2">Migration Checklist</h3>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span className="text-sm">Update import statements</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span className="text-sm">Replace hook names in components</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span className="text-sm">Test functionality</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <AlertCircle className="h-4 w-4 text-yellow-500" />
                    <span className="text-sm">Legacy hooks still available for compatibility</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}