"use client";

import Link from "next/link";
import { <PERSON><PERSON><PERSON>, <PERSON>, Star, Users, ShoppingBag, Sparkles } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

export function ModernHeroSection() {
  return (
    <section className="relative min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-20 left-20 w-72 h-72 bg-black rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-20 w-96 h-96 bg-gray-800 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gray-600 rounded-full blur-3xl"></div>
      </div>

      {/* <PERSON><PERSON> */}
      <div className="absolute inset-0 opacity-[0.02]">
        <div className="h-full w-full" style={{
          backgroundImage: `url("data:image/svg+xml,%3csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3e%3cg fill='none' fill-rule='evenodd'%3e%3cg fill='%23000000' fill-opacity='1'%3e%3ccircle cx='30' cy='30' r='1'/%3e%3c/g%3e%3c/g%3e%3c/svg%3e")`,
        }}></div>
      </div>

      <div className="relative container mx-auto px-4 py-20 lg:py-32">
        <div className="grid lg:grid-cols-2 gap-12 lg:gap-20 items-center">
          {/* Left Content */}
          <div className="space-y-8">
            {/* Badge */}
            <div className="flex items-center gap-3">
              <Badge variant="outline" className="bg-white/80 backdrop-blur-sm border-gray-200 text-gray-700 px-4 py-2">
                <Sparkles className="h-3 w-3 mr-2" />
                Bộ sưu tập mới 2024
              </Badge>
              <div className="flex items-center gap-1 text-sm text-gray-600">
                <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                <span className="font-medium">4.9</span>
                <span className="text-gray-400">từ 10k+ đánh giá</span>
              </div>
            </div>

            {/* Main Heading */}
            <div className="space-y-4">
              <h1 className="text-5xl lg:text-7xl font-bold leading-tight">
                <span className="text-gray-900">Thời trang</span>
                <br />
                <span className="text-gray-600">hiện đại</span>
                <br />
                <span className="bg-gradient-to-r from-gray-900 via-black to-gray-800 bg-clip-text text-transparent">
                  cho bạn
                </span>
              </h1>
              <p className="text-xl lg:text-2xl text-gray-600 max-w-lg leading-relaxed">
                Khám phá xu hướng thời trang mới nhất với thiết kế tinh tế, chất lượng cao và phong cách độc đáo.
              </p>
            </div>

            {/* Stats */}
            <div className="flex items-center gap-8 py-4">
              <div className="text-center">
                <div className="text-2xl lg:text-3xl font-bold text-gray-900">10K+</div>
                <div className="text-sm text-gray-600">Sản phẩm</div>
              </div>
              <div className="w-px h-12 bg-gray-200"></div>
              <div className="text-center">
                <div className="text-2xl lg:text-3xl font-bold text-gray-900">50K+</div>
                <div className="text-sm text-gray-600">Khách hàng</div>
              </div>
              <div className="w-px h-12 bg-gray-200"></div>
              <div className="text-center">
                <div className="text-2xl lg:text-3xl font-bold text-gray-900">100+</div>
                <div className="text-sm text-gray-600">Thương hiệu</div>
              </div>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Button 
                size="lg" 
                className="bg-black hover:bg-gray-800 text-white px-8 py-4 text-lg font-medium group"
                asChild
              >
                <Link href="/products">
                  <ShoppingBag className="h-5 w-5 mr-2" />
                  Mua sắm ngay
                  <ArrowRight className="h-5 w-5 ml-2 group-hover:translate-x-1 transition-transform" />
                </Link>
              </Button>
              <Button 
                variant="outline" 
                size="lg" 
                className="border-2 border-gray-300 hover:border-gray-400 text-gray-700 hover:text-gray-900 px-8 py-4 text-lg font-medium group bg-white/80 backdrop-blur-sm"
                asChild
              >
                <Link href="/categories">
                  <Play className="h-5 w-5 mr-2" />
                  Xem bộ sưu tập
                </Link>
              </Button>
            </div>

            {/* Trust Indicators */}
            <div className="flex items-center gap-6 pt-4">
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <div className="flex -space-x-2">
                  <div className="w-8 h-8 rounded-full bg-gray-300 border-2 border-white"></div>
                  <div className="w-8 h-8 rounded-full bg-gray-400 border-2 border-white"></div>
                  <div className="w-8 h-8 rounded-full bg-gray-500 border-2 border-white"></div>
                </div>
                <span>Được tin tưởng bởi 50K+ khách hàng</span>
              </div>
            </div>
          </div>

          {/* Right Content - Hero Image */}
          <div className="relative">
            <div className="relative aspect-[4/5] bg-gradient-to-br from-gray-100 to-gray-200 rounded-3xl overflow-hidden">
              {/* Placeholder for hero image */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center space-y-4">
                  <div className="w-24 h-24 bg-gray-300 rounded-full mx-auto flex items-center justify-center">
                    <ShoppingBag className="h-12 w-12 text-gray-600" />
                  </div>
                  <div className="text-gray-600 font-medium">Hero Image</div>
                </div>
              </div>
              
              {/* Floating Elements */}
              <div className="absolute top-8 right-8 bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-lg">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-sm font-medium text-gray-700">Miễn phí vận chuyển</span>
                </div>
              </div>
              
              <div className="absolute bottom-8 left-8 bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-lg">
                <div className="flex items-center gap-3">
                  <Users className="h-5 w-5 text-gray-600" />
                  <div>
                    <div className="text-sm font-medium text-gray-900">1,234</div>
                    <div className="text-xs text-gray-600">Đang mua sắm</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2">
        <div className="flex flex-col items-center gap-2 text-gray-400">
          <span className="text-sm">Cuộn xuống</span>
          <div className="w-px h-8 bg-gray-300 animate-pulse"></div>
        </div>
      </div>
    </section>
  );
}
