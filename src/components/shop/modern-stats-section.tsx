"use client";

import { useState, useEffect } from "react";
import { ShoppingBag, Users, Star, Truck, Award, Globe, Clock, Shield } from "lucide-react";

interface StatItem {
  icon: React.ReactNode;
  value: string;
  label: string;
  suffix: string;
  description: string;
}

export function ModernStatsSection() {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.1 }
    );

    const element = document.getElementById("stats-section");
    if (element) {
      observer.observe(element);
    }

    return () => {
      if (element) {
        observer.unobserve(element);
      }
    };
  }, []);

  const stats: StatItem[] = [
    {
      icon: <ShoppingBag className="h-8 w-8 text-gray-700" />,
      value: "10,000",
      label: "Sản phẩm",
      suffix: "+",
      description: "Đa dạng và chất lượng",
    },
    {
      icon: <Users className="h-8 w-8 text-gray-700" />,
      value: "50,000",
      label: "Khách hàng",
      suffix: "+",
      description: "Tin tưởng và hài lòng",
    },
    {
      icon: <Star className="h-8 w-8 text-gray-700" />,
      value: "4.9",
      label: "Đánh giá",
      suffix: "/5",
      description: "Từ khách hàng thực tế",
    },
    {
      icon: <Truck className="h-8 w-8 text-gray-700" />,
      value: "24",
      label: "Giờ giao hàng",
      suffix: "h",
      description: "Nhanh chóng và đáng tin cậy",
    },
  ];

  const features = [
    {
      icon: <Award className="h-6 w-6 text-gray-600" />,
      title: "Chất lượng đảm bảo",
      description: "100% sản phẩm chính hãng",
    },
    {
      icon: <Globe className="h-6 w-6 text-gray-600" />,
      title: "Giao hàng toàn quốc",
      description: "Miễn phí với đơn từ 500k",
    },
    {
      icon: <Clock className="h-6 w-6 text-gray-600" />,
      title: "Hỗ trợ 24/7",
      description: "Luôn sẵn sàng phục vụ bạn",
    },
    {
      icon: <Shield className="h-6 w-6 text-gray-600" />,
      title: "Bảo hành dài hạn",
      description: "Đổi trả trong 30 ngày",
    },
  ];

  const AnimatedNumber = ({
    value,
    suffix = "",
  }: {
    value: string;
    suffix?: string;
  }) => {
    const [displayValue, setDisplayValue] = useState("0");

    useEffect(() => {
      if (!isVisible) return;

      const numericValue = parseFloat(value.replace(/,/g, ""));
      const isDecimal = value.includes(".");
      
      if (isNaN(numericValue)) {
        setDisplayValue(value);
        return;
      }

      let start = 0;
      const duration = 2000;
      const increment = numericValue / (duration / 16);

      const timer = setInterval(() => {
        start += increment;
        if (start >= numericValue) {
          setDisplayValue(isDecimal ? numericValue.toFixed(1) : numericValue.toLocaleString());
          clearInterval(timer);
        } else {
          setDisplayValue(isDecimal ? start.toFixed(1) : Math.floor(start).toLocaleString());
        }
      }, 16);

      return () => clearInterval(timer);
    }, [isVisible, value]);

    return (
      <span className="font-bold text-4xl lg:text-5xl text-gray-900">
        {displayValue}{suffix}
      </span>
    );
  };

  return (
    <section id="stats-section" className="py-20 lg:py-32 bg-white">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-gray-100 rounded-full px-4 py-2 mb-6">
            <div className="w-2 h-2 bg-black rounded-full"></div>
            <span className="text-sm font-medium text-gray-700">Thống kê</span>
          </div>
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Những con số
            <br />
            <span className="text-gray-600">ấn tượng của chúng tôi</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Được hàng nghìn khách hàng tin tưởng và lựa chọn mỗi ngày.
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-8 mb-20">
          {stats.map((stat, index) => (
            <div
              key={index}
              className="text-center group"
            >
              <div className="flex justify-center mb-4">
                <div className="p-4 bg-gray-100 rounded-2xl group-hover:bg-gray-200 transition-colors duration-300">
                  {stat.icon}
                </div>
              </div>
              <div className="mb-2">
                <AnimatedNumber value={stat.value} suffix={stat.suffix} />
              </div>
              <div className="text-lg font-semibold text-gray-900 mb-1">
                {stat.label}
              </div>
              <div className="text-sm text-gray-600">
                {stat.description}
              </div>
            </div>
          ))}
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <div
              key={index}
              className="text-center p-6 bg-gray-50 rounded-2xl hover:bg-gray-100 transition-colors duration-300"
            >
              <div className="flex justify-center mb-4">
                <div className="p-3 bg-white rounded-xl shadow-sm">
                  {feature.icon}
                </div>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {feature.title}
              </h3>
              <p className="text-sm text-gray-600">
                {feature.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
