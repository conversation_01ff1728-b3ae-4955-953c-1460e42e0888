"use client";

import Link from "next/link";
import { Instagram, Heart, MessageCircle, ExternalLink } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

export function InstagramFeed() {
  // Mock Instagram posts data
  const instagramPosts = [
    {
      id: "1",
      image: "/images/instagram/post-1.jpg",
      likes: 1234,
      comments: 56,
      caption: "Outfit of the day 💫 #OOTD #NSShop",
      url: "https://instagram.com/p/example1"
    },
    {
      id: "2", 
      image: "/images/instagram/post-2.jpg",
      likes: 2341,
      comments: 89,
      caption: "New collection vibes ✨ #NewIn",
      url: "https://instagram.com/p/example2"
    },
    {
      id: "3",
      image: "/images/instagram/post-3.jpg", 
      likes: 3456,
      comments: 123,
      caption: "Summer essentials 🌞 #Summer2024",
      url: "https://instagram.com/p/example3"
    },
    {
      id: "4",
      image: "/images/instagram/post-4.jpg",
      likes: 1876,
      comments: 67,
      caption: "Street style inspiration 🔥 #StreetStyle",
      url: "https://instagram.com/p/example4"
    },
    {
      id: "5",
      image: "/images/instagram/post-5.jpg",
      likes: 2987,
      comments: 145,
      caption: "Weekend mood 💕 #WeekendVibes",
      url: "https://instagram.com/p/example5"
    },
    {
      id: "6",
      image: "/images/instagram/post-6.jpg",
      likes: 4123,
      comments: 234,
      caption: "Fashion forward 👗 #FashionForward",
      url: "https://instagram.com/p/example6"
    }
  ];

  return (
    <section className="py-20 bg-gradient-to-br from-fashion-50 via-background to-fashion-50 dark:from-fashion-900/10 dark:via-background dark:to-fashion-900/10">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="p-3 bg-gradient-to-br from-pink-500 to-purple-600 rounded-2xl">
              <Instagram className="h-8 w-8 text-white" />
            </div>
            <h2 className="text-3xl lg:text-4xl font-bold">
              Theo dõi chúng tôi trên Instagram
            </h2>
          </div>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto mb-6">
            Khám phá những xu hướng thời trang mới nhất và cách phối đồ từ cộng đồng NS Shop
          </p>
          <Button asChild variant="outline" size="lg" className="group">
            <Link href="https://instagram.com/nsshop" target="_blank" rel="noopener noreferrer">
              <Instagram className="h-5 w-5 mr-2" />
              @nsshop
              <ExternalLink className="h-4 w-4 ml-2 opacity-0 group-hover:opacity-100 transition-opacity" />
            </Link>
          </Button>
        </div>

        {/* Instagram Grid */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          {instagramPosts.map((post, index) => (
            <div
              key={post.id}
              className="group relative aspect-square overflow-hidden rounded-xl bg-gradient-to-br from-muted to-muted-foreground/10 hover:scale-105 transition-all duration-300"
            >
              {/* Placeholder for Instagram image */}
              <div className="absolute inset-0 bg-gradient-to-br from-fashion-200 via-fashion-300 to-fashion-400 opacity-30" />
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-2xl font-bold text-fashion-500/50">
                  {index + 1}
                </div>
              </div>

              {/* Hover Overlay */}
              <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                <div className="text-center text-white">
                  <div className="flex items-center justify-center gap-4 mb-2">
                    <div className="flex items-center gap-1">
                      <Heart className="h-4 w-4" />
                      <span className="text-sm font-medium">
                        {post.likes.toLocaleString()}
                      </span>
                    </div>
                    <div className="flex items-center gap-1">
                      <MessageCircle className="h-4 w-4" />
                      <span className="text-sm font-medium">
                        {post.comments}
                      </span>
                    </div>
                  </div>
                  <p className="text-xs opacity-90 line-clamp-2 px-2">
                    {post.caption}
                  </p>
                </div>
              </div>

              {/* Link to Instagram post */}
              <Link
                href={post.url}
                target="_blank"
                rel="noopener noreferrer"
                className="absolute inset-0 z-10"
                aria-label={`Xem bài viết Instagram: ${post.caption}`}
              />

              {/* Instagram icon indicator */}
              <div className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity">
                <Badge className="bg-white/20 text-white border-white/30">
                  <Instagram className="h-3 w-3" />
                </Badge>
              </div>
            </div>
          ))}
        </div>

        {/* Call to Action */}
        <div className="text-center mt-12">
          <p className="text-muted-foreground mb-4">
            Chia sẻ outfit của bạn với hashtag <span className="font-semibold text-fashion-600">#NSShop</span> để có cơ hội được featured!
          </p>
          <Button asChild variant="fashion" size="lg">
            <Link href="https://instagram.com/nsshop" target="_blank" rel="noopener noreferrer">
              <Instagram className="h-5 w-5 mr-2" />
              Theo dõi ngay
            </Link>
          </Button>
        </div>
      </div>
    </section>
  );
}
