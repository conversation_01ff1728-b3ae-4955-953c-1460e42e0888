"use client";

import { useState } from "react";
import { Mail, Send, Check, Gift, Zap, Bell } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";

export function ModernNewsletterSection() {
  const [email, setEmail] = useState("");
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email) return;

    setIsLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsSubscribed(true);
      setIsLoading(false);
      setEmail("");
    }, 1500);
  };

  const benefits = [
    {
      icon: <Gift className="h-6 w-6 text-white" />,
      title: "Ưu đãi độc quyền",
      description: "<PERSON><PERSON><PERSON><PERSON> g<PERSON><PERSON> đến 50% chỉ dành cho subscriber",
    },
    {
      icon: <Zap className="h-6 w-6 text-white" />,
      title: "<PERSON> hướng mới nhất",
      description: "Cập nhật bộ sưu tập và trend thời trang",
    },
    {
      icon: <Bell className="h-6 w-6 text-white" />,
      title: "Thông báo sớm",
      description: "Biết trước về sale và sự kiện đặc biệt",
    },
  ];

  return (
    <section className="py-20 lg:py-32 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          {/* Main Newsletter Card */}
          <Card className="border-0 bg-gradient-to-br from-gray-900 via-black to-gray-800 text-white overflow-hidden relative">
            <CardContent className="p-0">
              {/* Background Pattern */}
              <div className="absolute inset-0 opacity-10">
                <div className="absolute top-10 left-10 w-32 h-32 bg-white rounded-full blur-2xl"></div>
                <div className="absolute bottom-10 right-10 w-48 h-48 bg-white rounded-full blur-3xl"></div>
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-white rounded-full blur-3xl"></div>
              </div>

              <div className="relative p-8 lg:p-12">
                <div className="grid lg:grid-cols-2 gap-12 items-center">
                  {/* Left Content */}
                  <div>
                    <div className="inline-flex items-center gap-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 mb-6">
                      <Mail className="h-4 w-4" />
                      <span className="text-sm font-medium">Newsletter</span>
                    </div>
                    
                    <h2 className="text-3xl lg:text-4xl font-bold mb-6">
                      Đăng ký nhận tin
                      <br />
                      <span className="text-gray-300">để không bỏ lỡ ưu đãi</span>
                    </h2>
                    
                    <p className="text-lg text-gray-300 mb-8">
                      Hãy là người đầu tiên biết về những bộ sưu tập mới, 
                      xu hướng thời trang và những ưu đãi đặc biệt chỉ dành cho bạn.
                    </p>

                    {/* Newsletter Form */}
                    {!isSubscribed ? (
                      <form onSubmit={handleSubmit} className="space-y-4">
                        <div className="flex flex-col sm:flex-row gap-4">
                          <Input
                            type="email"
                            placeholder="Nhập email của bạn..."
                            value={email}
                            onChange={(e) => setEmail(e.target.value)}
                            className="flex-1 bg-white/10 backdrop-blur-sm border-white/20 text-white placeholder:text-gray-400 focus:border-white/40 h-12"
                            required
                          />
                          <Button
                            type="submit"
                            disabled={isLoading}
                            className="bg-white text-black hover:bg-gray-100 px-8 h-12 font-medium"
                          >
                            {isLoading ? (
                              <div className="w-5 h-5 border-2 border-black/20 border-t-black rounded-full animate-spin" />
                            ) : (
                              <>
                                <Send className="h-4 w-4 mr-2" />
                                Đăng ký
                              </>
                            )}
                          </Button>
                        </div>
                        <p className="text-sm text-gray-400">
                          Bằng cách đăng ký, bạn đồng ý với{" "}
                          <a href="/privacy" className="underline hover:text-white transition-colors">
                            Chính sách bảo mật
                          </a>{" "}
                          của chúng tôi.
                        </p>
                      </form>
                    ) : (
                      <div className="bg-green-500/20 border border-green-500/30 rounded-2xl p-6">
                        <div className="flex items-center gap-3 mb-2">
                          <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                            <Check className="h-5 w-5 text-white" />
                          </div>
                          <span className="font-semibold">Đăng ký thành công!</span>
                        </div>
                        <p className="text-gray-300">
                          Cảm ơn bạn đã đăng ký. Chúng tôi sẽ gửi những ưu đãi tốt nhất đến email của bạn.
                        </p>
                      </div>
                    )}
                  </div>

                  {/* Right Content - Benefits */}
                  <div className="space-y-6">
                    <h3 className="text-xl font-semibold mb-6">Quyền lợi của subscriber:</h3>
                    {benefits.map((benefit, index) => (
                      <div key={index} className="flex items-start gap-4">
                        <div className="w-12 h-12 bg-white/10 backdrop-blur-sm rounded-xl flex items-center justify-center flex-shrink-0">
                          {benefit.icon}
                        </div>
                        <div>
                          <h4 className="font-semibold mb-1">{benefit.title}</h4>
                          <p className="text-gray-300 text-sm">{benefit.description}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-16">
            <div className="text-center">
              <div className="text-3xl lg:text-4xl font-bold text-gray-900 mb-2">25K+</div>
              <div className="text-gray-600">Subscriber hài lòng</div>
            </div>
            <div className="text-center">
              <div className="text-3xl lg:text-4xl font-bold text-gray-900 mb-2">50%</div>
              <div className="text-gray-600">Giảm giá tối đa</div>
            </div>
            <div className="text-center">
              <div className="text-3xl lg:text-4xl font-bold text-gray-900 mb-2">2x</div>
              <div className="text-gray-600">Mỗi tuần gửi tin</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
