"use client";

import { ClientImage } from "@/components/ui/client-image";

const brands = [
  {
    id: 1,
    name: "<PERSON>",
    logo: "/images/brands/nike.svg",
    description: "Thể thao và lifestyle",
  },
  {
    id: 2,
    name: "Adidas",
    logo: "/images/brands/adidas.svg",
    description: "Performance và style",
  },
  {
    id: 3,
    name: "<PERSON><PERSON>",
    logo: "/images/brands/zara.svg",
    description: "Fast fashion",
  },
  {
    id: 4,
    name: "H&M",
    logo: "/images/brands/hm.svg",
    description: "Thời trang bền vững",
  },
  {
    id: 5,
    name: "Uniqlo",
    logo: "/images/brands/uniqlo.svg",
    description: "Minimalist và chất lượng",
  },
  {
    id: 6,
    name: "G<PERSON>",
    logo: "/images/brands/gucci.svg",
    description: "Luxury fashion",
  },
  {
    id: 7,
    name: "<PERSON>",
    logo: "/images/brands/lv.svg",
    description: "Haute couture",
  },
  {
    id: 8,
    name: "<PERSON><PERSON>",
    logo: "/images/brands/chanel.svg",
    description: "Timeless elegance",
  },
];

export function ModernBrandsSection() {
  return (
    <section className="py-20 lg:py-32 bg-gray-50">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-white rounded-full px-4 py-2 mb-6 shadow-sm">
            <div className="w-2 h-2 bg-black rounded-full"></div>
            <span className="text-sm font-medium text-gray-700">
              Thương hiệu
            </span>
          </div>
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Đối tác thương hiệu
            <br />
            <span className="text-gray-600">hàng đầu thế giới</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Chúng tôi tự hào hợp tác với những thương hiệu uy tín nhất để mang
            đến cho bạn những sản phẩm chất lượng cao.
          </p>
        </div>

        {/* Brands Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-8 mb-16">
          {brands.map((brand) => (
            <div key={brand.id} className="group cursor-pointer">
              <div className="bg-white rounded-2xl p-6 hover:shadow-lg transition-all duration-300 border border-gray-100 hover:border-gray-200">
                <div className="aspect-square relative mb-4">
                  <ClientImage
                    src={brand.logo}
                    alt={brand.name}
                    fill
                    className="object-contain filter grayscale group-hover:grayscale-0 transition-all duration-300"
                    fallbackSrc="/images/placeholder.jpg"
                  />
                </div>
                <div className="text-center">
                  <h3 className="font-semibold text-gray-900 mb-1 group-hover:text-black transition-colors">
                    {brand.name}
                  </h3>
                  <p className="text-xs text-gray-600">{brand.description}</p>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Brand Stats */}
        <div className="bg-white rounded-3xl p-8 lg:p-12 shadow-sm border border-gray-100">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
            <div>
              <div className="text-3xl lg:text-4xl font-bold text-gray-900 mb-2">
                100+
              </div>
              <div className="text-lg font-semibold text-gray-700 mb-1">
                Thương hiệu
              </div>
              <div className="text-sm text-gray-600">Đối tác chính thức</div>
            </div>
            <div>
              <div className="text-3xl lg:text-4xl font-bold text-gray-900 mb-2">
                50+
              </div>
              <div className="text-lg font-semibold text-gray-700 mb-1">
                Quốc gia
              </div>
              <div className="text-sm text-gray-600">Trên toàn thế giới</div>
            </div>
            <div>
              <div className="text-3xl lg:text-4xl font-bold text-gray-900 mb-2">
                99%
              </div>
              <div className="text-lg font-semibold text-gray-700 mb-1">
                Chính hãng
              </div>
              <div className="text-sm text-gray-600">Đảm bảo chất lượng</div>
            </div>
          </div>
        </div>

        {/* Partnership Benefits */}
        <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="text-center">
            <div className="w-16 h-16 bg-black rounded-2xl flex items-center justify-center mx-auto mb-4">
              <div className="w-8 h-8 bg-white rounded-lg"></div>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Chính hãng 100%
            </h3>
            <p className="text-gray-600">
              Tất cả sản phẩm đều được nhập khẩu chính hãng từ các thương hiệu
              uy tín.
            </p>
          </div>
          <div className="text-center">
            <div className="w-16 h-16 bg-black rounded-2xl flex items-center justify-center mx-auto mb-4">
              <div className="w-8 h-8 bg-white rounded-lg"></div>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Giá tốt nhất
            </h3>
            <p className="text-gray-600">
              Cam kết mang đến giá cả cạnh tranh nhất thị trường cho khách hàng.
            </p>
          </div>
          <div className="text-center">
            <div className="w-16 h-16 bg-black rounded-2xl flex items-center justify-center mx-auto mb-4">
              <div className="w-8 h-8 bg-white rounded-lg"></div>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Bảo hành chính hãng
            </h3>
            <p className="text-gray-600">
              Được hưởng đầy đủ chế độ bảo hành và hậu mãi từ thương hiệu.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}
