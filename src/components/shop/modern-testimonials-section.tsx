"use client";

import { useState } from "react";
import { Star, ChevronLeft, ChevronRight, Quote } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";

const testimonials = [
  {
    id: 1,
    name: "<PERSON>uyễn <PERSON>",
    role: "<PERSON>h<PERSON>ch hàng thân thiết",
    avatar: "/images/avatars/avatar-1.jpg",
    rating: 5,
    content: "Chất lượng sản phẩm tuyệt vời, giao hàng nhanh chóng. Tôi đã mua rất nhiều lần và luôn hài lòng với dịch vụ của NS Shop.",
    product: "Váy maxi hoa nhí",
    date: "2 tuần trước",
  },
  {
    id: 2,
    name: "Trầ<PERSON>",
    role: "Blogger thời trang",
    avatar: "/images/avatars/avatar-2.jpg",
    rating: 5,
    content: "NS Shop có bộ sưu tập đa dạng và trendy. Đặc biệt là chất lượng vải rất tốt, gi<PERSON> cả hợp lý. Highly recommended!",
    product: "Áo thun cotton premium",
    date: "1 tuần trước",
  },
  {
    id: 3,
    name: "Lê Thị Hương",
    role: "Nhân viên văn phòng",
    avatar: "/images/avatars/avatar-3.jpg",
    rating: 5,
    content: "Mình rất thích phong cách phục vụ của NS Shop. Nhân viên tư vấn nhiệt tình, sản phẩm đúng như mô tả.",
    product: "Quần jeans skinny",
    date: "3 ngày trước",
  },
  {
    id: 4,
    name: "Phạm Đức Anh",
    role: "Sinh viên",
    avatar: "/images/avatars/avatar-4.jpg",
    rating: 4,
    content: "Giá cả phải chăng, phù hợp với sinh viên như mình. Chất lượng ổn, giao hàng đúng hẹn. Sẽ tiếp tục ủng hộ!",
    product: "Áo khoác blazer",
    date: "5 ngày trước",
  },
  {
    id: 5,
    name: "Võ Thị Mai",
    role: "Giáo viên",
    avatar: "/images/avatars/avatar-5.jpg",
    rating: 5,
    content: "Tôi đã giới thiệu NS Shop cho nhiều đồng nghiệp. Sản phẩm chất lượng, phù hợp với nhiều độ tuổi và phong cách.",
    product: "Túi xách da thật",
    date: "1 tuần trước",
  },
];

export function ModernTestimonialsSection() {
  const [currentIndex, setCurrentIndex] = useState(0);

  const nextTestimonial = () => {
    setCurrentIndex((prev) => (prev + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setCurrentIndex((prev) => (prev - 1 + testimonials.length) % testimonials.length);
  };

  const goToTestimonial = (index: number) => {
    setCurrentIndex(index);
  };

  return (
    <section className="py-20 lg:py-32 bg-white">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-gray-100 rounded-full px-4 py-2 mb-6">
            <div className="w-2 h-2 bg-black rounded-full"></div>
            <span className="text-sm font-medium text-gray-700">Đánh giá khách hàng</span>
          </div>
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Khách hàng nói gì
            <br />
            <span className="text-gray-600">về chúng tôi</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Hàng nghìn khách hàng đã tin tưởng và hài lòng với sản phẩm, dịch vụ của NS Shop.
          </p>
        </div>

        {/* Testimonials Carousel */}
        <div className="relative max-w-4xl mx-auto">
          <Card className="border-0 shadow-xl bg-gray-50">
            <CardContent className="p-8 lg:p-12">
              <div className="text-center">
                {/* Quote Icon */}
                <div className="flex justify-center mb-6">
                  <div className="w-16 h-16 bg-black rounded-2xl flex items-center justify-center">
                    <Quote className="h-8 w-8 text-white" />
                  </div>
                </div>

                {/* Rating */}
                <div className="flex justify-center mb-6">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`h-6 w-6 ${
                        i < testimonials[currentIndex].rating
                          ? "text-yellow-400 fill-current"
                          : "text-gray-300"
                      }`}
                    />
                  ))}
                </div>

                {/* Content */}
                <blockquote className="text-xl lg:text-2xl text-gray-900 font-medium mb-8 leading-relaxed">
                  "{testimonials[currentIndex].content}"
                </blockquote>

                {/* Product */}
                <div className="text-sm text-gray-600 mb-6">
                  Sản phẩm: <span className="font-medium">{testimonials[currentIndex].product}</span>
                </div>

                {/* Author */}
                <div className="flex items-center justify-center gap-4">
                  <div className="w-16 h-16 bg-gray-300 rounded-full flex items-center justify-center">
                    <span className="text-gray-600 font-medium text-lg">
                      {testimonials[currentIndex].name.charAt(0)}
                    </span>
                  </div>
                  <div className="text-left">
                    <div className="font-semibold text-gray-900">
                      {testimonials[currentIndex].name}
                    </div>
                    <div className="text-sm text-gray-600">
                      {testimonials[currentIndex].role}
                    </div>
                    <div className="text-xs text-gray-500">
                      {testimonials[currentIndex].date}
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Navigation Buttons */}
          <div className="flex justify-center gap-4 mt-8">
            <Button
              variant="outline"
              size="icon"
              onClick={prevTestimonial}
              className="w-12 h-12 rounded-full border-2 border-gray-300 hover:border-gray-400"
            >
              <ChevronLeft className="h-5 w-5" />
            </Button>
            <Button
              variant="outline"
              size="icon"
              onClick={nextTestimonial}
              className="w-12 h-12 rounded-full border-2 border-gray-300 hover:border-gray-400"
            >
              <ChevronRight className="h-5 w-5" />
            </Button>
          </div>

          {/* Dots Indicator */}
          <div className="flex justify-center gap-2 mt-6">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => goToTestimonial(index)}
                className={`w-3 h-3 rounded-full transition-colors ${
                  index === currentIndex ? "bg-black" : "bg-gray-300"
                }`}
              />
            ))}
          </div>
        </div>

        {/* Overall Rating */}
        <div className="mt-16 text-center">
          <div className="inline-flex items-center gap-4 bg-gray-50 rounded-2xl px-8 py-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-gray-900">4.9</div>
              <div className="flex justify-center mt-1">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className="h-4 w-4 text-yellow-400 fill-current"
                  />
                ))}
              </div>
            </div>
            <div className="w-px h-12 bg-gray-300"></div>
            <div className="text-left">
              <div className="text-lg font-semibold text-gray-900">Đánh giá trung bình</div>
              <div className="text-sm text-gray-600">Từ 10,000+ khách hàng</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
