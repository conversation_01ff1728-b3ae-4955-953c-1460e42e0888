'use client';

import React from 'react';
import { 
  useSettings, 
  useUser, 
  useCart, 
  useSearch, 
  useNotifications,
  useTheme 
} from '@/contexts';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ThemeToggle } from '@/contexts';

/**
 * Example component demonstrating proper usage of all context hooks
 * This serves as documentation and reference for developers
 */
export function HooksUsageExample() {
  // ✅ Recommended: Use standardized hook names
  const { settings, loading: settingsLoading } = useSettings();
  const { user, loading: userLoading, updateProfile } = useUser();
  const { cart, summary, addToCart, removeFromCart } = useCart();
  const { query, setQuery, results, loading: searchLoading } = useSearch();
  const { notifications, unreadCount } = useNotifications();
  const { theme, setTheme } = useTheme();

  const handleAddToCart = async () => {
    try {
      await addToCart({
        productId: 'example-product-id',
        quantity: 1,
        variantId: 'example-variant-id'
      });
    } catch (error) {
      console.error('Failed to add to cart:', error);
    }
  };

  const handleSearch = (searchTerm: string) => {
    setQuery(searchTerm);
  };

  const handleUpdateProfile = async () => {
    try {
      await updateProfile({
        name: 'Updated Name',
        phone: '0123456789'
      });
    } catch (error) {
      console.error('Failed to update profile:', error);
    }
  };

  return (
    <div className="space-y-6 p-6">
      <Card>
        <CardHeader>
          <CardTitle>Hooks Usage Example</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          
          {/* Settings Hook */}
          <div className="space-y-2">
            <h3 className="font-semibold">Settings Hook</h3>
            {settingsLoading ? (
              <div className="text-sm text-muted-foreground">Loading settings...</div>
            ) : (
              <div className="text-sm space-y-1">
                <div>Site Name: {settings?.siteName}</div>
                <div>Contact Phone: {settings?.contactInfo?.phone}</div>
                <div>Free Shipping Threshold: {settings?.shippingSettings?.freeShippingThreshold?.toLocaleString('vi-VN')}đ</div>
              </div>
            )}
          </div>

          {/* User Hook */}
          <div className="space-y-2">
            <h3 className="font-semibold">User Hook</h3>
            {userLoading ? (
              <div className="text-sm text-muted-foreground">Loading user...</div>
            ) : user ? (
              <div className="text-sm space-y-1">
                <div>Name: {user.name}</div>
                <div>Email: {user.email}</div>
                <div>Phone: {user.phone || 'Not set'}</div>
                <Button size="sm" onClick={handleUpdateProfile}>
                  Update Profile
                </Button>
              </div>
            ) : (
              <div className="text-sm text-muted-foreground">Not logged in</div>
            )}
          </div>

          {/* Cart Hook */}
          <div className="space-y-2">
            <h3 className="font-semibold">Cart Hook</h3>
            <div className="text-sm space-y-1">
              <div>Items in cart: {summary?.totalQuantity || 0}</div>
              <div>Total: {summary?.total?.toLocaleString('vi-VN')}đ</div>
              <div className="flex gap-2">
                <Button size="sm" onClick={handleAddToCart}>
                  Add to Cart
                </Button>
                {cart?.items?.length > 0 && (
                  <Button 
                    size="sm" 
                    variant="outline"
                    onClick={() => removeFromCart(cart.items[0].id)}
                  >
                    Remove First Item
                  </Button>
                )}
              </div>
            </div>
          </div>

          {/* Search Hook */}
          <div className="space-y-2">
            <h3 className="font-semibold">Search Hook</h3>
            <div className="text-sm space-y-1">
              <div>Current query: {query || 'None'}</div>
              <div>Results count: {results?.length || 0}</div>
              <div className="flex gap-2">
                <Button size="sm" onClick={() => handleSearch('shoes')}>
                  Search "shoes"
                </Button>
                <Button size="sm" onClick={() => handleSearch('')}>
                  Clear Search
                </Button>
              </div>
              {searchLoading && (
                <div className="text-muted-foreground">Searching...</div>
              )}
            </div>
          </div>

          {/* Notifications Hook */}
          <div className="space-y-2">
            <h3 className="font-semibold">Notifications Hook</h3>
            <div className="text-sm space-y-1">
              <div className="flex items-center gap-2">
                <span>Unread notifications:</span>
                <Badge variant="secondary">{unreadCount}</Badge>
              </div>
              <div>Total notifications: {notifications?.length || 0}</div>
            </div>
          </div>

          {/* Theme Hook */}
          <div className="space-y-2">
            <h3 className="font-semibold">Theme Hook</h3>
            <div className="text-sm space-y-1">
              <div>Current theme: {theme}</div>
              <div className="flex gap-2 items-center">
                <span>Toggle theme:</span>
                <ThemeToggle />
              </div>
              <div className="flex gap-2">
                <Button size="sm" onClick={() => setTheme('light')}>
                  Light
                </Button>
                <Button size="sm" onClick={() => setTheme('dark')}>
                  Dark
                </Button>
                <Button size="sm" onClick={() => setTheme('system')}>
                  System
                </Button>
              </div>
            </div>
          </div>

        </CardContent>
      </Card>

      {/* Best Practices */}
      <Card>
        <CardHeader>
          <CardTitle>Best Practices</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-sm">
          <div>✅ Use standardized hook names: useSettings, useUser, useCart, useSearch</div>
          <div>✅ Import from @/contexts for consistency</div>
          <div>✅ Handle loading states appropriately</div>
          <div>✅ Wrap async operations in try-catch blocks</div>
          <div>✅ Check for data existence before using</div>
          <div>⚠️ Avoid direct context imports unless needed for specific functionality</div>
          <div>❌ Don't forget to handle error states</div>
        </CardContent>
      </Card>
    </div>
  );
}

// Usage in pages:
/*
import { HooksUsageExample } from '@/components/examples/HooksUsageExample';

export default function ExamplePage() {
  return (
    <div>
      <HooksUsageExample />
    </div>
  );
}
*/