'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
	Search, 
	Filter, 
	X, 
	Star,
	DollarSign,
	Package,
	Tags
} from 'lucide-react';

interface Category {
	id: string;
	name: string;
	children?: Category[];
}

interface AdvancedSearchProps {
	categories: Category[];
	onClose?: () => void;
}

export function AdvancedSearch({ categories, onClose }: AdvancedSearchProps) {
	const router = useRouter();
	const searchParams = useSearchParams();
	
	const [filters, setFilters] = useState({
		search: searchParams.get('search') || '',
		category: searchParams.get('category') || '',
		minPrice: searchParams.get('minPrice') || '',
		maxPrice: searchParams.get('maxPrice') || '',
		rating: searchParams.get('rating') || '',
		inStock: searchParams.get('inStock') === 'true',
		featured: searchParams.get('featured') === 'true',
		sortBy: searchParams.get('sortBy') || 'createdAt',
		sortOrder: searchParams.get('sortOrder') || 'desc',
	});

	const handleFilterChange = (key: string, value: string | boolean) => {
		setFilters({ ...filters, [key]: value });
	};

	const handleSearch = () => {
		const params = new URLSearchParams();

		// Add non-empty filters to params
		Object.entries(filters).forEach(([key, value]) => {
			if (value !== '' && value !== false) {
				params.set(key, value.toString());
			}
		});

		// Navigate to products page with filters
		router.push(`/products?${params.toString()}`);
		onClose?.();
	};

	const handleReset = () => {
		setFilters({
			search: '',
			category: '',
			minPrice: '',
			maxPrice: '',
			rating: '',
			inStock: false,
			featured: false,
			sortBy: 'createdAt',
			sortOrder: 'desc',
		});
	};

	const renderCategories = (cats: Category[], level = 0) => {
		return cats.map((cat) => (
			<div key={cat.id} style={{ marginLeft: level * 16 }}>
				<label className="flex items-center space-x-2 py-1">
					<input
						type="radio"
						name="category"
						value={cat.id}
						checked={filters.category === cat.id}
						onChange={(e) => handleFilterChange('category', e.target.value)}
						className="text-pink-600 focus:ring-pink-500"
					/>
					<span className="text-sm">{cat.name}</span>
				</label>
				{cat.children && cat.children.length > 0 && (
					<div className="ml-4">
						{renderCategories(cat.children, level + 1)}
					</div>
				)}
			</div>
		));
	};

	return (
		<Card className="w-full max-w-4xl mx-auto">
			<CardHeader>
				<div className="flex items-center justify-between">
					<CardTitle className="flex items-center gap-2">
						<Filter className="h-5 w-5" />
						Tìm kiếm nâng cao
					</CardTitle>
					{onClose && (
						<Button variant="ghost" size="sm" onClick={onClose}>
							<X className="h-4 w-4" />
						</Button>
					)}
				</div>
			</CardHeader>
			<CardContent className="space-y-6">
				{/* Search Input */}
				<div>
					<label className="block text-sm font-medium mb-2">
						Từ khóa tìm kiếm
					</label>
					<div className="relative">
						<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
						<input
							type="text"
							value={filters.search}
							onChange={(e) => handleFilterChange('search', e.target.value)}
							placeholder="Tìm kiếm sản phẩm..."
							className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
						/>
					</div>
				</div>

				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
					{/* Categories */}
					<div>
						<label className="block text-sm font-medium mb-2">
							<Tags className="inline h-4 w-4 mr-1" />
							Danh mục
						</label>
						<div className="max-h-48 overflow-y-auto border border-gray-200 rounded-lg p-3">
							<label className="flex items-center space-x-2 py-1 mb-2">
								<input
									type="radio"
									name="category"
									value=""
									checked={filters.category === ''}
									onChange={(e) => handleFilterChange('category', e.target.value)}
									className="text-pink-600 focus:ring-pink-500"
								/>
								<span className="text-sm font-medium">Tất cả danh mục</span>
							</label>
							{renderCategories(categories)}
						</div>
					</div>

					{/* Price Range */}
					<div>
						<label className="block text-sm font-medium mb-2">
							<DollarSign className="inline h-4 w-4 mr-1" />
							Khoảng giá
						</label>
						<div className="space-y-2">
							<input
								type="number"
								value={filters.minPrice}
								onChange={(e) => handleFilterChange('minPrice', e.target.value)}
								placeholder="Giá từ"
								className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
							/>
							<input
								type="number"
								value={filters.maxPrice}
								onChange={(e) => handleFilterChange('maxPrice', e.target.value)}
								placeholder="Giá đến"
								className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
							/>
						</div>
						
						{/* Quick Price Ranges */}
						<div className="mt-3 space-y-1">
							<p className="text-xs text-muted-foreground">Khoảng giá phổ biến:</p>
							<div className="flex flex-wrap gap-1">
								{[
									{ label: 'Dưới 100K', min: '', max: '100000' },
									{ label: '100K-500K', min: '100000', max: '500000' },
									{ label: '500K-1M', min: '500000', max: '1000000' },
									{ label: 'Trên 1M', min: '1000000', max: '' },
								].map((range) => (
									<button
										key={range.label}
										type="button"
										onClick={() => {
											handleFilterChange('minPrice', range.min);
											handleFilterChange('maxPrice', range.max);
										}}
										className="text-xs px-2 py-1 bg-gray-100 hover:bg-gray-200 rounded transition-colors"
									>
										{range.label}
									</button>
								))}
							</div>
						</div>
					</div>

					{/* Rating & Other Filters */}
					<div className="space-y-4">
						{/* Rating */}
						<div>
							<label className="block text-sm font-medium mb-2">
								<Star className="inline h-4 w-4 mr-1" />
								Đánh giá tối thiểu
							</label>
							<select
								value={filters.rating}
								onChange={(e) => handleFilterChange('rating', e.target.value)}
								className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
							>
								<option value="">Tất cả</option>
								<option value="4">4 sao trở lên</option>
								<option value="3">3 sao trở lên</option>
								<option value="2">2 sao trở lên</option>
								<option value="1">1 sao trở lên</option>
							</select>
						</div>

						{/* Stock & Featured */}
						<div>
							<label className="block text-sm font-medium mb-2">
								<Package className="inline h-4 w-4 mr-1" />
								Tình trạng
							</label>
							<div className="space-y-2">
								<label className="flex items-center space-x-2">
									<input
										type="checkbox"
										checked={filters.inStock}
										onChange={(e) => handleFilterChange('inStock', e.target.checked)}
										className="text-pink-600 focus:ring-pink-500 rounded"
									/>
									<span className="text-sm">Còn hàng</span>
								</label>
								<label className="flex items-center space-x-2">
									<input
										type="checkbox"
										checked={filters.featured}
										onChange={(e) => handleFilterChange('featured', e.target.checked)}
										className="text-pink-600 focus:ring-pink-500 rounded"
									/>
									<span className="text-sm">Sản phẩm nổi bật</span>
								</label>
							</div>
						</div>
					</div>
				</div>

				{/* Sort Options */}
				<div>
					<label className="block text-sm font-medium mb-2">
						Sắp xếp theo
					</label>
					<div className="grid grid-cols-2 md:grid-cols-4 gap-2">
						{[
							{ value: 'createdAt-desc', label: 'Mới nhất' },
							{ value: 'createdAt-asc', label: 'Cũ nhất' },
							{ value: 'price-asc', label: 'Giá thấp đến cao' },
							{ value: 'price-desc', label: 'Giá cao đến thấp' },
							{ value: 'name-asc', label: 'Tên A-Z' },
							{ value: 'name-desc', label: 'Tên Z-A' },
							{ value: 'avgRating-desc', label: 'Đánh giá cao nhất' },
							{ value: 'reviewCount-desc', label: 'Nhiều đánh giá nhất' },
						].map((option) => {
							const [sortBy, sortOrder] = option.value.split('-');
							const isSelected = filters.sortBy === sortBy && filters.sortOrder === sortOrder;
							
							return (
								<button
									key={option.value}
									type="button"
									onClick={() => {
										handleFilterChange('sortBy', sortBy);
										handleFilterChange('sortOrder', sortOrder);
									}}
									className={`text-sm px-3 py-2 border rounded-lg transition-colors ${
										isSelected
											? 'border-pink-500 bg-pink-50 text-pink-600'
											: 'border-gray-200 hover:border-gray-300'
									}`}
								>
									{option.label}
								</button>
							);
						})}
					</div>
				</div>

				{/* Actions */}
				<div className="flex gap-2 pt-4 border-t">
					<Button
						onClick={handleSearch}
						className="flex-1 bg-pink-600 hover:bg-pink-700"
					>
						<Search className="h-4 w-4 mr-2" />
						Tìm kiếm
					</Button>
					<Button
						variant="outline"
						onClick={handleReset}
						className="flex-1"
					>
						<X className="h-4 w-4 mr-2" />
						Đặt lại
					</Button>
				</div>
			</CardContent>
		</Card>
	);
}
