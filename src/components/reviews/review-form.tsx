'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Star, Upload, X } from 'lucide-react';
import { useReviews } from '@/hooks/use-reviews';

interface ReviewFormProps {
	productId: string;
	productName: string;
	onReviewSubmitted?: () => void;
}

export function ReviewForm({ productId, productName, onReviewSubmitted }: ReviewFormProps) {
	const [rating, setRating] = useState(0);
	const [hoverRating, setHoverRating] = useState(0);
	const [comment, setComment] = useState('');
	const [images, setImages] = useState<string[]>([]);

	const { submitReview, submitting } = useReviews({
		onSubmitSuccess: () => {
			setRating(0);
			setComment('');
			setImages([]);
			onReviewSubmitted?.();
		},
	});

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();

		if (rating === 0) {
			return;
		}

		await submitReview({
			productId,
			rating,
			title: comment.trim() || `Đánh giá ${rating} sao`,
			comment: comment.trim(),
		});
	};

	const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
		const files = e.target.files;
		if (!files) return;

		// In a real app, you would upload these files to a storage service
		// For now, we'll just create object URLs
		const newImages: string[] = [];
		for (let i = 0; i < Math.min(files.length, 5 - images.length); i++) {
			const file = files[i];
			if (file.type.startsWith('image/')) {
				newImages.push(URL.createObjectURL(file));
			}
		}

		setImages([...images, ...newImages]);
	};

	const removeImage = (index: number) => {
		const newImages = [...images];
		URL.revokeObjectURL(newImages[index]); // Clean up object URL
		newImages.splice(index, 1);
		setImages(newImages);
	};

	return (
		<Card>
			<CardHeader>
				<CardTitle>Đánh giá sản phẩm</CardTitle>
				<p className="text-sm text-muted-foreground">{productName}</p>
			</CardHeader>
			<CardContent>
				<form onSubmit={handleSubmit} className="space-y-6">
					{/* Rating */}
					<div>
						<label className="block text-sm font-medium mb-2">
							Đánh giá của bạn *
						</label>
						<div className="flex items-center gap-1">
							{Array.from({ length: 5 }, (_, i) => (
								<button
									key={i}
									type="button"
									onClick={() => setRating(i + 1)}
									onMouseEnter={() => setHoverRating(i + 1)}
									onMouseLeave={() => setHoverRating(0)}
									className="p-1 hover:scale-110 transition-transform"
								>
									<Star
										className={`h-8 w-8 ${
											i < (hoverRating || rating)
												? 'text-yellow-400 fill-current'
												: 'text-gray-300'
										}`}
									/>
								</button>
							))}
							<span className="ml-2 text-sm text-muted-foreground">
								{rating > 0 && (
									<>
										{rating} sao
										{rating === 1 && ' - Rất tệ'}
										{rating === 2 && ' - Tệ'}
										{rating === 3 && ' - Bình thường'}
										{rating === 4 && ' - Tốt'}
										{rating === 5 && ' - Rất tốt'}
									</>
								)}
							</span>
						</div>
					</div>

					{/* Comment */}
					<div>
						<label className="block text-sm font-medium mb-2">
							Nhận xét (tùy chọn)
						</label>
						<textarea
							value={comment}
							onChange={(e) => setComment(e.target.value)}
							rows={4}
							placeholder="Chia sẻ trải nghiệm của bạn về sản phẩm này..."
							className="w-full px-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent resize-none"
							maxLength={1000}
						/>
						<div className="text-right text-xs text-muted-foreground mt-1">
							{comment.length}/1000
						</div>
					</div>

					{/* Images */}
					<div>
						<label className="block text-sm font-medium mb-2">
							Hình ảnh (tùy chọn)
						</label>
						<div className="space-y-4">
							{/* Upload Button */}
							{images.length < 5 && (
								<div>
									<input
										type="file"
										id="review-images"
										multiple
										accept="image/*"
										onChange={handleImageUpload}
										className="hidden"
									/>
									<label
										htmlFor="review-images"
										className="inline-flex items-center gap-2 px-4 py-2 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors"
									>
										<Upload className="h-4 w-4" />
										Thêm hình ảnh ({images.length}/5)
									</label>
								</div>
							)}

							{/* Image Preview */}
							{images.length > 0 && (
								<div className="grid grid-cols-3 md:grid-cols-5 gap-2">
									{images.map((image, index) => (
										<div key={index} className="relative group">
											<img
												src={image}
												alt={`Review image ${index + 1}`}
												className="w-full aspect-square object-cover rounded-lg"
											/>
											<button
												type="button"
												onClick={() => removeImage(index)}
												className="absolute top-1 right-1 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
											>
												<X className="h-3 w-3" />
											</button>
										</div>
									))}
								</div>
							)}
						</div>
						<p className="text-xs text-muted-foreground mt-2">
							Tối đa 5 hình ảnh. Định dạng: JPG, PNG, GIF
						</p>
					</div>

					{/* Submit Button */}
					<div className="flex gap-2">
						<Button
							type="submit"
							disabled={submitting || rating === 0}
							className="bg-pink-600 hover:bg-pink-700"
						>
							{submitting ? 'Đang gửi...' : 'Gửi đánh giá'}
						</Button>
					</div>
				</form>
			</CardContent>
		</Card>
	);
}
