"use client";

import { motion } from "framer-motion";
import { useState, useEffect } from "react";
import { ChevronDown, ChevronUp, Search, Tag, Clock, Truck, CreditCard, Scissors, HelpCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

interface FAQ {
  id: string;
  question: string;
  answer: string;
  category: string;
  tags: string[];
  popular: boolean;
}

export function FAQListSection() {
  const [openIndex, setOpenIndex] = useState<number | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [filteredFAQs, setFilteredFAQs] = useState<FAQ[]>([]);

  const categories = [
    { id: "all", name: "Tất cả", icon: HelpCircle, color: "gray" },
    { id: "order", name: "Đặt hàng", icon: Scissors, color: "pink" },
    { id: "shipping", name: "<PERSON>ậ<PERSON> chuyển", icon: Truck, color: "blue" },
    { id: "payment", name: "<PERSON><PERSON> toán", icon: CreditCard, color: "green" },
    { id: "production", name: "Sản xuất", icon: Clock, color: "purple" },
  ];

  const faqs: FAQ[] = [
    {
      id: "1",
      question: "Phí vận chuyển thế nào?",
      answer: "Sau khi bạn đặt hàng tại xưởng may NS Shop, bạn sẽ được báo giá tổng phí ship. Tổng tiền phí ship sẽ phụ thuộc vào vị trí và hình thức nhận hàng của khách. Chúng tôi hỗ trợ giao hàng toàn quốc với nhiều hình thức vận chuyển khác nhau để phù hợp với nhu cầu của khách hàng.\n\n• Giao hàng nội thành TP.HCM: 30.000 - 50.000đ\n• Giao hàng tỉnh thành khác: 50.000 - 100.000đ\n• Miễn phí ship cho đơn hàng từ 2.000.000đ",
      category: "shipping",
      tags: ["vận chuyển", "phí ship", "giao hàng"],
      popular: true,
    },
    {
      id: "2",
      question: "Cách đặt hàng may gia công đơn giản nhất tại NS Shop?",
      answer: "Có rất nhiều cách đặt hàng may gia công tại NS Shop:\n\n• Đặt trực tiếp tại website: nsshop.com\n• Qua email: <EMAIL>\n• Qua Fanpage: NS Shop Facebook\n• Số điện thoại hotline: 0796 597 878\n• Zalo: 0796 597 878\n\nChúng tôi luôn sẵn sàng hỗ trợ khách hàng 24/7 qua các kênh liên lạc trên. Quy trình đặt hàng đơn giản chỉ 3 bước: Tư vấn → Báo giá → Sản xuất.",
      category: "order",
      tags: ["đặt hàng", "quy trình", "liên hệ"],
      popular: true,
    },
    {
      id: "3",
      question: "Thời gian sản xuất mất bao lâu?",
      answer: "Thời gian sản xuất tại NS Shop phụ thuộc vào độ phức tạp và số lượng sản phẩm:\n\n• May mẫu thử: 3-5 ngày làm việc\n• Sản xuất số lượng ít (30-100 sản phẩm): 7-10 ngày\n• Sản xuất số lượng lớn (100+ sản phẩm): 10-15 ngày\n• Sản phẩm phức tạp có thể cần thêm 2-3 ngày\n\nChúng tôi cam kết giao hàng đúng hạn và thông báo tiến độ thường xuyên cho khách hàng.",
      category: "production",
      tags: ["thời gian", "sản xuất", "may mẫu"],
      popular: true,
    },
    {
      id: "4",
      question: "Các hình thức thanh toán được hỗ trợ?",
      answer: "NS Shop hỗ trợ nhiều hình thức thanh toán linh hoạt:\n\n• Thanh toán khi nhận hàng (COD)\n• Chuyển khoản ngân hàng\n• Thanh toán qua ví điện tử (MoMo, ZaloPay)\n• Thanh toán trực tiếp tại showroom\n\nĐối với đơn hàng lớn, khách hàng có thể thanh toán theo đợt: 50% khi bắt đầu sản xuất, 50% khi hoàn thành.",
      category: "payment",
      tags: ["thanh toán", "COD", "chuyển khoản"],
      popular: false,
    },
    {
      id: "5",
      question: "Có hỗ trợ thiết kế miễn phí không?",
      answer: "Có! NS Shop cung cấp dịch vụ tư vấn thiết kế miễn phí:\n\n• Tư vấn thiết kế cơ bản miễn phí\n• Chỉnh sửa thiết kế theo yêu cầu\n• Tư vấn chọn chất liệu phù hợp\n• Tư vấn màu sắc và phối hợp\n\nĐối với thiết kế phức tạp hoặc thiết kế hoàn toàn mới, chúng tôi sẽ báo giá riêng cho dịch vụ thiết kế chuyên nghiệp.",
      category: "order",
      tags: ["thiết kế", "tư vấn", "miễn phí"],
      popular: false,
    },
    {
      id: "6",
      question: "Chính sách đổi trả như thế nào?",
      answer: "NS Shop có chính sách đổi trả rõ ràng và công bằng:\n\n• Đổi trả trong vòng 7 ngày nếu sản phẩm lỗi do sản xuất\n• Miễn phí sửa chữa nếu có lỗi kỹ thuật\n• Hoàn tiền 100% nếu sản phẩm không đúng yêu cầu\n• Không áp dụng đổi trả với sản phẩm đã sử dụng hoặc giặt\n\nChúng tôi cam kết chất lượng và luôn đặt sự hài lòng của khách hàng lên hàng đầu.",
      category: "order",
      tags: ["đổi trả", "bảo hành", "chính sách"],
      popular: false,
    },
  ];

  useEffect(() => {
    let filtered = faqs;

    // Filter by category
    if (selectedCategory !== "all") {
      filtered = filtered.filter((faq) => faq.category === selectedCategory);
    }

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (faq) =>
          faq.question.toLowerCase().includes(query) ||
          faq.answer.toLowerCase().includes(query) ||
          faq.tags.some((tag) => tag.toLowerCase().includes(query))
      );
    }

    setFilteredFAQs(filtered);
  }, [selectedCategory, searchQuery]);

  useEffect(() => {
    // Listen for search events from hero section
    const handleFAQSearch = (event: CustomEvent) => {
      setSearchQuery(event.detail);
    };

    window.addEventListener("faq-search", handleFAQSearch as EventListener);
    return () => {
      window.removeEventListener("faq-search", handleFAQSearch as EventListener);
    };
  }, []);

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <section id="faq-list" className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Danh Sách{" "}
              <span className="bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent">
                Câu Hỏi
              </span>
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Tìm hiểu thông tin chi tiết về dịch vụ và quy trình làm việc của chúng tôi
            </p>
          </motion.div>

          {/* Search and Filter */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="mb-12"
          >
            {/* Search Bar */}
            <div className="mb-8">
              <div className="relative max-w-md mx-auto">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <Input
                  type="text"
                  placeholder="Tìm kiếm trong FAQ..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 pr-4 py-3 rounded-xl border-gray-200 focus:border-pink-400 focus:ring-pink-400"
                />
              </div>
            </div>

            {/* Category Filter */}
            <div className="flex flex-wrap justify-center gap-3">
              {categories.map((category) => {
                const Icon = category.icon;
                return (
                  <Button
                    key={category.id}
                    onClick={() => setSelectedCategory(category.id)}
                    variant={selectedCategory === category.id ? "default" : "outline"}
                    className={`flex items-center gap-2 px-4 py-2 rounded-full transition-all duration-300 ${
                      selectedCategory === category.id
                        ? "bg-gradient-to-r from-pink-500 to-purple-600 text-white"
                        : "hover:bg-gray-100"
                    }`}
                  >
                    <Icon className="h-4 w-4" />
                    {category.name}
                  </Button>
                );
              })}
            </div>
          </motion.div>

          {/* FAQ List */}
          <div className="space-y-4">
            {filteredFAQs.length === 0 ? (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="text-center py-12"
              >
                <HelpCircle className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-600 mb-2">
                  Không tìm thấy câu hỏi nào
                </h3>
                <p className="text-gray-500">
                  Thử thay đổi từ khóa tìm kiếm hoặc chọn danh mục khác
                </p>
              </motion.div>
            ) : (
              filteredFAQs.map((faq, index) => (
                <motion.div
                  key={faq.id}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100 hover:shadow-xl transition-all duration-300"
                >
                  <button
                    onClick={() => toggleFAQ(index)}
                    className="w-full px-6 py-6 text-left flex items-center justify-between hover:bg-gray-50 transition-colors duration-200"
                  >
                    <div className="flex items-start gap-4 flex-1">
                      <div className="flex-shrink-0 w-10 h-10 bg-pink-100 rounded-full flex items-center justify-center">
                        <HelpCircle className="h-5 w-5 text-pink-600" />
                      </div>
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">
                          {faq.question}
                          {faq.popular && (
                            <span className="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-pink-100 text-pink-800">
                              Phổ biến
                            </span>
                          )}
                        </h3>
                        <div className="flex flex-wrap gap-2">
                          {faq.tags.map((tag, tagIndex) => (
                            <span
                              key={tagIndex}
                              className="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-600"
                            >
                              <Tag className="h-3 w-3" />
                              {tag}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>
                    <div className="flex-shrink-0 ml-4">
                      {openIndex === index ? (
                        <ChevronUp className="h-5 w-5 text-gray-400" />
                      ) : (
                        <ChevronDown className="h-5 w-5 text-gray-400" />
                      )}
                    </div>
                  </button>

                  {openIndex === index && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: "auto" }}
                      exit={{ opacity: 0, height: 0 }}
                      transition={{ duration: 0.3 }}
                      className="px-6 pb-6"
                    >
                      <div className="pl-14">
                        <div className="prose prose-gray max-w-none">
                          <div className="text-gray-700 leading-relaxed whitespace-pre-line">
                            {faq.answer}
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  )}
                </motion.div>
              ))
            )}
          </div>
        </div>
      </div>
    </section>
  );
}
