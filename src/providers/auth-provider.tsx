"use client";

import { Session<PERSON>rovider } from "next-auth/react";
import {
  ReactNode,
  createContext,
  useContext,
  useEffect,
  useState,
} from "react";
import { authManager } from "@/lib/auth-manager";

interface AuthState {
  isAuthenticated: boolean;
  user: any | null;
  isLoading: boolean;
  error: string | null;
}

interface AuthContextType extends AuthState {
  refresh: () => Promise<void>;
  queueRequest: <T>(fetchFn: () => Promise<T>) => Promise<T>;
}

const AuthContext = createContext<AuthContextType | null>(null);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [authState, setAuthState] = useState<AuthState>({
    isAuthenticated: false,
    user: null,
    isLoading: true,
    error: null,
  });

  useEffect(() => {
    // Subscribe to auth manager state changes
    const unsubscribe = authManager.subscribe((state) => {
      setAuthState(state);
    });

    return unsubscribe;
  }, []);

  const refresh = async () => {
    await authManager.refresh();
  };

  const queueRequest = async <T,>(fetchFn: () => Promise<T>): Promise<T> => {
    return authManager.queueRequest(fetchFn);
  };

  const contextValue: AuthContextType = {
    ...authState,
    refresh,
    queueRequest,
  };

  return (
    <SessionProvider>
      <AuthContext.Provider value={contextValue}>
        {children}
      </AuthContext.Provider>
    </SessionProvider>
  );
}

// Hook to use auth context
export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
