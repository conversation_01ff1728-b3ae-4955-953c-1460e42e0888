"use client";

import { usePathname } from "next/navigation";
import { ReactNode } from "react";
import { CartProvider } from "@/contexts/cart-context";

interface ConditionalCartProviderProps {
  children: ReactNode;
}

/**
 * ConditionalCartProvider only loads CartProvider for customer-facing pages
 * Admin dashboard doesn't need cart functionality, so we skip it to improve performance
 */
export function ConditionalCartProvider({ children }: ConditionalCartProviderProps) {
  const pathname = usePathname();
  
  // Check if current path is admin route
  const isAdminRoute = pathname?.startsWith("/admin");
  
  // If it's an admin route, don't load CartProvider
  if (isAdminRoute) {
    return <>{children}</>;
  }
  
  // For customer-facing pages, wrap with CartProvider
  return <CartProvider>{children}</CartProvider>;
}
