'use client';

import { <PERSON><PERSON>, <PERSON><PERSON> } from '@/components/layout';
import { HooksUsageExample } from '@/components/examples/HooksUsageExample';
import { HooksDocumentation } from '@/components/docs/HooksDocumentation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  useSettings, 
  useUser, 
  useCart, 
  useSearch, 
  useNotifications, 
  useTheme 
} from '@/contexts';

export default function DemoHooksPage() {
  const { settings } = useSettings();
  const { user } = useUser();
  const { summary } = useCart();
  const { query } = useSearch();
  const { unreadCount } = useNotifications();
  const { theme } = useTheme();

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
      <main className="flex-1 container mx-auto px-4 py-8">
        {/* <PERSON> Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold mb-4">
            Hooks Integration Demo
          </h1>
          <p className="text-lg text-muted-foreground mb-6">
            Trang demo showcasing tất cả hooks được tích hợp trong ứng dụng
          </p>
          
          {/* Quick Stats */}
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-8">
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {settings?.siteName ? '✅' : '❌'}
                </div>
                <div className="text-sm text-muted-foreground">Settings</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-green-600">
                  {user ? '✅' : '❌'}
                </div>
                <div className="text-sm text-muted-foreground">User</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {summary?.totalQuantity || 0}
                </div>
                <div className="text-sm text-muted-foreground">Cart Items</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-orange-600">
                  {query ? '✅' : '❌'}
                </div>
                <div className="text-sm text-muted-foreground">Search</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-red-600">
                  {unreadCount}
                </div>
                <div className="text-sm text-muted-foreground">Notifications</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-indigo-600">
                  {theme}
                </div>
                <div className="text-sm text-muted-foreground">Theme</div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Integration Status */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Integration Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 className="font-semibold mb-3">✅ Fully Integrated Pages</h3>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span>Home Page</span>
                    <Badge variant="secondary">Components</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Cart Page</span>
                    <Badge className="bg-green-500">useCart</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Checkout Page</span>
                    <Badge className="bg-green-500">useCart</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Profile Page</span>
                    <Badge className="bg-blue-500">useUser</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Wishlist Page</span>
                    <Badge className="bg-blue-500">useUser</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Orders Page</span>
                    <Badge variant="outline">Custom Hook</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Categories Page</span>
                    <Badge variant="outline">Custom Hook</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Contact Page</span>
                    <Badge className="bg-purple-500">useSettings</Badge>
                  </div>
                </div>
              </div>
              
              <div>
                <h3 className="font-semibold mb-3">🔧 Component Integration</h3>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span>Header</span>
                    <div className="flex gap-1">
                      <Badge className="bg-purple-500">useSettings</Badge>
                      <Badge className="bg-green-500">useCart</Badge>
                      <Badge className="bg-orange-500">useSearch</Badge>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Footer</span>
                    <Badge className="bg-purple-500">useSettings</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Product Components</span>
                    <div className="flex gap-1">
                      <Badge className="bg-green-500">useCart</Badge>
                      <Badge className="bg-blue-500">useUser</Badge>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Admin Components</span>
                    <div className="flex gap-1">
                      <Badge className="bg-red-500">useAdminAuth</Badge>
                      <Badge className="bg-red-600">useAdmin</Badge>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Detailed Example */}
        <HooksUsageExample />

        {/* Documentation */}
        <HooksDocumentation />

        {/* Implementation Guide */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>Implementation Guide</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="font-semibold mb-2">1. Import Standardized Hooks</h3>
              <pre className="bg-gray-100 p-3 rounded text-sm overflow-x-auto">
{`import { useSettings, useUser, useCart, useSearch } from '@/contexts';`}
              </pre>
            </div>
            
            <div>
              <h3 className="font-semibold mb-2">2. Use in Components</h3>
              <pre className="bg-gray-100 p-3 rounded text-sm overflow-x-auto">
{`function MyComponent() {
  const { settings } = useSettings();
  const { user } = useUser();
  const { cart, addToCart } = useCart();
  const { query, setQuery } = useSearch();
  
  // Your component logic
}`}
              </pre>
            </div>
            
            <div>
              <h3 className="font-semibold mb-2">3. Handle Loading States</h3>
              <pre className="bg-gray-100 p-3 rounded text-sm overflow-x-auto">
{`const { user, loading } = useUser();

if (loading) {
  return <LoadingSpinner />;
}

return <UserProfile user={user} />;`}
              </pre>
            </div>

            <div>
              <h3 className="font-semibold mb-2">4. Error Boundaries</h3>
              <p className="text-sm text-muted-foreground">
                All contexts are wrapped with error boundaries that will gracefully handle failures
                and display appropriate fallback UI.
              </p>
            </div>
          </CardContent>
        </Card>
      </main>
      
      <Footer />
    </div>
  );
}