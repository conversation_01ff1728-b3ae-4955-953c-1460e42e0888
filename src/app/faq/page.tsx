import { Metadata } from "next";
import { Header, Footer } from "@/components/layout";
import { FAQHeroSection } from "@/components/faq/faq-hero-section";
import { FAQListSection } from "@/components/faq/faq-list-section";
import { FAQContactSection } from "@/components/faq/faq-contact-section";

export const metadata: Metadata = {
  title: "Câu Hỏi Thường Gặp - NS Shop | Hỗ Trợ Khách Hàng",
  description:
    "Tìm câu trả lời cho các câu hỏi thường gặp về dịch vụ may gia công, quy trình đặt hàng, vận chuyển và thanh toán tại NS Shop. Hỗ trợ khách hàng 24/7.",
  keywords: [
    "FAQ",
    "câu hỏi thường gặp",
    "hỗ trợ khách hàng",
    "may gia công",
    "NS Shop",
    "quy trình đặt hàng",
    "vận chuyển",
    "thanh toán",
  ],
  openGraph: {
    title: "Câu Hỏi Thường Gặp - NS Shop | Hỗ Trợ Khách Hàng",
    description:
      "Tìm câu trả lời cho các câu hỏi thường gặp về dịch vụ may gia công, quy trình đặt hàng, vận chuyển và thanh toán tại NS Shop.",
    images: ["/og-image-faq.jpg"],
    url: "https://nsshop.com/faq",
  },
  alternates: {
    canonical: "https://nsshop.com/faq",
  },
};

export default function FAQPage() {
  return (
    <div className="min-h-screen flex flex-col bg-white">
      <Header />
      <main className="flex-1">
        {/* Hero Section */}
        <FAQHeroSection />

        {/* FAQ List Section */}
        <FAQListSection />

        {/* Contact Section */}
        <FAQContactSection />
      </main>
      <Footer />
    </div>
  );
}
