import { Metadata } from "next";
import { <PERSON><PERSON>, Footer } from "@/components/layout";
import { AboutHeroSection } from "@/components/about/about-hero-section";
import { AboutIntroSection } from "@/components/about/about-intro-section";
import { AboutServicesSection } from "@/components/about/about-services-section";
import { AboutWhyChooseUsSection } from "@/components/about/about-why-choose-us-section";
import { AboutStatsSection } from "@/components/about/about-stats-section";
import { AboutPortfolioSection } from "@/components/about/about-portfolio-section";
import { AboutTestimonialsSection } from "@/components/about/about-testimonials-section";
import { AboutFAQSection } from "@/components/about/about-faq-section";
import { AboutPricingSection } from "@/components/about/about-pricing-section";
import { AboutContactSection } from "@/components/about/about-contact-section";

export const metadata: Metadata = {
  title: "Về Chúng Tôi - NS Shop | Xưởng May Thời Trang <PERSON>ê<PERSON>",
  description:
    "NS Shop - Xưởng may thời trang chuyên nghiệp với hơn 10 năm kinh nghiệm. Chuyên sản xuất quần áo thiết kế số lượng ít, chất lượng cao, giá cả cạnh tranh. Đối tác tin cậy của các thương hiệu thời trang.",
  keywords: [
    "xưởng may",
    "may gia công",
    "thời trang thiết kế",
    "sản xuất quần áo",
    "NS Shop",
    "may số lượng ít",
  ],
  openGraph: {
    title: "Về Chúng Tôi - NS Shop | Xưởng May Thời Trang Chuyên Nghiệp",
    description:
      "NS Shop - Xưởng may thời trang chuyên nghiệp với hơn 10 năm kinh nghiệm. Chuyên sản xuất quần áo thiết kế số lượng ít, chất lượng cao.",
    images: ["/og-image-about.jpg"],
    url: "https://nsshop.com/about-us",
  },
  alternates: {
    canonical: "https://nsshop.com/about-us",
  },
};

export default function AboutUsPage() {
  return (
    <div className="min-h-screen flex flex-col bg-white">
      <Header />
      <main className="flex-1">
        {/* Hero Section */}
        <AboutHeroSection />

        {/* Introduction Section */}
        <AboutIntroSection />

        {/* Services Section */}
        <AboutServicesSection />

        {/* Why Choose Us Section */}
        <AboutWhyChooseUsSection />

        {/* Stats Section */}
        <AboutStatsSection />

        {/* Portfolio Section */}
        <AboutPortfolioSection />

        {/* Testimonials Section */}
        <AboutTestimonialsSection />

        {/* FAQ Section */}
        <AboutFAQSection />

        {/* Pricing Section */}
        <AboutPricingSection />

        {/* Contact Section */}
        <AboutContactSection />
      </main>
      <Footer />
    </div>
  );
}
