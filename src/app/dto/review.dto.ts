/**
 * Review DTOs
 * Data Transfer Objects cho Review
 */

import { z } from 'zod';

/**
 * Review Response DTO
 */
export interface ReviewResponseDto {
  id: string;
  productId: string;
  userId: string;
  rating: number;
  comment?: string;
  status: string;
  isVerified: boolean;
  createdAt: string;
  updatedAt: string;
  user?: {
    name: string;
    avatar?: string;
  };
}

/**
 * Create Review Request DTO
 */
export interface CreateReviewRequestDto {
  productId: string;
  rating: number;
  comment?: string;
}

/**
 * Update Review Request DTO
 */
export interface UpdateReviewRequestDto {
  rating?: number;
  comment?: string;
}

/**
 * Validation Schemas
 */
export const CreateReviewRequestSchema = z.object({
  productId: z.string().uuid('Invalid product ID'),
  rating: z.number().int().min(1, 'Rating must be at least 1').max(5, 'Rating cannot exceed 5'),
  comment: z.string().max(1000, 'Comment too long').optional(),
});

export const UpdateReviewRequestSchema = z.object({
  rating: z.number().int().min(1, 'Rating must be at least 1').max(5, 'Rating cannot exceed 5').optional(),
  comment: z.string().max(1000, 'Comment too long').optional(),
});

/**
 * Validation functions
 */
export const validateCreateReview = (data: any): CreateReviewRequestDto => {
  return CreateReviewRequestSchema.parse(data);
};

export const validateUpdateReview = (data: any): UpdateReviewRequestDto => {
  return UpdateReviewRequestSchema.parse(data);
};
