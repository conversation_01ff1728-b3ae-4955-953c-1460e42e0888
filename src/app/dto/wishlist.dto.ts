/**
 * Wishlist DTOs
 * Data Transfer Objects cho Wishlist
 */

import { z } from 'zod';

/**
 * Wishlist Response DTO
 */
export interface WishlistResponseDto {
  id: string;
  userId: string;
  productId: string;
  createdAt: string;
  product?: {
    id: string;
    name: string;
    slug: string;
    price: number;
    salePrice?: number;
    image?: string;
    status: string;
  };
}

/**
 * Add to Wishlist Request DTO
 */
export interface AddToWishlistRequestDto {
  productId: string;
}

/**
 * Validation Schemas
 */
export const AddToWishlistRequestSchema = z.object({
  productId: z.string().uuid('Invalid product ID'),
});

/**
 * Validation functions
 */
export const validateAddToWishlist = (data: any): AddToWishlistRequestDto => {
  return AddToWishlistRequestSchema.parse(data);
};
