/**
 * Address DTOs
 * Data Transfer Objects cho Address
 */

import { z } from 'zod';

/**
 * Address Response DTO
 */
export interface AddressResponseDto {
  id: string;
  userId: string;
  type: string;
  firstName: string;
  lastName: string;
  company?: string;
  address1: string;
  address2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  phone?: string;
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
}

/**
 * Create Address Request DTO
 */
export interface CreateAddressRequestDto {
  type: 'BILLING' | 'SHIPPING' | 'BOTH';
  firstName: string;
  lastName: string;
  company?: string;
  address1: string;
  address2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  phone?: string;
  isDefault?: boolean;
}

/**
 * Update Address Request DTO
 */
export interface UpdateAddressRequestDto {
  type?: 'BILLING' | 'SHIPPING' | 'BOTH';
  firstName?: string;
  lastName?: string;
  company?: string;
  address1?: string;
  address2?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  phone?: string;
  isDefault?: boolean;
}

/**
 * Validation Schemas
 */
export const CreateAddressRequestSchema = z.object({
  type: z.enum(['BILLING', 'SHIPPING', 'BOTH']),
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  company: z.string().optional(),
  address1: z.string().min(1, 'Address is required'),
  address2: z.string().optional(),
  city: z.string().min(1, 'City is required'),
  state: z.string().min(1, 'State is required'),
  postalCode: z.string().min(1, 'Postal code is required'),
  country: z.string().min(1, 'Country is required'),
  phone: z.string().optional(),
  isDefault: z.boolean().default(false),
});

export const UpdateAddressRequestSchema = CreateAddressRequestSchema.partial();

/**
 * Validation functions
 */
export const validateCreateAddress = (data: any): CreateAddressRequestDto => {
  return CreateAddressRequestSchema.parse(data);
};

export const validateUpdateAddress = (data: any): UpdateAddressRequestDto => {
  return UpdateAddressRequestSchema.parse(data);
};
