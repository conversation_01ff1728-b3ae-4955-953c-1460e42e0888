/**
 * Order Transform Functions
 * Chuyển đổi giữa Order entities và DTOs
 */

import {
  OrderEntity,
  OrderWithRelations,
  CreateOrderData,
  UpdateOrderData,
  OrderItemEntity,
} from "../../models/order.model";
import { OrderStatus, PaymentStatus } from "../../models/common.model";
import { OrderStatusDto, PaymentStatusDto } from "../common.dto";
import { toISOString, removeUndefined } from "./common.transform";

import { OrderResponseDto } from "../order.dto";

/**
 * Transform OrderEntity to OrderResponseDto
 */
export function orderToResponseDto(order: OrderEntity): OrderResponseDto {
  return removeUndefined({
    id: order.id,
    orderNumber: order.orderNumber,
    userId: order.userId,
    status: transformOrderStatus(order.status),
    paymentStatus: transformPaymentStatus(order.paymentStatus),
    total: order.total,
    subtotal: order.subtotal,
    tax: order.tax,
    shipping: order.shipping,
    discount: order.discount,
    createdAt: toISOString(order.createdAt)!,
    updatedAt: toISOString(order.updatedAt)!,
    metadata: order.metadata,
  }) as OrderResponseDto;
}

/**
 * Transform OrderStatus to OrderStatusDto
 */
export function transformOrderStatus(status: OrderStatus): OrderStatusDto {
  switch (status) {
    case OrderStatus.PENDING:
      return OrderStatusDto.PENDING;
    case OrderStatus.CONFIRMED:
      return OrderStatusDto.CONFIRMED;
    case OrderStatus.PROCESSING:
      return OrderStatusDto.PROCESSING;
    case OrderStatus.SHIPPED:
      return OrderStatusDto.SHIPPED;
    case OrderStatus.DELIVERED:
      return OrderStatusDto.DELIVERED;
    case OrderStatus.CANCELLED:
      return OrderStatusDto.CANCELLED;
    case OrderStatus.REFUNDED:
      return OrderStatusDto.REFUNDED;
    default:
      return OrderStatusDto.PENDING;
  }
}

/**
 * Transform PaymentStatus to PaymentStatusDto
 */
export function transformPaymentStatus(
  status: PaymentStatus
): PaymentStatusDto {
  switch (status) {
    case PaymentStatus.PENDING:
      return PaymentStatusDto.PENDING;
    case PaymentStatus.PAID:
      return PaymentStatusDto.PAID;
    case PaymentStatus.FAILED:
      return PaymentStatusDto.FAILED;
    case PaymentStatus.REFUNDED:
      return PaymentStatusDto.REFUNDED;
    case PaymentStatus.CANCELLED:
      return PaymentStatusDto.CANCELLED;
    default:
      return PaymentStatusDto.PENDING;
  }
}

/**
 * Transform array of orders to DTOs
 */
export function ordersToResponseDtos(
  orders: OrderEntity[]
): OrderResponseDto[] {
  return orders.map(orderToResponseDto);
}
