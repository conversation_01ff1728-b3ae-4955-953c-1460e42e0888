/**
 * Common Transform Functions
 * <PERSON><PERSON>c hàm transform chung
 */

import { 
  BaseResponseDto, 
  PaginationResponseDto, 
  ErrorResponseDto,
  ResponseMetaDto 
} from '../common.dto';
import { PaginatedResult } from '../../models/common.model';

/**
 * Transform entity to response DTO
 */
export function toResponseDto<T>(
  data: T,
  message?: string,
  meta?: ResponseMetaDto
): BaseResponseDto<T> {
  return {
    success: true,
    data,
    message,
    meta: {
      timestamp: new Date().toISOString(),
      ...meta,
    },
  };
}

/**
 * Transform paginated result to pagination response DTO
 */
export function toPaginationResponseDto<T, R>(
  result: PaginatedResult<T>,
  transformFn: (item: T) => R,
  meta?: ResponseMetaDto
): BaseResponseDto<PaginationResponseDto<R>> {
  return {
    success: true,
    data: {
      data: result.data.map(transformFn),
      pagination: {
        page: result.page,
        limit: result.limit,
        total: result.total,
        totalPages: result.totalPages,
        hasNext: result.hasNext,
        hasPrev: result.hasPrev,
      },
    },
    meta: {
      timestamp: new Date().toISOString(),
      ...meta,
    },
  };
}

/**
 * Transform error to error response DTO
 */
export function toErrorResponseDto(
  error: string,
  message?: string,
  details?: any,
  code?: string,
  statusCode?: number
): ErrorResponseDto {
  return {
    success: false,
    error,
    message,
    details,
    code,
    statusCode,
  };
}

/**
 * Transform array of entities to array of DTOs
 */
export function toArrayDto<T, R>(
  entities: T[],
  transformFn: (entity: T) => R
): R[] {
  return entities.map(transformFn);
}

/**
 * Transform Date to ISO string
 */
export function toISOString(date: Date | null | undefined): string | undefined {
  return date ? date.toISOString() : undefined;
}

/**
 * Transform ISO string to Date
 */
export function fromISOString(dateString: string | null | undefined): Date | undefined {
  return dateString ? new Date(dateString) : undefined;
}

/**
 * Transform boolean to string
 */
export function booleanToString(value: boolean | null | undefined): string | undefined {
  return value !== null && value !== undefined ? value.toString() : undefined;
}

/**
 * Transform string to boolean
 */
export function stringToBoolean(value: string | null | undefined): boolean | undefined {
  if (value === null || value === undefined) return undefined;
  return value.toLowerCase() === 'true';
}

/**
 * Transform number to string
 */
export function numberToString(value: number | null | undefined): string | undefined {
  return value !== null && value !== undefined ? value.toString() : undefined;
}

/**
 * Transform string to number
 */
export function stringToNumber(value: string | null | undefined): number | undefined {
  if (value === null || value === undefined) return undefined;
  const num = parseFloat(value);
  return isNaN(num) ? undefined : num;
}

/**
 * Remove undefined values from object
 */
export function removeUndefined<T extends Record<string, any>>(obj: T): Partial<T> {
  const result: Partial<T> = {};
  
  Object.keys(obj).forEach(key => {
    if (obj[key] !== undefined) {
      result[key as keyof T] = obj[key];
    }
  });
  
  return result;
}

/**
 * Remove null and undefined values from object
 */
export function removeNullish<T extends Record<string, any>>(obj: T): Partial<T> {
  const result: Partial<T> = {};
  
  Object.keys(obj).forEach(key => {
    if (obj[key] !== undefined && obj[key] !== null) {
      result[key as keyof T] = obj[key];
    }
  });
  
  return result;
}

/**
 * Transform enum value
 */
export function transformEnum<T extends string>(
  value: T | null | undefined,
  enumObject: Record<string, T>
): T | undefined {
  if (value === null || value === undefined) return undefined;
  return Object.values(enumObject).includes(value) ? value : undefined;
}

/**
 * Transform metadata object
 */
export function transformMetadata(
  metadata: Record<string, any> | null | undefined
): Record<string, any> | undefined {
  if (!metadata || Object.keys(metadata).length === 0) return undefined;
  return metadata;
}

/**
 * Transform array with filter
 */
export function transformArrayWithFilter<T, R>(
  array: T[] | null | undefined,
  transformFn: (item: T) => R | null,
  filterFn?: (item: R) => boolean
): R[] {
  if (!array) return [];
  
  const transformed = array
    .map(transformFn)
    .filter((item): item is R => item !== null);
  
  return filterFn ? transformed.filter(filterFn) : transformed;
}

/**
 * Transform with fallback
 */
export function transformWithFallback<T, R>(
  value: T | null | undefined,
  transformFn: (value: T) => R,
  fallback: R
): R {
  try {
    return value !== null && value !== undefined ? transformFn(value) : fallback;
  } catch {
    return fallback;
  }
}

/**
 * Safe transform function that catches errors
 */
export function safeTransform<T, R>(
  value: T,
  transformFn: (value: T) => R,
  fallback?: R
): R | undefined {
  try {
    return transformFn(value);
  } catch (error) {
    console.warn('Transform error:', error);
    return fallback;
  }
}

/**
 * Transform pagination parameters
 */
export function transformPaginationParams(params: {
  page?: string | number;
  limit?: string | number;
  sortBy?: string;
  sortOrder?: string;
}): {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder: 'asc' | 'desc';
} {
  const page = typeof params.page === 'string' ? parseInt(params.page) : params.page || 1;
  const limit = typeof params.limit === 'string' ? parseInt(params.limit) : params.limit || 10;
  const sortOrder = params.sortOrder === 'asc' ? 'asc' : 'desc';
  
  return {
    page: Math.max(1, page),
    limit: Math.min(100, Math.max(1, limit)),
    sortBy: params.sortBy,
    sortOrder,
  };
}

/**
 * Transform search parameters
 */
export function transformSearchParams(params: Record<string, any>): Record<string, any> {
  const transformed: Record<string, any> = {};
  
  Object.keys(params).forEach(key => {
    const value = params[key];
    
    if (value === null || value === undefined || value === '') {
      return;
    }
    
    // Transform boolean strings
    if (value === 'true' || value === 'false') {
      transformed[key] = value === 'true';
      return;
    }
    
    // Transform number strings
    if (typeof value === 'string' && !isNaN(Number(value))) {
      transformed[key] = Number(value);
      return;
    }
    
    // Transform array strings (comma-separated)
    if (typeof value === 'string' && value.includes(',')) {
      transformed[key] = value.split(',').map(v => v.trim()).filter(v => v);
      return;
    }
    
    transformed[key] = value;
  });
  
  return transformed;
}

/**
 * Create response with timing metadata
 */
export function createTimedResponse<T>(
  data: T,
  startTime: number,
  message?: string
): BaseResponseDto<T> {
  const duration = Date.now() - startTime;
  
  return toResponseDto(data, message, {
    duration: `${duration}ms`,
    timestamp: new Date().toISOString(),
  });
}
