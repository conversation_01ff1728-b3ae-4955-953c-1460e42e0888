/**
 * Cart Transform Functions
 * Chuyển đổi giữa Cart entities và DTOs
 */

import {
  CartEntity,
  CartWithRelations,
  CreateCartData,
  UpdateCartData,
  CartItemEntity,
  CartStatus,
} from "../../models/cart.model";
import { toISOString, removeUndefined } from "./common.transform";

import { CartResponseDto } from "../cart.dto";

/**
 * Transform CartEntity to CartResponseDto
 */
export function cartToResponseDto(cart: CartEntity): CartResponseDto {
  return removeUndefined({
    id: cart.id,
    userId: cart.userId,
    status: cart.status,
    total: cart.total,
    itemCount: 0, // Will be calculated from items if needed
    createdAt: toISOString(cart.createdAt)!,
    updatedAt: toISOString(cart.updatedAt)!,
    expiresAt: toISOString(cart.expiresAt),
    metadata: cart.metadata,
  }) as CartResponseDto;
}

/**
 * Transform array of carts to DTOs
 */
export function cartsToResponseDtos(carts: CartEntity[]): CartResponseDto[] {
  return carts.map(cartToResponseDto);
}
