/**
 * Product DTOs
 * Data Transfer Objects cho Product
 */

import { z } from "zod";
import { ProductStatusDto } from "./common.dto";

/**
 * Product Response DTO
 */
export interface ProductResponseDto {
  id: string;
  name: string;
  slug: string;
  description: string;
  shortDescription?: string;
  price: number;
  salePrice?: number;
  sku: string;
  stock: number;
  weight?: number;
  dimensions?: ProductDimensionsDto;
  status: ProductStatusDto;
  featured: boolean;
  tags: string[];
  categoryId: string;
  brandId?: string;
  createdAt: string;
  updatedAt: string;
  seo?: SEODataDto;
  metadata?: Record<string, any>;
}

/**
 * Product Dimensions DTO
 */
export interface ProductDimensionsDto {
  length: number;
  width: number;
  height: number;
  unit: "cm" | "inch";
}

/**
 * SEO Data DTO
 */
export interface SEODataDto {
  title?: string;
  description?: string;
  keywords?: string[];
  ogImage?: string;
  canonicalUrl?: string;
}

/**
 * Product with Relations DTO
 */
export interface ProductWithRelationsDto extends ProductResponseDto {
  category?: CategorySummaryDto;
  brand?: BrandSummaryDto;
  media?: ProductMediaDto[];
  reviews?: ReviewSummaryDto[];
  attributes?: ProductAttributeValueDto[];
  variants?: ProductVariantDto[];
  stats?: ProductStatsDto;
}

/**
 * Category Summary DTO
 */
export interface CategorySummaryDto {
  id: string;
  name: string;
  slug: string;
}

/**
 * Brand Summary DTO
 */
export interface BrandSummaryDto {
  id: string;
  name: string;
  slug: string;
  logo?: string;
}

/**
 * Product Media DTO
 */
export interface ProductMediaDto {
  id: string;
  url: string;
  alt?: string;
  isPrimary: boolean;
  order: number;
  thumbnailUrl?: string;
}

/**
 * Review Summary DTO
 */
export interface ReviewSummaryDto {
  id: string;
  rating: number;
  comment: string;
  userName: string;
  userAvatar?: string;
  createdAt: string;
}

/**
 * Product Attribute Value DTO
 */
export interface ProductAttributeValueDto {
  attributeId: string;
  attributeName: string;
  valueId: string;
  value: string;
  displayOrder: number;
}

/**
 * Product Variant DTO
 */
export interface ProductVariantDto {
  id: string;
  name: string;
  sku: string;
  price: number;
  salePrice?: number;
  stock: number;
  attributes: ProductAttributeValueDto[];
  media?: ProductMediaDto[];
}

/**
 * Product Statistics DTO
 */
export interface ProductStatsDto {
  totalViews: number;
  totalSales: number;
  totalRevenue: number;
  averageRating: number;
  totalReviews: number;
  conversionRate: number;
  lastSoldAt?: string;
}

/**
 * Create Product Request DTO
 */
export interface CreateProductRequestDto {
  name: string;
  description: string;
  shortDescription?: string;
  price: number;
  salePrice?: number;
  sku: string;
  stock: number;
  weight?: number;
  dimensions?: ProductDimensionsDto;
  categoryId: string;
  brandId?: string;
  featured?: boolean;
  tags?: string[];
  status?: ProductStatusDto;
  seo?: SEODataDto;
  metadata?: Record<string, any>;
  mediaIds?: string[];
  attributes?: Omit<ProductAttributeValueDto, "attributeName">[];
}

/**
 * Update Product Request DTO
 */
export interface UpdateProductRequestDto {
  name?: string;
  description?: string;
  shortDescription?: string;
  price?: number;
  salePrice?: number;
  sku?: string;
  stock?: number;
  weight?: number;
  dimensions?: ProductDimensionsDto;
  categoryId?: string;
  brandId?: string;
  featured?: boolean;
  tags?: string[];
  status?: ProductStatusDto;
  seo?: SEODataDto;
  metadata?: Record<string, any>;
}

/**
 * Product Search Request DTO
 */
export interface ProductSearchRequestDto {
  search?: string;
  categoryId?: string;
  brandId?: string;
  status?: ProductStatusDto;
  featured?: boolean;
  priceMin?: number;
  priceMax?: number;
  inStock?: boolean;
  tags?: string[];
  attributes?: Record<string, string[]>;
  rating?: number;
  page?: number;
  limit?: number;
  sortBy?: ProductSortByDto;
  sortOrder?: "asc" | "desc";
}

/**
 * Product Sort Options DTO
 */
export type ProductSortByDto =
  | "name"
  | "price"
  | "createdAt"
  | "updatedAt"
  | "rating"
  | "sales"
  | "views"
  | "stock";

/**
 * Product Inventory Update DTO
 */
export interface ProductInventoryUpdateDto {
  quantity: number;
  type: "INCREASE" | "DECREASE" | "SET";
  reason?: string;
  reference?: string;
}

/**
 * Validation Schemas
 */
export const ProductDimensionsSchema = z.object({
  length: z.number().positive("Length must be positive"),
  width: z.number().positive("Width must be positive"),
  height: z.number().positive("Height must be positive"),
  unit: z.enum(["cm", "inch"]),
});

export const SEODataSchema = z.object({
  title: z.string().max(60).optional(),
  description: z.string().max(160).optional(),
  keywords: z.array(z.string()).optional(),
  ogImage: z.string().url().optional(),
  canonicalUrl: z.string().url().optional(),
});

export const CreateProductRequestSchema = z
  .object({
    name: z
      .string()
      .min(1, "Product name is required")
      .max(255, "Name too long"),
    description: z.string().min(1, "Description is required"),
    shortDescription: z.string().max(500).optional(),
    price: z.number().positive("Price must be positive"),
    salePrice: z.number().positive().optional(),
    sku: z
      .string()
      .min(3, "SKU must be at least 3 characters")
      .max(50, "SKU too long"),
    stock: z.number().int().min(0, "Stock cannot be negative"),
    weight: z.number().positive().optional(),
    dimensions: ProductDimensionsSchema.optional(),
    categoryId: z.string().uuid("Invalid category ID"),
    brandId: z.string().uuid("Invalid brand ID").optional(),
    featured: z.boolean().default(false),
    tags: z.array(z.string()).default([]),
    status: z.nativeEnum(ProductStatusDto).default(ProductStatusDto.ACTIVE),
    seo: SEODataSchema.optional(),
    metadata: z.record(z.any()).optional(),
    mediaIds: z.array(z.string().uuid()).optional(),
    attributes: z
      .array(
        z.object({
          attributeId: z.string().uuid(),
          valueId: z.string().uuid(),
          value: z.string(),
          displayOrder: z.number().int().min(0).default(0),
        })
      )
      .optional(),
  })
  .refine(
    (data) => {
      if (data.salePrice && data.salePrice >= data.price) {
        return false;
      }
      return true;
    },
    {
      message: "Sale price must be less than regular price",
      path: ["salePrice"],
    }
  );

export const UpdateProductRequestSchema = z
  .object({
    name: z
      .string()
      .min(1, "Product name is required")
      .max(255, "Name too long")
      .optional(),
    description: z.string().min(1, "Description is required").optional(),
    shortDescription: z.string().max(500).optional(),
    price: z.number().positive("Price must be positive").optional(),
    salePrice: z.number().positive().optional(),
    stock: z.number().int().min(0, "Stock cannot be negative").optional(),
    weight: z.number().positive().optional(),
    dimensions: ProductDimensionsSchema.optional(),
    categoryId: z.string().uuid("Invalid category ID").optional(),
    brandId: z.string().uuid("Invalid brand ID").optional(),
    featured: z.boolean().optional(),
    tags: z.array(z.string()).optional(),
    status: z.nativeEnum(ProductStatusDto).optional(),
    seo: SEODataSchema.optional(),
    metadata: z.record(z.any()).optional(),
    mediaIds: z.array(z.string().uuid()).optional(),
    attributes: z
      .array(
        z.object({
          attributeId: z.string().uuid(),
          valueId: z.string().uuid(),
          value: z.string(),
          displayOrder: z.number().int().min(0).default(0),
        })
      )
      .optional(),
  })
  .refine(
    (data) => {
      if (data.salePrice && data.price && data.salePrice >= data.price) {
        return false;
      }
      return true;
    },
    {
      message: "Sale price must be less than regular price",
      path: ["salePrice"],
    }
  );

export const ProductSearchRequestSchema = z.object({
  search: z.string().optional(),
  categoryId: z.string().uuid().optional(),
  brandId: z.string().uuid().optional(),
  status: z.nativeEnum(ProductStatusDto).optional(),
  featured: z.coerce.boolean().optional(),
  priceMin: z.coerce.number().positive().optional(),
  priceMax: z.coerce.number().positive().optional(),
  inStock: z.coerce.boolean().optional(),
  tags: z.array(z.string()).optional(),
  attributes: z.record(z.array(z.string())).optional(),
  rating: z.coerce.number().min(1).max(5).optional(),
  page: z.coerce.number().int().min(1).default(1),
  limit: z.coerce.number().int().min(1).max(100).default(12),
  sortBy: z
    .enum([
      "name",
      "price",
      "createdAt",
      "updatedAt",
      "rating",
      "sales",
      "views",
      "stock",
    ])
    .default("createdAt"),
  sortOrder: z.enum(["asc", "desc"]).default("desc"),
});

export const ProductInventoryUpdateSchema = z.object({
  quantity: z.number().int().positive("Quantity must be positive"),
  type: z.enum(["INCREASE", "DECREASE", "SET"]),
  reason: z.string().optional(),
  reference: z.string().optional(),
});

/**
 * Validation functions
 */
export const validateCreateProduct = (data: any): CreateProductRequestDto => {
  return CreateProductRequestSchema.parse(data);
};

export const validateUpdateProduct = (data: any): UpdateProductRequestDto => {
  return UpdateProductRequestSchema.parse(data);
};

export const validateProductSearch = (data: any): ProductSearchRequestDto => {
  return ProductSearchRequestSchema.parse(data);
};

export const validateProductInventoryUpdate = (
  data: any
): ProductInventoryUpdateDto => {
  return ProductInventoryUpdateSchema.parse(data);
};

/**
 * Type aliases for backward compatibility
 */
export type ProductDto = ProductResponseDto;
export type CreateProductDto = CreateProductRequestDto;
export type UpdateProductDto = UpdateProductRequestDto;
