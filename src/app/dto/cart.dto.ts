/**
 * Cart DTOs
 * Data Transfer Objects cho Cart
 */

import { z } from 'zod';

/**
 * Cart Response DTO
 */
export interface CartResponseDto {
  id: string;
  userId: string;
  status: string;
  total: number;
  itemCount: number;
  createdAt: string;
  updatedAt: string;
  expiresAt?: string;
  metadata?: Record<string, any>;
}

/**
 * Add to Cart Request DTO
 */
export interface AddToCartRequestDto {
  productId: string;
  quantity: number;
  variantId?: string;
}

/**
 * Update Cart Item Request DTO
 */
export interface UpdateCartItemRequestDto {
  quantity: number;
}

/**
 * Validation Schemas
 */
export const AddToCartRequestSchema = z.object({
  productId: z.string().uuid('Invalid product ID'),
  quantity: z.number().int().min(1, 'Quantity must be at least 1').max(100, 'Quantity too large'),
  variantId: z.string().uuid().optional(),
});

export const UpdateCartItemRequestSchema = z.object({
  quantity: z.number().int().min(0, 'Quantity cannot be negative').max(100, 'Quantity too large'),
});

/**
 * Validation functions
 */
export const validateAddToCart = (data: any): AddToCartRequestDto => {
  return AddToCartRequestSchema.parse(data);
};

export const validateUpdateCartItem = (data: any): UpdateCartItemRequestDto => {
  return UpdateCartItemRequestSchema.parse(data);
};
