/**
 * User Model
 * Business entity cho User
 */

import {
  BaseEntity,
  ContactInfo,
  UserRole,
  PaginationOptions,
} from "./common.model";

/**
 * User Entity
 */
export interface UserEntity extends BaseEntity {
  email: string;
  name: string;
  password?: string; // Optional vì có thể không trả về password
  avatarId?: string | null;
  phone?: string | null;
  dateOfBirth?: Date | null;
  gender?: string | null;
  isActive: boolean;
  role?: UserRole; // Optional since not in Prisma schema
  emailVerified?: Date;
  phoneVerified?: Date;
  lastLoginAt?: Date;
  preferences?: UserPreferences;
  profile?: UserProfile;
}

/**
 * User Preferences
 */
export interface UserPreferences {
  language: string;
  currency: string;
  timezone: string;
  notifications: NotificationPreferences;
  privacy: PrivacySettings;
}

export interface NotificationPreferences {
  email: boolean;
  sms: boolean;
  push: boolean;
  marketing: boolean;
  orderUpdates: boolean;
  promotions: boolean;
}

export interface PrivacySettings {
  profileVisibility: "PUBLIC" | "PRIVATE" | "FRIENDS";
  showEmail: boolean;
  showPhone: boolean;
  allowDataCollection: boolean;
}

/**
 * User Profile
 */
export interface UserProfile {
  bio?: string;
  website?: string;
  location?: string;
  interests?: string[];
  socialMedia?: ContactInfo["socialMedia"];
}

/**
 * User Statistics
 */
export interface UserStats {
  totalOrders: number;
  totalSpent: number;
  totalReviews: number;
  averageRating: number;
  joinedDays: number;
  lastOrderDate?: Date;
}

/**
 * User with Relations
 */
export interface UserWithRelations extends UserEntity {
  orders?: any[]; // OrderEntity[]
  addresses?: any[]; // AddressEntity[]
  reviews?: any[]; // ReviewEntity[]
  cart?: any; // CartEntity
  wishlistItems?: any[]; // WishlistEntity[]
  stats?: UserStats;
}

/**
 * User Creation Data
 */
export interface CreateUserData {
  email: string;
  name: string;
  password: string;
  phone?: string;
  dateOfBirth?: Date;
  gender?: string;
  role?: UserRole;
  preferences?: Partial<UserPreferences>;
}

/**
 * User Update Data
 */
export interface UpdateUserData {
  name?: string;
  phone?: string;
  dateOfBirth?: Date;
  gender?: string;
  avatar?: string;
  isActive?: boolean;
  preferences?: Partial<UserPreferences>;
  profile?: Partial<UserProfile>;
}

/**
 * User Search Filters
 */
export interface UserSearchFilters extends PaginationOptions {
  search?: string;
  isActive?: boolean;
  role?: UserRole;
  gender?: string;
  dateFrom?: Date;
  dateTo?: Date;
  hasOrders?: boolean;
  hasReviews?: boolean;
}

/**
 * User Authentication Data
 */
export interface UserAuthData {
  id: string;
  email: string;
  name: string;
  role: UserRole;
  avatar?: string;
  isActive: boolean;
  emailVerified?: Date;
}

/**
 * Password Reset Data
 */
export interface PasswordResetData {
  email: string;
  token: string;
  expiresAt: Date;
}

/**
 * Email Verification Data
 */
export interface EmailVerificationData {
  email: string;
  token: string;
  expiresAt: Date;
}

/**
 * User Session Data
 */
export interface UserSession {
  userId: string;
  sessionId: string;
  ipAddress: string;
  userAgent: string;
  createdAt: Date;
  expiresAt: Date;
  isActive: boolean;
}

/**
 * User Activity Log
 */
export interface UserActivity {
  userId: string;
  action: string;
  resource?: string;
  resourceId?: string;
  metadata?: Record<string, any>;
  ipAddress: string;
  userAgent: string;
  timestamp: Date;
}

/**
 * User Business Rules
 */
export class UserBusinessRules {
  static validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  static validatePassword(password: string): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (password.length < 8) {
      errors.push("Password must be at least 8 characters long");
    }

    if (!/[A-Z]/.test(password)) {
      errors.push("Password must contain at least one uppercase letter");
    }

    if (!/[a-z]/.test(password)) {
      errors.push("Password must contain at least one lowercase letter");
    }

    if (!/\d/.test(password)) {
      errors.push("Password must contain at least one number");
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  static validatePhone(phone: string): boolean {
    const phoneRegex = /^[+]?[1-9][\d]{0,15}$/;
    return phoneRegex.test(phone.replace(/\s/g, ""));
  }

  static canUpdateProfile(user: UserEntity, updatedBy: UserEntity): boolean {
    return (
      user.id === updatedBy.id ||
      updatedBy.role === UserRole.ADMIN ||
      updatedBy.role === UserRole.SUPER_ADMIN
    );
  }

  static canDeleteUser(user: UserEntity, deletedBy: UserEntity): boolean {
    if (user.id === deletedBy.id) return false; // Cannot delete self
    return (
      deletedBy.role === UserRole.ADMIN ||
      deletedBy.role === UserRole.SUPER_ADMIN
    );
  }

  static canViewUserDetails(user: UserEntity, viewer: UserEntity): boolean {
    if (user.id === viewer.id) return true;
    if (viewer.role === UserRole.ADMIN || viewer.role === UserRole.SUPER_ADMIN)
      return true;
    return user.preferences?.privacy?.profileVisibility === "PUBLIC";
  }
}
