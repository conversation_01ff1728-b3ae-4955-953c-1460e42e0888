/**
 * Wishlist Model
 * Business entity cho Wishlist
 */

import { BaseEntity } from "./common.model";

/**
 * Wishlist Entity
 */
export interface WishlistEntity extends BaseEntity {
  userId: string;
  name: string;
  description?: string;
  isPublic: boolean;
  isDefault: boolean;
  metadata?: Record<string, any>;
}

/**
 * Wishlist Item Entity
 */
export interface WishlistItemEntity extends BaseEntity {
  wishlistId: string;
  productId: string;
  note?: string;
  priority: WishlistPriority;
  metadata?: Record<string, any>;
}

/**
 * Wishlist Priority
 */
export enum WishlistPriority {
  LOW = "LOW",
  MEDIUM = "MEDIUM",
  HIGH = "HIGH",
}

/**
 * Wishlist with Relations
 */
export interface WishlistWithRelations extends WishlistEntity {
  items?: WishlistItemWithRelations[];
  itemCount?: number;
}

/**
 * Wishlist Item with Relations
 */
export interface WishlistItemWithRelations extends WishlistItemEntity {
  product?: any; // ProductEntity
  wishlist?: WishlistEntity;
}

/**
 * Create Wishlist Data
 */
export interface CreateWishlistData {
  userId: string;
  productId?: string; // For simple wishlist item creation
  name?: string;
  description?: string;
  isPublic?: boolean;
  isDefault?: boolean;
}

/**
 * Update Wishlist Data
 */
export interface UpdateWishlistData {
  name?: string;
  description?: string;
  isPublic?: boolean;
  isDefault?: boolean;
}

/**
 * Create Wishlist Item Data
 */
export interface CreateWishlistItemData {
  wishlistId: string;
  productId: string;
  note?: string;
  priority?: WishlistPriority;
}

/**
 * Update Wishlist Item Data
 */
export interface UpdateWishlistItemData {
  note?: string;
  priority?: WishlistPriority;
}

/**
 * Wishlist Business Rules
 */
export class WishlistBusinessRules {
  static readonly MAX_WISHLISTS_PER_USER = 10;
  static readonly MAX_ITEMS_PER_WISHLIST = 100;

  static validateWishlistName(name: string): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (!name || name.trim().length === 0) {
      errors.push("Wishlist name is required");
    }

    if (name.length < 2) {
      errors.push("Wishlist name must be at least 2 characters");
    }

    if (name.length > 100) {
      errors.push("Wishlist name must be less than 100 characters");
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  static canAddItem(
    wishlist: WishlistWithRelations,
    productId: string
  ): boolean {
    // Check if product already in wishlist
    if (wishlist.items?.some((item) => item.productId === productId)) {
      return false;
    }

    // Check item limit
    if ((wishlist.itemCount || 0) >= this.MAX_ITEMS_PER_WISHLIST) {
      return false;
    }

    return true;
  }

  static canCreateWishlist(userWishlistCount: number): boolean {
    return userWishlistCount < this.MAX_WISHLISTS_PER_USER;
  }

  static ensureDefaultWishlist(wishlists: WishlistEntity[]): boolean {
    return wishlists.some((wishlist) => wishlist.isDefault);
  }
}
