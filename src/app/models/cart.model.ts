/**
 * Cart Model
 * Business entity cho Cart
 */

import { BaseEntity } from "./common.model";

/**
 * Cart Entity
 */
export interface CartEntity extends BaseEntity {
  userId: string;
  sessionId?: string; // For guest carts
  status: CartStatus;
  subtotal: number;
  tax: number;
  shipping: number;
  discount: number;
  total: number;
  currency: string;
  expiresAt?: Date;
  metadata?: Record<string, any>;
}

export enum CartStatus {
  ACTIVE = "ACTIVE",
  ABANDONED = "ABANDONED",
  CONVERTED = "CONVERTED",
  EXPIRED = "EXPIRED",
}

/**
 * Cart Item Entity
 */
export interface CartItemEntity extends BaseEntity {
  cartId: string;
  productId: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  addedAt: Date;
  metadata?: Record<string, any>;
}

/**
 * Cart with Relations
 */
export interface CartWithRelations extends CartEntity {
  user?: any; // UserEntity
  items?: CartItemWithProduct[];
}

/**
 * Cart Item with Product
 */
export interface CartItemWithProduct extends CartItemEntity {
  product?: any; // ProductEntity
}

/**
 * Add to Cart Data
 */
export interface AddToCartData {
  productId: string;
  quantity: number;
  unitPrice?: number; // Optional, will be fetched from product if not provided
  metadata?: Record<string, any>;
}

/**
 * Update Cart Item Data
 */
export interface UpdateCartItemData {
  quantity?: number;
  metadata?: Record<string, any>;
}

/**
 * Cart Summary
 */
export interface CartSummary {
  itemCount: number;
  subtotal: number;
  tax: number;
  shipping: number;
  discount: number;
  total: number;
  currency: string;
  savings?: number; // Total savings from discounts
}

/**
 * Cart Validation Result
 */
export interface CartValidationResult {
  valid: boolean;
  errors: CartValidationError[];
  warnings: CartValidationWarning[];
}

export interface CartValidationError {
  type:
    | "OUT_OF_STOCK"
    | "PRICE_CHANGED"
    | "PRODUCT_UNAVAILABLE"
    | "INVALID_QUANTITY";
  itemId: string;
  productId: string;
  message: string;
  currentValue?: any;
  expectedValue?: any;
}

export interface CartValidationWarning {
  type: "LOW_STOCK" | "PRICE_INCREASE" | "SALE_ENDING";
  itemId: string;
  productId: string;
  message: string;
  details?: any;
}

/**
 * Create Cart Data
 */
export interface CreateCartData {
  userId: string;
  status?: CartStatus;
  expiresAt?: Date;
}

/**
 * Update Cart Data
 */
export interface UpdateCartData {
  status?: CartStatus;
  expiresAt?: Date;
}

/**
 * Cart Business Rules
 */
export class CartBusinessRules {
  static readonly MAX_ITEMS_PER_CART = 100;
  static readonly MAX_QUANTITY_PER_ITEM = 99;
  static readonly CART_EXPIRY_DAYS = 30;

  static validateQuantity(quantity: number): {
    valid: boolean;
    error?: string;
  } {
    if (!Number.isInteger(quantity) || quantity <= 0) {
      return { valid: false, error: "Quantity must be a positive integer" };
    }

    if (quantity > this.MAX_QUANTITY_PER_ITEM) {
      return {
        valid: false,
        error: `Quantity cannot exceed ${this.MAX_QUANTITY_PER_ITEM}`,
      };
    }

    return { valid: true };
  }

  static canAddItem(
    cart: CartWithRelations,
    productId: string
  ): { valid: boolean; error?: string } {
    if (!cart.items) return { valid: true };

    // Check if cart has too many items
    if (cart.items.length >= this.MAX_ITEMS_PER_CART) {
      return {
        valid: false,
        error: `Cart cannot have more than ${this.MAX_ITEMS_PER_CART} items`,
      };
    }

    // Check if product already exists in cart
    const existingItem = cart.items.find(
      (item) => item.productId === productId
    );
    if (existingItem) {
      return {
        valid: false,
        error: "Product already exists in cart. Use update instead.",
      };
    }

    return { valid: true };
  }

  static calculateItemTotal(quantity: number, unitPrice: number): number {
    return quantity * unitPrice;
  }

  static calculateCartTotals(items: CartItemEntity[]): CartSummary {
    const subtotal = items.reduce((sum, item) => sum + item.totalPrice, 0);

    // These would typically be calculated based on business rules
    const tax = subtotal * 0.1; // 10% tax
    const shipping = subtotal > 100 ? 0 : 10; // Free shipping over $100
    const discount = 0; // Would be calculated based on coupons/promotions

    const total = subtotal + tax + shipping - discount;

    return {
      itemCount: items.length,
      subtotal,
      tax,
      shipping,
      discount,
      total,
      currency: "USD",
      savings: discount,
    };
  }

  static isExpired(cart: CartEntity): boolean {
    if (!cart.expiresAt) return false;
    return new Date() > cart.expiresAt;
  }

  static getExpiryDate(): Date {
    const expiryDate = new Date();
    expiryDate.setDate(expiryDate.getDate() + this.CART_EXPIRY_DAYS);
    return expiryDate;
  }

  static canCheckout(cart: CartWithRelations): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (!cart.items || cart.items.length === 0) {
      errors.push("Cart is empty");
    }

    if (cart.status !== CartStatus.ACTIVE) {
      errors.push("Cart is not active");
    }

    if (this.isExpired(cart)) {
      errors.push("Cart has expired");
    }

    if (cart.total <= 0) {
      errors.push("Cart total must be greater than 0");
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  static mergeGuestCart(
    guestCart: CartWithRelations,
    userCart: CartWithRelations
  ): CartItemEntity[] {
    const mergedItems: CartItemEntity[] = [...(userCart.items || [])];

    guestCart.items?.forEach((guestItem) => {
      const existingItem = mergedItems.find(
        (item) => item.productId === guestItem.productId
      );

      if (existingItem) {
        // Merge quantities
        existingItem.quantity += guestItem.quantity;
        existingItem.totalPrice = this.calculateItemTotal(
          existingItem.quantity,
          existingItem.unitPrice
        );
      } else {
        // Add new item
        mergedItems.push({
          ...guestItem,
          cartId: userCart.id,
          id: "", // Will be generated when saved
        });
      }
    });

    return mergedItems;
  }

  static validateCartItems(items: CartItemWithProduct[]): CartValidationResult {
    const errors: CartValidationError[] = [];
    const warnings: CartValidationWarning[] = [];

    items.forEach((item) => {
      if (!item.product) {
        errors.push({
          type: "PRODUCT_UNAVAILABLE",
          itemId: item.id,
          productId: item.productId,
          message: "Product is no longer available",
        });
        return;
      }

      // Check stock
      if (item.product.stock < item.quantity) {
        if (item.product.stock === 0) {
          errors.push({
            type: "OUT_OF_STOCK",
            itemId: item.id,
            productId: item.productId,
            message: "Product is out of stock",
            currentValue: 0,
            expectedValue: item.quantity,
          });
        } else {
          errors.push({
            type: "INVALID_QUANTITY",
            itemId: item.id,
            productId: item.productId,
            message: `Only ${item.product.stock} items available`,
            currentValue: item.product.stock,
            expectedValue: item.quantity,
          });
        }
      } else if (item.product.stock <= 5) {
        warnings.push({
          type: "LOW_STOCK",
          itemId: item.id,
          productId: item.productId,
          message: `Only ${item.product.stock} items left in stock`,
        });
      }

      // Check price changes
      const currentPrice = item.product.salePrice || item.product.price;
      if (currentPrice !== item.unitPrice) {
        if (currentPrice > item.unitPrice) {
          warnings.push({
            type: "PRICE_INCREASE",
            itemId: item.id,
            productId: item.productId,
            message: `Price has increased from $${item.unitPrice} to $${currentPrice}`,
            details: { oldPrice: item.unitPrice, newPrice: currentPrice },
          });
        }
      }
    });

    return {
      valid: errors.length === 0,
      errors,
      warnings,
    };
  }

  static calculateExpiryDate(): Date {
    return this.getExpiryDate();
  }

  static isProductAvailable(product: any): boolean {
    return product && product.status === "ACTIVE" && product.stock > 0;
  }

  static isValidQuantity(quantity: number): boolean {
    return this.validateQuantity(quantity).valid;
  }

  static calculateTax(subtotal: number): number {
    return subtotal * 0.1; // 10% tax
  }

  static calculateShipping(subtotal: number): number {
    return subtotal > 100 ? 0 : 10; // Free shipping over $100
  }
}
