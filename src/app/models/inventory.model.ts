/**
 * Inventory Model
 * Business entity cho Inventory/Stock management
 */

import { BaseEntity } from "./common.model";

/**
 * Inventory Entity
 */
export interface InventoryEntity extends BaseEntity {
  productId: string;
  location?: string;
  quantity: number;
  reserved: number;
  available: number;
  minStock: number;
  maxStock?: number;
}

/**
 * Create Inventory Data
 */
export interface CreateInventoryData {
  productId: string;
  variantId?: string;
  location?: string;
  quantity: number;
  minStock: number;
  maxStock?: number;
}

/**
 * Update Inventory Data
 */
export interface UpdateInventoryData {
  quantity?: number;
  reserved?: number;
  minStock?: number;
  maxStock?: number;
  location?: string;
}

/**
 * Stock Movement Entity
 */
export interface StockMovementEntity extends BaseEntity {
  inventoryId: string;
  type: StockMovementType;
  quantity: number;
  reason?: string;
  reference?: string;
  performedBy?: string;
}

/**
 * Stock Movement Type
 */
export enum StockMovementType {
  IN = "IN",
  OUT = "OUT",
  ADJUSTMENT = "ADJUSTMENT",
  RESERVED = "RESERVED",
  RELEASED = "RELEASED",
}

/**
 * Create Stock Movement Data
 */
export interface CreateStockMovementData {
  inventoryId: string;
  type: StockMovementType;
  quantity: number;
  reason?: string;
  reference?: string;
  performedBy?: string;
}

/**
 * Inventory Business Rules
 */
export class InventoryBusinessRules {
  static validateQuantity(quantity: number): boolean {
    return quantity >= 0;
  }

  static validateMinStock(minStock: number): boolean {
    return minStock >= 0;
  }

  static validateMaxStock(maxStock: number, minStock: number): boolean {
    return maxStock >= minStock;
  }

  static isLowStock(inventory: InventoryEntity): boolean {
    return inventory.available <= inventory.minStock;
  }

  static isOutOfStock(inventory: InventoryEntity): boolean {
    return inventory.available <= 0;
  }

  static canReserve(inventory: InventoryEntity, quantity: number): boolean {
    return inventory.available >= quantity;
  }

  static calculateAvailable(quantity: number, reserved: number): number {
    return Math.max(0, quantity - reserved);
  }

  static validateStockMovement(data: CreateStockMovementData): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (!data.inventoryId) {
      errors.push("Inventory ID is required");
    }

    if (!data.type || !Object.values(StockMovementType).includes(data.type)) {
      errors.push("Valid movement type is required");
    }

    if (!this.validateQuantity(data.quantity)) {
      errors.push("Quantity must be non-negative");
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  static validateInventory(data: CreateInventoryData): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (!data.productId) {
      errors.push("Product ID is required");
    }

    if (!this.validateQuantity(data.quantity)) {
      errors.push("Quantity must be non-negative");
    }

    if (!this.validateMinStock(data.minStock)) {
      errors.push("Min stock must be non-negative");
    }

    if (
      data.maxStock !== undefined &&
      !this.validateMaxStock(data.maxStock, data.minStock)
    ) {
      errors.push("Max stock must be greater than or equal to min stock");
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  static getStockStatus(
    inventory: InventoryEntity
  ): "OUT_OF_STOCK" | "LOW_STOCK" | "IN_STOCK" {
    if (this.isOutOfStock(inventory)) {
      return "OUT_OF_STOCK";
    }
    if (this.isLowStock(inventory)) {
      return "LOW_STOCK";
    }
    return "IN_STOCK";
  }

  static calculateReorderQuantity(inventory: InventoryEntity): number {
    if (!inventory.maxStock) {
      return inventory.minStock * 2; // Default to double min stock
    }
    return inventory.maxStock - inventory.available;
  }

  static shouldReorder(inventory: InventoryEntity): boolean {
    return this.isLowStock(inventory);
  }
}
