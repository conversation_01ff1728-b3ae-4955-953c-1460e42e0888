/**
 * Notification Model
 * Business entity cho Notification
 */

import { BaseEntity } from "./common.model";

/**
 * Notification Entity
 */
export interface NotificationEntity extends BaseEntity {
  userId: string;
  type: NotificationType;
  title: string;
  message: string;
  isRead: boolean;
  readAt?: Date;
  data?: Record<string, any>;
  metadata?: Record<string, any>;
}

/**
 * Notification Type
 */
export enum NotificationType {
  ORDER_CREATED = "ORDER_CREATED",
  ORDER_UPDATED = "ORDER_UPDATED",
  ORDER_SHIPPED = "ORDER_SHIPPED",
  ORDER_DELIVERED = "ORDER_DELIVERED",
  PAYMENT_SUCCESS = "PAYMENT_SUCCESS",
  PAYMENT_FAILED = "PAYMENT_FAILED",
  PRODUCT_BACK_IN_STOCK = "PRODUCT_BACK_IN_STOCK",
  PROMOTION = "PROMOTION",
  SYSTEM = "SYSTEM",
}

/**
 * Create Notification Data
 */
export interface CreateNotificationData {
  userId: string;
  type: NotificationType;
  title: string;
  message: string;
  data?: Record<string, any>;
}

/**
 * Update Notification Data
 */
export interface UpdateNotificationData {
  isRead?: boolean;
  readAt?: Date;
}

/**
 * Notification Business Rules
 */
export class NotificationBusinessRules {
  static markAsRead(notification: NotificationEntity): UpdateNotificationData {
    return {
      isRead: true,
      readAt: new Date(),
    };
  }

  static createOrderNotification(
    userId: string,
    orderId: string,
    status: string
  ): CreateNotificationData {
    return {
      userId,
      type: NotificationType.ORDER_UPDATED,
      title: "Order Update",
      message: `Your order #${orderId} status has been updated to ${status}`,
      data: { orderId, status },
    };
  }

  static validateNotification(data: CreateNotificationData): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    // Validate userId
    if (!data.userId?.trim()) {
      errors.push("User ID is required");
    }

    // Validate type
    if (!Object.values(NotificationType).includes(data.type)) {
      errors.push("Invalid notification type");
    }

    // Validate title
    if (!data.title?.trim()) {
      errors.push("Title is required");
    } else if (data.title.trim().length < 1) {
      errors.push("Title must be at least 1 character");
    } else if (data.title.trim().length > 200) {
      errors.push("Title must not exceed 200 characters");
    }

    // Validate message
    if (!data.message?.trim()) {
      errors.push("Message is required");
    } else if (data.message.trim().length < 1) {
      errors.push("Message must be at least 1 character");
    } else if (data.message.trim().length > 1000) {
      errors.push("Message must not exceed 1000 characters");
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  static getTypeLabel(type: NotificationType): string {
    const typeLabels: Record<NotificationType, string> = {
      [NotificationType.ORDER_CREATED]: "Order Created",
      [NotificationType.ORDER_UPDATED]: "Order Updated",
      [NotificationType.ORDER_SHIPPED]: "Order Shipped",
      [NotificationType.ORDER_DELIVERED]: "Order Delivered",
      [NotificationType.PAYMENT_SUCCESS]: "Payment Success",
      [NotificationType.PAYMENT_FAILED]: "Payment Failed",
      [NotificationType.PRODUCT_BACK_IN_STOCK]: "Product Back in Stock",
      [NotificationType.PROMOTION]: "Promotion",
      [NotificationType.SYSTEM]: "System",
    };

    return typeLabels[type] || type;
  }

  static getPriorityLevel(type: NotificationType): number {
    const priorityLevels: Record<NotificationType, number> = {
      [NotificationType.PAYMENT_FAILED]: 5, // Highest priority
      [NotificationType.ORDER_DELIVERED]: 4,
      [NotificationType.ORDER_SHIPPED]: 3,
      [NotificationType.PAYMENT_SUCCESS]: 3,
      [NotificationType.ORDER_CREATED]: 2,
      [NotificationType.ORDER_UPDATED]: 2,
      [NotificationType.PRODUCT_BACK_IN_STOCK]: 2,
      [NotificationType.SYSTEM]: 1,
      [NotificationType.PROMOTION]: 1, // Lowest priority
    };

    return priorityLevels[type] || 1;
  }

  static shouldSendEmail(type: NotificationType): boolean {
    const emailTypes = [
      NotificationType.ORDER_CREATED,
      NotificationType.ORDER_SHIPPED,
      NotificationType.ORDER_DELIVERED,
      NotificationType.PAYMENT_SUCCESS,
      NotificationType.PAYMENT_FAILED,
    ];

    return emailTypes.includes(type);
  }

  static shouldSendPush(type: NotificationType): boolean {
    const pushTypes = [
      NotificationType.ORDER_SHIPPED,
      NotificationType.ORDER_DELIVERED,
      NotificationType.PAYMENT_FAILED,
      NotificationType.PRODUCT_BACK_IN_STOCK,
    ];

    return pushTypes.includes(type);
  }

  static createPaymentNotification(
    userId: string,
    orderId: string,
    success: boolean,
    amount?: number
  ): CreateNotificationData {
    const type = success
      ? NotificationType.PAYMENT_SUCCESS
      : NotificationType.PAYMENT_FAILED;
    const title = success ? "Payment Successful" : "Payment Failed";
    const message = success
      ? `Your payment for order #${orderId} was successful${amount ? ` (${amount})` : ""}`
      : `Your payment for order #${orderId} failed. Please try again.`;

    return {
      userId,
      type,
      title,
      message,
      data: { orderId, success, amount },
    };
  }

  static createPromotionNotification(
    userId: string,
    promotionTitle: string,
    promotionCode?: string
  ): CreateNotificationData {
    return {
      userId,
      type: NotificationType.PROMOTION,
      title: "New Promotion Available",
      message: `${promotionTitle}${promotionCode ? ` Use code: ${promotionCode}` : ""}`,
      data: { promotionTitle, promotionCode },
    };
  }

  static createStockNotification(
    userId: string,
    productName: string,
    productId: string
  ): CreateNotificationData {
    return {
      userId,
      type: NotificationType.PRODUCT_BACK_IN_STOCK,
      title: "Product Back in Stock",
      message: `${productName} is now back in stock!`,
      data: { productId, productName },
    };
  }

  static getRetentionDays(type: NotificationType): number {
    const retentionDays: Record<NotificationType, number> = {
      [NotificationType.ORDER_CREATED]: 365,
      [NotificationType.ORDER_UPDATED]: 365,
      [NotificationType.ORDER_SHIPPED]: 365,
      [NotificationType.ORDER_DELIVERED]: 365,
      [NotificationType.PAYMENT_SUCCESS]: 365,
      [NotificationType.PAYMENT_FAILED]: 365,
      [NotificationType.PRODUCT_BACK_IN_STOCK]: 30,
      [NotificationType.PROMOTION]: 30,
      [NotificationType.SYSTEM]: 90,
    };

    return retentionDays[type] || 30;
  }

  static isExpired(notification: NotificationEntity): boolean {
    const retentionDays = this.getRetentionDays(notification.type);
    const expiryDate = new Date(notification.createdAt);
    expiryDate.setDate(expiryDate.getDate() + retentionDays);

    return new Date() > expiryDate;
  }
}
