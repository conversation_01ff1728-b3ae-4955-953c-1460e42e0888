/**
 * Media Model
 * Business entity cho Media
 */

import { BaseEntity } from "./common.model";

/**
 * Media Entity
 */
export interface MediaEntity extends BaseEntity {
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  url: string;
  thumbnailUrl?: string;
  alt?: string;
  caption?: string;
  type: MediaType;
  folder?: string;
  uploadedBy?: string;
  metadata?: Record<string, any>;
}

/**
 * Media Type
 */
export enum MediaType {
  IMAGE = "IMAGE",
  VIDEO = "VIDEO",
  DOCUMENT = "DOCUMENT",
  AUDIO = "AUDIO",
  OTHER = "OTHER",
}

/**
 * Create Media Data
 */
export interface CreateMediaData {
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  url: string;
  thumbnailUrl?: string;
  alt?: string;
  caption?: string;
  type: MediaType;
  folder?: string;
  uploadedBy?: string;
}

/**
 * Update Media Data
 */
export interface UpdateMediaData {
  alt?: string;
  caption?: string;
  folder?: string;
}

/**
 * Media Business Rules
 */
export class MediaBusinessRules {
  static readonly MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
  static readonly ALLOWED_IMAGE_TYPES = [
    "image/jpeg",
    "image/png",
    "image/webp",
    "image/gif",
  ];
  static readonly ALLOWED_VIDEO_TYPES = ["video/mp4", "video/webm"];
  static readonly ALLOWED_DOCUMENT_TYPES = [
    "application/pdf",
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  ];
  static readonly ALLOWED_AUDIO_TYPES = [
    "audio/mpeg",
    "audio/wav",
    "audio/ogg",
  ];

  static getMediaType(mimeType: string): MediaType {
    if (mimeType.startsWith("image/")) return MediaType.IMAGE;
    if (mimeType.startsWith("video/")) return MediaType.VIDEO;
    if (mimeType.startsWith("audio/")) return MediaType.AUDIO;
    if (mimeType.includes("pdf") || mimeType.includes("document"))
      return MediaType.DOCUMENT;
    return MediaType.OTHER;
  }

  static validateFile(data: CreateMediaData): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    // Validate file size
    if (!this.isValidFileSize(data.size)) {
      errors.push(
        `File size must be less than ${this.MAX_FILE_SIZE / (1024 * 1024)}MB`
      );
    }

    // Validate file type
    if (!this.isAllowedFileType(data.mimeType)) {
      errors.push("File type is not allowed");
    }

    // Validate filename
    if (!data.filename?.trim()) {
      errors.push("Filename is required");
    }

    // Validate original name
    if (!data.originalName?.trim()) {
      errors.push("Original name is required");
    }

    // Validate URL
    if (!data.url?.trim()) {
      errors.push("URL is required");
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  static validateFileSize(size: number): boolean {
    return size <= this.MAX_FILE_SIZE;
  }

  // Alias for validateFileSize (required by services)
  static isValidFileSize(size: number): boolean {
    return this.validateFileSize(size);
  }

  static validateImageType(mimeType: string): boolean {
    return this.ALLOWED_IMAGE_TYPES.includes(mimeType);
  }

  static isAllowedFileType(mimeType: string): boolean {
    const allAllowedTypes = [
      ...this.ALLOWED_IMAGE_TYPES,
      ...this.ALLOWED_VIDEO_TYPES,
      ...this.ALLOWED_DOCUMENT_TYPES,
      ...this.ALLOWED_AUDIO_TYPES,
    ];
    return allAllowedTypes.includes(mimeType);
  }

  static generateAltText(filename: string, originalName?: string): string {
    const name = originalName || filename;
    // Remove extension and replace underscores/hyphens with spaces
    const cleanName = name
      .replace(/\.[^/.]+$/, "")
      .replace(/[_-]/g, " ")
      .replace(/([a-z])([A-Z])/g, "$1 $2")
      .toLowerCase();

    return cleanName.charAt(0).toUpperCase() + cleanName.slice(1);
  }

  static canDelete(
    media: MediaEntity,
    usageCount: number
  ): {
    canDelete: boolean;
    reason?: string;
  } {
    if (usageCount > 0) {
      return {
        canDelete: false,
        reason: `Media is being used in ${usageCount} place(s)`,
      };
    }

    return { canDelete: true };
  }

  static getMimeTypesByCategory(category: MediaType): string[] {
    switch (category) {
      case MediaType.IMAGE:
        return this.ALLOWED_IMAGE_TYPES;
      case MediaType.VIDEO:
        return this.ALLOWED_VIDEO_TYPES;
      case MediaType.DOCUMENT:
        return this.ALLOWED_DOCUMENT_TYPES;
      case MediaType.AUDIO:
        return this.ALLOWED_AUDIO_TYPES;
      default:
        return [];
    }
  }

  static getFileExtension(filename: string): string {
    return filename.split(".").pop()?.toLowerCase() || "";
  }

  static generateThumbnailUrl(url: string, size: string = "150x150"): string {
    // Simple thumbnail URL generation - in real app, use image processing service
    const extension = this.getFileExtension(url);
    return url.replace(`.${extension}`, `_thumb_${size}.${extension}`);
  }

  static formatFileSize(bytes: number): string {
    if (bytes === 0) return "0 Bytes";

    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }
}
