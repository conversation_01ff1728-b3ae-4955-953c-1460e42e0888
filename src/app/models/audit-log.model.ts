/**
 * Audit Log Model
 * Business entity cho Audit Log
 */

import { BaseEntity } from "./common.model";

/**
 * Audit Log Entity
 */
export interface AuditLogEntity extends BaseEntity {
  userId?: string;
  action: string;
  resource: string;
  resourceId?: string;
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  metadata?: Record<string, any>;
}

/**
 * Create Audit Log Data
 */
export interface CreateAuditLogData {
  adminId: string;
  action: string;
  resource: string;
  resourceId?: string;
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  metadata?: Record<string, any>;
}

/**
 * Audit Log Business Rules
 */
export class AuditLogBusinessRules {
  static readonly SENSITIVE_FIELDS = ["password", "token", "secret", "key"];

  static sanitizeValues(values: Record<string, any>): Record<string, any> {
    const sanitized = { ...values };

    this.SENSITIVE_FIELDS.forEach((field) => {
      if (sanitized[field]) {
        sanitized[field] = "[REDACTED]";
      }
    });

    return sanitized;
  }

  static createLogEntry(
    action: string,
    resource: string,
    adminId?: string,
    resourceId?: string,
    oldValues?: Record<string, any>,
    newValues?: Record<string, any>
  ): CreateAuditLogData {
    return {
      adminId: adminId || "",
      action,
      resource,
      resourceId,
      oldValues: oldValues ? this.sanitizeValues(oldValues) : undefined,
      newValues: newValues ? this.sanitizeValues(newValues) : undefined,
    };
  }

  static validateAuditLog(data: CreateAuditLogData): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    // Validate action
    if (!data.action?.trim()) {
      errors.push("Action is required");
    } else if (data.action.trim().length < 2) {
      errors.push("Action must be at least 2 characters");
    }

    // Validate resource
    if (!data.resource?.trim()) {
      errors.push("Resource is required");
    } else if (data.resource.trim().length < 2) {
      errors.push("Resource must be at least 2 characters");
    }

    // Validate IP address format if provided
    if (data.ipAddress && !this.isValidIP(data.ipAddress)) {
      errors.push("Invalid IP address format");
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  static isValidIP(ip: string): boolean {
    // Simple IP validation (IPv4)
    const ipv4Regex =
      /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    // Simple IPv6 validation
    const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;

    return ipv4Regex.test(ip) || ipv6Regex.test(ip) || ip === "unknown";
  }

  static getActionLabel(action: string): string {
    const actionLabels: Record<string, string> = {
      CREATE: "Created",
      UPDATE: "Updated",
      DELETE: "Deleted",
      LOGIN: "Logged In",
      LOGOUT: "Logged Out",
      VIEW: "Viewed",
      EXPORT: "Exported",
      IMPORT: "Imported",
      APPROVE: "Approved",
      REJECT: "Rejected",
    };

    return actionLabels[action.toUpperCase()] || action;
  }

  static shouldLogAction(action: string, resource: string): boolean {
    // Don't log certain actions for performance
    const skipActions = ["VIEW", "LIST"];
    const skipResources = ["session", "heartbeat"];

    return (
      !skipActions.includes(action.toUpperCase()) &&
      !skipResources.includes(resource.toLowerCase())
    );
  }

  static calculateRetentionDate(daysToKeep: number = 90): Date {
    const date = new Date();
    date.setDate(date.getDate() - daysToKeep);
    return date;
  }
}
