/**
 * Address Model
 * Business entity cho Address
 */

import { BaseEntity } from "./common.model";

/**
 * Address Entity
 */
export interface AddressEntity extends BaseEntity {
  userId: string;
  type: AddressType;
  firstName: string;
  lastName: string;
  company?: string;
  address1: string;
  address2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  phone?: string;
  isDefault: boolean;
  metadata?: Record<string, any>;
}

/**
 * Address Types
 */
export enum AddressType {
  BILLING = "BILLING",
  SHIPPING = "SHIPPING",
  BOTH = "BOTH",
}

/**
 * Create Address Data
 */
export interface CreateAddressData {
  userId: string;
  type: AddressType;
  firstName: string;
  lastName: string;
  company?: string;
  address1: string;
  address2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  phone?: string;
  isDefault?: boolean;
}

/**
 * Update Address Data
 */
export interface UpdateAddressData {
  type?: AddressType;
  firstName?: string;
  lastName?: string;
  company?: string;
  address1?: string;
  address2?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  phone?: string;
  isDefault?: boolean;
}

/**
 * Address Business Rules
 */
export class AddressBusinessRules {
  static readonly MAX_ADDRESSES_PER_USER = 10;

  static validatePostalCode(postalCode: string, country: string): boolean {
    // Basic validation - can be extended for specific countries
    return postalCode.length >= 3 && postalCode.length <= 10;
  }

  static validateAddress(data: CreateAddressData | UpdateAddressData): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    // Validate required fields for create
    if ("userId" in data) {
      if (!data.firstName?.trim()) {
        errors.push("First name is required");
      }
      if (!data.lastName?.trim()) {
        errors.push("Last name is required");
      }
      if (!data.address1?.trim()) {
        errors.push("Address line 1 is required");
      }
      if (!data.city?.trim()) {
        errors.push("City is required");
      }
      if (!data.state?.trim()) {
        errors.push("State is required");
      }
      if (!data.postalCode?.trim()) {
        errors.push("Postal code is required");
      }
      if (!data.country?.trim()) {
        errors.push("Country is required");
      }
    }

    // Validate postal code format if provided
    if (data.postalCode && data.country) {
      if (!this.validatePostalCode(data.postalCode, data.country)) {
        errors.push("Invalid postal code format");
      }
    }

    // Validate phone format if provided
    if (data.phone && !this.validatePhone(data.phone)) {
      errors.push("Invalid phone number format");
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  static validatePhone(phone: string): boolean {
    // Basic phone validation - can be extended
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ""));
  }

  static canAddMoreAddresses(currentCount: number): boolean {
    return currentCount < this.MAX_ADDRESSES_PER_USER;
  }

  static canDelete(
    address: AddressEntity,
    userAddresses: AddressEntity[]
  ): {
    canDelete: boolean;
    reason?: string;
  } {
    // Cannot delete if it's the only address
    if (userAddresses.length <= 1) {
      return {
        canDelete: false,
        reason: "Cannot delete the only address",
      };
    }

    // Cannot delete if it's the default address and there are other addresses
    if (address.isDefault && userAddresses.length > 1) {
      const otherAddresses = userAddresses.filter(
        (addr) => addr.id !== address.id
      );
      if (otherAddresses.length > 0) {
        return {
          canDelete: false,
          reason:
            "Cannot delete default address. Please set another address as default first.",
        };
      }
    }

    return { canDelete: true };
  }

  static formatFullAddress(address: AddressEntity): string {
    const parts = [
      address.address1,
      address.address2,
      address.city,
      address.state,
      address.postalCode,
      address.country,
    ].filter(Boolean);

    return parts.join(", ");
  }

  static formatDisplayName(address: AddressEntity): string {
    return `${address.firstName} ${address.lastName}`;
  }

  static getAddressTypeLabel(type: AddressType): string {
    switch (type) {
      case AddressType.BILLING:
        return "Billing Address";
      case AddressType.SHIPPING:
        return "Shipping Address";
      case AddressType.BOTH:
        return "Billing & Shipping Address";
      default:
        return "Address";
    }
  }
}
