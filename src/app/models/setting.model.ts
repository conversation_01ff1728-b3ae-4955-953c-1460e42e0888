/**
 * Setting Model
 * Business entity cho Setting
 */

import { BaseEntity } from "./common.model";

/**
 * Setting Entity
 */
export interface SettingEntity extends BaseEntity {
  key: string;
  value: string;
  type: SettingType;
  category: string;
  description?: string;
  isPublic: boolean;
  metadata?: Record<string, any>;
}

/**
 * Setting Type
 */
export enum SettingType {
  STRING = "STRING",
  NUMBER = "NUMBER",
  BOOLEAN = "BOOLEAN",
  JSON = "JSON",
  TEXT = "TEXT",
}

/**
 * Create Setting Data
 */
export interface CreateSettingData {
  key: string;
  value: string;
  type: SettingType;
  category: string;
  description?: string;
  isPublic?: boolean;
}

/**
 * Update Setting Data
 */
export interface UpdateSettingData {
  key?: string;
  value?: string;
  type?: SettingType;
  description?: string;
  isPublic?: boolean;
}

/**
 * Setting Business Rules
 */
export class SettingBusinessRules {
  static validateKey(key: string): boolean {
    const keyRegex = /^[a-z0-9_]+$/;
    return keyRegex.test(key);
  }

  static parseValue(value: string, type: SettingType): any {
    switch (type) {
      case SettingType.NUMBER:
        return parseFloat(value);
      case SettingType.BOOLEAN:
        return value.toLowerCase() === "true";
      case SettingType.JSON:
        try {
          return JSON.parse(value);
        } catch {
          return null;
        }
      default:
        return value;
    }
  }

  static validateSetting(data: CreateSettingData): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (!data.key || !this.validateKey(data.key)) {
      errors.push("Invalid setting key format");
    }

    if (!data.value) {
      errors.push("Setting value is required");
    }

    if (!data.category) {
      errors.push("Setting category is required");
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  static validateValue(value: string, type: SettingType): boolean {
    switch (type) {
      case SettingType.NUMBER:
        return !isNaN(parseFloat(value));
      case SettingType.BOOLEAN:
        return ["true", "false"].includes(value.toLowerCase());
      case SettingType.JSON:
        try {
          JSON.parse(value);
          return true;
        } catch {
          return false;
        }
      default:
        return true;
    }
  }

  static canDelete(setting: SettingEntity): {
    canDelete: boolean;
    reason?: string;
  } {
    // System settings cannot be deleted
    if (setting.category === "system") {
      return {
        canDelete: false,
        reason: "System settings cannot be deleted",
      };
    }

    return { canDelete: true };
  }

  static getDefaultValue(type: SettingType): string {
    switch (type) {
      case SettingType.NUMBER:
        return "0";
      case SettingType.BOOLEAN:
        return "false";
      case SettingType.JSON:
        return "{}";
      default:
        return "";
    }
  }
}
