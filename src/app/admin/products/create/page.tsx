"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { ProductForm } from "@/components/admin/products/ProductForm";
import { useAdminAuth, useAdmin } from "@/contexts";

export default function CreateProductPage() {
  const { adminUser } = useAdminAuth();
  const { setState } = useAdmin();
  const router = useRouter();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (data: any) => {
    setLoading(true);
    try {
      const response = await fetch("/api/admin/products", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...data,
          slug: data.name
            .toLowerCase()
            .normalize("NFD")
            .replace(/[\u0300-\u036f]/g, "")
            .replace(/[^a-z0-9\s-]/g, "")
            .replace(/\s+/g, "-")
            .replace(/-+/g, "-")
            .trim(),
        }),
      });

      if (response.ok) {
        toast.success("Tạo sản phẩm thành công");
        router.push("/admin/products");
      } else {
        const responseData = await response.json();
        toast.error(responseData.error || "Có lỗi xảy ra khi tạo sản phẩm");
      }
    } catch {
      toast.error("Có lỗi xảy ra khi tạo sản phẩm");
    } finally {
      setLoading(false);
    }
  };

  return (
    <ProductForm mode="create" onSubmit={handleSubmit} loading={loading} />
  );
}
