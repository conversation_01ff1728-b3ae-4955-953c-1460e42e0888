"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Plus, Edit, Trash2, Eye, Package } from "lucide-react";
import { toast } from "sonner";
import {
  AdminProductImage,
  AdminBrandLogo,
} from "@/components/admin/AdminImage";
import { getProductImage } from "@/lib/admin/image-utils";
import {
  AdminPageWrapper,
  AdminErrorBoundary,
} from "@/components/admin/AdminPageWrapper";
import { AdminFilters } from "@/components/admin/AdminFilters";
import { AdminPagination } from "@/components/admin/AdminPagination";
import { useAdminProducts, AdminProductsFilters } from "@/hooks/admin";

export default function AdminProductsPage() {
  const {
    products,
    loading,
    error,
    pagination,
    fetchProducts,
    deleteProduct,
    changePage,
    changePageSize,
    refresh,
  } = useAdminProducts();

  // Local state for filters
  const [localFilters, setLocalFilters] = useState<AdminProductsFilters>({});

  // Fetch products on mount
  useEffect(() => {
    fetchProducts(1, 20, {});
  }, [fetchProducts]);

  const handleFilterSubmit = () => {
    fetchProducts(1, pagination.limit, localFilters);
  };

  const handleFilterChange = (key: string, value: string) => {
    setLocalFilters((prev: AdminProductsFilters) => ({
      ...prev,
      [key]: value || undefined,
    }));
  };

  const handleFilterReset = () => {
    setLocalFilters({});
    fetchProducts(1, pagination.limit, {});
  };

  const handleDeleteProduct = async (productId: string) => {
    if (!confirm("Bạn có chắc chắn muốn xóa sản phẩm này?")) {
      return;
    }

    try {
      await deleteProduct(productId);
      toast.success("Xóa sản phẩm thành công");
    } catch (err) {
      console.error("Delete product error:", err);
      toast.error("Có lỗi xảy ra khi xóa sản phẩm");
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    }).format(price);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("vi-VN");
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "ACTIVE":
        return "text-green-600 bg-green-100";
      case "INACTIVE":
        return "text-red-600 bg-red-100";
      case "OUT_OF_STOCK":
        return "text-yellow-600 bg-yellow-100";
      default:
        return "text-gray-600 bg-gray-100";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "ACTIVE":
        return "Hoạt động";
      case "INACTIVE":
        return "Không hoạt động";
      case "OUT_OF_STOCK":
        return "Hết hàng";
      default:
        return status;
    }
  };

  // Định nghĩa filter fields
  const filterFields = [
    {
      key: "search",
      label: "Tìm kiếm",
      type: "text" as const,
      placeholder: "Tìm kiếm sản phẩm...",
    },
    {
      key: "category",
      label: "Danh mục",
      type: "select" as const,
      options: [
        // TODO: Fetch categories from API or context
        { value: "", label: "Tất cả danh mục" },
      ],
    },
    {
      key: "status",
      label: "Trạng thái",
      type: "select" as const,
      options: [
        { value: "", label: "Tất cả trạng thái" },
        { value: "ACTIVE", label: "Hoạt động" },
        { value: "INACTIVE", label: "Không hoạt động" },
        { value: "OUT_OF_STOCK", label: "Hết hàng" },
      ],
    },
    {
      key: "featured",
      label: "Nổi bật",
      type: "boolean" as const,
      placeholder: "Tất cả",
    },
  ];

  return (
    <AdminErrorBoundary>
      <AdminPageWrapper
        title="Quản lý sản phẩm"
        description="Quản lý danh sách sản phẩm trong cửa hàng"
        loading={loading}
        error={error}
        onRefresh={refresh}
        actions={
          <Link href="/admin/products/create">
            <Button className="bg-pink-600 hover:bg-pink-700">
              <Plus className="h-4 w-4 mr-2" />
              Thêm sản phẩm
            </Button>
          </Link>
        }
      >
        {/* Filters */}
        <AdminFilters
          fields={filterFields}
          values={localFilters}
          onChange={handleFilterChange}
          onSubmit={handleFilterSubmit}
          onReset={handleFilterReset}
          loading={loading}
        />

        {/* Products Table */}
        <Card>
          <CardHeader>
            <CardTitle>Danh sách sản phẩm ({pagination.total})</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="space-y-4">
                {Array.from({ length: 5 }, (_, i) => (
                  <div key={i} className="animate-pulse">
                    <div className="flex items-center space-x-4">
                      <div className="w-16 h-16 bg-gray-200 rounded" />
                      <div className="flex-1 space-y-2">
                        <div className="h-4 bg-gray-200 rounded" />
                        <div className="h-4 bg-gray-200 rounded w-2/3" />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (products?.length || 0) === 0 ? (
              <div className="text-center py-12">
                <Package className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">
                  Không có sản phẩm nào
                </h3>
                <p className="text-muted-foreground mb-4">
                  Bắt đầu bằng cách thêm sản phẩm đầu tiên
                </p>
                <Link href="/admin/products/create">
                  <Button className="bg-pink-600 hover:bg-pink-700">
                    <Plus className="h-4 w-4 mr-2" />
                    Thêm sản phẩm
                  </Button>
                </Link>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left py-3 px-4">Sản phẩm</th>
                      <th className="text-left py-3 px-4">SKU</th>
                      <th className="text-left py-3 px-4">Danh mục</th>
                      <th className="text-left py-3 px-4">Thương hiệu</th>
                      <th className="text-left py-3 px-4">Giá</th>
                      <th className="text-left py-3 px-4">Kho</th>
                      <th className="text-left py-3 px-4">Trạng thái</th>
                      <th className="text-left py-3 px-4">Ngày tạo</th>
                      <th className="text-left py-3 px-4">Thao tác</th>
                    </tr>
                  </thead>
                  <tbody>
                    {products.map((product) => (
                      <tr
                        key={product.id}
                        className="border-b hover:bg-gray-50"
                      >
                        <td className="py-3 px-4">
                          <div className="flex items-center space-x-3">
                            <AdminProductImage
                              src={getProductImage(product)}
                              alt={product.name}
                              size={48}
                            />
                            <div>
                              <p className="font-medium">{product.name}</p>
                              {product.featured && (
                                <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded">
                                  Nổi bật
                                </span>
                              )}
                            </div>
                          </div>
                        </td>
                        <td className="py-3 px-4 text-sm text-muted-foreground">
                          {product.sku}
                        </td>
                        <td className="py-3 px-4 text-sm">
                          {product.category?.name || "Chưa phân loại"}
                        </td>
                        <td className="py-3 px-4 text-sm">
                          {product.brand ? (
                            <div className="flex items-center gap-2">
                              <AdminBrandLogo
                                src={product.brand.logo?.url || null}
                                alt={product.brand.name}
                                size={24}
                              />
                              <span>{product.brand.name}</span>
                            </div>
                          ) : (
                            <span className="text-gray-400">—</span>
                          )}
                        </td>
                        <td className="py-3 px-4">
                          <div className="space-y-1">
                            {product.salePrice ? (
                              <>
                                <div className="font-semibold text-pink-600">
                                  {formatPrice(product.salePrice)}
                                </div>
                                <div className="text-sm text-muted-foreground line-through">
                                  {formatPrice(product.price)}
                                </div>
                              </>
                            ) : (
                              <div className="font-semibold">
                                {formatPrice(product.price)}
                              </div>
                            )}
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <span
                            className={`text-sm ${
                              product.stock > 0
                                ? "text-green-600"
                                : "text-red-600"
                            }`}
                          >
                            {product.stock}
                          </span>
                        </td>
                        <td className="py-3 px-4">
                          <span
                            className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(
                              product.status
                            )}`}
                          >
                            {getStatusText(product.status)}
                          </span>
                        </td>
                        <td className="py-3 px-4 text-sm text-muted-foreground">
                          {formatDate(product.createdAt)}
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex items-center space-x-2">
                            <Link href={`/products/${product.id}`}>
                              <Button variant="ghost" size="sm">
                                <Eye className="h-4 w-4" />
                              </Button>
                            </Link>
                            <Link href={`/admin/products/${product.id}/edit`}>
                              <Button variant="ghost" size="sm">
                                <Edit className="h-4 w-4" />
                              </Button>
                            </Link>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeleteProduct(product.id)}
                              className="text-red-600 hover:text-red-700 hover:bg-red-50"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Pagination */}
        <AdminPagination
          pagination={{
            ...pagination,
            total: pagination.total || 0,
            totalPages: pagination.totalPages || 0,
            hasNext: pagination.hasNext || false,
            hasPrev: pagination.hasPrev || false,
          }}
          onPageChange={changePage}
          onPageSizeChange={changePageSize}
          className="mt-6"
        />
      </AdminPageWrapper>
    </AdminErrorBoundary>
  );
}
