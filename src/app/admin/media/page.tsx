"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Plus,
  Search,
  Filter,
  Trash2,
  Edit,
  Upload,
  Image as ImageIcon,
  File,
  Grid,
  List,
  RefreshCw,
  Download,
} from "lucide-react";
import { toast } from "sonner";
import { AdminImage } from "@/components/admin/AdminImage";
import { useAdminAuth, useAdmin } from "@/contexts";

interface MediaFile {
  id: string;
  filename: string;
  url: string;
  size: number;
  width?: number;
  height?: number;
  alt?: string;
  title?: string;
  description?: string;
  mimeType: string;
  folder: string;
  type: "INTERNAL" | "EXTERNAL";
  createdAt: string;
}

interface MediaFilters {
  search: string;
  folder: string;
  type: string;
  mimeType: string;
}

export default function MediaPage() {
  const { adminUser } = useAdminAuth();
  const { getState, setState } = useAdmin();

  const [files, setFiles] = useState<MediaFile[]>([]);
  const [loading, setLoading] = useState(true);
  const [uploading, setUploading] = useState(false);
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);
  const [editingFile, setEditingFile] = useState<MediaFile | null>(null);
  const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false);

  const [filters, setFilters] = useState<MediaFilters>({
    search: "",
    folder: "",
    type: "",
    mimeType: "",
  });

  const [pagination, setPagination] = useState({
    page: 1,
    limit: 50,
    total: 0,
  });

  // Fetch media files
  const fetchFiles = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams();
      params.append('limit', pagination.limit.toString());
      params.append('offset', ((pagination.page - 1) * pagination.limit).toString());
      
      if (filters.search) params.append('search', filters.search);
      if (filters.folder) params.append('folder', filters.folder);
      if (filters.type) params.append('type', filters.type);
      if (filters.mimeType) params.append('mimeType', filters.mimeType);

      const response = await fetch(`/api/admin/media/list?${params}`);
      const data = await response.json();

      if (response.ok && data.success) {
        setFiles(data.data.items || []);
        setPagination((prev) => ({
          ...prev,
          total: data.data.total || 0,
        }));
      } else {
        toast.error(data.error || "Có lỗi xảy ra khi tải media");
      }
    } catch (error) {
      console.error("Fetch files error:", error);
      toast.error("Có lỗi xảy ra khi tải media");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchFiles();
  }, [pagination.page, pagination.limit]);

  // Handle file upload
  const handleFileUpload = async (file: File, folder: string = "uploads") => {
    setUploading(true);
    try {
      const formData = new FormData();
      formData.append("file", file);
      formData.append("folder", folder);

      const response = await fetch("/api/admin/media/upload", {
        method: "POST",
        body: formData,
      });

      const result = await response.json();

      if (result.success) {
        toast.success("Tải file thành công");
        fetchFiles();
        setIsUploadDialogOpen(false);
      } else {
        toast.error(result.error || "Không thể tải file");
      }
    } catch (error) {
      console.error("Upload error:", error);
      toast.error("Có lỗi xảy ra khi tải file");
    } finally {
      setUploading(false);
    }
  };

  // Handle file delete
  const handleDeleteFile = async (fileId: string) => {
    if (!confirm("Bạn có chắc chắn muốn xóa file này?")) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/media/${fileId}`, {
        method: "DELETE",
      });

      const result = await response.json();

      if (result.success) {
        toast.success("Xóa file thành công");
        fetchFiles();
      } else {
        toast.error(result.error || "Không thể xóa file");
      }
    } catch (error) {
      console.error("Delete error:", error);
      toast.error("Có lỗi xảy ra khi xóa file");
    }
  };

  // Handle bulk delete
  const handleBulkDelete = async () => {
    if (selectedFiles.length === 0) return;

    if (!confirm(`Bạn có chắc chắn muốn xóa ${selectedFiles.length} file?`)) {
      return;
    }

    try {
      await Promise.all(
        selectedFiles.map((fileId) =>
          fetch(`/api/admin/media/${fileId}`, { method: "DELETE" })
        )
      );

      toast.success(`Đã xóa ${selectedFiles.length} file`);
      setSelectedFiles([]);
      fetchFiles();
    } catch (error) {
      console.error("Bulk delete error:", error);
      toast.error("Có lỗi xảy ra khi xóa file");
    }
  };

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  // Check if file is image
  const isImage = (mimeType: string) => {
    return mimeType.startsWith("image/");
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Quản lý Media</h1>
          <p className="text-muted-foreground">
            Quản lý tất cả file media trong hệ thống
          </p>
        </div>
        <Button
          onClick={() => setIsUploadDialogOpen(true)}
          className="bg-pink-600 hover:bg-pink-700"
        >
          <Upload className="h-4 w-4 mr-2" />
          Tải lên
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Bộ lọc
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Tìm kiếm..."
                value={filters.search}
                onChange={(e) =>
                  setFilters((prev) => ({ ...prev, search: e.target.value }))
                }
                className="pl-10"
              />
            </div>

            <Select
              value={filters.folder}
              onValueChange={(value) =>
                setFilters((prev) => ({ ...prev, folder: value }))
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Thư mục" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Tất cả</SelectItem>
                <SelectItem value="uploads">Uploads</SelectItem>
                <SelectItem value="products">Products</SelectItem>
                <SelectItem value="brands">Brands</SelectItem>
                <SelectItem value="posts">Posts</SelectItem>
                <SelectItem value="pages">Pages</SelectItem>
                <SelectItem value="events">Events</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={filters.type}
              onValueChange={(value) =>
                setFilters((prev) => ({ ...prev, type: value }))
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Loại" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Tất cả</SelectItem>
                <SelectItem value="INTERNAL">Internal</SelectItem>
                <SelectItem value="EXTERNAL">External</SelectItem>
              </SelectContent>
            </Select>

            <div className="flex gap-2">
              <Button variant="outline" onClick={fetchFiles} className="flex-1">
                <RefreshCw className="h-4 w-4 mr-2" />
                Làm mới
              </Button>
              <Button
                variant={viewMode === "grid" ? "default" : "outline"}
                size="sm"
                onClick={() => setViewMode("grid")}
              >
                <Grid className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === "list" ? "default" : "outline"}
                size="sm"
                onClick={() => setViewMode("list")}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Bulk Actions */}
      {selectedFiles.length > 0 && (
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">
                Đã chọn {selectedFiles.length} file
              </span>
              <div className="flex gap-2">
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={handleBulkDelete}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Xóa đã chọn
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setSelectedFiles([])}
                >
                  Bỏ chọn
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Media Grid/List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ImageIcon className="h-5 w-5 text-pink-600" />
            Media Files ({pagination.total})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <RefreshCw className="h-8 w-8 animate-spin text-pink-600" />
              <span className="ml-2">Đang tải...</span>
            </div>
          ) : files.length === 0 ? (
            <div className="text-center py-12">
              <ImageIcon className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Chưa có file nào</h3>
              <p className="text-muted-foreground mb-4">
                Bắt đầu bằng cách tải lên file đầu tiên
              </p>
              <Button
                onClick={() => setIsUploadDialogOpen(true)}
                className="bg-pink-600 hover:bg-pink-700"
              >
                <Upload className="h-4 w-4 mr-2" />
                Tải lên file
              </Button>
            </div>
          ) : viewMode === "grid" ? (
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
              {files.map((file) => (
                <div
                  key={file.id}
                  className={`relative group border rounded-lg p-2 cursor-pointer hover:bg-muted/50 ${
                    selectedFiles.includes(file.id)
                      ? "ring-2 ring-pink-500"
                      : ""
                  }`}
                  onClick={() => {
                    if (selectedFiles.includes(file.id)) {
                      setSelectedFiles(
                        selectedFiles.filter((id) => id !== file.id)
                      );
                    } else {
                      setSelectedFiles([...selectedFiles, file.id]);
                    }
                  }}
                >
                  <div className="aspect-square bg-muted rounded flex items-center justify-center mb-2">
                    {isImage(file.mimeType) ? (
                      <AdminImage
                        src={file.url}
                        alt={file.alt || file.filename}
                        className="w-full h-full object-cover rounded"
                      />
                    ) : (
                      <File className="h-8 w-8 text-muted-foreground" />
                    )}
                  </div>
                  <p
                    className="text-xs truncate"
                    title={file.title || file.filename}
                  >
                    {file.title || file.filename}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {formatFileSize(file.size)}
                  </p>

                  <div className="absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity flex gap-1">
                    <Button
                      variant="secondary"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        setEditingFile(file);
                      }}
                    >
                      <Edit className="h-3 w-3" />
                    </Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteFile(file.id);
                      }}
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="space-y-2">
              {files.map((file) => (
                <div
                  key={file.id}
                  className={`flex items-center gap-3 p-3 border rounded-lg cursor-pointer hover:bg-muted/50 ${
                    selectedFiles.includes(file.id)
                      ? "ring-2 ring-pink-500"
                      : ""
                  }`}
                  onClick={() => {
                    if (selectedFiles.includes(file.id)) {
                      setSelectedFiles(
                        selectedFiles.filter((id) => id !== file.id)
                      );
                    } else {
                      setSelectedFiles([...selectedFiles, file.id]);
                    }
                  }}
                >
                  <div className="w-10 h-10 bg-muted rounded flex items-center justify-center">
                    {isImage(file.mimeType) ? (
                      <ImageIcon className="h-5 w-5 text-muted-foreground" />
                    ) : (
                      <File className="h-5 w-5 text-muted-foreground" />
                    )}
                  </div>
                  <div className="flex-1">
                    <p className="font-medium">{file.title || file.filename}</p>
                    <p className="text-sm text-muted-foreground">
                      {formatFileSize(file.size)} • {file.mimeType} •{" "}
                      {file.folder}
                    </p>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        window.open(file.url, "_blank");
                      }}
                    >
                      <Download className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        setEditingFile(file);
                      }}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteFile(file.id);
                      }}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Upload Dialog */}
      <Dialog open={isUploadDialogOpen} onOpenChange={setIsUploadDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Tải lên file</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">
                Chọn file
              </label>
              <input
                type="file"
                multiple
                onChange={(e) => {
                  const files = Array.from(e.target.files || []);
                  files.forEach((file) => handleFileUpload(file));
                }}
                className="w-full"
                disabled={uploading}
              />
            </div>
            {uploading && (
              <div className="flex items-center gap-2">
                <RefreshCw className="h-4 w-4 animate-spin" />
                <span>Đang tải lên...</span>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
