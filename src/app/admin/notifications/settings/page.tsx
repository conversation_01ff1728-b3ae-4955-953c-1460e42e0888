import { Metada<PERSON> } from "next";
import { getServerSession } from "next-auth";
import { redirect } from "next/navigation";
import { adminAuthOptions } from "@/lib/admin-auth";
import { NotificationSettings } from "@/components/admin/notifications/NotificationSettings";

export const metadata: Metadata = {
  title: "Notification Settings - NS Shop Admin",
  description: "Cài đặt thông báo và preferences cho admin",
};

export default async function NotificationSettingsPage() {
  // Check admin authentication
  const session = await getServerSession(adminAuthOptions);
  if (!session?.user || session.user.type !== "admin") {
    redirect("/admin/auth/signin");
  }

  return (
    <div className="container mx-auto py-6">
      <NotificationSettings adminId={session.user.id} />
    </div>
  );
}
