"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Eye, Package } from "lucide-react";
import { toast } from "sonner";
import {
  AdminPageWrapper,
  AdminErrorBoundary,
} from "@/components/admin/AdminPageWrapper";
import {
  AdminFilters,
  AdminQuickFilters,
} from "@/components/admin/AdminFilters";
import { AdminPagination } from "@/components/admin/AdminPagination";
import { useAdminOrders, AdminOrdersFilters } from "@/hooks/admin";

export default function AdminOrdersPage() {
  const {
    orders,
    loading,
    error,
    pagination,
    fetchOrders,
    updateOrderStatus,
    searchOrders,
    changePage,
    changePageSize,
    refresh,
  } = useAdminOrders();

  // Local state for filters
  const [localFilters, setLocalFilters] = useState<AdminOrdersFilters>({});

  // Fetch orders on mount
  useEffect(() => {
    fetchOrders(1, 20, {});
  }, [fetchOrders]);

  const handleFilterSubmit = () => {
    searchOrders(localFilters);
  };

  const handleFilterChange = (key: string, value: string) => {
    setLocalFilters((prev: AdminOrdersFilters) => ({
      ...prev,
      [key]: value || undefined,
    }));
  };

  const handleFilterReset = () => {
    setLocalFilters({});
    searchOrders({});
  };

  const handleUpdateOrderStatus = async (
    orderId: string,
    newStatus: AdminOrdersFilters["status"]
  ) => {
    try {
      await updateOrderStatus(orderId, newStatus);
      toast.success("Cập nhật trạng thái đơn hàng thành công");
    } catch (err) {
      console.error("Update order status error:", err);
      toast.error("Có lỗi xảy ra khi cập nhật đơn hàng");
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    }).format(price);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("vi-VN", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "PENDING":
        return "text-yellow-600 bg-yellow-100";
      case "CONFIRMED":
        return "text-blue-600 bg-blue-100";
      case "PROCESSING":
        return "text-purple-600 bg-purple-100";
      case "SHIPPED":
        return "text-orange-600 bg-orange-100";
      case "DELIVERED":
        return "text-green-600 bg-green-100";
      case "CANCELLED":
        return "text-red-600 bg-red-100";
      default:
        return "text-gray-600 bg-gray-100";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "PENDING":
        return "Chờ xác nhận";
      case "CONFIRMED":
        return "Đã xác nhận";
      case "PROCESSING":
        return "Đang xử lý";
      case "SHIPPED":
        return "Đang giao hàng";
      case "DELIVERED":
        return "Đã giao hàng";
      case "CANCELLED":
        return "Đã hủy";
      default:
        return status;
    }
  };

  const getPaymentMethodText = (method: string) => {
    switch (method) {
      case "COD":
        return "COD";
      case "BANK_TRANSFER":
        return "Chuyển khoản";
      case "CREDIT_CARD":
        return "Thẻ tín dụng";
      default:
        return method;
    }
  };

  // Định nghĩa filter fields
  const filterFields = [
    {
      key: "search",
      label: "Tìm kiếm",
      type: "text" as const,
      placeholder: "Tìm kiếm đơn hàng, khách hàng...",
    },
    {
      key: "status",
      label: "Trạng thái",
      type: "select" as const,
      options: [
        { value: "", label: "Tất cả trạng thái" },
        { value: "PENDING", label: "Chờ xác nhận" },
        { value: "CONFIRMED", label: "Đã xác nhận" },
        { value: "PROCESSING", label: "Đang xử lý" },
        { value: "SHIPPED", label: "Đang giao hàng" },
        { value: "DELIVERED", label: "Đã giao hàng" },
        { value: "CANCELLED", label: "Đã hủy" },
      ],
    },
    {
      key: "paymentMethod",
      label: "Phương thức thanh toán",
      type: "select" as const,
      options: [
        { value: "", label: "Tất cả phương thức" },
        { value: "COD", label: "COD" },
        { value: "BANK_TRANSFER", label: "Chuyển khoản" },
        { value: "CREDIT_CARD", label: "Thẻ tín dụng" },
      ],
    },
    {
      key: "paymentStatus",
      label: "Trạng thái thanh toán",
      type: "select" as const,
      options: [
        { value: "", label: "Tất cả" },
        { value: "PENDING", label: "Chờ thanh toán" },
        { value: "PAID", label: "Đã thanh toán" },
        { value: "FAILED", label: "Thất bại" },
      ],
    },
  ];

  // Quick filters cho các trạng thái phổ biến
  const quickFilters = [
    { label: "Tất cả", value: "" },
    { label: "Chờ xác nhận", value: "PENDING" },
    { label: "Đang xử lý", value: "PROCESSING" },
    { label: "Đang giao hàng", value: "SHIPPED" },
    { label: "Đã giao hàng", value: "DELIVERED" },
  ];

  return (
    <AdminErrorBoundary>
      <AdminPageWrapper
        title="Quản lý đơn hàng"
        description="Quản lý và theo dõi tất cả đơn hàng"
        loading={loading}
        error={error}
        onRefresh={refresh}
      >
        {/* Quick Filters */}
        <AdminQuickFilters
          filters={quickFilters}
          activeFilter={localFilters.status || ""}
          onChange={(value) => handleFilterChange("status", value)}
          className="mb-4"
        />

        {/* Filters */}
        <AdminFilters
          fields={filterFields}
          values={localFilters}
          onChange={handleFilterChange}
          onSubmit={handleFilterSubmit}
          onReset={handleFilterReset}
          loading={loading}
        />

        {/* Orders Table */}
        <Card>
          <CardHeader>
            <CardTitle>Danh sách đơn hàng ({pagination.total})</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="space-y-4">
                {Array.from({ length: 5 }, (_, i) => (
                  <div key={i} className="animate-pulse">
                    <div className="flex items-center space-x-4">
                      <div className="w-16 h-16 bg-gray-200 rounded" />
                      <div className="flex-1 space-y-2">
                        <div className="h-4 bg-gray-200 rounded" />
                        <div className="h-4 bg-gray-200 rounded w-2/3" />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : orders.length === 0 ? (
              <div className="text-center py-12">
                <Package className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">
                  Không có đơn hàng nào
                </h3>
                <p className="text-muted-foreground">
                  Chưa có đơn hàng nào được tạo
                </p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left py-3 px-4">Đơn hàng</th>
                      <th className="text-left py-3 px-4">Khách hàng</th>
                      <th className="text-left py-3 px-4">Sản phẩm</th>
                      <th className="text-left py-3 px-4">Tổng tiền</th>
                      <th className="text-left py-3 px-4">Thanh toán</th>
                      <th className="text-left py-3 px-4">Trạng thái</th>
                      <th className="text-left py-3 px-4">Ngày tạo</th>
                      <th className="text-left py-3 px-4">Thao tác</th>
                    </tr>
                  </thead>
                  <tbody>
                    {orders.map((order) => (
                      <tr key={order.id} className="border-b hover:bg-gray-50">
                        <td className="py-3 px-4">
                          <div>
                            <p className="font-medium">
                              #{order.id.slice(-8).toUpperCase()}
                            </p>
                            <p className="text-sm text-muted-foreground">
                              {order.items.length} sản phẩm
                            </p>
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <div>
                            <p className="font-medium">{order.user.name}</p>
                            <p className="text-sm text-muted-foreground">
                              {order.user.email}
                            </p>
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex -space-x-2">
                            {order.items.slice(0, 3).map((item: any) => (
                              <div
                                key={item.id}
                                className="relative w-8 h-8 rounded border-2 border-white overflow-hidden"
                              >
                                <Image
                                  src={
                                    item.product.images[0] ||
                                    "/images/placeholder.jpg"
                                  }
                                  alt={item.product.name}
                                  fill
                                  className="object-cover"
                                />
                              </div>
                            ))}
                            {order.items.length > 3 && (
                              <div className="w-8 h-8 rounded border-2 border-white bg-gray-100 flex items-center justify-center text-xs">
                                +{order.items.length - 3}
                              </div>
                            )}
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <p className="font-semibold">
                            {formatPrice(order.total)}
                          </p>
                        </td>
                        <td className="py-3 px-4">
                          <div>
                            <p className="text-sm">
                              {getPaymentMethodText(order.paymentMethod)}
                            </p>
                            <span
                              className={`text-xs px-2 py-1 rounded ${
                                order.paymentStatus === "PAID"
                                  ? "bg-green-100 text-green-800"
                                  : order.paymentStatus === "FAILED"
                                    ? "bg-red-100 text-red-800"
                                    : "bg-yellow-100 text-yellow-800"
                              }`}
                            >
                              {order.paymentStatus === "PAID"
                                ? "Đã thanh toán"
                                : order.paymentStatus === "FAILED"
                                  ? "Thất bại"
                                  : "Chờ thanh toán"}
                            </span>
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <select
                            value={order.status}
                            onChange={(e) =>
                              handleUpdateOrderStatus(
                                order.id,
                                e.target.value as AdminOrdersFilters["status"]
                              )
                            }
                            className={`text-xs px-2 py-1 rounded border-0 ${getStatusColor(order.status)}`}
                          >
                            <option value="PENDING">Chờ xác nhận</option>
                            <option value="CONFIRMED">Đã xác nhận</option>
                            <option value="PROCESSING">Đang xử lý</option>
                            <option value="SHIPPED">Đang giao hàng</option>
                            <option value="DELIVERED">Đã giao hàng</option>
                            <option value="CANCELLED">Đã hủy</option>
                          </select>
                        </td>
                        <td className="py-3 px-4 text-sm text-muted-foreground">
                          {formatDate(order.createdAt)}
                        </td>
                        <td className="py-3 px-4">
                          <Link href={`/admin/orders/${order.id}`}>
                            <Button variant="ghost" size="sm">
                              <Eye className="h-4 w-4" />
                            </Button>
                          </Link>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Pagination */}
        <AdminPagination
          pagination={{
            ...pagination,
            total: pagination.total || 0,
            totalPages: pagination.totalPages || 0,
            hasNext: pagination.hasNext || false,
            hasPrev: pagination.hasPrev || false,
          }}
          onPageChange={changePage}
          onPageSizeChange={changePageSize}
          className="mt-6"
        />
      </AdminPageWrapper>
    </AdminErrorBoundary>
  );
}
