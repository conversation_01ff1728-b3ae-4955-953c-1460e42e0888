"use client";

import { useState, useEffect, use } from "react";
import { useRouter } from "next/navigation";
import { PageForm } from "@/components/admin/PageForm";
import { Button } from "@/components/ui/button";
import { ArrowLeft, FileText, Loader2 } from "lucide-react";
import { toast } from "sonner";
import Link from "next/link";

interface PageFormData {
  title: string;
  content: string;
  excerpt?: string;
  slug?: string;
  status: "DRAFT" | "PUBLISHED" | "ARCHIVED";
  featured: boolean;
  featuredImage?: string;
  metaTitle?: string;
  metaDescription?: string;
}

interface Page extends PageFormData {
  id: string;
  createdAt: string;
  updatedAt: string;
  author: {
    id: string;
    name: string;
    email: string;
  };
}

export default function EditPagePage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const resolvedParams = use(params);
  const router = useRouter();
  const [page, setPage] = useState<Page | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingPage, setIsLoadingPage] = useState(true);

  // Load page data
  useEffect(() => {
    const fetchPage = async () => {
      try {
        const response = await fetch(`/api/admin/pages/${resolvedParams.id}`);
        const result = await response.json();

        if (result.success) {
          setPage(result.data);
        } else {
          toast.error(result.error || "Không thể tải thông tin trang");
          router.push("/admin/pages");
        }
      } catch (error) {
        console.error("Fetch page error:", error);
        toast.error("Có lỗi xảy ra khi tải thông tin trang");
        router.push("/admin/pages");
      } finally {
        setIsLoadingPage(false);
      }
    };

    fetchPage();
  }, [resolvedParams.id, router]);

  const handleSubmit = async (data: PageFormData) => {
    setIsLoading(true);

    try {
      const response = await fetch(`/api/admin/pages/${resolvedParams.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        toast.success(result.message || "Cập nhật trang thành công");

        // Update local state
        setPage((prev) => (prev ? { ...prev, ...data } : null));
      } else {
        toast.error(result.error || "Có lỗi xảy ra khi cập nhật trang");
      }
    } catch (error) {
      console.error("Update page error:", error);
      toast.error("Có lỗi xảy ra khi cập nhật trang");
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoadingPage) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center gap-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Đang tải...</span>
        </div>
      </div>
    );
  }

  if (!page) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h3 className="text-lg font-semibold">Không tìm thấy trang</h3>
          <p className="text-muted-foreground">
            Trang bạn đang tìm kiếm không tồn tại hoặc đã bị xóa.
          </p>
          <Link href="/admin/pages">
            <Button className="mt-4">Quay lại danh sách</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Link href="/admin/pages">
          <Button variant="ghost" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Quay lại
          </Button>
        </Link>

        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
            <FileText className="h-5 w-5 text-blue-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold">Chỉnh sửa trang</h1>
            <p className="text-muted-foreground">
              Cập nhật thông tin trang "{page.title}"
            </p>
          </div>
        </div>
      </div>

      {/* Form */}
      <PageForm
        initialData={page}
        onSubmit={handleSubmit}
        isLoading={isLoading}
      />
    </div>
  );
}
