"use client";

import { useState } from "react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Plus,
  Edit,
  Trash2,
  FileText,
  Eye,
  Calendar,
  User,
  Star,
} from "lucide-react";
import { toast } from "sonner";
import { AdminDataTable, AdminErrorState } from "@/lib/admin/components";
import { useAdminData, useAdminCrud } from "@/lib/admin/hooks";
import { TableConfig, TableColumn, BulkAction } from "@/lib/admin/types";
import { formatDistanceToNow } from "date-fns";
import { vi } from "date-fns/locale";

interface Page {
  id: string;
  title: string;
  content: string;
  excerpt?: string;
  slug: string;
  status: "DRAFT" | "PUBLISHED" | "ARCHIVED";
  featured: boolean;
  featuredImage?: string;
  metaTitle?: string;
  metaDescription?: string;
  viewCount: number;
  createdAt: string;
  updatedAt: string;
  author: {
    id: string;
    name: string;
    email: string;
  };
}

export default function AdminPagesPage() {
  const [selectedItems, setSelectedItems] = useState<Page[]>([]);

  // Data fetching
  const {
    data: pages,
    loading,
    error,
    pagination,
    refresh,
    setParams,
    params,
  } = useAdminData<Page>({
    endpoint: "/api/admin/pages",
    initialParams: { page: 1, limit: 20 },
  });

  // CRUD operations
  const { remove, bulkDelete } = useAdminCrud("/api/admin/pages");

  // Handle delete page
  const handleDeletePage = async (page: Page) => {
    if (!confirm(`Bạn có chắc chắn muốn xóa trang "${page.title}"?`)) {
      return;
    }

    const result = await remove(page.id);
    if (result) {
      toast.success("Xóa trang thành công");
      refresh();
    }
  };

  // Handle bulk delete
  const handleBulkDelete = async () => {
    if (selectedItems.length === 0) {
      toast.error("Vui lòng chọn ít nhất một trang để xóa");
      return;
    }

    if (
      !confirm(
        `Bạn có chắc chắn muốn xóa ${selectedItems.length} trang đã chọn?`
      )
    ) {
      return;
    }

    const result = await bulkDelete(selectedItems.map((item) => item.id));
    if (result) {
      toast.success(`Đã xóa ${selectedItems.length} trang`);
      setSelectedItems([]);
      refresh();
    }
  };

  // Handle toggle featured
  const handleToggleFeatured = async (page: Page) => {
    try {
      const response = await fetch(
        `/api/admin/pages/${page.id}/toggle-featured`,
        {
          method: "PATCH",
        }
      );

      const result = await response.json();

      if (result.success) {
        toast.success(result.message);
        refresh();
      } else {
        toast.error(result.error || "Có lỗi xảy ra");
      }
    } catch (error) {
      console.error("Toggle featured error:", error);
      toast.error("Có lỗi xảy ra khi cập nhật trạng thái nổi bật");
    }
  };

  // Status badge component
  const StatusBadge = ({ status }: { status: Page["status"] }) => {
    const variants = {
      DRAFT: "secondary",
      PUBLISHED: "default",
      ARCHIVED: "outline",
    } as const;

    const labels = {
      DRAFT: "Bản nháp",
      PUBLISHED: "Đã xuất bản",
      ARCHIVED: "Lưu trữ",
    };

    return <Badge variant={variants[status]}>{labels[status]}</Badge>;
  };

  // Table columns
  const columns: TableColumn<Page>[] = [
    {
      key: "title",
      title: "Tiêu đề",
      render: (page) => (
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <Link
              href={`/admin/pages/${page.id}/edit`}
              className="font-medium hover:text-pink-600 transition-colors"
            >
              {page.title}
            </Link>
            {page.featured && (
              <Star className="h-4 w-4 text-yellow-500 fill-current" />
            )}
          </div>
          <div className="text-sm text-muted-foreground">/{page.slug}</div>
        </div>
      ),
    },
    {
      key: "status",
      title: "Trạng thái",
      render: (page) => <StatusBadge status={page.status} />,
    },
    {
      key: "author",
      title: "Tác giả",
      render: (page) => (
        <div className="flex items-center gap-2">
          <User className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm">{page.author.name}</span>
        </div>
      ),
    },
    {
      key: "viewCount",
      title: "Lượt xem",
      render: (page) => (
        <div className="flex items-center gap-2">
          <Eye className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm">{page.viewCount.toLocaleString()}</span>
        </div>
      ),
    },
    {
      key: "createdAt",
      title: "Ngày tạo",
      render: (page) => (
        <div className="flex items-center gap-2">
          <Calendar className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm">
            {formatDistanceToNow(new Date(page.createdAt), {
              addSuffix: true,
              locale: vi,
            })}
          </span>
        </div>
      ),
    },
  ];

  // Bulk actions
  const bulkActions: BulkAction<Page>[] = [
    {
      key: "delete",
      label: "Xóa đã chọn",
      icon: Trash2,
      type: "danger",
      onClick: handleBulkDelete,
    },
  ];

  // Table configuration
  const tableConfig: TableConfig<Page> = {
    columns,
    rowKey: "id",
    selection: {
      enabled: true,
      type: "checkbox",
    },
    actions: {
      enabled: true,
      items: [
        {
          key: "edit",
          label: "Chỉnh sửa",
          icon: Edit,
          onClick: (record: Page) => {
            window.location.href = `/admin/pages/${record.id}/edit`;
          },
        },
        {
          key: "featured",
          label: (record: Page) =>
            record.featured ? "Bỏ nổi bật" : "Đánh dấu nổi bật",
          icon: Star,
          onClick: handleToggleFeatured,
        },
        {
          key: "delete",
          label: "Xóa",
          icon: Trash2,
          type: "danger",
          onClick: handleDeletePage,
        },
      ],
    },
    pagination: {
      enabled: true,
      pageSize: 20,
    },
    search: {
      enabled: true,
      placeholder: "Tìm kiếm trang...",
    },
    filters: [
      {
        key: "status",
        label: "Trạng thái",
        type: "select",
        options: [
          { label: "Tất cả", value: "" },
          { label: "Bản nháp", value: "DRAFT" },
          { label: "Đã xuất bản", value: "PUBLISHED" },
          { label: "Lưu trữ", value: "ARCHIVED" },
        ],
      },
      {
        key: "featured",
        label: "Nổi bật",
        type: "select",
        options: [
          { label: "Tất cả", value: "" },
          { label: "Nổi bật", value: "true" },
          { label: "Không nổi bật", value: "false" },
        ],
      },
    ],
  };

  if (error) {
    return <AdminErrorState error={error} onRetry={refresh} />;
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
            <FileText className="h-5 w-5 text-blue-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold">Quản lý Trang</h1>
            <p className="text-muted-foreground">
              Quản lý các trang tĩnh của website
            </p>
          </div>
        </div>
        <Link href="/admin/pages/create">
          <Button className="bg-pink-600 hover:bg-pink-700">
            <Plus className="h-4 w-4 mr-2" />
            Tạo trang mới
          </Button>
        </Link>
      </div>

      {/* Data Table */}
      <AdminDataTable
        dataSource={pages || []}
        loading={loading}
        config={tableConfig}
        pagination={
          pagination
            ? {
                current: pagination.page,
                pageSize: pagination.limit,
                total: pagination.total,
              }
            : undefined
        }
        selectedRows={selectedItems}
        onSelectionChange={setSelectedItems}
        bulkActions={bulkActions}
        onSearch={(search) => setParams({ search })}
        onPageChange={(page) => setParams({ page })}
        onPageSizeChange={(pageSize) => setParams({ limit: pageSize })}
        searchValue={params.search}
      />
    </div>
  );
}
