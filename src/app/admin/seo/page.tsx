"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Search as SearchIcon,
  Globe,
  Settings,
  Eye,
  Edit,
  Trash2,
  Plus,
  Save,
  FileText,
  BarChart3,
  Share2,
  Shield,
} from "lucide-react";
import { toast } from "sonner";
import { AdminDataTable } from "@/lib/admin/components/AdminDataTable";
import { useAdminData, useAdminCrud } from "@/lib/admin/hooks";
import { useAdminSEO } from "@/hooks/admin";

interface SEOSettings {
  id: string;
  siteName?: string;
  siteDescription?: string;
  siteKeywords: string[];
  defaultTitle?: string;
  titleTemplate?: string;
  defaultDescription?: string;
  defaultImage?: string;
  ogSiteName?: string;
  ogType?: string;
  twitterSite?: string;
  twitterCreator?: string;
  robotsTxt?: string;
  sitemapEnabled: boolean;
  sitemapFrequency?: string;
  googleAnalyticsId?: string;
  googleTagManagerId?: string;
  facebookPixelId?: string;
  organizationName?: string;
  organizationLogo?: string;
  organizationType?: string;
  contactEmail?: string;
  contactPhone?: string;
  address?: any;
  googleSiteVerification?: string;
  bingSiteVerification?: string;
  createdAt: string;
  updatedAt: string;
}

interface PageSEO {
  id: string;
  path: string;
  title?: string;
  description?: string;
  keywords: string[];
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  ogType?: string;
  twitterTitle?: string;
  twitterDescription?: string;
  twitterImage?: string;
  twitterCard?: string;
  canonical?: string;
  noindex: boolean;
  nofollow: boolean;
  priority?: number;
  changefreq?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

const sitemapFrequencyOptions = [
  { value: "always", label: "Luôn luôn" },
  { value: "hourly", label: "Hàng giờ" },
  { value: "daily", label: "Hàng ngày" },
  { value: "weekly", label: "Hàng tuần" },
  { value: "monthly", label: "Hàng tháng" },
  { value: "yearly", label: "Hàng năm" },
  { value: "never", label: "Không bao giờ" },
];

const twitterCardTypes = [
  { value: "summary", label: "Summary" },
  { value: "summary_large_image", label: "Summary Large Image" },
  { value: "app", label: "App" },
  { value: "player", label: "Player" },
];

export default function AdminSEOPage() {
  const [activeTab, setActiveTab] = useState("global");
  const [selectedPageSEO, setSelectedPageSEO] = useState<PageSEO | null>(null);
  const [isCreatePageSEODialogOpen, setIsCreatePageSEODialogOpen] =
    useState(false);
  const [isEditPageSEODialogOpen, setIsEditPageSEODialogOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState<string>("");

  // Global SEO settings state
  const {
    globalSEO,
    loading: seoLoading,
    saving: seoSaving,
    loadGlobalSEO,
    saveGlobalSEO,
    updateGlobalSEO,
  } = useAdminSEO();

  // Page SEO form state
  const [pageSEOFormData, setPageSEOFormData] = useState({
    path: "",
    title: "",
    description: "",
    keywords: [] as string[],
    ogTitle: "",
    ogDescription: "",
    ogImage: "",
    ogType: "website",
    twitterTitle: "",
    twitterDescription: "",
    twitterImage: "",
    twitterCard: "summary",
    canonical: "",
    noindex: false,
    nofollow: false,
    priority: 0.5,
    changefreq: "weekly",
    isActive: true,
  });

  // Data fetching for page SEO
  const {
    data: pageSEOs,
    loading: pageSEOsLoading,
    pagination: pageSEOsPagination,
    refresh: refreshPageSEOs,
    setParams: setPageSEOsParams,
  } = useAdminData<PageSEO>({
    endpoint: "/api/admin/seo/pages",
    initialParams: { page: 1, limit: 20 },
  });

  // CRUD operations
  const {
    create: createPageSEO,
    update: updatePageSEO,
    remove: deletePageSEO,
  } = useAdminCrud("/api/admin/seo/pages");

  // Load global SEO settings is handled by useAdminSEO hook

  // Load global SEO on mount
  useEffect(() => {
    loadGlobalSEO();
  }, [loadGlobalSEO]);

  // Handle search
  const handleSearch = (value: string) => {
    setSearchQuery(value);
    setPageSEOsParams({ search: value, page: 1 });
  };

  // Handle global SEO save
  const handleSaveGlobalSEO = async () => {
    if (globalSEO) {
      await saveGlobalSEO(globalSEO);
    }
  };

  // Page SEO handlers
  const handleViewPageSEO = (pageSEO: PageSEO) => {
    setSelectedPageSEO(pageSEO);
  };

  const handleEditPageSEO = (pageSEO: PageSEO) => {
    setPageSEOFormData({
      path: pageSEO.path,
      title: pageSEO.title || "",
      description: pageSEO.description || "",
      keywords: pageSEO.keywords,
      ogTitle: pageSEO.ogTitle || "",
      ogDescription: pageSEO.ogDescription || "",
      ogImage: pageSEO.ogImage || "",
      ogType: pageSEO.ogType || "website",
      twitterTitle: pageSEO.twitterTitle || "",
      twitterDescription: pageSEO.twitterDescription || "",
      twitterImage: pageSEO.twitterImage || "",
      twitterCard: pageSEO.twitterCard || "summary",
      canonical: pageSEO.canonical || "",
      noindex: pageSEO.noindex,
      nofollow: pageSEO.nofollow,
      priority: pageSEO.priority || 0.5,
      changefreq: pageSEO.changefreq || "weekly",
      isActive: pageSEO.isActive,
    });
    setSelectedPageSEO(pageSEO);
    setIsEditPageSEODialogOpen(true);
  };

  const handleDeletePageSEO = async (pageSEO: PageSEO) => {
    if (confirm("Bạn có chắc chắn muốn xóa cấu hình SEO cho trang này?")) {
      const result = await deletePageSEO(pageSEO.id);
      if (result) {
        toast.success("Đã xóa cấu hình SEO trang");
        refreshPageSEOs();
      }
    }
  };

  // Form submit handler
  const handlePageSEOSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    let result;
    if (selectedPageSEO && isEditPageSEODialogOpen) {
      result = await updatePageSEO(selectedPageSEO.id, pageSEOFormData);
    } else {
      result = await createPageSEO(pageSEOFormData);
    }

    if (result) {
      toast.success(
        selectedPageSEO
          ? "Đã cập nhật cấu hình SEO trang"
          : "Đã tạo cấu hình SEO trang mới"
      );
      setIsCreatePageSEODialogOpen(false);
      setIsEditPageSEODialogOpen(false);
      setSelectedPageSEO(null);
      resetPageSEOForm();
      refreshPageSEOs();
    }
  };

  // Reset form
  const resetPageSEOForm = () => {
    setPageSEOFormData({
      path: "",
      title: "",
      description: "",
      keywords: [],
      ogTitle: "",
      ogDescription: "",
      ogImage: "",
      ogType: "website",
      twitterTitle: "",
      twitterDescription: "",
      twitterImage: "",
      twitterCard: "summary",
      canonical: "",
      noindex: false,
      nofollow: false,
      priority: 0.5,
      changefreq: "weekly",
      isActive: true,
    });
  };

  // Helper functions
  const handleKeywordsChange = (value: string, isGlobal = false) => {
    const keywords = value
      .split(",")
      .map((k) => k.trim())
      .filter((k) => k);
    if (isGlobal) {
      if (globalSEO) {
        updateGlobalSEO({ siteKeywords: keywords });
      }
    } else {
      setPageSEOFormData({ ...pageSEOFormData, keywords });
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-pink-100 rounded-lg flex items-center justify-center">
            <SearchIcon className="h-5 w-5 text-pink-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold">Quản lý SEO</h1>
            <p className="text-muted-foreground">
              Cấu hình SEO toàn cục và cho từng trang
            </p>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Trang có SEO
                </p>
                <p className="text-2xl font-bold">{pageSEOs?.length || 0}</p>
              </div>
              <Globe className="h-8 w-8 text-pink-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Trang được index
                </p>
                <p className="text-2xl font-bold">
                  {pageSEOs?.filter((p) => !p.noindex).length || 0}
                </p>
              </div>
              <SearchIcon className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Sitemap
                </p>
                <p className="text-2xl font-bold">
                  {globalSEO.sitemapEnabled ? "Bật" : "Tắt"}
                </p>
              </div>
              <FileText className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Analytics
                </p>
                <p className="text-2xl font-bold">
                  {globalSEO.googleAnalyticsId ? "Có" : "Chưa"}
                </p>
              </div>
              <BarChart3 className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="global">Cấu hình toàn cục</TabsTrigger>
          <TabsTrigger value="pages">SEO từng trang</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="technical">Kỹ thuật</TabsTrigger>
        </TabsList>

        {/* Global SEO Tab */}
        <TabsContent value="global" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold">Cấu hình SEO toàn cục</h2>
            <Button onClick={handleSaveGlobalSEO}>
              <Save className="h-4 w-4 mr-2" />
              Lưu cấu hình
            </Button>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Basic Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Globe className="h-5 w-5" />
                  Thông tin cơ bản
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="siteName">Tên website</Label>
                  <Input
                    id="siteName"
                    value={globalSEO.siteName || ""}
                    onChange={(e) =>
                      updateGlobalSEO({
                        siteName: e.target.value,
                      })
                    }
                    placeholder="Nhập tên website"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="siteDescription">Mô tả website</Label>
                  <Textarea
                    id="siteDescription"
                    value={globalSEO.siteDescription || ""}
                    onChange={(e) =>
                      updateGlobalSEO({
                        siteDescription: e.target.value,
                      })
                    }
                    placeholder="Nhập mô tả website"
                    rows={3}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="siteKeywords">
                    Từ khóa chính (phân cách bằng dấu phẩy)
                  </Label>
                  <Input
                    id="siteKeywords"
                    value={globalSEO.siteKeywords?.join(", ") || ""}
                    onChange={(e) => handleKeywordsChange(e.target.value, true)}
                    placeholder="từ khóa 1, từ khóa 2, từ khóa 3"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="defaultTitle">Tiêu đề mặc định</Label>
                  <Input
                    id="defaultTitle"
                    value={globalSEO.defaultTitle || ""}
                    onChange={(e) =>
                      updateGlobalSEO({
                        defaultTitle: e.target.value,
                      })
                    }
                    placeholder="Nhập tiêu đề mặc định"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="titleTemplate">Template tiêu đề</Label>
                  <Input
                    id="titleTemplate"
                    value={globalSEO.titleTemplate || ""}
                    onChange={(e) =>
                      updateGlobalSEO({
                        titleTemplate: e.target.value,
                      })
                    }
                    placeholder="%s | Tên website"
                  />
                  <p className="text-xs text-muted-foreground">
                    Sử dụng %s để thay thế cho tiêu đề trang
                  </p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="defaultDescription">Mô tả mặc định</Label>
                  <Textarea
                    id="defaultDescription"
                    value={globalSEO.defaultDescription || ""}
                    onChange={(e) =>
                      updateGlobalSEO({
                        defaultDescription: e.target.value,
                      })
                    }
                    placeholder="Nhập mô tả mặc định"
                    rows={3}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="defaultImage">Hình ảnh mặc định (URL)</Label>
                  <Input
                    id="defaultImage"
                    value={globalSEO.defaultImage || ""}
                    onChange={(e) =>
                      updateGlobalSEO({
                        defaultImage: e.target.value,
                      })
                    }
                    placeholder="https://example.com/default-image.jpg"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Social Media Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Share2 className="h-5 w-5" />
                  Mạng xã hội
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="ogSiteName">Open Graph - Tên site</Label>
                  <Input
                    id="ogSiteName"
                    value={globalSEO.ogSiteName || ""}
                    onChange={(e) =>
                      updateGlobalSEO({
                        ogSiteName: e.target.value,
                      })
                    }
                    placeholder="Nhập tên site cho Open Graph"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="ogType">Open Graph - Loại</Label>
                  <Select
                    value={globalSEO.ogType || "website"}
                    onValueChange={(value) =>
                      updateGlobalSEO({ ogType: value })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="website">Website</SelectItem>
                      <SelectItem value="article">Article</SelectItem>
                      <SelectItem value="product">Product</SelectItem>
                      <SelectItem value="profile">Profile</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="twitterSite">Twitter Site (@username)</Label>
                  <Input
                    id="twitterSite"
                    value={globalSEO.twitterSite || ""}
                    onChange={(e) =>
                      updateGlobalSEO({
                        twitterSite: e.target.value,
                      })
                    }
                    placeholder="@yoursite"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="twitterCreator">
                    Twitter Creator (@username)
                  </Label>
                  <Input
                    id="twitterCreator"
                    value={globalSEO.twitterCreator || ""}
                    onChange={(e) =>
                      updateGlobalSEO({
                        twitterCreator: e.target.value,
                      })
                    }
                    placeholder="@yourcreator"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Organization Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  Thông tin tổ chức
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="organizationName">Tên tổ chức</Label>
                  <Input
                    id="organizationName"
                    value={globalSEO.organizationName || ""}
                    onChange={(e) =>
                      updateGlobalSEO({
                        organizationName: e.target.value,
                      })
                    }
                    placeholder="Nhập tên tổ chức"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="organizationType">Loại tổ chức</Label>
                  <Select
                    value={globalSEO.organizationType || "Organization"}
                    onValueChange={(value) =>
                      updateGlobalSEO({
                        organizationType: value,
                      })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Organization">Organization</SelectItem>
                      <SelectItem value="Corporation">Corporation</SelectItem>
                      <SelectItem value="LocalBusiness">
                        Local Business
                      </SelectItem>
                      <SelectItem value="Store">Store</SelectItem>
                      <SelectItem value="OnlineStore">Online Store</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="organizationLogo">Logo tổ chức (URL)</Label>
                  <Input
                    id="organizationLogo"
                    value={globalSEO.organizationLogo || ""}
                    onChange={(e) =>
                      updateGlobalSEO({
                        organizationLogo: e.target.value,
                      })
                    }
                    placeholder="https://example.com/logo.png"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="contactEmail">Email liên hệ</Label>
                  <Input
                    id="contactEmail"
                    type="email"
                    value={globalSEO.contactEmail || ""}
                    onChange={(e) =>
                      updateGlobalSEO({
                        contactEmail: e.target.value,
                      })
                    }
                    placeholder="<EMAIL>"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="contactPhone">Số điện thoại</Label>
                  <Input
                    id="contactPhone"
                    value={globalSEO.contactPhone || ""}
                    onChange={(e) =>
                      updateGlobalSEO({
                        contactPhone: e.target.value,
                      })
                    }
                    placeholder="+84 123 456 789"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Verification Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  Xác thực Search Console
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="googleSiteVerification">
                    Google Search Console
                  </Label>
                  <Input
                    id="googleSiteVerification"
                    value={globalSEO.googleSiteVerification || ""}
                    onChange={(e) =>
                      updateGlobalSEO({
                        googleSiteVerification: e.target.value,
                      })
                    }
                    placeholder="Mã xác thực Google"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="bingSiteVerification">
                    Bing Webmaster Tools
                  </Label>
                  <Input
                    id="bingSiteVerification"
                    value={globalSEO.bingSiteVerification || ""}
                    onChange={(e) =>
                      updateGlobalSEO({
                        bingSiteVerification: e.target.value,
                      })
                    }
                    placeholder="Mã xác thực Bing"
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Pages SEO Tab */}
        <TabsContent value="pages" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold">SEO từng trang</h2>
            <Dialog
              open={isCreatePageSEODialogOpen}
              onOpenChange={setIsCreatePageSEODialogOpen}
            >
              <DialogTrigger asChild>
                <Button onClick={resetPageSEOForm}>
                  <Plus className="h-4 w-4 mr-2" />
                  Thêm trang SEO
                </Button>
              </DialogTrigger>
            </Dialog>
          </div>

          {/* Search */}
          <Card>
            <CardContent className="p-6">
              <div className="relative">
                <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Tìm kiếm theo đường dẫn, tiêu đề..."
                  value={searchQuery}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent>
              <AdminDataTable
                dataSource={pageSEOs || []}
                config={{
                  columns: [
                    {
                      key: "page",
                      title: "Trang",
                      render: (pageSEO: PageSEO) => (
                        <div className="space-y-1">
                          <div className="font-medium">{pageSEO.path}</div>
                          <div className="text-sm text-muted-foreground">
                            {pageSEO.title || "Chưa có tiêu đề"}
                          </div>
                        </div>
                      ),
                    },
                    {
                      key: "seo",
                      title: "SEO",
                      render: (pageSEO: PageSEO) => (
                        <div className="space-y-1">
                          <div className="flex items-center gap-2">
                            {pageSEO.noindex && (
                              <Badge
                                variant="secondary"
                                className="bg-red-100 text-red-800"
                              >
                                No Index
                              </Badge>
                            )}
                            {pageSEO.nofollow && (
                              <Badge
                                variant="secondary"
                                className="bg-orange-100 text-orange-800"
                              >
                                No Follow
                              </Badge>
                            )}
                            {!pageSEO.noindex && !pageSEO.nofollow && (
                              <Badge
                                variant="secondary"
                                className="bg-green-100 text-green-800"
                              >
                                Indexable
                              </Badge>
                            )}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            Priority: {pageSEO.priority} | {pageSEO.changefreq}
                          </div>
                        </div>
                      ),
                    },
                    {
                      key: "keywords",
                      title: "Từ khóa",
                      render: (pageSEO: PageSEO) => (
                        <div className="text-sm">
                          {pageSEO.keywords.length > 0
                            ? `${pageSEO.keywords.length} từ khóa`
                            : "Chưa có từ khóa"}
                        </div>
                      ),
                    },
                    {
                      key: "status",
                      title: "Trạng thái",
                      render: (pageSEO: PageSEO) => (
                        <Badge
                          variant="secondary"
                          className={
                            pageSEO.isActive
                              ? "bg-green-100 text-green-800"
                              : "bg-red-100 text-red-800"
                          }
                        >
                          {pageSEO.isActive ? "Hoạt động" : "Tạm dừng"}
                        </Badge>
                      ),
                    },
                  ],
                  rowKey: "id",
                  actions: {
                    enabled: true,
                    items: [
                      {
                        key: "view",
                        label: "Xem chi tiết",
                        icon: Eye,
                        onClick: handleViewPageSEO,
                      },
                      {
                        key: "edit",
                        label: "Chỉnh sửa",
                        icon: Edit,
                        onClick: handleEditPageSEO,
                      },
                      {
                        key: "delete",
                        label: "Xóa",
                        icon: Trash2,
                        onClick: handleDeletePageSEO,
                        type: "danger" as const,
                      },
                    ],
                  },
                  selection: {
                    enabled: true,
                    type: "checkbox" as const,
                  },
                }}
                loading={pageSEOsLoading}
                pagination={
                  pageSEOsPagination
                    ? {
                        current: pageSEOsPagination.page,
                        pageSize: pageSEOsPagination.limit,
                        total: pageSEOsPagination.total,
                      }
                    : undefined
                }
                onPageChange={(page) => setPageSEOsParams({ page })}
                onPageSizeChange={(limit) =>
                  setPageSEOsParams({ limit, page: 1 })
                }
              />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold">Analytics & Tracking</h2>
            <Button onClick={handleSaveGlobalSEO}>
              <Save className="h-4 w-4 mr-2" />
              Lưu cấu hình
            </Button>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Google Analytics
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="googleAnalyticsId">Google Analytics ID</Label>
                  <Input
                    id="googleAnalyticsId"
                    value={globalSEO.googleAnalyticsId || ""}
                    onChange={(e) =>
                      updateGlobalSEO({
                        googleAnalyticsId: e.target.value,
                      })
                    }
                    placeholder="G-XXXXXXXXXX hoặc UA-XXXXXXXX-X"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="googleTagManagerId">
                    Google Tag Manager ID
                  </Label>
                  <Input
                    id="googleTagManagerId"
                    value={globalSEO.googleTagManagerId || ""}
                    onChange={(e) =>
                      updateGlobalSEO({
                        googleTagManagerId: e.target.value,
                      })
                    }
                    placeholder="GTM-XXXXXXX"
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Share2 className="h-5 w-5" />
                  Facebook Pixel
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="facebookPixelId">Facebook Pixel ID</Label>
                  <Input
                    id="facebookPixelId"
                    value={globalSEO.facebookPixelId || ""}
                    onChange={(e) =>
                      updateGlobalSEO({
                        facebookPixelId: e.target.value,
                      })
                    }
                    placeholder="XXXXXXXXXXXXXXX"
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Technical Tab */}
        <TabsContent value="technical" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold">Cấu hình kỹ thuật</h2>
            <Button onClick={handleSaveGlobalSEO}>
              <Save className="h-4 w-4 mr-2" />
              Lưu cấu hình
            </Button>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Sitemap
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="sitemapEnabled"
                    checked={globalSEO.sitemapEnabled || false}
                    onCheckedChange={(checked) =>
                      updateGlobalSEO({
                        sitemapEnabled: checked,
                      })
                    }
                  />
                  <Label htmlFor="sitemapEnabled">
                    Bật tạo sitemap tự động
                  </Label>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="sitemapFrequency">
                    Tần suất cập nhật sitemap
                  </Label>
                  <Select
                    value={globalSEO.sitemapFrequency || "weekly"}
                    onValueChange={(value) =>
                      updateGlobalSEO({
                        sitemapFrequency: value,
                      })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {sitemapFrequencyOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  Robots.txt
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="robotsTxt">Nội dung robots.txt</Label>
                  <Textarea
                    id="robotsTxt"
                    value={globalSEO.robotsTxt || ""}
                    onChange={(e) =>
                      updateGlobalSEO({
                        robotsTxt: e.target.value,
                      })
                    }
                    placeholder={`User-agent: *\nDisallow: /admin/\nDisallow: /api/\n\nSitemap: https://yoursite.com/sitemap.xml`}
                    rows={8}
                    className="font-mono text-sm"
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Create/Edit Page SEO Dialog */}
      <Dialog
        open={isCreatePageSEODialogOpen || isEditPageSEODialogOpen}
        onOpenChange={(open) => {
          if (!open) {
            setIsCreatePageSEODialogOpen(false);
            setIsEditPageSEODialogOpen(false);
            setSelectedPageSEO(null);
            resetPageSEOForm();
          }
        }}
      >
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {selectedPageSEO ? "Chỉnh sửa SEO trang" : "Tạo SEO trang mới"}
            </DialogTitle>
          </DialogHeader>
          <form onSubmit={handlePageSEOSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="pagePath">Đường dẫn trang *</Label>
                <Input
                  id="pagePath"
                  value={pageSEOFormData.path}
                  onChange={(e) =>
                    setPageSEOFormData({
                      ...pageSEOFormData,
                      path: e.target.value,
                    })
                  }
                  placeholder="/about-us, /products/[slug]"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="pageTitle">Tiêu đề trang</Label>
                <Input
                  id="pageTitle"
                  value={pageSEOFormData.title}
                  onChange={(e) =>
                    setPageSEOFormData({
                      ...pageSEOFormData,
                      title: e.target.value,
                    })
                  }
                  placeholder="Nhập tiêu đề trang"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="pageDescription">Mô tả trang</Label>
              <Textarea
                id="pageDescription"
                value={pageSEOFormData.description}
                onChange={(e) =>
                  setPageSEOFormData({
                    ...pageSEOFormData,
                    description: e.target.value,
                  })
                }
                placeholder="Nhập mô tả trang"
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="pageKeywords">
                Từ khóa (phân cách bằng dấu phẩy)
              </Label>
              <Input
                id="pageKeywords"
                value={pageSEOFormData.keywords.join(", ")}
                onChange={(e) => handleKeywordsChange(e.target.value)}
                placeholder="từ khóa 1, từ khóa 2, từ khóa 3"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="canonical">Canonical URL</Label>
                <Input
                  id="canonical"
                  value={pageSEOFormData.canonical}
                  onChange={(e) =>
                    setPageSEOFormData({
                      ...pageSEOFormData,
                      canonical: e.target.value,
                    })
                  }
                  placeholder="https://example.com/canonical-url"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="priority">Sitemap Priority</Label>
                <Input
                  id="priority"
                  type="number"
                  min="0"
                  max="1"
                  step="0.1"
                  value={pageSEOFormData.priority}
                  onChange={(e) =>
                    setPageSEOFormData({
                      ...pageSEOFormData,
                      priority: Number(e.target.value),
                    })
                  }
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="noindex"
                  checked={pageSEOFormData.noindex}
                  onCheckedChange={(checked) =>
                    setPageSEOFormData({ ...pageSEOFormData, noindex: checked })
                  }
                />
                <Label htmlFor="noindex">No Index</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="nofollow"
                  checked={pageSEOFormData.nofollow}
                  onCheckedChange={(checked) =>
                    setPageSEOFormData({
                      ...pageSEOFormData,
                      nofollow: checked,
                    })
                  }
                />
                <Label htmlFor="nofollow">No Follow</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="pageIsActive"
                  checked={pageSEOFormData.isActive}
                  onCheckedChange={(checked) =>
                    setPageSEOFormData({
                      ...pageSEOFormData,
                      isActive: checked,
                    })
                  }
                />
                <Label htmlFor="pageIsActive">Kích hoạt</Label>
              </div>
            </div>

            <div className="flex justify-end gap-3">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setIsCreatePageSEODialogOpen(false);
                  setIsEditPageSEODialogOpen(false);
                  setSelectedPageSEO(null);
                  resetPageSEOForm();
                }}
              >
                Hủy
              </Button>
              <Button type="submit">
                {selectedPageSEO ? "Cập nhật" : "Tạo mới"}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
