import { DashboardStats, RecentOrders } from "@/components/admin";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default function AdminDashboard() {
  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold">Dashboard</h1>
        <p className="text-muted-foreground">
          Tổng quan về hoạt động kinh doanh của cửa hàng
        </p>
      </div>

      {/* Stats Cards */}
      <DashboardStats />

      {/* Charts and Tables */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Sales Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Doanh thu theo tháng</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-64 flex items-center justify-center text-muted-foreground">
              <PERSON><PERSON><PERSON><PERSON> đồ doanh thu sẽ được hiển thị ở đây
            </div>
          </CardContent>
        </Card>

        {/* Top Products */}
        <Card data-testid="top-products">
          <CardHeader>
            <CardTitle>Sản phẩm bán chạy</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4" data-testid="products-list">
              {[
                {
                  name: "Áo thun cotton premium",
                  sales: 156,
                  revenue: 31200000,
                },
                { name: "Váy maxi hoa nhí", sales: 89, revenue: 53311000 },
                { name: "Quần jeans skinny", sales: 134, revenue: 80266000 },
                { name: "Áo khoác bomber", sales: 67, revenue: 60233000 },
              ].map((product, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">{product.name}</div>
                    <div className="text-sm text-muted-foreground">
                      {product.sales} đã bán
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium">
                      {new Intl.NumberFormat("vi-VN", {
                        style: "currency",
                        currency: "VND",
                      }).format(product.revenue)}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Orders */}
      <RecentOrders />
    </div>
  );
}
