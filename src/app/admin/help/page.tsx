"use client";

import { useAdminAuth, useAdmin } from "@/contexts";

export default function AdminHelpPage() {
  const { adminUser } = useAdminAuth();
  const { getState } = useAdmin();
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Trợ giúp & Hỗ trợ</h1>
        <p className="text-gray-600 mt-2">
          Hướng dẫn sử dụng và liên hệ hỗ trợ
        </p>
      </div>

      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h2 className="text-lg font-semibold mb-4">Hướng dẫn sử dụng</h2>
        <p>Tài liệu hướng dẫn sẽ được hiển thị ở đây...</p>
      </div>
    </div>
  );
}
