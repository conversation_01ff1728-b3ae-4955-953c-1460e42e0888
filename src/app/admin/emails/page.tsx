"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Mail,
  Search,
  Filter,
  Eye,
  Edit,
  Trash2,
  Plus,
  Send,
  Settings,
  FileText,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
} from "lucide-react";
import { toast } from "sonner";
import { AdminDataTable } from "@/lib/admin/components/AdminDataTable";
import { useAdminData, useAdminCrud } from "@/lib/admin/hooks";
import { formatDistanceToNow } from "date-fns";
import { vi } from "date-fns/locale";

interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  content: string;
  type:
    | "WELCOME"
    | "ORDER_CONFIRMATION"
    | "ORDER_SHIPPED"
    | "ORDER_DELIVERED"
    | "PASSWORD_RESET"
    | "NEWSLETTER"
    | "PROMOTION"
    | "CUSTOM";
  variables?: any;
  isActive: boolean;
  isDefault: boolean;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  _count: {
    emailLogs: number;
  };
}

interface EmailLog {
  id: string;
  templateId?: string;
  recipient: string;
  subject: string;
  content: string;
  status: "PENDING" | "SENT" | "FAILED" | "BOUNCED";
  error?: string;
  sentAt?: string;
  createdAt: string;
  updatedAt: string;
  template?: {
    id: string;
    name: string;
  };
}

interface SMTPConfig {
  id: string;
  name: string;
  host: string;
  port: number;
  secure: boolean;
  username: string;
  password: string;
  fromName: string;
  fromEmail: string;
  isActive: boolean;
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
}

const emailTemplateTypeLabels = {
  WELCOME: "Chào mừng",
  ORDER_CONFIRMATION: "Xác nhận đơn hàng",
  ORDER_SHIPPED: "Đơn hàng đã gửi",
  ORDER_DELIVERED: "Đơn hàng đã giao",
  PASSWORD_RESET: "Đặt lại mật khẩu",
  NEWSLETTER: "Bản tin",
  PROMOTION: "Khuyến mãi",
  CUSTOM: "Tùy chỉnh",
};

const emailTemplateTypeColors = {
  WELCOME: "bg-green-100 text-green-800",
  ORDER_CONFIRMATION: "bg-blue-100 text-blue-800",
  ORDER_SHIPPED: "bg-purple-100 text-purple-800",
  ORDER_DELIVERED: "bg-emerald-100 text-emerald-800",
  PASSWORD_RESET: "bg-orange-100 text-orange-800",
  NEWSLETTER: "bg-cyan-100 text-cyan-800",
  PROMOTION: "bg-pink-100 text-pink-800",
  CUSTOM: "bg-gray-100 text-gray-800",
};

const emailStatusLabels = {
  PENDING: "Chờ gửi",
  SENT: "Đã gửi",
  FAILED: "Thất bại",
  BOUNCED: "Bị trả về",
};

const emailStatusColors = {
  PENDING: "bg-yellow-100 text-yellow-800",
  SENT: "bg-green-100 text-green-800",
  FAILED: "bg-red-100 text-red-800",
  BOUNCED: "bg-orange-100 text-orange-800",
};

const emailStatusIcons = {
  PENDING: Clock,
  SENT: CheckCircle,
  FAILED: XCircle,
  BOUNCED: AlertCircle,
};

export default function AdminEmailsPage() {
  const [activeTab, setActiveTab] = useState("templates");
  const [selectedTemplate, setSelectedTemplate] =
    useState<EmailTemplate | null>(null);
  const [selectedLog, setSelectedLog] = useState<EmailLog | null>(null);
  const [selectedConfig, setSelectedConfig] = useState<SMTPConfig | null>(null);
  const [isCreateTemplateDialogOpen, setIsCreateTemplateDialogOpen] =
    useState(false);
  const [isEditTemplateDialogOpen, setIsEditTemplateDialogOpen] =
    useState(false);
  const [isCreateConfigDialogOpen, setIsCreateConfigDialogOpen] =
    useState(false);
  const [isEditConfigDialogOpen, setIsEditConfigDialogOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [filterStatus, setFilterStatus] = useState<string>("");
  const [filterType, setFilterType] = useState<string>("");

  // Form state for templates
  const [templateFormData, setTemplateFormData] = useState<{
    name: string;
    subject: string;
    content: string;
    type: EmailTemplate["type"];
    isActive: boolean;
    isDefault: boolean;
  }>({
    name: "",
    subject: "",
    content: "",
    type: "CUSTOM",
    isActive: true,
    isDefault: false,
  });

  // Form state for SMTP config
  const [configFormData, setConfigFormData] = useState({
    name: "",
    host: "",
    port: 587,
    secure: true,
    username: "",
    password: "",
    fromName: "",
    fromEmail: "",
    isActive: false,
    isDefault: false,
  });

  // Data fetching for templates
  const {
    data: templates,
    loading: templatesLoading,
    pagination: templatesPagination,
    refresh: refreshTemplates,
    setParams: setTemplatesParams,
  } = useAdminData<EmailTemplate>({
    endpoint: "/api/admin/emails/templates",
    initialParams: { page: 1, limit: 20 },
  });

  // Data fetching for logs
  const {
    data: logs,
    loading: logsLoading,
    pagination: logsPagination,
    refresh: refreshLogs,
    setParams: setLogsParams,
  } = useAdminData<EmailLog>({
    endpoint: "/api/admin/emails/logs",
    initialParams: { page: 1, limit: 20 },
  });

  // Data fetching for SMTP configs
  const {
    data: configs,
    loading: configsLoading,
    pagination: configsPagination,
    refresh: refreshConfigs,
    setParams: setConfigsParams,
  } = useAdminData<SMTPConfig>({
    endpoint: "/api/admin/emails/configs",
    initialParams: { page: 1, limit: 20 },
  });

  // CRUD operations
  const {
    create: createTemplate,
    update: updateTemplate,
    remove: deleteTemplate,
  } = useAdminCrud("/api/admin/emails/templates");
  const {
    create: createConfig,
    update: updateConfig,
    remove: deleteConfig,
  } = useAdminCrud("/api/admin/emails/configs");

  // Handle search
  const handleSearch = (value: string) => {
    setSearchQuery(value);
    if (activeTab === "templates") {
      setTemplatesParams({ search: value, page: 1 });
    } else if (activeTab === "logs") {
      setLogsParams({ search: value, page: 1 });
    } else {
      setConfigsParams({ search: value, page: 1 });
    }
  };

  // Handle filter
  const handleFilterStatus = (value: string) => {
    setFilterStatus(value);
    if (activeTab === "logs") {
      setLogsParams({ status: value === "all" ? undefined : value, page: 1 });
    }
  };

  const handleFilterType = (value: string) => {
    setFilterType(value);
    if (activeTab === "templates") {
      setTemplatesParams({
        type: value === "all" ? undefined : value,
        page: 1,
      });
    }
  };

  // Template handlers
  const handleViewTemplate = (template: EmailTemplate) => {
    setSelectedTemplate(template);
  };

  const handleEditTemplate = (template: EmailTemplate) => {
    setTemplateFormData({
      name: template.name,
      subject: template.subject,
      content: template.content,
      type: template.type,
      isActive: template.isActive,
      isDefault: template.isDefault,
    });
    setSelectedTemplate(template);
    setIsEditTemplateDialogOpen(true);
  };

  const handleDeleteTemplate = async (template: EmailTemplate) => {
    if (confirm("Bạn có chắc chắn muốn xóa template email này?")) {
      const result = await deleteTemplate(template.id);
      if (result) {
        toast.success("Đã xóa template email");
        refreshTemplates();
      }
    }
  };

  // Config handlers
  const handleViewConfig = (config: SMTPConfig) => {
    setSelectedConfig(config);
  };

  const handleEditConfig = (config: SMTPConfig) => {
    setConfigFormData({
      name: config.name,
      host: config.host,
      port: config.port,
      secure: config.secure,
      username: config.username,
      password: "", // Don't show password
      fromName: config.fromName,
      fromEmail: config.fromEmail,
      isActive: config.isActive,
      isDefault: config.isDefault,
    });
    setSelectedConfig(config);
    setIsEditConfigDialogOpen(true);
  };

  const handleDeleteConfig = async (config: SMTPConfig) => {
    if (confirm("Bạn có chắc chắn muốn xóa cấu hình SMTP này?")) {
      const result = await deleteConfig(config.id);
      if (result) {
        toast.success("Đã xóa cấu hình SMTP");
        refreshConfigs();
      }
    }
  };

  // Form submit handlers
  const handleTemplateSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    let result;
    if (selectedTemplate && isEditTemplateDialogOpen) {
      result = await updateTemplate(selectedTemplate.id, templateFormData);
    } else {
      result = await createTemplate(templateFormData);
    }

    if (result) {
      toast.success(
        selectedTemplate
          ? "Đã cập nhật template email"
          : "Đã tạo template email mới"
      );
      setIsCreateTemplateDialogOpen(false);
      setIsEditTemplateDialogOpen(false);
      setSelectedTemplate(null);
      resetTemplateForm();
      refreshTemplates();
    }
  };

  const handleConfigSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    let result;
    if (selectedConfig && isEditConfigDialogOpen) {
      result = await updateConfig(selectedConfig.id, configFormData);
    } else {
      result = await createConfig(configFormData);
    }

    if (result) {
      toast.success(
        selectedConfig
          ? "Đã cập nhật cấu hình SMTP"
          : "Đã tạo cấu hình SMTP mới"
      );
      setIsCreateConfigDialogOpen(false);
      setIsEditConfigDialogOpen(false);
      setSelectedConfig(null);
      resetConfigForm();
      refreshConfigs();
    }
  };

  // Reset forms
  const resetTemplateForm = () => {
    setTemplateFormData({
      name: "",
      subject: "",
      content: "",
      type: "CUSTOM",
      isActive: true,
      isDefault: false,
    });
  };

  const resetConfigForm = () => {
    setConfigFormData({
      name: "",
      host: "",
      port: 587,
      secure: true,
      username: "",
      password: "",
      fromName: "",
      fromEmail: "",
      isActive: false,
      isDefault: false,
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-pink-100 rounded-lg flex items-center justify-center">
            <Mail className="h-5 w-5 text-pink-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold">Quản lý Email</h1>
            <p className="text-muted-foreground">
              Quản lý template email, cấu hình SMTP và lịch sử gửi email
            </p>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Email Templates
                </p>
                <p className="text-2xl font-bold">{templates?.length || 0}</p>
              </div>
              <FileText className="h-8 w-8 text-pink-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Emails đã gửi
                </p>
                <p className="text-2xl font-bold">
                  {logs?.filter((l) => l.status === "SENT").length || 0}
                </p>
              </div>
              <Send className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Emails thất bại
                </p>
                <p className="text-2xl font-bold">
                  {logs?.filter((l) => l.status === "FAILED").length || 0}
                </p>
              </div>
              <XCircle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Cấu hình SMTP
                </p>
                <p className="text-2xl font-bold">{configs?.length || 0}</p>
              </div>
              <Settings className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Tìm kiếm template, email, cấu hình..."
                  value={searchQuery}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              {activeTab === "templates" && (
                <Select value={filterType} onValueChange={handleFilterType}>
                  <SelectTrigger className="w-[150px]">
                    <Filter className="h-4 w-4 mr-2" />
                    <SelectValue placeholder="Loại" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tất cả</SelectItem>
                    <SelectItem value="WELCOME">Chào mừng</SelectItem>
                    <SelectItem value="ORDER_CONFIRMATION">
                      Xác nhận đơn hàng
                    </SelectItem>
                    <SelectItem value="ORDER_SHIPPED">
                      Đơn hàng đã gửi
                    </SelectItem>
                    <SelectItem value="ORDER_DELIVERED">
                      Đơn hàng đã giao
                    </SelectItem>
                    <SelectItem value="PASSWORD_RESET">
                      Đặt lại mật khẩu
                    </SelectItem>
                    <SelectItem value="NEWSLETTER">Bản tin</SelectItem>
                    <SelectItem value="PROMOTION">Khuyến mãi</SelectItem>
                    <SelectItem value="CUSTOM">Tùy chỉnh</SelectItem>
                  </SelectContent>
                </Select>
              )}
              {activeTab === "logs" && (
                <Select value={filterStatus} onValueChange={handleFilterStatus}>
                  <SelectTrigger className="w-[150px]">
                    <Filter className="h-4 w-4 mr-2" />
                    <SelectValue placeholder="Trạng thái" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tất cả</SelectItem>
                    <SelectItem value="PENDING">Chờ gửi</SelectItem>
                    <SelectItem value="SENT">Đã gửi</SelectItem>
                    <SelectItem value="FAILED">Thất bại</SelectItem>
                    <SelectItem value="BOUNCED">Bị trả về</SelectItem>
                  </SelectContent>
                </Select>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="templates">Email Templates</TabsTrigger>
          <TabsTrigger value="logs">Lịch sử Email</TabsTrigger>
          <TabsTrigger value="configs">Cấu hình SMTP</TabsTrigger>
        </TabsList>

        {/* Templates Tab */}
        <TabsContent value="templates" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold">Email Templates</h2>
            <Dialog
              open={isCreateTemplateDialogOpen}
              onOpenChange={setIsCreateTemplateDialogOpen}
            >
              <DialogTrigger asChild>
                <Button onClick={resetTemplateForm}>
                  <Plus className="h-4 w-4 mr-2" />
                  Tạo template
                </Button>
              </DialogTrigger>
            </Dialog>
          </div>

          <Card>
            <CardContent>
              <AdminDataTable
                dataSource={templates || []}
                config={{
                  columns: [
                    {
                      key: "template",
                      title: "Template",
                      render: (template: EmailTemplate) => (
                        <div className="space-y-1">
                          <div className="font-medium">{template.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {template.subject}
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge
                              variant="secondary"
                              className={emailTemplateTypeColors[template.type]}
                            >
                              {emailTemplateTypeLabels[template.type]}
                            </Badge>
                            {template.isDefault && (
                              <Badge variant="outline">Mặc định</Badge>
                            )}
                          </div>
                        </div>
                      ),
                    },
                    {
                      key: "usage",
                      title: "Sử dụng",
                      render: (template: EmailTemplate) => (
                        <div className="text-sm">
                          {template._count.emailLogs} lần gửi
                        </div>
                      ),
                    },
                    {
                      key: "status",
                      title: "Trạng thái",
                      render: (template: EmailTemplate) => (
                        <Badge
                          variant="secondary"
                          className={
                            template.isActive
                              ? "bg-green-100 text-green-800"
                              : "bg-red-100 text-red-800"
                          }
                        >
                          {template.isActive ? "Hoạt động" : "Tạm dừng"}
                        </Badge>
                      ),
                    },
                    {
                      key: "createdAt",
                      title: "Ngày tạo",
                      sortable: true,
                      render: (template: EmailTemplate) => (
                        <div className="text-sm">
                          {formatDistanceToNow(new Date(template.createdAt), {
                            addSuffix: true,
                            locale: vi,
                          })}
                        </div>
                      ),
                    },
                  ],
                  rowKey: "id",
                  actions: {
                    enabled: true,
                    items: [
                      {
                        key: "view",
                        label: "Xem chi tiết",
                        icon: Eye,
                        onClick: handleViewTemplate,
                      },
                      {
                        key: "edit",
                        label: "Chỉnh sửa",
                        icon: Edit,
                        onClick: handleEditTemplate,
                      },
                      {
                        key: "delete",
                        label: "Xóa",
                        icon: Trash2,
                        onClick: handleDeleteTemplate,
                        type: "danger" as const,
                      },
                    ],
                  },
                  selection: {
                    enabled: true,
                    type: "checkbox" as const,
                  },
                }}
                loading={templatesLoading}
                pagination={
                  templatesPagination
                    ? {
                        current: templatesPagination.page,
                        pageSize: templatesPagination.limit,
                        total: templatesPagination.total,
                      }
                    : undefined
                }
                onPageChange={(page) => setTemplatesParams({ page })}
                onPageSizeChange={(limit) =>
                  setTemplatesParams({ limit, page: 1 })
                }
              />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Logs Tab */}
        <TabsContent value="logs" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold">Lịch sử Email</h2>
          </div>

          <Card>
            <CardContent>
              <AdminDataTable
                dataSource={logs || []}
                config={{
                  columns: [
                    {
                      key: "email",
                      title: "Email",
                      render: (log: EmailLog) => (
                        <div className="space-y-1">
                          <div className="font-medium">{log.recipient}</div>
                          <div className="text-sm text-muted-foreground">
                            {log.subject}
                          </div>
                          {log.template && (
                            <div className="text-xs text-muted-foreground">
                              Template: {log.template.name}
                            </div>
                          )}
                        </div>
                      ),
                    },
                    {
                      key: "status",
                      title: "Trạng thái",
                      render: (log: EmailLog) => {
                        const StatusIcon = emailStatusIcons[log.status];
                        return (
                          <div className="flex items-center gap-2">
                            <StatusIcon className="h-4 w-4" />
                            <Badge
                              variant="secondary"
                              className={emailStatusColors[log.status]}
                            >
                              {emailStatusLabels[log.status]}
                            </Badge>
                          </div>
                        );
                      },
                    },
                    {
                      key: "sentAt",
                      title: "Thời gian gửi",
                      render: (log: EmailLog) => (
                        <div className="text-sm">
                          {log.sentAt
                            ? formatDistanceToNow(new Date(log.sentAt), {
                                addSuffix: true,
                                locale: vi,
                              })
                            : "Chưa gửi"}
                        </div>
                      ),
                    },
                    {
                      key: "error",
                      title: "Lỗi",
                      render: (log: EmailLog) => (
                        <div className="text-sm text-red-600">
                          {log.error && (
                            <span className="line-clamp-1">{log.error}</span>
                          )}
                        </div>
                      ),
                    },
                  ],
                  rowKey: "id",
                  actions: {
                    enabled: true,
                    items: [
                      {
                        key: "view",
                        label: "Xem chi tiết",
                        icon: Eye,
                        onClick: (log: EmailLog) => setSelectedLog(log),
                      },
                    ],
                  },
                  selection: {
                    enabled: true,
                    type: "checkbox" as const,
                  },
                }}
                loading={logsLoading}
                pagination={
                  logsPagination
                    ? {
                        current: logsPagination.page,
                        pageSize: logsPagination.limit,
                        total: logsPagination.total,
                      }
                    : undefined
                }
                onPageChange={(page) => setLogsParams({ page })}
                onPageSizeChange={(limit) => setLogsParams({ limit, page: 1 })}
              />
            </CardContent>
          </Card>
        </TabsContent>

        {/* SMTP Configs Tab */}
        <TabsContent value="configs" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold">Cấu hình SMTP</h2>
            <Dialog
              open={isCreateConfigDialogOpen}
              onOpenChange={setIsCreateConfigDialogOpen}
            >
              <DialogTrigger asChild>
                <Button onClick={resetConfigForm}>
                  <Plus className="h-4 w-4 mr-2" />
                  Thêm cấu hình
                </Button>
              </DialogTrigger>
            </Dialog>
          </div>

          <Card>
            <CardContent>
              <AdminDataTable
                dataSource={configs || []}
                config={{
                  columns: [
                    {
                      key: "config",
                      title: "Cấu hình",
                      render: (config: SMTPConfig) => (
                        <div className="space-y-1">
                          <div className="font-medium">{config.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {config.host}:{config.port}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {config.fromName} &lt;{config.fromEmail}&gt;
                          </div>
                        </div>
                      ),
                    },
                    {
                      key: "security",
                      title: "Bảo mật",
                      render: (config: SMTPConfig) => (
                        <Badge
                          variant="secondary"
                          className={
                            config.secure
                              ? "bg-green-100 text-green-800"
                              : "bg-yellow-100 text-yellow-800"
                          }
                        >
                          {config.secure ? "SSL/TLS" : "Không mã hóa"}
                        </Badge>
                      ),
                    },
                    {
                      key: "status",
                      title: "Trạng thái",
                      render: (config: SMTPConfig) => (
                        <div className="flex items-center gap-2">
                          <Badge
                            variant="secondary"
                            className={
                              config.isActive
                                ? "bg-green-100 text-green-800"
                                : "bg-red-100 text-red-800"
                            }
                          >
                            {config.isActive ? "Hoạt động" : "Tạm dừng"}
                          </Badge>
                          {config.isDefault && (
                            <Badge variant="outline">Mặc định</Badge>
                          )}
                        </div>
                      ),
                    },
                  ],
                  rowKey: "id",
                  actions: {
                    enabled: true,
                    items: [
                      {
                        key: "view",
                        label: "Xem chi tiết",
                        icon: Eye,
                        onClick: handleViewConfig,
                      },
                      {
                        key: "edit",
                        label: "Chỉnh sửa",
                        icon: Edit,
                        onClick: handleEditConfig,
                      },
                      {
                        key: "delete",
                        label: "Xóa",
                        icon: Trash2,
                        onClick: handleDeleteConfig,
                        type: "danger" as const,
                      },
                    ],
                  },
                  selection: {
                    enabled: true,
                    type: "checkbox" as const,
                  },
                }}
                loading={configsLoading}
                pagination={
                  configsPagination
                    ? {
                        current: configsPagination.page,
                        pageSize: configsPagination.limit,
                        total: configsPagination.total,
                      }
                    : undefined
                }
                onPageChange={(page) => setConfigsParams({ page })}
                onPageSizeChange={(limit) =>
                  setConfigsParams({ limit, page: 1 })
                }
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
