"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import {
  Plus,
  Calendar,
  Clock,
  MapPin,
  Users,
  DollarSign,
  Eye,
  Edit,
  Trash2,
  Filter,
  Search,
  CalendarDays,
  Zap,
  TrendingUp,
  CheckCircle,
} from "lucide-react";
import { toast } from "sonner";
import { AdminDataTable } from "@/lib/admin/components/AdminDataTable";
import { useAdminData, useAdminCrud } from "@/lib/admin/hooks";
import { MediaSelector } from "@/components/admin/MediaManager";
import { AdminImage } from "@/components/admin/AdminImage";

interface Event {
  id: string;
  title: string;
  description?: string;
  content?: string;
  slug: string;
  type:
    | "PROMOTION"
    | "LAUNCH"
    | "WORKSHOP"
    | "WEBINAR"
    | "SALE"
    | "SEASONAL"
    | "COMMUNITY"
    | "OTHER";
  status:
    | "DRAFT"
    | "PUBLISHED"
    | "SCHEDULED"
    | "ONGOING"
    | "COMPLETED"
    | "CANCELLED";
  startDate: string;
  endDate?: string;
  isAllDay: boolean;
  location?: string;
  maxAttendees?: number;
  currentAttendees: number;
  price?: number;
  currency: string;
  image?: string;
  tags: string[];
  seoTitle?: string;
  seoDescription?: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  creator: {
    id: string;
    name: string;
    email: string;
  };
}

const eventTypes = [
  {
    value: "PROMOTION",
    label: "Khuyến mãi",
    color: "bg-pink-100 text-pink-800",
  },
  {
    value: "LAUNCH",
    label: "Ra mắt sản phẩm",
    color: "bg-blue-100 text-blue-800",
  },
  {
    value: "WORKSHOP",
    label: "Workshop",
    color: "bg-green-100 text-green-800",
  },
  {
    value: "WEBINAR",
    label: "Webinar",
    color: "bg-purple-100 text-purple-800",
  },
  { value: "SALE", label: "Flash Sale", color: "bg-red-100 text-red-800" },
  {
    value: "SEASONAL",
    label: "Theo mùa",
    color: "bg-orange-100 text-orange-800",
  },
  {
    value: "COMMUNITY",
    label: "Cộng đồng",
    color: "bg-indigo-100 text-indigo-800",
  },
  { value: "OTHER", label: "Khác", color: "bg-gray-100 text-gray-800" },
];

const eventStatuses = [
  { value: "DRAFT", label: "Bản nháp", color: "bg-gray-100 text-gray-800" },
  {
    value: "PUBLISHED",
    label: "Đã xuất bản",
    color: "bg-green-100 text-green-800",
  },
  {
    value: "SCHEDULED",
    label: "Đã lên lịch",
    color: "bg-blue-100 text-blue-800",
  },
  {
    value: "ONGOING",
    label: "Đang diễn ra",
    color: "bg-yellow-100 text-yellow-800",
  },
  {
    value: "COMPLETED",
    label: "Đã kết thúc",
    color: "bg-purple-100 text-purple-800",
  },
  { value: "CANCELLED", label: "Đã hủy", color: "bg-red-100 text-red-800" },
];

export default function AdminEventsPage() {
  const [selectedEvent, setSelectedEvent] = useState<Event | null>(null);
  const [isCreateEventOpen, setIsCreateEventOpen] = useState(false);
  const [editingEvent, setEditingEvent] = useState<Event | null>(null);
  const [filterType, setFilterType] = useState<string>("");
  const [filterStatus, setFilterStatus] = useState<string>("");

  // Data fetching for events
  const {
    data: events,
    loading: eventsLoading,
    error: eventsError,
    refresh: refreshEvents,
    setParams: setEventParams,
    params: eventParams,
  } = useAdminData<Event>({
    endpoint: "/api/admin/events",
    initialParams: { page: 1, limit: 20 },
  });

  // CRUD operations
  const {
    create: createEvent,
    update: updateEvent,
    remove: deleteEvent,
  } = useAdminCrud("/api/admin/events");

  // Event table configuration
  const eventTableConfig = {
    columns: [
      {
        key: "title",
        title: "Sự kiện",
        sortable: true,
        render: (event: Event) => (
          <div className="flex items-center space-x-3">
            {event?.image && (
              <AdminImage
                src={event.image}
                alt={event?.title || "Event"}
                width={48}
                height={48}
                className="rounded-lg"
              />
            )}
            <div>
              <div className="font-medium">{event?.title || "N/A"}</div>
              <div className="text-sm text-muted-foreground line-clamp-1">
                {event?.description || ""}
              </div>
            </div>
          </div>
        ),
      },
      {
        key: "type",
        title: "Loại",
        render: (event: Event) => {
          const type = eventTypes.find((t) => t.value === event?.type);
          return (
            <Badge className={type?.color}>
              {type?.label || event?.type || "N/A"}
            </Badge>
          );
        },
      },
      {
        key: "status",
        title: "Trạng thái",
        render: (event: Event) => {
          const status = eventStatuses.find((s) => s.value === event?.status);
          return (
            <Badge className={status?.color}>
              {status?.label || event?.status || "N/A"}
            </Badge>
          );
        },
      },
      {
        key: "startDate",
        title: "Thời gian",
        sortable: true,
        render: (event: Event) => (
          <div className="text-sm">
            <div className="flex items-center space-x-1">
              <Calendar className="h-3 w-3" />
              <span>
                {event?.startDate
                  ? new Date(event.startDate).toLocaleDateString("vi-VN")
                  : "N/A"}
              </span>
            </div>
            {!event?.isAllDay && event?.startDate && (
              <div className="flex items-center space-x-1 text-muted-foreground">
                <Clock className="h-3 w-3" />
                <span>
                  {new Date(event.startDate).toLocaleTimeString("vi-VN", {
                    hour: "2-digit",
                    minute: "2-digit",
                  })}
                </span>
              </div>
            )}
          </div>
        ),
      },
      {
        key: "attendees",
        title: "Tham gia",
        render: (event: Event) => (
          <div className="text-sm">
            <div className="flex items-center space-x-1">
              <Users className="h-3 w-3" />
              <span>
                {event?.currentAttendees || 0}
                {event?.maxAttendees && `/${event.maxAttendees}`}
              </span>
            </div>
          </div>
        ),
      },
      {
        key: "price",
        title: "Giá",
        render: (event: Event) => (
          <div className="text-sm">
            {event?.price ? (
              <div className="flex items-center space-x-1">
                <DollarSign className="h-3 w-3" />
                <span>
                  {event.price.toLocaleString()} {event?.currency || "VND"}
                </span>
              </div>
            ) : (
              <Badge variant="outline">Miễn phí</Badge>
            )}
          </div>
        ),
      },
      {
        key: "creator",
        title: "Người tạo",
        render: (event: Event) => (
          <div className="text-sm">
            <div className="font-medium">{event?.creator?.name || "N/A"}</div>
            <div className="text-muted-foreground">
              {event?.creator?.email || ""}
            </div>
          </div>
        ),
      },
    ],
    rowKey: "id",
    actions: {
      enabled: true,
      items: [
        {
          key: "view",
          label: "Xem chi tiết",
          icon: Eye,
          onClick: (event: Event) => setSelectedEvent(event),
        },
        {
          key: "edit",
          label: "Chỉnh sửa",
          icon: Edit,
          onClick: (event: Event) => setEditingEvent(event),
        },
        {
          key: "delete",
          label: "Xóa",
          icon: Trash2,
          onClick: (event: Event) => handleDeleteEvent(event),
          variant: "destructive" as const,
        },
      ],
    },
    selection: {
      enabled: true,
      type: "checkbox" as const,
    },
  };

  const handleDeleteEvent = async (event: Event) => {
    if (!confirm(`Bạn có chắc chắn muốn xóa sự kiện "${event.title}"?`)) {
      return;
    }

    const result = await deleteEvent(event.id);
    if (result) {
      toast.success("Xóa sự kiện thành công");
      refreshEvents();
    }
  };

  // Calculate stats
  const stats = {
    total: events?.length || 0,
    published: events?.filter((e) => e.status === "PUBLISHED").length || 0,
    ongoing: events?.filter((e) => e.status === "ONGOING").length || 0,
    scheduled: events?.filter((e) => e.status === "SCHEDULED").length || 0,
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Quản lý Sự kiện</h1>
          <p className="text-muted-foreground">
            Quản lý các sự kiện, khuyến mãi và hoạt động của cửa hàng
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={() => setIsCreateEventOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Tạo Sự kiện Mới
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <CalendarDays className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-muted-foreground">
                  Tổng Sự kiện
                </p>
                <p className="text-2xl font-bold">{stats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <CheckCircle className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-muted-foreground">
                  Đã xuất bản
                </p>
                <p className="text-2xl font-bold">{stats.published}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Zap className="h-8 w-8 text-yellow-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-muted-foreground">
                  Đang diễn ra
                </p>
                <p className="text-2xl font-bold">{stats.ongoing}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <TrendingUp className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-muted-foreground">
                  Đã lên lịch
                </p>
                <p className="text-2xl font-bold">{stats.scheduled}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-[200px]">
              <Input
                placeholder="Tìm kiếm sự kiện..."
                value={eventParams.search || ""}
                onChange={(e) => setEventParams({ search: e.target.value })}
                className="w-full"
              />
            </div>
            <Select
              value={filterType}
              onValueChange={(value) => {
                setFilterType(value);
                setEventParams({ type: value === "all" ? undefined : value });
              }}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Loại sự kiện" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả loại</SelectItem>
                {eventTypes.map((type) => (
                  <SelectItem key={type.value} value={type.value}>
                    {type.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select
              value={filterStatus}
              onValueChange={(value) => {
                setFilterStatus(value);
                setEventParams({ status: value === "all" ? undefined : value });
              }}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Trạng thái" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả trạng thái</SelectItem>
                {eventStatuses.map((status) => (
                  <SelectItem key={status.value} value={status.value}>
                    {status.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Events Table */}
      <Card>
        <CardHeader>
          <CardTitle>Danh sách Sự kiện</CardTitle>
        </CardHeader>
        <CardContent>
          <AdminDataTable
            config={eventTableConfig}
            dataSource={events || []}
            loading={eventsLoading}
            onRefresh={refreshEvents}
            onSearch={(search) => setEventParams({ search })}
            searchValue={eventParams.search || ""}
          />
        </CardContent>
      </Card>

      {/* Create Event Dialog */}
      <Dialog open={isCreateEventOpen} onOpenChange={setIsCreateEventOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Tạo Sự kiện Mới</DialogTitle>
          </DialogHeader>
          <CreateEventForm
            onSuccess={() => {
              setIsCreateEventOpen(false);
              refreshEvents();
            }}
            onCancel={() => setIsCreateEventOpen(false)}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
}

// Create Event Form Component
function CreateEventForm({
  onSuccess,
  onCancel,
}: {
  onSuccess: () => void;
  onCancel: () => void;
}) {
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    type: "",
    status: "DRAFT",
    startDate: "",
    endDate: "",
    location: "",
    maxAttendees: "",
    price: "",
    currency: "VND",
    isAllDay: false,
    isActive: true,
  });

  const { create: createEvent } = useAdminCrud("/api/admin/events");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const eventData = {
      ...formData,
      maxAttendees: formData.maxAttendees
        ? parseInt(formData.maxAttendees)
        : null,
      price: formData.price ? parseFloat(formData.price) : null,
      startDate: formData.startDate
        ? new Date(formData.startDate).toISOString()
        : null,
      endDate: formData.endDate
        ? new Date(formData.endDate).toISOString()
        : null,
    };

    const result = await createEvent(eventData);
    if (result) {
      onSuccess();
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div className="col-span-2">
          <Label htmlFor="title">Tên sự kiện *</Label>
          <Input
            id="title"
            value={formData.title}
            onChange={(e) =>
              setFormData({ ...formData, title: e.target.value })
            }
            required
          />
        </div>

        <div>
          <Label htmlFor="type">Loại sự kiện *</Label>
          <Select
            value={formData.type}
            onValueChange={(value) => setFormData({ ...formData, type: value })}
          >
            <SelectTrigger>
              <SelectValue placeholder="Chọn loại sự kiện..." />
            </SelectTrigger>
            <SelectContent>
              {eventTypes.map((type) => (
                <SelectItem key={type.value} value={type.value}>
                  {type.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor="status">Trạng thái</Label>
          <Select
            value={formData.status}
            onValueChange={(value) =>
              setFormData({ ...formData, status: value })
            }
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {eventStatuses.map((status) => (
                <SelectItem key={status.value} value={status.value}>
                  {status.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor="startDate">Ngày bắt đầu *</Label>
          <Input
            id="startDate"
            type="datetime-local"
            value={formData.startDate}
            onChange={(e) =>
              setFormData({ ...formData, startDate: e.target.value })
            }
            required
          />
        </div>

        <div>
          <Label htmlFor="endDate">Ngày kết thúc</Label>
          <Input
            id="endDate"
            type="datetime-local"
            value={formData.endDate}
            onChange={(e) =>
              setFormData({ ...formData, endDate: e.target.value })
            }
          />
        </div>

        <div>
          <Label htmlFor="location">Địa điểm</Label>
          <Input
            id="location"
            value={formData.location}
            onChange={(e) =>
              setFormData({ ...formData, location: e.target.value })
            }
          />
        </div>

        <div>
          <Label htmlFor="maxAttendees">Số người tham gia tối đa</Label>
          <Input
            id="maxAttendees"
            type="number"
            value={formData.maxAttendees}
            onChange={(e) =>
              setFormData({ ...formData, maxAttendees: e.target.value })
            }
          />
        </div>

        <div>
          <Label htmlFor="price">Giá vé</Label>
          <Input
            id="price"
            type="number"
            step="0.01"
            value={formData.price}
            onChange={(e) =>
              setFormData({ ...formData, price: e.target.value })
            }
          />
        </div>

        <div>
          <Label htmlFor="currency">Đơn vị tiền tệ</Label>
          <Select
            value={formData.currency}
            onValueChange={(value) =>
              setFormData({ ...formData, currency: value })
            }
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="VND">VND</SelectItem>
              <SelectItem value="USD">USD</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="col-span-2">
          <Label htmlFor="description">Mô tả</Label>
          <Textarea
            id="description"
            value={formData.description}
            onChange={(e) =>
              setFormData({ ...formData, description: e.target.value })
            }
            rows={3}
          />
        </div>

        <div className="col-span-2 flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Switch
              id="isAllDay"
              checked={formData.isAllDay}
              onCheckedChange={(checked) =>
                setFormData({ ...formData, isAllDay: checked })
              }
            />
            <Label htmlFor="isAllDay">Sự kiện cả ngày</Label>
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="isActive"
              checked={formData.isActive}
              onCheckedChange={(checked) =>
                setFormData({ ...formData, isActive: checked })
              }
            />
            <Label htmlFor="isActive">Kích hoạt sự kiện</Label>
          </div>
        </div>
      </div>

      <div className="flex justify-end space-x-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Hủy
        </Button>
        <Button type="submit">Tạo Sự kiện</Button>
      </div>
    </form>
  );
}
