'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { 
  ArrowLeft, 
  Plus, 
  Trash2, 
  Settings,
  Save,
  X
} from 'lucide-react';
import Link from 'next/link';
import { toast } from 'sonner';
import { AttributeType, ATTRIBUTE_TYPE_LABELS, ATTRIBUTE_TYPE_DESCRIPTIONS } from '@/types/attribute';

interface AttributeValueForm {
  id?: string;
  value: string;
  slug?: string;
  sortOrder: number;
}

export default function CreateAttributePage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    slug: '',
    description: '',
    type: 'TEXT' as AttributeType,
    isRequired: false,
    isFilterable: true,
    sortOrder: 0,
  });
  const [values, setValues] = useState<AttributeValueForm[]>([]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const response = await fetch('/api/admin/attributes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          values: values.length > 0 ? values : undefined,
        }),
      });

      if (response.ok) {
        toast.success('Tạo thuộc tính thành công');
        router.push('/admin/attributes');
      } else {
        const error = await response.json();
        toast.error(error.error || 'Có lỗi xảy ra khi tạo thuộc tính');
      }
    } catch (error) {
      console.error('Error creating attribute:', error);
      toast.error('Có lỗi xảy ra khi tạo thuộc tính');
    } finally {
      setLoading(false);
    }
  };

  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[àáạảãâầấậẩẫăằắặẳẵ]/g, 'a')
      .replace(/[èéẹẻẽêềếệểễ]/g, 'e')
      .replace(/[ìíịỉĩ]/g, 'i')
      .replace(/[òóọỏõôồốộổỗơờớợởỡ]/g, 'o')
      .replace(/[ùúụủũưừứựửữ]/g, 'u')
      .replace(/[ỳýỵỷỹ]/g, 'y')
      .replace(/[đ]/g, 'd')
      .replace(/[^a-z0-9-]/g, '');
  };

  const handleNameChange = (name: string) => {
    setFormData(prev => ({
      ...prev,
      name,
      slug: prev.slug || generateSlug(name),
    }));
  };

  const addValue = () => {
    setValues(prev => [
      ...prev,
      {
        value: '',
        sortOrder: prev.length,
      },
    ]);
  };

  const updateValue = (index: number, field: keyof AttributeValueForm, value: string | number) => {
    setValues(prev => prev.map((item, i) => 
      i === index ? { ...item, [field]: value } : item
    ));
  };

  const removeValue = (index: number) => {
    setValues(prev => prev.filter((_, i) => i !== index));
  };

  const needsValues = ['SELECT', 'MULTI_SELECT', 'COLOR', 'SIZE'].includes(formData.type);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/admin/attributes">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Quay lại
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold">Thêm thuộc tính mới</h1>
            <p className="text-muted-foreground">
              Tạo thuộc tính mới cho sản phẩm
            </p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="h-5 w-5 mr-2" />
              Thông tin cơ bản
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Tên thuộc tính *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleNameChange(e.target.value)}
                  placeholder="Ví dụ: Màu sắc, Kích thước..."
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="slug">Slug</Label>
                <Input
                  id="slug"
                  value={formData.slug}
                  onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
                  placeholder="mau-sac, kich-thuoc..."
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Mô tả</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Mô tả về thuộc tính này..."
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="type">Loại thuộc tính *</Label>
              <Select
                value={formData.type}
                onValueChange={(value: AttributeType) => setFormData(prev => ({ ...prev, type: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Chọn loại thuộc tính" />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(ATTRIBUTE_TYPE_LABELS).map(([value, label]) => (
                    <SelectItem key={value} value={value}>
                      <div>
                        <div className="font-medium">{label}</div>
                        <div className="text-xs text-muted-foreground">
                          {ATTRIBUTE_TYPE_DESCRIPTIONS[value as AttributeType]}
                        </div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="isRequired"
                  checked={formData.isRequired}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isRequired: checked }))}
                />
                <Label htmlFor="isRequired">Bắt buộc</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="isFilterable"
                  checked={formData.isFilterable}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isFilterable: checked }))}
                />
                <Label htmlFor="isFilterable">Có thể lọc</Label>
              </div>
              <div className="space-y-2">
                <Label htmlFor="sortOrder">Thứ tự sắp xếp</Label>
                <Input
                  id="sortOrder"
                  type="number"
                  value={formData.sortOrder}
                  onChange={(e) => setFormData(prev => ({ ...prev, sortOrder: parseInt(e.target.value) || 0 }))}
                  min="0"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Attribute Values */}
        {needsValues && (
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center">
                  <Settings className="h-5 w-5 mr-2" />
                  Giá trị thuộc tính
                </CardTitle>
                <Button type="button" onClick={addValue} size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  Thêm giá trị
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {values.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-muted-foreground mb-4">
                    Chưa có giá trị nào. Thêm giá trị đầu tiên.
                  </p>
                  <Button type="button" onClick={addValue} variant="outline">
                    <Plus className="h-4 w-4 mr-2" />
                    Thêm giá trị
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {values.map((value, index) => (
                    <div key={index} className="flex items-center space-x-4 p-4 border rounded-lg">
                      <div className="flex-1">
                        <Input
                          value={value.value}
                          onChange={(e) => updateValue(index, 'value', e.target.value)}
                          placeholder="Nhập giá trị..."
                          required
                        />
                      </div>
                      <div className="w-24">
                        <Input
                          type="number"
                          value={value.sortOrder}
                          onChange={(e) => updateValue(index, 'sortOrder', parseInt(e.target.value) || 0)}
                          placeholder="Thứ tự"
                          min="0"
                        />
                      </div>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeValue(index)}
                        className="text-destructive hover:text-destructive"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Thao tác</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-4">
              <Button type="submit" disabled={loading}>
                {loading ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                ) : (
                  <Save className="h-4 w-4 mr-2" />
                )}
                Tạo thuộc tính
              </Button>
              <Link href="/admin/attributes">
                <Button type="button" variant="outline">
                  <X className="h-4 w-4 mr-2" />
                  Hủy
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </form>
    </div>
  );
}
