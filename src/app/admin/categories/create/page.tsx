'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
	Save, 
	ArrowLeft, 
	Tag
} from 'lucide-react';
import { toast } from 'sonner';

interface Category {
	id: string;
	name: string;
	slug: string;
}

interface CategoryForm {
	name: string;
	description: string;
	parentId: string | null;
}

export default function CreateCategoryPage() {
	const router = useRouter();
	const [categories, setCategories] = useState<Category[]>([]);
	const [loading, setLoading] = useState(false);
	const [form, setForm] = useState<CategoryForm>({
		name: '',
		description: '',
		parentId: null,
	});

	useEffect(() => {
		fetchCategories();
	}, []);

	const fetchCategories = async () => {
		try {
			const response = await fetch('/api/categories');
			const data = await response.json();
			if (response.ok) {
				// Only show parent categories (no parentId)
				setCategories(data.filter((cat: Category & { parentId?: string }) => !cat.parentId));
			}
		} catch (error) {
			toast.error('Có lỗi xảy ra khi tải danh mục');
		}
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		
		if (!form.name) {
			toast.error('Vui lòng nhập tên danh mục');
			return;
		}

		setLoading(true);
		try {
			const slug = form.name
				.toLowerCase()
				.normalize('NFD')
				.replace(/[\u0300-\u036f]/g, '')
				.replace(/[^a-z0-9\s-]/g, '')
				.replace(/\s+/g, '-')
				.replace(/-+/g, '-')
				.trim();

			const response = await fetch('/api/admin/categories', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					...form,
					slug,
					parentId: form.parentId || null,
				}),
			});

			if (response.ok) {
				toast.success('Tạo danh mục thành công');
				router.push('/admin/categories');
			} else {
				const data = await response.json();
				toast.error(data.error || 'Có lỗi xảy ra khi tạo danh mục');
			}
		} catch (error) {
			toast.error('Có lỗi xảy ra khi tạo danh mục');
		} finally {
			setLoading(false);
		}
	};

	const handleInputChange = (field: keyof CategoryForm, value: any) => {
		setForm({ ...form, [field]: value });
	};

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex items-center gap-4">
				<Button
					variant="ghost"
					onClick={() => router.back()}
				>
					<ArrowLeft className="h-4 w-4 mr-2" />
					Quay lại
				</Button>
				<div>
					<h1 className="text-3xl font-bold">Thêm danh mục mới</h1>
					<p className="text-muted-foreground">
						Tạo danh mục sản phẩm mới
					</p>
				</div>
			</div>

			<div className="max-w-2xl">
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<Tag className="h-5 w-5 text-pink-600" />
							Thông tin danh mục
						</CardTitle>
					</CardHeader>
					<CardContent>
						<form onSubmit={handleSubmit} className="space-y-6">
							<div>
								<label className="block text-sm font-medium mb-2">
									Tên danh mục *
								</label>
								<input
									type="text"
									value={form.name}
									onChange={(e) => handleInputChange('name', e.target.value)}
									className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
									placeholder="Nhập tên danh mục"
									required
								/>
								<p className="text-xs text-muted-foreground mt-1">
									Slug sẽ được tự động tạo từ tên danh mục
								</p>
							</div>

							<div>
								<label className="block text-sm font-medium mb-2">
									Mô tả
								</label>
								<textarea
									value={form.description}
									onChange={(e) => handleInputChange('description', e.target.value)}
									rows={4}
									className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
									placeholder="Nhập mô tả danh mục (tùy chọn)"
								/>
							</div>

							<div>
								<label className="block text-sm font-medium mb-2">
									Danh mục cha
								</label>
								<select
									value={form.parentId || ''}
									onChange={(e) => handleInputChange('parentId', e.target.value || null)}
									className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
								>
									<option value="">Không có (Danh mục gốc)</option>
									{categories.map((category) => (
										<option key={category.id} value={category.id}>
											{category.name}
										</option>
									))}
								</select>
								<p className="text-xs text-muted-foreground mt-1">
									Chọn danh mục cha nếu đây là danh mục con
								</p>
							</div>

							<div className="flex gap-4 pt-4">
								<Button
									type="submit"
									disabled={loading}
									className="bg-pink-600 hover:bg-pink-700"
								>
									<Save className="h-4 w-4 mr-2" />
									{loading ? 'Đang tạo...' : 'Tạo danh mục'}
								</Button>
								<Button
									type="button"
									variant="outline"
									onClick={() => router.back()}
								>
									Hủy
								</Button>
							</div>
						</form>
					</CardContent>
				</Card>

				{/* Preview */}
				{form.name && (
					<Card>
						<CardHeader>
							<CardTitle>Xem trước</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="space-y-2">
								<div>
									<span className="text-sm text-muted-foreground">Tên:</span>
									<span className="ml-2 font-medium">{form.name}</span>
								</div>
								<div>
									<span className="text-sm text-muted-foreground">Slug:</span>
									<span className="ml-2 font-mono text-sm bg-gray-100 px-2 py-1 rounded">
										{form.name
											.toLowerCase()
											.normalize('NFD')
											.replace(/[\u0300-\u036f]/g, '')
											.replace(/[^a-z0-9\s-]/g, '')
											.replace(/\s+/g, '-')
											.replace(/-+/g, '-')
											.trim()}
									</span>
								</div>
								{form.description && (
									<div>
										<span className="text-sm text-muted-foreground">Mô tả:</span>
										<p className="ml-2 text-sm">{form.description}</p>
									</div>
								)}
								{form.parentId && (
									<div>
										<span className="text-sm text-muted-foreground">Danh mục cha:</span>
										<span className="ml-2 text-sm">
											{categories.find(cat => cat.id === form.parentId)?.name}
										</span>
									</div>
								)}
							</div>
						</CardContent>
					</Card>
				)}
			</div>
		</div>
	);
}
