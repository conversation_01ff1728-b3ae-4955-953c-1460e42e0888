"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { PostForm } from "@/components/admin/PostForm";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowLeft, FileText } from "lucide-react";
import { toast } from "sonner";
import Link from "next/link";
import { useAdminAuth, useAdmin } from "@/contexts";
import { usePosts, type CreatePostData } from "@/lib/admin/hooks";

interface PostFormData {
  title: string;
  content: string;
  excerpt?: string;
  slug?: string;
  status: "DRAFT" | "PUBLISHED" | "ARCHIVED";
  featured: boolean;
  featuredImage?: string;
  tags: string[];
  categoryId?: string | null;
}

export default function CreatePostPage() {
  const { adminUser } = useAdminAuth();
  const { setState } = useAdmin();
  const router = useRouter();
  const { createPost, loading } = usePosts();

  const handleSubmit = async (data: PostFormData) => {
    // Map form data to hook's expected format
    const postData: CreatePostData = {
      title: data.title,
      content: data.content,
      excerpt: data.excerpt,
      slug: data.slug || data.title.toLowerCase().replace(/[^a-z0-9]+/g, '-'),
      status: data.status === "DRAFT" ? "draft" : 
              data.status === "PUBLISHED" ? "published" : "archived",
      featuredImage: data.featuredImage,
      tags: data.tags,
      categories: data.categoryId ? [data.categoryId] : undefined,
    };

    const result = await createPost(postData);

    if (result) {
      // Redirect to posts list regardless of status
      router.push("/admin/posts");
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Link href="/admin/posts">
          <Button variant="ghost" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Quay lại
          </Button>
        </Link>

        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-pink-100 rounded-lg flex items-center justify-center">
            <FileText className="h-5 w-5 text-pink-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold">Viết bài mới</h1>
            <p className="text-muted-foreground">
              Tạo bài viết mới cho blog và nội dung marketing
            </p>
          </div>
        </div>
      </div>

      {/* Form */}
      <PostForm onSubmit={handleSubmit} isLoading={loading} />
    </div>
  );
}
