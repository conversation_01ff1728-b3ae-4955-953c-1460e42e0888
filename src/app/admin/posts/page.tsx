"use client";

import { useState } from "react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Plus,
  Edit,
  Trash2,
  FileText,
  Eye,
  Calendar,
  User,
  Tag as Tag<PERSON><PERSON>,
  Star,
} from "lucide-react";
import { toast } from "sonner";
import { AdminDataTable, AdminErrorState } from "@/lib/admin/components";
import { useAdminPosts } from "@/hooks/admin";
import { TableConfig, TableColumn, BulkAction } from "@/lib/admin/types";
import { PostsStats } from "@/components/admin/PostsStats";

interface Post {
  id: string;
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  status: "DRAFT" | "PUBLISHED" | "ARCHIVED";
  featured: boolean;
  featuredImage?: string;
  tags: string[];
  createdAt: string;
  updatedAt: string;
  author: {
    id: string;
    name: string;
    email: string;
  };
  category?: {
    id: string;
    name: string;
    slug: string;
  };
}

export default function AdminPostsPage() {
  const [selectedItems, setSelectedItems] = useState<Post[]>([]);

  // Using admin posts hook
  const {
    posts,
    loading,
    totalCount,
    fetchPosts,
    updatePost,
    toggleFeatured,
    bulkUpdatePosts,
  } = useAdminPosts();

  // Handle delete post
  const handleDeletePost = async (post: Post) => {
    if (!confirm(`Bạn có chắc chắn muốn xóa bài viết "${post.title}"?`)) {
      return;
    }

    try {
      // Note: You'll need to add a deletePost method to useAdminPosts hook
      // For now, keeping the direct API call but using proper URLSearchParams
      const response = await fetch(`/api/admin/posts/${post.id}`, {
        method: "DELETE",
      });
      
      if (response.ok) {
        toast.success("Xóa bài viết thành công");
        fetchPosts();
      } else {
        toast.error("Có lỗi xảy ra khi xóa bài viết");
      }
    } catch (error) {
      toast.error("Có lỗi xảy ra khi xóa bài viết");
    }
  };

  // Handle toggle featured
  const handleToggleFeatured = async (post: Post) => {
    try {
      const result = await toggleFeatured(post);
      if (result.success) {
        fetchPosts(); // Refresh the list
      }
    } catch (error) {
      // Error is already handled in the hook with toast
    }
  };

  // Format date helper
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("vi-VN", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Table columns configuration
  const columns: TableColumn<Post>[] = [
    {
      key: "title",
      title: "Tiêu đề",
      dataIndex: "title",
      sortable: true,
      searchable: true,
      render: (record: Post) => (
        <div className="space-y-1">
          <div className="font-medium">{record.title}</div>
          <div className="text-sm text-muted-foreground line-clamp-2">
            {record.excerpt}
          </div>
          <div className="flex items-center gap-2">
            {record.featured && (
              <Badge
                variant="secondary"
                className="bg-yellow-100 text-yellow-800"
              >
                <Star className="h-3 w-3 mr-1" />
                Nổi bật
              </Badge>
            )}
            {record.tags.length > 0 && (
              <div className="flex items-center gap-1">
                <TagIcon className="h-3 w-3 text-muted-foreground" />
                <span className="text-xs text-muted-foreground">
                  {record.tags.slice(0, 2).join(", ")}
                  {record.tags.length > 2 && "..."}
                </span>
              </div>
            )}
          </div>
        </div>
      ),
    },
    {
      key: "status",
      title: "Trạng thái",
      dataIndex: "status",
      sortable: true,
      render: (record: Post) => {
        const getStatusConfig = (status: string) => {
          switch (status) {
            case "PUBLISHED":
              return {
                text: "Đã xuất bản",
                className: "bg-green-100 text-green-800",
              };
            case "DRAFT":
              return {
                text: "Bản nháp",
                className: "bg-yellow-100 text-yellow-800",
              };
            case "ARCHIVED":
              return {
                text: "Đã lưu trữ",
                className: "bg-gray-100 text-gray-800",
              };
            default:
              return { text: status, className: "bg-gray-100 text-gray-800" };
          }
        };
        const config = getStatusConfig(record.status);
        return (
          <Badge variant="secondary" className={config.className}>
            {config.text}
          </Badge>
        );
      },
    },
    {
      key: "author",
      title: "Tác giả",
      dataIndex: "author",
      render: (record: Post) => (
        <div className="flex items-center gap-2">
          <User className="h-4 w-4 text-muted-foreground" />
          <span>{record.author.name}</span>
        </div>
      ),
    },
    {
      key: "createdAt",
      title: "Ngày tạo",
      dataIndex: "createdAt",
      sortable: true,
      render: (record: Post) => (
        <div className="flex items-center gap-2">
          <Calendar className="h-4 w-4 text-muted-foreground" />
          <span>{formatDate(record.createdAt)}</span>
        </div>
      ),
    },
  ];

  // Bulk actions
  const bulkActions: BulkAction<Post>[] = [
    {
      key: "publish",
      label: "Xuất bản",
      icon: Eye,
      onClick: async (items: Post[]) => {
        const ids = items.map((item) => item.id);
        try {
          const result = await bulkUpdatePosts('publish', ids);
          if (result.success) {
            fetchPosts();
          }
        } catch (error) {
          // Error is already handled in the hook
        }
      },
    },
    {
      key: "draft",
      label: "Chuyển về bản nháp",
      icon: FileText,
      onClick: async (items: Post[]) => {
        const ids = items.map((item) => item.id);
        try {
          const result = await bulkUpdatePosts('unpublish', ids);
          if (result.success) {
            fetchPosts();
          }
        } catch (error) {
          // Error is already handled in the hook
        }
      },
    },
    {
      key: "delete",
      label: "Xóa",
      icon: Trash2,
      type: "danger",
      onClick: async (items: Post[]) => {
        if (!confirm(`Bạn có chắc chắn muốn xóa ${items.length} bài viết?`)) {
          return;
        }
        try {
          // Note: You'll need to add a bulkDelete method to useAdminPosts hook
          // For now, using direct API calls
          const ids = items.map((item) => item.id);
          const response = await Promise.all(
            ids.map(id => fetch(`/api/admin/posts/${id}`, { method: "DELETE" }))
          );
          
          if (response.every(r => r.ok)) {
            toast.success(`Đã xóa ${items.length} bài viết`);
            fetchPosts();
          } else {
            toast.error("Có lỗi xảy ra khi xóa bài viết");
          }
        } catch (error) {
          toast.error("Có lỗi xảy ra khi xóa bài viết");
        }
      },
    },
  ];

  // Table configuration
  const tableConfig: TableConfig<Post> = {
    columns,
    rowKey: "id",
    selection: {
      enabled: true,
      type: "checkbox",
    },
    actions: {
      enabled: true,
      items: [
        {
          key: "edit",
          label: "Chỉnh sửa",
          icon: Edit,
          onClick: (record: Post) => {
            window.location.href = `/admin/posts/${record.id}/edit`;
          },
        },
        {
          key: "featured",
          label: (record: Post) =>
            record.featured ? "Bỏ nổi bật" : "Đánh dấu nổi bật",
          icon: Star,
          onClick: handleToggleFeatured,
        },
        {
          key: "delete",
          label: "Xóa",
          icon: Trash2,
          type: "danger",
          onClick: handleDeletePost,
        },
      ],
    },
    pagination: {
      enabled: true,
      pageSize: 20,
    },
    search: {
      enabled: true,
      placeholder: "Tìm kiếm bài viết...",
    },
    filters: [
      {
        key: "status",
        label: "Trạng thái",
        type: "select",
        options: [
          { label: "Tất cả", value: "" },
          { label: "Đã xuất bản", value: "PUBLISHED" },
          { label: "Bản nháp", value: "DRAFT" },
          { label: "Đã lưu trữ", value: "ARCHIVED" },
        ],
      },
      {
        key: "featured",
        label: "Nổi bật",
        type: "select",
        options: [
          { label: "Tất cả", value: "" },
          { label: "Nổi bật", value: "true" },
          { label: "Không nổi bật", value: "false" },
        ],
      },
    ],
    emptyText: "Chưa có bài viết nào",
  };

  // Handle error state
  if (error) {
    return (
      <AdminErrorState
        title="Có lỗi xảy ra"
        description="Không thể tải danh sách bài viết"
        onRetry={fetchPosts}
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Quản lý bài viết</h1>
          <p className="text-muted-foreground">
            Quản lý blog và nội dung marketing
          </p>
        </div>
        <Link href="/admin/posts/create">
          <Button className="bg-pink-600 hover:bg-pink-700">
            <Plus className="h-4 w-4 mr-2" />
            Viết bài mới
          </Button>
        </Link>
      </div>

      {/* Stats */}
      <PostsStats />

      {/* Data Table */}
      <AdminDataTable
        config={tableConfig}
        dataSource={posts}
        loading={loading}
        onRefresh={fetchPosts}
        onSearch={(search) => fetchPosts({ search })}
        onPageChange={(page) => fetchPosts({}, page)}
        selectedRows={selectedItems}
        onSelectionChange={setSelectedItems}
        bulkActions={bulkActions}
        pagination={
          pagination
            ? {
                current: pagination.page,
                pageSize: pagination.limit,
                total: pagination.total,
              }
            : undefined
        }
      />
    </div>
  );
}
