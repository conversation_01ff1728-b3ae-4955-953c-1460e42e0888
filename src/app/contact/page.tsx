"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Mail,
  Phone,
  MapPin,
  Clock,
  Send,
  MessageCircle,
  Facebook,
  Instagram,
  Twitter,
  CheckCircle,
} from "lucide-react";
import { useContactForm } from "@/hooks/use-contact-form";
import {
  useContactInfo,
  useContactActions,
  useFormattedAddress,
  useBusinessHours,
} from "@/hooks/use-contact-info";
import { useSettings } from "@/contexts";
import { Header, Footer } from "@/components/layout";

interface ContactForm {
  name: string;
  email: string;
  phone: string;
  company: string;
  service: string;
  subject: string;
  message: string;
}

export default function ContactPage() {
  const { settings } = useSettings();
  const contactData = useContactInfo();
  const contactActions = useContactActions();
  const formattedAddress = useFormattedAddress();
  const businessHours = useBusinessHours();

  const [form, setForm] = useState<ContactForm>({
    name: "",
    email: "",
    phone: "",
    company: "",
    service: "",
    subject: "",
    message: "",
  });

  const { submitForm, isSubmitting, isSuccess } = useContactForm({
    onSuccess: () => {
      setForm({
        name: "",
        email: "",
        phone: "",
        company: "",
        service: "",
        subject: "",
        message: "",
      });
    },
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!form.name || !form.email || !form.message) {
      return;
    }

    // Create subject if not provided
    const subject = form.subject || `Liên hệ từ ${form.name}`;

    await submitForm({
      ...form,
      subject,
    });
  };

  const handleInputChange = (field: keyof ContactForm, value: string) => {
    setForm({ ...form, [field]: value });
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <div className="flex-1 bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-pink-500 to-purple-600 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              Liên hệ với chúng tôi
            </h1>
            <p className="text-xl opacity-90 max-w-2xl mx-auto">
              Chúng tôi luôn sẵn sàng lắng nghe và hỗ trợ bạn. Hãy để lại thông
              tin để được tư vấn tốt nhất!
            </p>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Contact Form */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MessageCircle className="h-5 w-5 text-pink-600" />
                  Gửi tin nhắn cho chúng tôi
                </CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-2">
                        Họ và tên *
                      </label>
                      <input
                        type="text"
                        value={form.name}
                        onChange={(e) =>
                          handleInputChange("name", e.target.value)
                        }
                        className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                        placeholder="Nhập họ và tên"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2">
                        Email *
                      </label>
                      <input
                        type="email"
                        value={form.email}
                        onChange={(e) =>
                          handleInputChange("email", e.target.value)
                        }
                        className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                        placeholder="Nhập địa chỉ email"
                        required
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-2">
                        Số điện thoại
                      </label>
                      <input
                        type="tel"
                        value={form.phone}
                        onChange={(e) =>
                          handleInputChange("phone", e.target.value)
                        }
                        className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                        placeholder="Nhập số điện thoại"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2">
                        Tên công ty/shop
                      </label>
                      <input
                        type="text"
                        value={form.company}
                        onChange={(e) =>
                          handleInputChange("company", e.target.value)
                        }
                        className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                        placeholder="Tên công ty hoặc shop"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-2">
                        Dịch vụ quan tâm
                      </label>
                      <select
                        value={form.service}
                        onChange={(e) =>
                          handleInputChange("service", e.target.value)
                        }
                        className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                      >
                        <option value="">Chọn dịch vụ</option>
                        <option value="May gia công số lượng ít">
                          May gia công số lượng ít
                        </option>
                        <option value="Thiết kế thời trang">
                          Thiết kế thời trang
                        </option>
                        <option value="Tư vấn thương hiệu">
                          Tư vấn thương hiệu
                        </option>
                        <option value="In thêu logo">In thêu logo</option>
                        <option value="Chụp ảnh sản phẩm">
                          Chụp ảnh sản phẩm
                        </option>
                        <option value="Dịch vụ khác">Dịch vụ khác</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2">
                        Chủ đề
                      </label>
                      <input
                        type="text"
                        value={form.subject}
                        onChange={(e) =>
                          handleInputChange("subject", e.target.value)
                        }
                        className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                        placeholder="Chủ đề tin nhắn (tùy chọn)"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">
                      Tin nhắn *
                    </label>
                    <textarea
                      value={form.message}
                      onChange={(e) =>
                        handleInputChange("message", e.target.value)
                      }
                      rows={6}
                      className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                      placeholder="Nhập nội dung tin nhắn..."
                      required
                    />
                  </div>

                  {isSuccess && (
                    <div className="flex items-center gap-2 p-4 bg-green-50 border border-green-200 rounded-lg text-green-700">
                      <CheckCircle className="h-5 w-5" />
                      <span>
                        Gửi tin nhắn thành công! Chúng tôi sẽ phản hồi sớm nhất.
                      </span>
                    </div>
                  )}

                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full bg-pink-600 hover:bg-pink-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isSubmitting ? (
                      <>
                        <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                        Đang gửi...
                      </>
                    ) : (
                      <>
                        <Send className="h-4 w-4 mr-2" />
                        Gửi tin nhắn
                      </>
                    )}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>

          {/* Contact Info */}
          <div className="space-y-6">
            {/* Contact Details */}
            <Card>
              <CardHeader>
                <CardTitle>Thông tin liên hệ</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-start gap-3">
                  <MapPin className="h-5 w-5 text-pink-600 mt-1 flex-shrink-0" />
                  <div>
                    <p className="font-medium">Địa chỉ</p>
                    <p className="text-sm text-muted-foreground">
                      {formattedAddress?.fullAddress || contactData.address}
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <Phone className="h-5 w-5 text-pink-600 mt-1 flex-shrink-0" />
                  <div>
                    <p className="font-medium">Điện thoại</p>
                    <p className="text-sm text-muted-foreground">
                      <a
                        href={`tel:${contactData.phone}`}
                        className="hover:text-pink-600"
                        onClick={contactActions.callPhone}
                      >
                        {contactData.phone}
                      </a>
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <Mail className="h-5 w-5 text-pink-600 mt-1 flex-shrink-0" />
                  <div>
                    <p className="font-medium">Email</p>
                    <p className="text-sm text-muted-foreground">
                      <a
                        href={`mailto:${contactData.email}`}
                        className="hover:text-pink-600"
                        onClick={() =>
                          contactActions.sendEmail("Liên hệ từ website")
                        }
                      >
                        {contactData.email}
                      </a>
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <Clock className="h-5 w-5 text-pink-600 mt-1 flex-shrink-0" />
                  <div>
                    <p className="font-medium">Giờ làm việc</p>
                    <div className="text-sm text-muted-foreground">
                      {businessHours ? (
                        Object.entries(businessHours).map(([day, hours]) => (
                          <div key={day}>
                            {day}:{" "}
                            {typeof hours === "object" &&
                            hours !== null &&
                            "closed" in hours &&
                            !Array.isArray(hours)
                              ? (hours as any).closed
                                ? "Nghỉ"
                                : `${(hours as any).open} - ${(hours as any).close}`
                              : "8:00 - 18:00"}
                          </div>
                        ))
                      ) : (
                        <>
                          Thứ 2 - Thứ 6: 8:00 - 18:00
                          <br />
                          Thứ 7 - Chủ nhật: 9:00 - 17:00
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Social Media */}
            <Card>
              <CardHeader>
                <CardTitle>Kết nối với chúng tôi</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex gap-4">
                  {contactData.socialMedia.facebook && (
                    <a
                      href={contactData.socialMedia.facebook}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center justify-center w-10 h-10 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-colors"
                    >
                      <Facebook className="h-5 w-5" />
                    </a>
                  )}
                  {contactData.socialMedia.instagram && (
                    <a
                      href={contactData.socialMedia.instagram}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center justify-center w-10 h-10 bg-pink-600 text-white rounded-full hover:bg-pink-700 transition-colors"
                    >
                      <Instagram className="h-5 w-5" />
                    </a>
                  )}
                  {contactData.socialMedia.twitter && (
                    <a
                      href={contactData.socialMedia.twitter}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center justify-center w-10 h-10 bg-blue-400 text-white rounded-full hover:bg-blue-500 transition-colors"
                    >
                      <Twitter className="h-5 w-5" />
                    </a>
                  )}
                  {contactData.socialMedia.zalo && (
                    <a
                      href={contactData.socialMedia.zalo}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center justify-center w-10 h-10 bg-blue-500 text-white rounded-full hover:bg-blue-600 transition-colors"
                    >
                      <MessageCircle className="h-5 w-5" />
                    </a>
                  )}
                </div>
                <p className="text-sm text-muted-foreground mt-4">
                  Theo dõi chúng tôi để cập nhật những xu hướng thời trang mới
                  nhất!
                </p>
              </CardContent>
            </Card>

            {/* FAQ */}
            <Card>
              <CardHeader>
                <CardTitle>Câu hỏi thường gặp</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <p className="font-medium text-sm">
                    Làm sao để theo dõi đơn hàng?
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Bạn có thể theo dõi đơn hàng trong mục &quot;Đơn hàng của
                    tôi&quot; sau khi đăng nhập.
                  </p>
                </div>
                <div>
                  <p className="font-medium text-sm">
                    Chính sách đổi trả như thế nào?
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Chúng tôi hỗ trợ đổi trả trong vòng 7 ngày với sản phẩm còn
                    nguyên tem mác.
                  </p>
                </div>
                <div>
                  <p className="font-medium text-sm">
                    Có miễn phí vận chuyển không?
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Miễn phí vận chuyển cho đơn hàng từ 500.000đ trở lên.
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Map Section */}
        <div className="mt-12">
          <Card>
            <CardHeader>
              <CardTitle>Vị trí cửa hàng</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="w-full h-64 bg-gray-200 rounded-lg flex items-center justify-center">
                <p className="text-muted-foreground">
                  Google Maps sẽ được tích hợp tại đây
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
      </div>
      <Footer />
    </div>
  );
}
