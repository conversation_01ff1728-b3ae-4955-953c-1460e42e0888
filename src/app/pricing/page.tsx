import { Metadata } from "next";
import { <PERSON><PERSON>, Footer } from "@/components/layout";
import { PricingHeroSection } from "@/components/pricing/pricing-hero-section";
import { PricingPlansSection } from "@/components/pricing/pricing-plans-section";
import { PricingServicesSection } from "@/components/pricing/pricing-services-section";
import { PricingComparisonSection } from "@/components/pricing/pricing-comparison-section";
import { PricingFAQSection } from "@/components/pricing/pricing-faq-section";
import { PricingContactSection } from "@/components/pricing/pricing-contact-section";

export const metadata: Metadata = {
  title: "Bảng Giá Dịch Vụ - NS Shop | May Gia Công Chuyên <PERSON>hi<PERSON>",
  description:
    "Xem bảng giá chi tiết các dịch vụ may gia công tại NS Shop. Giá cả cạnh tranh, chất lượng cao, hỗ trợ số lượng ít từ 30 sản phẩm. <PERSON>áo gi<PERSON> miễn phí 24/7.",
  keywords: [
    "bảng giá",
    "gi<PERSON> may gia công",
    "chi phí sản xuất",
    "báo giá",
    "NS Shop",
    "may số lượng ít",
    "giá cạnh tranh",
    "dịch vụ may",
  ],
  openGraph: {
    title: "Bảng Giá Dịch Vụ - NS Shop | May Gia Công Chuyên Nghiệp",
    description:
      "Xem bảng giá chi tiết các dịch vụ may gia công tại NS Shop. Giá cả cạnh tranh, chất lượng cao, hỗ trợ số lượng ít từ 30 sản phẩm.",
    images: ["/og-image-pricing.jpg"],
    url: "https://nsshop.com/pricing",
  },
  alternates: {
    canonical: "https://nsshop.com/pricing",
  },
};

export default function PricingPage() {
  return (
    <div className="min-h-screen flex flex-col bg-white">
      <Header />
      <main className="flex-1">
        {/* Hero Section */}
        <PricingHeroSection />

        {/* Pricing Plans Section */}
        <PricingPlansSection />

        {/* Additional Services Section */}
        <PricingServicesSection />

        {/* Comparison Section */}
        <PricingComparisonSection />

        {/* FAQ Section */}
        <PricingFAQSection />

        {/* Contact Section */}
        <PricingContactSection />
      </main>
      <Footer />
    </div>
  );
}
