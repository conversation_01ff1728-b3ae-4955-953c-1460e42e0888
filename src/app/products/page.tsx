import { Suspense } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "@/components/layout";
import { EnhancedProductsPage } from "@/components/shop/enhanced-products-page";

function ProductsPageContent() {
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <EnhancedProductsPage />
      <Footer />
    </div>
  );
}

export default function ProductsPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <ProductsPageContent />
    </Suspense>
  );
}
