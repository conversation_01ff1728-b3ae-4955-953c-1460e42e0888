/**
 * Brand Repository
 * <PERSON><PERSON><PERSON><PERSON> lý các thao tác database cho Brand model
 */

import { Brand, Prisma } from "@prisma/client";
import {
  BaseRepository,
  NotFoundError,
  ConflictError,
} from "./base.repository";

export type BrandCreateInput = Omit<
  Prisma.BrandCreateInput,
  "id" | "createdAt" | "updatedAt" | "logo"
> & {
  logoId?: string | null;
};
export type BrandUpdateInput = Partial<BrandCreateInput>;
export type BrandWithRelations = Prisma.BrandGetPayload<{
  include: {
    products: true;
    logo: true;
  };
}>;

export interface BrandSearchOptions {
  search?: string;
  isActive?: boolean;
  hasProducts?: boolean;
}

export class BrandRepository extends BaseRepository<
  Brand,
  BrandCreateInput,
  BrandUpdateInput
> {
  constructor() {
    super("brand");
  }

  // Tìm brand theo slug
  async findBySlug(slug: string): Promise<Brand | null> {
    return await this.model.findUnique({
      where: { slug },
    });
  }

  // Tìm brand với đầy đủ relations
  async findByIdWithRelations(id: string): Promise<BrandWithRelations | null> {
    return await this.model.findUnique({
      where: { id },
      include: {
        products: {
          where: { status: "ACTIVE" },
          select: {
            id: true,
            name: true,
            slug: true,
            price: true,
            stock: true,
            featured: true,
          },
          orderBy: { createdAt: "desc" },
        },
        logo: true,
        _count: {
          select: { products: true },
        },
      },
    });
  }

  // Tìm brand theo slug với relations
  async findBySlugWithRelations(
    slug: string
  ): Promise<BrandWithRelations | null> {
    return await this.model.findUnique({
      where: { slug },
      include: {
        products: {
          where: { status: "ACTIVE" },
          include: {
            category: {
              select: { id: true, name: true, slug: true },
            },
            media: {
              include: { media: true },
              where: { isPrimary: true },
              take: 1,
            },
          },
          orderBy: { createdAt: "desc" },
        },
        logo: true,
      },
    });
  }

  // Tạo brand mới với slug unique
  async createBrand(data: BrandCreateInput): Promise<Brand> {
    // Kiểm tra slug đã tồn tại
    if (data.slug) {
      const existingSlug = await this.findBySlug(data.slug);
      if (existingSlug) {
        throw new ConflictError("Slug đã tồn tại");
      }
    }

    return await this.create(data);
  }

  // Tìm kiếm brands với filter
  async searchBrands(
    options: BrandSearchOptions & {
      page?: number;
      limit?: number;
    }
  ) {
    const { search, isActive, hasProducts, page = 1, limit = 20 } = options;

    const where: Prisma.BrandWhereInput = {};

    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
      ];
    }

    if (typeof isActive === "boolean") {
      where.isActive = isActive;
    }

    if (hasProducts) {
      where.products = { some: {} };
    }

    return await this.findWithPagination({
      page,
      limit,
      where,
      include: {
        logo: true,
        _count: {
          select: { products: true },
        },
      },
      orderBy: { name: "asc" },
    });
  }

  // Lấy active brands
  async getActiveBrands(): Promise<Brand[]> {
    return await this.findMany({
      where: { isActive: true },
      include: {
        logo: true,
        _count: {
          select: { products: true },
        },
      },
      orderBy: { name: "asc" },
    });
  }

  // Lấy brands có sản phẩm
  async getBrandsWithProducts(): Promise<Brand[]> {
    return await this.findMany({
      where: {
        isActive: true,
        products: { some: { status: "ACTIVE" } },
      },
      include: {
        logo: true,
        _count: {
          select: { products: true },
        },
      },
      orderBy: { name: "asc" },
    });
  }

  // Lấy popular brands (có nhiều sản phẩm nhất)
  async getPopularBrands(limit: number = 10): Promise<Brand[]> {
    return await this.prisma.$queryRaw`
      SELECT b.*, COUNT(p.id) as product_count
      FROM brands b
      LEFT JOIN products p ON b.id = p.brand_id AND p.status = 'ACTIVE'
      WHERE b.is_active = true
      GROUP BY b.id
      HAVING COUNT(p.id) > 0
      ORDER BY product_count DESC
      LIMIT ${limit}
    `;
  }

  // Lấy featured brands
  async getFeaturedBrands(limit: number = 8): Promise<Brand[]> {
    return await this.findMany({
      where: {
        isActive: true,
        products: {
          some: {
            status: "ACTIVE",
            featured: true,
          },
        },
      },
      include: {
        logo: true,
        _count: {
          select: { products: true },
        },
      },
      orderBy: { name: "asc" },
      take: limit,
    });
  }

  // Activate brand
  async activateBrand(id: string): Promise<Brand> {
    const brand = await this.findById(id);
    if (!brand) {
      throw new NotFoundError("Brand", id);
    }

    return await this.update(id, { isActive: true });
  }

  // Deactivate brand
  async deactivateBrand(id: string): Promise<Brand> {
    const brand = await this.findById(id);
    if (!brand) {
      throw new NotFoundError("Brand", id);
    }

    return await this.update(id, { isActive: false });
  }

  // Kiểm tra có thể xóa brand không (không có products)
  async canDelete(id: string): Promise<boolean> {
    const brand = await this.model.findUnique({
      where: { id },
      include: {
        products: true,
      },
    });

    if (!brand) return false;

    return brand.products.length === 0;
  }

  // Xóa brand (chỉ khi không có products)
  async deleteBrand(id: string): Promise<Brand> {
    const canDelete = await this.canDelete(id);
    if (!canDelete) {
      throw new ConflictError("Không thể xóa brand có products");
    }

    return await this.delete(id);
  }

  // Lấy thống kê brands
  async getBrandStats() {
    const [total, active, inactive, withProducts, withoutProducts] =
      await Promise.all([
        this.count(),
        this.count({ isActive: true }),
        this.count({ isActive: false }),
        this.count({
          products: { some: {} },
        }),
        this.count({
          products: { none: {} },
        }),
      ]);

    return {
      total,
      active,
      inactive,
      withProducts,
      withoutProducts,
    };
  }

  // Lấy brands theo category
  async getBrandsByCategory(categoryId: string): Promise<Brand[]> {
    return await this.prisma.$queryRaw`
      SELECT DISTINCT b.*
      FROM brands b
      INNER JOIN products p ON b.id = p.brand_id
      WHERE p.category_id = ${categoryId}
        AND p.status = 'ACTIVE'
        AND b.is_active = true
      ORDER BY b.name ASC
    `;
  }

  // Cập nhật logo brand
  async updateLogo(id: string, logoId: string | null): Promise<Brand> {
    return await this.model.update({
      where: { id },
      data: logoId
        ? { logo: { connect: { id: logoId } } }
        : { logo: { disconnect: true } },
      include: {
        logo: {
          select: {
            id: true,
            url: true,
            alt: true,
          },
        },
        _count: {
          select: {
            products: true,
          },
        },
      },
    });
  }

  // Lấy top selling brands
  async getTopSellingBrands(limit: number = 10): Promise<any[]> {
    return await this.prisma.$queryRaw`
      SELECT b.*, COUNT(oi.id) as sales_count, SUM(oi.total) as total_revenue
      FROM brands b
      LEFT JOIN products p ON b.id = p.brand_id
      LEFT JOIN order_items oi ON p.id = oi.product_id
      WHERE b.is_active = true
      GROUP BY b.id
      HAVING COUNT(oi.id) > 0
      ORDER BY sales_count DESC
      LIMIT ${limit}
    `;
  }
}
