/**
 * AuditLog Repository
 * Quản lý các thao tác database cho AuditLog model
 */

import { AuditLog, Prisma } from "@prisma/client";
import { BaseRepository } from "./base.repository";

export type AuditLogCreateInput = Omit<
  Prisma.AuditLogCreateInput,
  "id" | "createdAt" | "admin"
> & {
  adminId: string;
};
export type AuditLogUpdateInput = Partial<AuditLogCreateInput>;
export type AuditLogWithAdmin = Prisma.AuditLogGetPayload<{
  include: {
    admin: true;
  };
}>;

export interface AuditLogSearchOptions {
  search?: string;
  adminId?: string;
  action?: string;
  resource?: string;
  dateFrom?: Date;
  dateTo?: Date;
  ipAddress?: string;
}

export class AuditLogRepository extends BaseRepository<
  AuditLog,
  AuditLogCreateInput,
  AuditLogUpdateInput
> {
  constructor() {
    super("auditLog");
  }

  // Tìm audit log với user info
  async findByIdWithAdmin(id: string): Promise<AuditLogWithAdmin | null> {
    return await this.model.findUnique({
      where: { id },
      include: {
        admin: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
          },
        },
      },
    });
  }

  // Tạo audit log
  async createAuditLog(data: AuditLogCreateInput): Promise<AuditLog> {
    return await this.create(data);
  }

  // Log action
  async logAction(
    adminId: string,
    action: string,
    resource: string,
    resourceId?: string,
    details?: any,
    ipAddress?: string,
    userAgent?: string
  ): Promise<AuditLog> {
    return await this.createAuditLog({
      adminId,
      action,
      resource,
      resourceId,
      description: details ? JSON.stringify(details) : undefined,
      ipAddress,
      userAgent,
    });
  }

  // Tìm kiếm audit logs với filter
  async searchAuditLogs(
    options: AuditLogSearchOptions & {
      page?: number;
      limit?: number;
    }
  ) {
    const {
      search,
      adminId,
      action,
      resource,
      dateFrom,
      dateTo,
      ipAddress,
      page = 1,
      limit = 50,
    } = options;

    const where: Prisma.AuditLogWhereInput = {};

    if (search) {
      where.OR = [
        { action: { contains: search, mode: "insensitive" } },
        { resource: { contains: search, mode: "insensitive" } },
        { resourceId: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
      ];
    }

    if (adminId) {
      where.adminId = adminId;
    }

    if (action) {
      where.action = { contains: action, mode: "insensitive" };
    }

    if (resource) {
      where.resource = { contains: resource, mode: "insensitive" };
    }

    if (ipAddress) {
      where.ipAddress = ipAddress;
    }

    if (dateFrom || dateTo) {
      where.createdAt = {};
      if (dateFrom) where.createdAt.gte = dateFrom;
      if (dateTo) where.createdAt.lte = dateTo;
    }

    return await this.findWithPagination({
      page,
      limit,
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: { createdAt: "desc" },
    });
  }

  // Lấy audit logs của admin
  async getAdminAuditLogs(
    adminId: string,
    options: {
      page?: number;
      limit?: number;
      action?: string;
      resource?: string;
    } = {}
  ): Promise<any> {
    const { page = 1, limit = 20, action, resource } = options;

    const where: Prisma.AuditLogWhereInput = { adminId };

    if (action) {
      where.action = { contains: action, mode: "insensitive" };
    }

    if (resource) {
      where.resource = { contains: resource, mode: "insensitive" };
    }

    return await this.findWithPagination({
      page,
      limit,
      where,
      orderBy: { createdAt: "desc" },
    });
  }

  // Lấy audit logs theo resource
  async getResourceAuditLogs(
    resource: string,
    resourceId?: string,
    options: {
      page?: number;
      limit?: number;
    } = {}
  ): Promise<any> {
    const { page = 1, limit = 20 } = options;

    const where: Prisma.AuditLogWhereInput = { resource };

    if (resourceId) {
      where.resourceId = resourceId;
    }

    return await this.findWithPagination({
      page,
      limit,
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: { createdAt: "desc" },
    });
  }

  // Lấy recent activities
  async getRecentActivities(limit: number = 20): Promise<AuditLog[]> {
    return await this.findMany({
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
          },
        },
      },
      orderBy: { createdAt: "desc" },
      take: limit,
    });
  }

  // Lấy thống kê audit logs
  async getAuditLogStats() {
    const [
      total,
      todayCount,
      thisWeekCount,
      actionStats,
      resourceStats,
      topAdmins,
    ] = await Promise.all([
      this.count(),
      this.count({
        createdAt: {
          gte: new Date(new Date().setHours(0, 0, 0, 0)),
        },
      }),
      this.count({
        createdAt: {
          gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
        },
      }),
      this.prisma.auditLog.groupBy({
        by: ["action"],
        _count: { action: true },
        orderBy: { _count: { action: "desc" } },
        take: 10,
      }),
      this.prisma.auditLog.groupBy({
        by: ["resource"],
        _count: { resource: true },
        orderBy: { _count: { resource: "desc" } },
        take: 10,
      }),
      this.prisma.auditLog.groupBy({
        by: ["adminId"],
        _count: { adminId: true },
        orderBy: { _count: { adminId: "desc" } },
        take: 10,
      }),
    ]);

    return {
      total,
      todayCount,
      thisWeekCount,
      topActions: actionStats.map((stat) => ({
        action: stat.action,
        count: stat._count.action,
      })),
      topResources: resourceStats.map((stat) => ({
        resource: stat.resource,
        count: stat._count.resource,
      })),
      topAdmins: topAdmins.map((stat) => ({
        adminId: stat.adminId,
        count: stat._count.adminId,
      })),
    };
  }

  // Xóa audit logs cũ
  async deleteOldAuditLogs(olderThanDays: number = 90): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

    const result = await this.deleteMany({
      createdAt: { lt: cutoffDate },
    });

    return result.count;
  }

  // Lấy activity timeline
  async getActivityTimeline(
    options: {
      adminId?: string;
      resource?: string;
      days?: number;
    } = {}
  ): Promise<Array<{ date: string; count: number }>> {
    const { adminId, resource, days = 30 } = options;

    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const where: Prisma.AuditLogWhereInput = {
      createdAt: { gte: startDate },
    };

    if (adminId) where.adminId = adminId;
    if (resource) where.resource = resource;

    const result = await this.prisma.$queryRaw<
      Array<{ date: string; count: bigint }>
    >`
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as count
      FROM audit_logs
      WHERE created_at >= ${startDate}
        ${adminId ? Prisma.sql`AND admin_id = ${adminId}` : Prisma.empty}
        ${resource ? Prisma.sql`AND resource = ${resource}` : Prisma.empty}
      GROUP BY DATE(created_at)
      ORDER BY date DESC
    `;

    return result.map((item) => ({
      date: item.date,
      count: Number(item.count),
    }));
  }

  // Helper methods cho các actions thường dùng

  // Log login
  async logLogin(
    userId: string,
    ipAddress?: string,
    userAgent?: string
  ): Promise<AuditLog> {
    return await this.logAction(
      userId,
      "LOGIN",
      "AUTH",
      userId,
      null,
      ipAddress,
      userAgent
    );
  }

  // Log logout
  async logLogout(
    userId: string,
    ipAddress?: string,
    userAgent?: string
  ): Promise<AuditLog> {
    return await this.logAction(
      userId,
      "LOGOUT",
      "AUTH",
      userId,
      null,
      ipAddress,
      userAgent
    );
  }

  // Log create
  async logCreate(
    userId: string,
    resource: string,
    resourceId: string,
    details?: any,
    ipAddress?: string
  ): Promise<AuditLog> {
    return await this.logAction(
      userId,
      "CREATE",
      resource,
      resourceId,
      details,
      ipAddress
    );
  }

  // Log update
  async logUpdate(
    userId: string,
    resource: string,
    resourceId: string,
    changes?: any,
    ipAddress?: string
  ): Promise<AuditLog> {
    return await this.logAction(
      userId,
      "UPDATE",
      resource,
      resourceId,
      changes,
      ipAddress
    );
  }

  // Log delete
  async logDelete(
    userId: string,
    resource: string,
    resourceId: string,
    details?: any,
    ipAddress?: string
  ): Promise<AuditLog> {
    return await this.logAction(
      userId,
      "DELETE",
      resource,
      resourceId,
      details,
      ipAddress
    );
  }

  // Log view
  async logView(
    userId: string,
    resource: string,
    resourceId: string,
    ipAddress?: string
  ): Promise<AuditLog> {
    return await this.logAction(
      userId,
      "VIEW",
      resource,
      resourceId,
      null,
      ipAddress
    );
  }

  // Log export
  async logExport(
    userId: string,
    resource: string,
    format: string,
    filters?: any,
    ipAddress?: string
  ): Promise<AuditLog> {
    return await this.logAction(
      userId,
      "EXPORT",
      resource,
      undefined,
      { format, filters },
      ipAddress
    );
  }

  // Log import
  async logImport(
    userId: string,
    resource: string,
    fileName: string,
    recordCount?: number,
    ipAddress?: string
  ): Promise<AuditLog> {
    return await this.logAction(
      userId,
      "IMPORT",
      resource,
      undefined,
      { fileName, recordCount },
      ipAddress
    );
  }

  // Delete old logs (required by services)
  async deleteOldLogs(daysToKeep: number = 90): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

    const result = await this.prisma.auditLog.deleteMany({
      where: {
        createdAt: {
          lt: cutoffDate,
        },
      },
    });

    return result.count;
  }
}
