/**
 * AdminUser Repository
 * Quản lý các thao tác database cho AdminUser model
 */

import { AdminUser, Prisma, AdminRole } from "@prisma/client";
import {
  BaseRepository,
  NotFoundError,
  ConflictError,
} from "./base.repository";
import * as bcrypt from "bcryptjs";

export type AdminUserCreateInput = Omit<
  Prisma.AdminUserCreateInput,
  "id" | "createdAt" | "updatedAt"
>;
export type AdminUserUpdateInput = Partial<AdminUserCreateInput>;
export type AdminUserWithRelations = Prisma.AdminUserGetPayload<{
  include: {
    createdByAdmin: true;
    createdAdmins: true;
    posts: true;
    pages: true;
    createdNotifications: true;
    auditLogs: true;
    avatar: true;
  };
}>;

// Type for admin permissions
export type AdminPermissions = {
  [key: string]: boolean;
};

export interface AdminUserSearchOptions {
  search?: string;
  role?: AdminRole;
  isActive?: boolean;
  department?: string;
  dateFrom?: Date;
  dateTo?: Date;
}

export class AdminUserRepository extends BaseRepository<
  AdminUser,
  AdminUserCreateInput,
  AdminUserUpdateInput
> {
  constructor() {
    super("adminUser");
  }

  // Tìm admin theo email
  async findByEmail(email: string): Promise<AdminUser | null> {
    return await this.model.findUnique({
      where: { email },
    });
  }

  // Tìm admin với đầy đủ relations
  async findByIdWithRelations(
    id: string
  ): Promise<AdminUserWithRelations | null> {
    return await this.model.findUnique({
      where: { id },
      include: {
        createdByAdmin: {
          select: { id: true, name: true, email: true },
        },
        createdAdmins: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
            isActive: true,
          },
          orderBy: { createdAt: "desc" },
        },
        posts: {
          select: { id: true, title: true, status: true, createdAt: true },
          orderBy: { createdAt: "desc" },
          take: 10,
        },
        pages: {
          select: { id: true, title: true, status: true, createdAt: true },
          orderBy: { createdAt: "desc" },
          take: 10,
        },
        createdNotifications: {
          select: { id: true, title: true, type: true, createdAt: true },
          orderBy: { createdAt: "desc" },
          take: 10,
        },
        auditLogs: {
          select: { id: true, action: true, resource: true, createdAt: true },
          orderBy: { createdAt: "desc" },
          take: 20,
        },
        avatar: true,
      },
    });
  }

  // Tạo admin mới với mật khẩu đã hash
  async createAdmin(
    data: AdminUserCreateInput & { password: string }
  ): Promise<AdminUser> {
    // Kiểm tra email đã tồn tại
    const existingAdmin = await this.findByEmail(data.email);
    if (existingAdmin) {
      throw new ConflictError("Email đã được sử dụng");
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(data.password, 12);

    return await this.create({
      ...data,
      password: hashedPassword,
    });
  }

  // Cập nhật mật khẩu
  async updatePassword(id: string, newPassword: string): Promise<AdminUser> {
    const hashedPassword = await bcrypt.hash(newPassword, 12);

    return await this.update(id, {
      password: hashedPassword,
    });
  }

  // Xác thực mật khẩu
  async verifyPassword(
    email: string,
    password: string
  ): Promise<AdminUser | null> {
    const admin = await this.findByEmail(email);
    if (!admin || !admin.isActive) return null;

    const isValid = await bcrypt.compare(password, admin.password);
    return isValid ? admin : null;
  }

  // Cập nhật last login
  async updateLastLogin(id: string): Promise<AdminUser> {
    return await this.update(id, {
      lastLoginAt: new Date(),
    });
  }

  // Tìm kiếm admins với filter
  async searchAdmins(
    options: AdminUserSearchOptions & {
      page?: number;
      limit?: number;
    }
  ) {
    const {
      search,
      role,
      isActive,
      department,
      dateFrom,
      dateTo,
      page = 1,
      limit = 20,
    } = options;

    const where: Prisma.AdminUserWhereInput = {};

    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { email: { contains: search, mode: "insensitive" } },
        { phone: { contains: search, mode: "insensitive" } },
        { department: { contains: search, mode: "insensitive" } },
      ];
    }

    if (role) {
      where.role = role;
    }

    if (typeof isActive === "boolean") {
      where.isActive = isActive;
    }

    if (department) {
      where.department = { contains: department, mode: "insensitive" };
    }

    if (dateFrom || dateTo) {
      where.createdAt = {};
      if (dateFrom) where.createdAt.gte = dateFrom;
      if (dateTo) where.createdAt.lte = dateTo;
    }

    return await this.findWithPagination({
      page,
      limit,
      where,
      include: {
        avatar: true,
        createdByAdmin: {
          select: { id: true, name: true, email: true },
        },
        _count: {
          select: {
            posts: true,
            pages: true,
            createdAdmins: true,
            auditLogs: true,
          },
        },
      },
      orderBy: { createdAt: "desc" },
    });
  }

  // Lấy admins theo role
  async getAdminsByRole(role: AdminRole): Promise<AdminUser[]> {
    return await this.findMany({
      where: { role, isActive: true },
      include: {
        avatar: true,
      },
      orderBy: { name: "asc" },
    });
  }

  // Lấy admins hoạt động
  async getActiveAdmins(limit: number = 10): Promise<AdminUser[]> {
    return await this.findMany({
      where: { isActive: true },
      orderBy: { lastLoginAt: "desc" },
      take: limit,
      include: {
        avatar: true,
      },
    });
  }

  // Deactivate admin
  async deactivateAdmin(id: string): Promise<AdminUser> {
    const admin = await this.findById(id);
    if (!admin) {
      throw new NotFoundError("AdminUser", id);
    }

    return await this.update(id, { isActive: false });
  }

  // Activate admin
  async activateAdmin(id: string): Promise<AdminUser> {
    const admin = await this.findById(id);
    if (!admin) {
      throw new NotFoundError("AdminUser", id);
    }

    return await this.update(id, { isActive: true });
  }

  // Cập nhật permissions
  async updatePermissions(
    id: string,
    permissions: Record<string, boolean>
  ): Promise<AdminUser> {
    return await this.update(id, { permissions });
  }

  // Lấy thống kê admin
  async getAdminStats() {
    const [total, active, inactive, admins, moderators] = await Promise.all([
      this.count(),
      this.count({ isActive: true }),
      this.count({ isActive: false }),
      this.count({ role: "ADMIN" }),
      this.count({ role: "MODERATOR" }),
    ]);

    return {
      total,
      active,
      inactive,
      admins,
      moderators,
    };
  }

  // Kiểm tra quyền admin
  async hasPermission(id: string, permission: string): Promise<boolean> {
    const admin = await this.findById(id);
    if (!admin || !admin.isActive) return false;

    // ADMIN có tất cả quyền
    if (admin.role === "ADMIN") return true;

    // Kiểm tra permissions cho MODERATOR
    if (admin.permissions && typeof admin.permissions === "object") {
      return (admin.permissions as AdminPermissions)[permission] === true;
    }

    return false;
  }
}
