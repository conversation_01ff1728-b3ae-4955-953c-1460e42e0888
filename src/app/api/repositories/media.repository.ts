/**
 * Media Repository
 * <PERSON>u<PERSON>n lý các thao tác database cho Media model
 */

import { Media, Prisma, MediaType } from "@prisma/client";
import { BaseRepository, NotFoundError } from "./base.repository";

export type MediaCreateInput = Omit<
  Prisma.MediaCreateInput,
  "id" | "createdAt" | "updatedAt"
>;
export type MediaUpdateInput = Partial<MediaCreateInput>;

export interface MediaSearchOptions {
  search?: string;
  type?: MediaType;
  mimeType?: string;
  sizeMin?: number;
  sizeMax?: number;
  dateFrom?: Date;
  dateTo?: Date;
}

export class MediaRepository extends BaseRepository<
  Media,
  MediaCreateInput,
  MediaUpdateInput
> {
  constructor() {
    super("media");
  }

  // Tìm media theo filename
  async findByFilename(filename: string): Promise<Media | null> {
    return await this.findFirst({ filename });
  }

  // Tìm media theo path
  async findByPath(path: string): Promise<Media | null> {
    return await this.findFirst({ path });
  }

  // Tìm kiếm media với filter
  async searchMedia(
    options: MediaSearchOptions & {
      page?: number;
      limit?: number;
      sortBy?: string;
      sortOrder?: "asc" | "desc";
    }
  ) {
    const {
      search,
      type,
      mimeType,
      sizeMin,
      sizeMax,
      dateFrom,
      dateTo,
      page = 1,
      limit = 20,
      sortBy = "createdAt",
      sortOrder = "desc",
    } = options;

    const where: Prisma.MediaWhereInput = {};

    if (search) {
      where.OR = [
        { filename: { contains: search, mode: "insensitive" } },
        { title: { contains: search, mode: "insensitive" } },
        { alt: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
      ];
    }

    if (type) {
      where.type = type;
    }

    if (mimeType) {
      where.mimeType = { contains: mimeType, mode: "insensitive" };
    }

    if (sizeMin !== undefined || sizeMax !== undefined) {
      where.size = {};
      if (sizeMin !== undefined) where.size.gte = sizeMin;
      if (sizeMax !== undefined) where.size.lte = sizeMax;
    }

    if (dateFrom || dateTo) {
      where.createdAt = {};
      if (dateFrom) where.createdAt.gte = dateFrom;
      if (dateTo) where.createdAt.lte = dateTo;
    }

    return await this.findWithPagination({
      page,
      limit,
      where,
      orderBy: { [sortBy]: sortOrder },
    });
  }

  // Lấy media theo type
  async getMediaByType(
    type: MediaType,
    options: {
      page?: number;
      limit?: number;
    } = {}
  ): Promise<any> {
    const { page = 1, limit = 20 } = options;

    return await this.findWithPagination({
      page,
      limit,
      where: { type },
      orderBy: { createdAt: "desc" },
    });
  }

  // Lấy images
  async getImages(
    options: {
      page?: number;
      limit?: number;
    } = {}
  ): Promise<any> {
    const { page = 1, limit = 20 } = options;
    return await this.findWithPagination({
      page,
      limit,
      where: {
        mimeType: {
          startsWith: "image/",
        },
      },
      orderBy: { createdAt: "desc" },
    });
  }

  // Lấy videos
  async getVideos(
    options: {
      page?: number;
      limit?: number;
    } = {}
  ): Promise<any> {
    const { page = 1, limit = 20 } = options;
    return await this.findWithPagination({
      page,
      limit,
      where: {
        mimeType: {
          startsWith: "video/",
        },
      },
      orderBy: { createdAt: "desc" },
    });
  }

  // Lấy documents
  async getDocuments(
    options: {
      page?: number;
      limit?: number;
    } = {}
  ): Promise<any> {
    const { page = 1, limit = 20 } = options;
    return await this.findWithPagination({
      page,
      limit,
      where: {
        mimeType: {
          in: [
            "application/pdf",
            "application/msword",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "text/plain",
          ],
        },
      },
      orderBy: { createdAt: "desc" },
    });
  }

  // Lấy recent media
  async getRecentMedia(limit: number = 20): Promise<Media[]> {
    return await this.findMany({
      orderBy: { createdAt: "desc" },
      take: limit,
    });
  }

  // Lấy media theo mime type
  async getMediaByMimeType(mimeType: string): Promise<Media[]> {
    return await this.findMany({
      where: { mimeType },
      orderBy: { createdAt: "desc" },
    });
  }

  // Lấy large files
  async getLargeFiles(minSize: number = 10 * 1024 * 1024): Promise<Media[]> {
    // 10MB
    return await this.findMany({
      where: { size: { gte: minSize } },
      orderBy: { size: "desc" },
    });
  }

  // Lấy thống kê media
  async getMediaStats() {
    const [total, images, videos, documents, totalSize, avgSize] =
      await Promise.all([
        this.count(),
        this.count({ mimeType: { startsWith: "image/" } }),
        this.count({ mimeType: { startsWith: "video/" } }),
        this.count({
          mimeType: {
            in: [
              "application/pdf",
              "application/msword",
              "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
              "text/plain",
            ],
          },
        }),
        this.prisma.media.aggregate({
          _sum: { size: true },
        }),
        this.prisma.media.aggregate({
          _avg: { size: true },
        }),
      ]);

    // Thống kê theo mime type
    const mimeTypeStats = await this.prisma.media.groupBy({
      by: ["mimeType"],
      _count: { mimeType: true },
      _sum: { size: true },
      orderBy: { _count: { mimeType: "desc" } },
    });

    return {
      total,
      images,
      videos,
      documents,
      totalSize: totalSize._sum.size || 0,
      avgSize: avgSize._avg.size || 0,
      mimeTypes: mimeTypeStats.map((stat) => ({
        mimeType: stat.mimeType,
        count: stat._count.mimeType,
        totalSize: stat._sum.size || 0,
      })),
    };
  }

  // Cập nhật alt text
  async updateAlt(id: string, alt: string): Promise<Media> {
    return await this.update(id, { alt });
  }

  // Cập nhật description
  async updateDescription(id: string, description: string): Promise<Media> {
    return await this.update(id, { description });
  }

  // Lấy unused media (không được sử dụng ở đâu)
  async getUnusedMedia(): Promise<Media[]> {
    // Tìm media không được reference bởi bất kỳ entity nào
    const usedMediaIds = await this.prisma.$queryRaw<Array<{ id: string }>>`
      SELECT DISTINCT m.id
      FROM media m
      WHERE m.id IN (
        SELECT featured_image_id FROM posts WHERE featured_image_id IS NOT NULL
        UNION
        SELECT featured_image_id FROM pages WHERE featured_image_id IS NOT NULL
        UNION
        SELECT logo_id FROM brands WHERE logo_id IS NOT NULL
        UNION
        SELECT image_id FROM categories WHERE image_id IS NOT NULL
        UNION
        SELECT avatar_id FROM users WHERE avatar_id IS NOT NULL
        UNION
        SELECT avatar_id FROM admin_users WHERE avatar_id IS NOT NULL
        UNION
        SELECT media_id FROM product_media WHERE media_id IS NOT NULL
        UNION
        SELECT media_id FROM review_media WHERE media_id IS NOT NULL
      )
    `;

    const usedIds = usedMediaIds.map((item) => item.id);

    return await this.findMany({
      where: {
        id: { notIn: usedIds },
      },
      orderBy: { createdAt: "desc" },
    });
  }

  // Cleanup unused media
  async cleanupUnusedMedia(olderThanDays: number = 30): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

    const unusedMedia = await this.getUnusedMedia();
    const oldUnusedMedia = unusedMedia.filter(
      (media) => media.createdAt < cutoffDate
    );

    let deletedCount = 0;
    for (const media of oldUnusedMedia) {
      try {
        await this.delete(media.id);
        deletedCount++;
      } catch (error) {
        console.error(`Failed to delete media ${media.id}:`, error);
      }
    }

    return deletedCount;
  }

  // Validate file type
  validateFileType(mimeType: string, allowedTypes: string[] = []): boolean {
    const defaultAllowedTypes = [
      // Images
      "image/jpeg",
      "image/jpg",
      "image/png",
      "image/gif",
      "image/webp",
      "image/svg+xml",
      // Videos
      "video/mp4",
      "video/webm",
      "video/ogg",
      // Documents
      "application/pdf",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      "application/vnd.ms-excel",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      "text/plain",
      "text/csv",
    ];

    const typesToCheck =
      allowedTypes.length > 0 ? allowedTypes : defaultAllowedTypes;
    return typesToCheck.includes(mimeType);
  }

  // Get media type from mime type
  getMediaTypeFromMimeType(_mimeType: string): "INTERNAL" | "EXTERNAL" {
    // Since MediaType enum only has INTERNAL/EXTERNAL,
    // we'll return INTERNAL for uploaded files
    return "INTERNAL";
  }

  // Format file size
  formatFileSize(bytes: number): string {
    if (bytes === 0) return "0 Bytes";

    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }

  // Generate unique filename
  generateUniqueFilename(originalName: string): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    const extension = originalName.split(".").pop();
    const nameWithoutExt = originalName.replace(/\.[^/.]+$/, "");

    return `${nameWithoutExt}-${timestamp}-${random}.${extension}`;
  }

  // Lấy media được sử dụng nhiều nhất
  async getMostUsedMedia(_limit: number = 10): Promise<any[]> {
    // Đây là một query phức tạp, cần implement logic đếm usage
    // Tạm thời return empty array
    return [];
  }

  // Duplicate media
  async duplicateMedia(id: string, newFilename?: string): Promise<Media> {
    const media = await this.findById(id);
    if (!media) {
      throw new NotFoundError("Media", id);
    }

    const filename = newFilename || this.generateUniqueFilename(media.filename);

    const newMediaData: MediaCreateInput = {
      filename,
      title: `${media.title || media.filename} (Copy)`,
      path: media.path.replace(media.filename, filename),
      url: media.url.replace(media.filename, filename),
      mimeType: media.mimeType,
      size: media.size,
      type: media.type,
      alt: media.alt,
      description: media.description
        ? `${media.description} (Copy)`
        : undefined,
    };

    return await this.create(newMediaData);
  }
}
