/**
 * Category Repository
 * <PERSON><PERSON><PERSON>n lý các thao tác database cho Category model
 */

import { Category, Prisma } from "@prisma/client";
import {
  BaseRepository,
  NotFoundError,
  ConflictError,
} from "./base.repository";

export type CategoryCreateInput = Omit<
  Prisma.CategoryCreateInput,
  "id" | "createdAt" | "updatedAt" | "parent" | "children"
> & {
  parentId?: string | null;
};
export type CategoryUpdateInput = Partial<CategoryCreateInput>;
export type CategoryWithRelations = Prisma.CategoryGetPayload<{
  include: {
    parent: true;
    children: true;
    products: true;
    image: true;
  };
}>;

export interface CategorySearchOptions {
  search?: string;
  parentId?: string | null;
  hasProducts?: boolean;
}

export class CategoryRepository extends BaseRepository<
  Category,
  CategoryCreateInput,
  CategoryUpdateInput
> {
  constructor() {
    super("category");
  }

  // Tìm category theo slug
  async findBySlug(slug: string): Promise<Category | null> {
    return await this.model.findUnique({
      where: { slug },
    });
  }

  // Tìm category với đầy đủ relations
  async findByIdWithRelations(
    id: string
  ): Promise<CategoryWithRelations | null> {
    return await this.model.findUnique({
      where: { id },
      include: {
        parent: true,
        children: {
          include: {
            image: true,
            _count: {
              select: { products: true },
            },
          },
          orderBy: { name: "asc" },
        },
        products: {
          where: { status: "ACTIVE" },
          select: { id: true, name: true, slug: true, price: true },
          take: 10,
          orderBy: { createdAt: "desc" },
        },
        image: true,
        _count: {
          select: { products: true },
        },
      },
    });
  }

  // Tìm category theo slug với relations
  async findBySlugWithRelations(
    slug: string
  ): Promise<CategoryWithRelations | null> {
    return await this.model.findUnique({
      where: { slug },
      include: {
        parent: true,
        children: {
          include: {
            image: true,
            _count: {
              select: { products: true },
            },
          },
          orderBy: { name: "asc" },
        },
        products: {
          where: { status: "ACTIVE" },
          include: {
            brand: {
              select: { id: true, name: true, slug: true },
            },
            media: {
              include: { media: true },
              where: { isPrimary: true },
              take: 1,
            },
          },
          orderBy: { createdAt: "desc" },
        },
        image: true,
      },
    });
  }

  // Tạo category mới với slug unique
  async createCategory(data: CategoryCreateInput): Promise<Category> {
    // Kiểm tra slug đã tồn tại
    if (data.slug) {
      const existingSlug = await this.findBySlug(data.slug);
      if (existingSlug) {
        throw new ConflictError("Slug đã tồn tại");
      }
    }

    // Kiểm tra parent category tồn tại
    if (data.parentId) {
      const parentCategory = await this.findById(data.parentId);
      if (!parentCategory) {
        throw new NotFoundError("Parent Category", data.parentId);
      }
    }

    // Convert parentId to parent relation for Prisma if needed
    const { parentId, ...categoryData } = data;
    return await this.model.create({
      data: {
        ...categoryData,
        ...(parentId && { parent: { connect: { id: parentId } } }),
      },
      include: {
        parent: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        _count: {
          select: {
            products: true,
            children: true,
          },
        },
      },
    });
  }

  // Tạo category với auto-generated slug
  async createCategoryWithAutoSlug(
    data: Omit<CategoryCreateInput, "slug"> & { name: string }
  ): Promise<Category> {
    // Generate slug from name
    const baseSlug = data.name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, "")
      .replace(/\s+/g, "-")
      .replace(/-+/g, "-")
      .trim();

    // Find unique slug
    let finalSlug = baseSlug;
    let counter = 1;
    while (await this.findBySlug(finalSlug)) {
      finalSlug = `${baseSlug}-${counter}`;
      counter++;
    }

    return await this.createCategory({
      ...data,
      slug: finalSlug,
    });
  }

  // Lấy root categories (không có parent)
  async getRootCategories(
    includeProducts: boolean = false
  ): Promise<Category[]> {
    return await this.findMany({
      where: { parentId: null },
      include: {
        image: true,
        children: {
          include: {
            image: true,
            _count: {
              select: { products: true },
            },
          },
          orderBy: { name: "asc" },
        },
        _count: {
          select: { products: true },
        },
        ...(includeProducts && {
          products: {
            where: { status: "ACTIVE" },
            select: {
              id: true,
              name: true,
              price: true,
              salePrice: true,
              slug: true,
              featured: true,
            },
            take: 8,
          },
        }),
      },
      orderBy: { name: "asc" },
    });
  }

  // Lấy child categories của một parent
  async getChildCategories(
    parentId: string,
    includeProducts: boolean = false
  ): Promise<Category[]> {
    return await this.findMany({
      where: { parentId },
      include: {
        image: true,
        children: {
          include: {
            image: true,
            _count: {
              select: { products: true },
            },
          },
          orderBy: { name: "asc" },
        },
        parent: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        _count: {
          select: { products: true },
        },
        ...(includeProducts && {
          products: {
            where: { status: "ACTIVE" },
            select: {
              id: true,
              name: true,
              price: true,
              salePrice: true,
              slug: true,
              featured: true,
            },
            take: 8,
          },
        }),
      },
      orderBy: { name: "asc" },
    });
  }

  // Lấy tất cả categories với hierarchy
  async getAllCategoriesWithHierarchy(
    includeProducts: boolean = false
  ): Promise<Category[]> {
    return await this.findMany({
      include: {
        image: true,
        children: {
          include: {
            image: true,
            _count: {
              select: { products: true },
            },
          },
          orderBy: { name: "asc" },
        },
        parent: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        _count: {
          select: { products: true },
        },
        ...(includeProducts && {
          products: {
            where: { status: "ACTIVE" },
            select: {
              id: true,
              name: true,
              price: true,
              salePrice: true,
              slug: true,
              featured: true,
            },
            take: 8,
          },
        }),
      },
      orderBy: { name: "asc" },
    });
  }

  // Lấy category tree (hierarchical structure)
  async getCategoryTree(): Promise<Category[]> {
    const rootCategories = await this.getRootCategories();

    // Recursively load children
    const loadChildren = async (category: any): Promise<any> => {
      if (category.children && category.children.length > 0) {
        for (const child of category.children) {
          child.children = await this.findMany({
            where: { parentId: child.id },
            include: {
              image: true,
              _count: {
                select: { products: true },
              },
            },
            orderBy: { name: "asc" },
          });

          if (child.children.length > 0) {
            await loadChildren(child);
          }
        }
      }
      return category;
    };

    for (const category of rootCategories) {
      await loadChildren(category);
    }

    return rootCategories;
  }

  // Lấy children của một category
  async getChildren(parentId: string): Promise<Category[]> {
    return await this.findMany({
      where: { parentId },
      include: {
        image: true,
        _count: {
          select: { products: true },
        },
      },
      orderBy: { name: "asc" },
    });
  }

  // Lấy breadcrumb path của category
  async getBreadcrumbPath(categoryId: string): Promise<Category[]> {
    const path: Category[] = [];
    let currentId: string | null = categoryId;

    while (currentId) {
      const category = await this.findById(currentId);
      if (!category) break;

      path.unshift(category);
      currentId = category.parentId;
    }

    return path;
  }

  // Tìm kiếm categories với filter
  async searchCategories(
    options: CategorySearchOptions & {
      page?: number;
      limit?: number;
    }
  ) {
    const { search, parentId, hasProducts, page = 1, limit = 20 } = options;

    const where: Prisma.CategoryWhereInput = {};

    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
      ];
    }

    if (parentId !== undefined) {
      where.parentId = parentId;
    }

    if (hasProducts) {
      where.products = { some: {} };
    }

    return await this.findWithPagination({
      page,
      limit,
      where,
      include: {
        parent: {
          select: { id: true, name: true, slug: true },
        },
        image: true,
        _count: {
          select: {
            products: true,
            children: true,
          },
        },
      },
      orderBy: { name: "asc" },
    });
  }

  // Lấy categories có sản phẩm
  async getCategoriesWithProducts(): Promise<Category[]> {
    return await this.findMany({
      where: {
        products: { some: { status: "ACTIVE" } },
      },
      include: {
        image: true,
        _count: {
          select: { products: true },
        },
      },
      orderBy: { name: "asc" },
    });
  }

  // Lấy popular categories (có nhiều sản phẩm nhất)
  async getPopularCategories(limit: number = 10): Promise<Category[]> {
    return await this.prisma.$queryRaw`
      SELECT c.*, COUNT(p.id) as product_count
      FROM categories c
      LEFT JOIN products p ON c.id = p.category_id AND p.status = 'ACTIVE'
      GROUP BY c.id
      HAVING COUNT(p.id) > 0
      ORDER BY product_count DESC
      LIMIT ${limit}
    `;
  }

  // Kiểm tra có thể xóa category không (không có children và products)
  async canDelete(id: string): Promise<boolean> {
    const category = await this.model.findUnique({
      where: { id },
      include: {
        children: true,
        products: true,
      },
    });

    if (!category) return false;

    return category.children.length === 0 && category.products.length === 0;
  }

  // Xóa category (chỉ khi không có children và products)
  async deleteCategory(id: string): Promise<Category> {
    const canDelete = await this.canDelete(id);
    if (!canDelete) {
      throw new ConflictError(
        "Không thể xóa category có subcategories hoặc products"
      );
    }

    return await this.delete(id);
  }

  // Lấy thống kê categories
  async getCategoryStats() {
    const [total, withProducts, withoutProducts, rootCategories] =
      await Promise.all([
        this.count(),
        this.count({
          products: { some: {} },
        }),
        this.count({
          products: { none: {} },
        }),
        this.count({ parentId: null }),
      ]);

    return {
      total,
      withProducts,
      withoutProducts,
      rootCategories,
    };
  }

  // Move category to different parent
  async moveCategory(
    id: string,
    newParentId: string | null
  ): Promise<Category> {
    // Kiểm tra không tạo circular reference
    if (newParentId) {
      const breadcrumb = await this.getBreadcrumbPath(newParentId);
      if (breadcrumb.some((cat) => cat.id === id)) {
        throw new ConflictError(
          "Không thể di chuyển category thành con của chính nó"
        );
      }
    }

    // Update with proper Prisma relation format
    return await this.model.update({
      where: { id },
      data: newParentId
        ? { parent: { connect: { id: newParentId } } }
        : { parent: { disconnect: true } },
      include: {
        parent: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        _count: {
          select: {
            products: true,
            children: true,
          },
        },
      },
    });
  }
}
