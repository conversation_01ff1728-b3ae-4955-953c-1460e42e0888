/**
 * Menu Repository
 * Quản lý các thao tác database cho Menu và MenuItem models
 */

import { Menu, MenuItem, Prisma } from "@prisma/client";
import {
  BaseRepository,
  NotFoundError,
  ConflictError,
} from "./base.repository";

export type MenuCreateInput = Omit<
  Prisma.MenuCreateInput,
  "id" | "createdAt" | "updatedAt"
>;
export type MenuUpdateInput = Partial<MenuCreateInput>;

export type MenuItemCreateInput = Omit<
  Prisma.MenuItemCreateInput,
  "id" | "createdAt" | "updatedAt" | "menu" | "parent"
> & {
  menuId: string;
  parentId?: string;
};

export type MenuItemUpdateInput = Omit<
  Partial<Prisma.MenuItemCreateInput>,
  "id" | "createdAt" | "updatedAt" | "menu" | "parent"
> & {
  parentId?: string;
};

export type MenuWithItems = Prisma.MenuGetPayload<{
  include: {
    items: {
      include: {
        children: true;
      };
    };
  };
}>;

export interface MenuSearchOptions {
  search?: string;
  location?: string;
  isActive?: boolean;
}

export class MenuRepository extends BaseRepository<
  Menu,
  MenuCreateInput,
  MenuUpdateInput
> {
  constructor() {
    super("menu");
  }

  // Tìm menu theo location
  async findByLocation(location: string): Promise<Menu | null> {
    return await this.findFirst({ location });
  }

  // Tìm menu với items
  async findByIdWithItems(id: string): Promise<MenuWithItems | null> {
    return await this.model.findUnique({
      where: { id },
      include: {
        items: {
          where: { parentId: null },
          include: {
            children: {
              include: {
                children: true,
              },
              orderBy: { order: "asc" },
            },
          },
          orderBy: { order: "asc" },
        },
      },
    });
  }

  // Tìm menu theo location với items
  async findByLocationWithItems(
    location: string
  ): Promise<MenuWithItems | null> {
    return await this.model.findFirst({
      where: { location, isActive: true },
      include: {
        items: {
          where: { parentId: null, isActive: true },
          include: {
            children: {
              where: { isActive: true },
              include: {
                children: {
                  where: { isActive: true },
                  orderBy: { order: "asc" },
                },
              },
              orderBy: { order: "asc" },
            },
          },
          orderBy: { order: "asc" },
        },
      },
    });
  }

  // Tạo menu mới
  async createMenu(data: MenuCreateInput): Promise<Menu> {
    // Kiểm tra location đã tồn tại
    if (data.location) {
      const existingMenu = await this.findByLocation(data.location);
      if (existingMenu) {
        throw new ConflictError("Menu location đã tồn tại");
      }
    }

    return await this.create(data);
  }

  // Tìm kiếm menus với filter
  async searchMenus(
    options: MenuSearchOptions & {
      page?: number;
      limit?: number;
    }
  ) {
    const { search, location, isActive, page = 1, limit = 20 } = options;

    const where: Prisma.MenuWhereInput = {};

    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { location: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
      ];
    }

    if (location) {
      where.location = { contains: location, mode: "insensitive" };
    }

    if (typeof isActive === "boolean") {
      where.isActive = isActive;
    }

    return await this.findWithPagination({
      page,
      limit,
      where,
      include: {
        _count: {
          select: { items: true },
        },
      },
      orderBy: { createdAt: "desc" },
    });
  }

  // Lấy active menus
  async getActiveMenus(): Promise<Menu[]> {
    return await this.findMany({
      where: { isActive: true },
      include: {
        _count: {
          select: { items: true },
        },
      },
      orderBy: { name: "asc" },
    });
  }

  // Activate menu
  async activateMenu(id: string): Promise<Menu> {
    return await this.update(id, { isActive: true });
  }

  // Deactivate menu
  async deactivateMenu(id: string): Promise<Menu> {
    return await this.update(id, { isActive: false });
  }

  // Lấy thống kê menus
  async getMenuStats() {
    const [total, active, inactive, totalItems] = await Promise.all([
      this.count(),
      this.count({ isActive: true }),
      this.count({ isActive: false }),
      this.prisma.menuItem.count(),
    ]);

    return {
      total,
      active,
      inactive,
      totalItems,
    };
  }

  // Menu Item Methods

  // Tạo menu item
  async createMenuItem(data: MenuItemCreateInput): Promise<MenuItem> {
    const { menuId, parentId, ...itemData } = data;

    // Kiểm tra menu tồn tại
    const menu = await this.findById(menuId);
    if (!menu) {
      throw new NotFoundError("Menu", menuId);
    }

    // Kiểm tra parent item tồn tại (nếu có)
    if (parentId) {
      const parentItem = await this.prisma.menuItem.findUnique({
        where: { id: parentId },
      });
      if (!parentItem) {
        throw new NotFoundError("Parent MenuItem", parentId);
      }
    }

    const createData: Prisma.MenuItemCreateInput = {
      ...itemData,
      menu: { connect: { id: menuId } },
      ...(parentId && { parent: { connect: { id: parentId } } }),
    };

    return await this.prisma.menuItem.create({ data: createData });
  }

  // Cập nhật menu item
  async updateMenuItem(
    id: string,
    data: MenuItemUpdateInput
  ): Promise<MenuItem> {
    const item = await this.prisma.menuItem.findUnique({
      where: { id },
    });
    if (!item) {
      throw new NotFoundError("MenuItem", id);
    }

    const { parentId, ...updateData } = data;

    // Kiểm tra parent item tồn tại (nếu có)
    if (parentId !== undefined) {
      if (parentId) {
        const parentItem = await this.prisma.menuItem.findUnique({
          where: { id: parentId },
        });
        if (!parentItem) {
          throw new NotFoundError("Parent MenuItem", parentId);
        }
      }
    }

    const prismaUpdateData: Prisma.MenuItemUpdateInput = {
      ...updateData,
      ...(parentId !== undefined && {
        parent: parentId ? { connect: { id: parentId } } : { disconnect: true },
      }),
    };

    return await this.prisma.menuItem.update({
      where: { id },
      data: prismaUpdateData,
    });
  }

  // Xóa menu item
  async deleteMenuItem(id: string): Promise<MenuItem> {
    const item = await this.prisma.menuItem.findUnique({
      where: { id },
      include: { children: true },
    });

    if (!item) {
      throw new NotFoundError("MenuItem", id);
    }

    // Xóa children trước
    if (item.children.length > 0) {
      await this.prisma.menuItem.deleteMany({
        where: { parentId: id },
      });
    }

    return await this.prisma.menuItem.delete({
      where: { id },
    });
  }

  // Lấy menu items của menu
  async getMenuItems(menuId: string): Promise<MenuItem[]> {
    return await this.prisma.menuItem.findMany({
      where: { menuId },
      include: {
        children: {
          include: {
            children: true,
          },
          orderBy: { order: "asc" },
        },
      },
      orderBy: { order: "asc" },
    });
  }

  // Reorder menu items
  async reorderMenuItems(
    itemOrders: Array<{ id: string; order: number }>
  ): Promise<void> {
    for (const { id, order } of itemOrders) {
      await this.prisma.menuItem.update({
        where: { id },
        data: { order },
      });
    }
  }

  // Move menu item to different parent
  async moveMenuItem(
    id: string,
    newParentId: string | null
  ): Promise<MenuItem> {
    const item = await this.prisma.menuItem.findUnique({
      where: { id },
    });

    if (!item) {
      throw new NotFoundError("MenuItem", id);
    }

    // Kiểm tra không tạo circular reference
    if (newParentId) {
      const breadcrumb = await this.getMenuItemBreadcrumb(newParentId);
      if (breadcrumb.some((breadcrumbItem) => breadcrumbItem.id === id)) {
        throw new ConflictError(
          "Không thể di chuyển menu item thành con của chính nó"
        );
      }
    }

    return await this.prisma.menuItem.update({
      where: { id },
      data: { parentId: newParentId },
    });
  }

  // Lấy breadcrumb của menu item
  async getMenuItemBreadcrumb(itemId: string): Promise<MenuItem[]> {
    const path: MenuItem[] = [];
    let currentId: string | null = itemId;

    while (currentId) {
      const item: MenuItem | null = await this.prisma.menuItem.findUnique({
        where: { id: currentId },
      });
      if (!item) break;

      path.unshift(item);
      currentId = item.parentId;
    }

    return path;
  }

  // Duplicate menu
  async duplicateMenu(id: string): Promise<Menu> {
    const menu = await this.findByIdWithItems(id);
    if (!menu) {
      throw new NotFoundError("Menu", id);
    }

    // Tạo menu mới
    const newMenu = await this.create({
      name: `${menu.name} (Copy)`,
      location: `${menu.location}-copy-${Date.now()}`,
      description: menu.description,
      isActive: false,
    });

    // Duplicate menu items
    await this.duplicateMenuItems(menu.items, newMenu.id);

    return newMenu;
  }

  // Duplicate menu items (recursive)
  private async duplicateMenuItems(
    items: any[],
    menuId: string,
    parentId: string | null = null
  ): Promise<void> {
    for (const item of items) {
      const newItem = await this.createMenuItem({
        menuId,
        parentId: parentId || undefined,
        title: item.title,
        url: item.url,
        target: item.target,
        icon: item.icon,
        order: item.order,
        isActive: item.isActive,
      });

      // Duplicate children
      if (item.children && item.children.length > 0) {
        await this.duplicateMenuItems(item.children, menuId, newItem.id);
      }
    }
  }

  // Validate menu structure
  async validateMenuStructure(menuId: string): Promise<{
    valid: boolean;
    issues: string[];
  }> {
    const issues: string[] = [];
    const menu = await this.findByIdWithItems(menuId);

    if (!menu) {
      return { valid: false, issues: ["Menu không tồn tại"] };
    }

    // Kiểm tra circular references
    const checkCircularRef = (
      items: any[],
      visited: Set<string> = new Set()
    ): void => {
      for (const item of items) {
        if (visited.has(item.id)) {
          issues.push(`Circular reference detected for item: ${item.title}`);
          continue;
        }

        visited.add(item.id);
        if (item.children) {
          checkCircularRef(item.children, new Set(visited));
        }
        visited.delete(item.id);
      }
    };

    checkCircularRef(menu.items);

    // Kiểm tra URL format
    const checkUrls = (items: any[]): void => {
      for (const item of items) {
        if (
          item.url &&
          !item.url.startsWith("/") &&
          !item.url.startsWith("http")
        ) {
          issues.push(`Invalid URL format for item: ${item.title}`);
        }

        if (item.children) {
          checkUrls(item.children);
        }
      }
    };

    checkUrls(menu.items);

    return {
      valid: issues.length === 0,
      issues,
    };
  }
}
