/**
 * Order Repository
 * <PERSON><PERSON><PERSON>n lý các thao tác database cho Order model
 */

import { Order, Prisma, OrderStatus, PaymentStatus, PaymentMethod } from "@prisma/client";
import { BaseRepository, NotFoundError } from "./base.repository";

export type OrderCreateInput = Omit<Prisma.OrderCreateInput, 'id' | 'createdAt' | 'updatedAt'>;
export type OrderUpdateInput = Partial<Omit<OrderCreateInput, 'userId'>>;
export type OrderWithRelations = Prisma.OrderGetPayload<{
  include: {
    user: true;
    items: { include: { product: true } };
    PaymentTransaction: true;
  };
}>;

export interface OrderSearchOptions {
  search?: string;
  userId?: string;
  status?: OrderStatus;
  paymentStatus?: PaymentStatus;
  paymentMethod?: PaymentMethod;
  dateFrom?: Date;
  dateTo?: Date;
  minTotal?: number;
  maxTotal?: number;
}

export class OrderRepository extends BaseRepository<Order, OrderCreateInput, OrderUpdateInput> {
  constructor() {
    super('order');
  }

  // Tìm order với đầy đủ relations
  async findByIdWithRelations(id: string): Promise<OrderWithRelations | null> {
    return await this.model.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
            avatar: true
          }
        },
        items: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                slug: true,
                sku: true,
                media: {
                  include: { media: true },
                  where: { isPrimary: true },
                  take: 1
                }
              }
            }
          },
          orderBy: { createdAt: 'asc' }
        },
        PaymentTransaction: {
          orderBy: { createdAt: 'desc' }
        }
      }
    });
  }

  // Tìm orders của user
  async findUserOrders(userId: string, options: {
    page?: number;
    limit?: number;
    status?: OrderStatus;
  } = {}): Promise<any> {
    const { page = 1, limit = 10, status } = options;

    const where: Prisma.OrderWhereInput = { userId };
    if (status) {
      where.status = status;
    }

    return await this.findWithPagination({
      page,
      limit,
      where,
      include: {
        items: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                slug: true,
                media: {
                  include: { media: true },
                  where: { isPrimary: true },
                  take: 1
                }
              }
            }
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });
  }

  // Tìm kiếm orders với filter
  async searchOrders(options: OrderSearchOptions & {
    page?: number;
    limit?: number;
  }) {
    const { 
      search, 
      userId, 
      status, 
      paymentStatus, 
      paymentMethod,
      dateFrom, 
      dateTo,
      minTotal,
      maxTotal,
      page = 1, 
      limit = 20 
    } = options;

    const where: Prisma.OrderWhereInput = {};

    if (search) {
      where.OR = [
        { id: { contains: search, mode: 'insensitive' } },
        { user: { 
          OR: [
            { name: { contains: search, mode: 'insensitive' } },
            { email: { contains: search, mode: 'insensitive' } },
            { phone: { contains: search, mode: 'insensitive' } }
          ]
        }}
      ];
    }

    if (userId) {
      where.userId = userId;
    }

    if (status) {
      where.status = status;
    }

    if (paymentStatus) {
      where.paymentStatus = paymentStatus;
    }

    if (paymentMethod) {
      where.paymentMethod = paymentMethod;
    }

    if (dateFrom || dateTo) {
      where.createdAt = {};
      if (dateFrom) where.createdAt.gte = dateFrom;
      if (dateTo) where.createdAt.lte = dateTo;
    }

    if (minTotal !== undefined || maxTotal !== undefined) {
      where.total = {};
      if (minTotal !== undefined) where.total.gte = minTotal;
      if (maxTotal !== undefined) where.total.lte = maxTotal;
    }

    return await this.findWithPagination({
      page,
      limit,
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true
          }
        },
        items: {
          select: {
            id: true,
            quantity: true,
            price: true,
            total: true,
            product: {
              select: {
                id: true,
                name: true,
                slug: true
              }
            }
          }
        },
        _count: {
          select: { items: true }
        }
      },
      orderBy: { createdAt: 'desc' }
    });
  }

  // Cập nhật status order
  async updateStatus(id: string, status: OrderStatus): Promise<Order> {
    const order = await this.findById(id);
    if (!order) {
      throw new NotFoundError('Order', id);
    }

    return await this.update(id, { status });
  }

  // Cập nhật payment status
  async updatePaymentStatus(id: string, paymentStatus: PaymentStatus): Promise<Order> {
    const order = await this.findById(id);
    if (!order) {
      throw new NotFoundError('Order', id);
    }

    return await this.update(id, { paymentStatus });
  }

  // Lấy orders theo status
  async getOrdersByStatus(status: OrderStatus, limit?: number): Promise<Order[]> {
    return await this.findMany({
      where: { status },
      include: {
        user: {
          select: { id: true, name: true, email: true }
        },
        _count: {
          select: { items: true }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: limit
    });
  }

  // Lấy pending orders
  async getPendingOrders(): Promise<Order[]> {
    return await this.getOrdersByStatus('PENDING');
  }

  // Lấy recent orders
  async getRecentOrders(limit: number = 10): Promise<Order[]> {
    return await this.findMany({
      include: {
        user: {
          select: { id: true, name: true, email: true }
        },
        _count: {
          select: { items: true }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: limit
    });
  }

  // Lấy thống kê orders
  async getOrderStats() {
    const [
      total,
      pending,
      confirmed,
      processing,
      shipped,
      delivered,
      cancelled,
      totalRevenue,
      thisMonthRevenue
    ] = await Promise.all([
      this.count(),
      this.count({ status: 'PENDING' }),
      this.count({ status: 'CONFIRMED' }),
      this.count({ status: 'PROCESSING' }),
      this.count({ status: 'SHIPPED' }),
      this.count({ status: 'DELIVERED' }),
      this.count({ status: 'CANCELLED' }),
      this.prisma.order.aggregate({
        where: { status: { not: 'CANCELLED' } },
        _sum: { total: true }
      }),
      this.prisma.order.aggregate({
        where: {
          status: { not: 'CANCELLED' },
          createdAt: {
            gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
          }
        },
        _sum: { total: true }
      })
    ]);

    return {
      total,
      pending,
      confirmed,
      processing,
      shipped,
      delivered,
      cancelled,
      totalRevenue: totalRevenue._sum.total || 0,
      thisMonthRevenue: thisMonthRevenue._sum.total || 0
    };
  }

  // Lấy revenue theo ngày
  async getDailyRevenue(days: number = 30): Promise<any[]> {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    return await this.prisma.$queryRaw`
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as order_count,
        SUM(total) as revenue
      FROM orders
      WHERE created_at >= ${startDate}
        AND status != 'CANCELLED'
      GROUP BY DATE(created_at)
      ORDER BY date DESC
    `;
  }

  // Lấy top customers
  async getTopCustomers(limit: number = 10): Promise<any[]> {
    return await this.prisma.$queryRaw`
      SELECT 
        u.id,
        u.name,
        u.email,
        COUNT(o.id) as order_count,
        SUM(o.total) as total_spent
      FROM users u
      INNER JOIN orders o ON u.id = o.user_id
      WHERE o.status != 'CANCELLED'
      GROUP BY u.id, u.name, u.email
      ORDER BY total_spent DESC
      LIMIT ${limit}
    `;
  }

  // Cancel order
  async cancelOrder(id: string, reason?: string): Promise<Order> {
    const order = await this.findById(id);
    if (!order) {
      throw new NotFoundError('Order', id);
    }

    // Chỉ có thể cancel order ở trạng thái PENDING hoặc CONFIRMED
    if (!['PENDING', 'CONFIRMED'].includes(order.status)) {
      throw new Error('Không thể hủy đơn hàng ở trạng thái này');
    }

    return await this.update(id, { 
      status: 'CANCELLED',
      notes: reason ? `${order.notes || ''}\nLý do hủy: ${reason}` : order.notes
    });
  }

  // Confirm order
  async confirmOrder(id: string): Promise<Order> {
    return await this.updateStatus(id, 'CONFIRMED');
  }

  // Ship order
  async shipOrder(id: string): Promise<Order> {
    return await this.updateStatus(id, 'SHIPPED');
  }

  // Deliver order
  async deliverOrder(id: string): Promise<Order> {
    return await this.updateStatus(id, 'DELIVERED');
  }
}
