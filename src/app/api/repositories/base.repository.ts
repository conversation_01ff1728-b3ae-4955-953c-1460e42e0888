/**
 * Base Repository Interface và Implementation
 * Cung cấp các phương thức CRUD cơ bản cho tất cả repositories
 */

import { PrismaClient } from "@prisma/client";
import { prisma } from "../../../lib/prisma";

// Type for Prisma model delegates
type PrismaModelDelegate = {
  findUnique: (args: any) => Promise<any>;
  findMany: (args?: any) => Promise<any[]>;
  findFirst: (args?: any) => Promise<any>;
  create: (args: any) => Promise<any>;
  update: (args: any) => Promise<any>;
  delete: (args: any) => Promise<any>;
  deleteMany: (args?: any) => Promise<{ count: number }>;
  count: (args?: any) => Promise<number>;
  createMany: (args: any) => Promise<{ count: number }>;
  updateMany: (args: any) => Promise<{ count: number }>;
  upsert: (args: any) => Promise<any>;
};

// Base interface cho tất cả repositories
export interface IBaseRepository<T, CreateInput, UpdateInput> {
  // CRUD Operations
  findById(id: string): Promise<T | null>;
  findMany(options?: FindManyOptions<T>): Promise<T[]>;
  findFirst(where: any): Promise<T | null>;
  create(data: CreateInput): Promise<T>;
  update(id: string, data: UpdateInput): Promise<T>;
  delete(id: string): Promise<T>;
  deleteMany(where: any): Promise<{ count: number }>;
  count(where?: any): Promise<number>;

  // Pagination
  findWithPagination(
    options: PaginationOptions<T>
  ): Promise<PaginatedResult<T>>;

  // Bulk operations
  createMany(data: CreateInput[]): Promise<{ count: number }>;
  updateMany(where: any, data: any): Promise<{ count: number }>;

  // Utility methods
  exists(where: any): Promise<boolean>;
  upsert(where: any, create: CreateInput, update: UpdateInput): Promise<T>;
}

// Types cho pagination
export interface PaginationOptions<_T> {
  page?: number;
  limit?: number;
  where?: any;
  orderBy?: any;
  include?: any;
  select?: any;
}

export interface PaginatedResult<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface FindManyOptions<_T> {
  where?: any;
  orderBy?: any;
  include?: any;
  select?: any;
  take?: number;
  skip?: number;
}

// Base Repository Implementation
export abstract class BaseRepository<T, CreateInput, UpdateInput>
  implements IBaseRepository<T, CreateInput, UpdateInput>
{
  protected prisma: PrismaClient;
  protected modelName: string;

  constructor(modelName: string) {
    this.prisma = prisma;
    this.modelName = modelName;
  }

  // Get Prisma model delegate
  protected get model(): PrismaModelDelegate {
    return (this.prisma as any)[this.modelName] as PrismaModelDelegate;
  }

  async findById(id: string): Promise<T | null> {
    return await this.model.findUnique({
      where: { id },
    });
  }

  async findMany(options: FindManyOptions<T> = {}): Promise<T[]> {
    return await this.model.findMany(options);
  }

  async findFirst(where: any): Promise<T | null> {
    return await this.model.findFirst({ where });
  }

  async create(data: CreateInput): Promise<T> {
    return await this.model.create({ data });
  }

  async update(id: string, data: UpdateInput): Promise<T> {
    return await this.model.update({
      where: { id },
      data,
    });
  }

  async delete(id: string): Promise<T> {
    return await this.model.delete({
      where: { id },
    });
  }

  async deleteMany(where: any): Promise<{ count: number }> {
    return await this.model.deleteMany({ where });
  }

  async count(where: any = {}): Promise<number> {
    return await this.model.count({ where });
  }

  async findWithPagination(
    options: PaginationOptions<T>
  ): Promise<PaginatedResult<T>> {
    const {
      page = 1,
      limit = 10,
      where = {},
      orderBy = { createdAt: "desc" },
      include,
      select,
    } = options;

    const skip = (page - 1) * limit;

    const [data, total] = await Promise.all([
      this.model.findMany({
        where,
        orderBy,
        include,
        select,
        skip,
        take: limit,
      }),
      this.model.count({ where }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      data,
      total,
      page,
      limit,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  async createMany(data: CreateInput[]): Promise<{ count: number }> {
    return await this.model.createMany({ data });
  }

  async updateMany(where: any, data: any): Promise<{ count: number }> {
    return await this.model.updateMany({ where, data });
  }

  async exists(where: any): Promise<boolean> {
    const count = await this.model.count({ where });
    return count > 0;
  }

  async upsert(
    where: any,
    create: CreateInput,
    update: UpdateInput
  ): Promise<T> {
    return await this.model.upsert({
      where,
      create,
      update,
    });
  }

  // Transaction support
  async transaction<R>(fn: (tx: any) => Promise<R>): Promise<R> {
    return await this.prisma.$transaction(fn);
  }

  // Raw query support
  async executeRaw(query: string, ..._values: any[]): Promise<any> {
    return await this.prisma.$executeRaw`${query}`;
  }

  async queryRaw(query: string, ...values: any[]): Promise<any> {
    return await this.prisma.$queryRaw`${query}`;
  }
}

// Error handling
export class RepositoryError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number = 500
  ) {
    super(message);
    this.name = "RepositoryError";
  }
}

export class NotFoundError extends RepositoryError {
  constructor(resource: string, id?: string) {
    super(
      `${resource}${id ? ` with id ${id}` : ""} not found`,
      "NOT_FOUND",
      404
    );
  }
}

export class ValidationError extends RepositoryError {
  constructor(
    message: string,
    public details?: any
  ) {
    super(message, "VALIDATION_ERROR", 400);
  }
}

export class ConflictError extends RepositoryError {
  constructor(message: string) {
    super(message, "CONFLICT", 409);
  }
}
