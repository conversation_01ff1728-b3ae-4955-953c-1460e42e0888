/**
 * Setting Repository
 * Quản lý các thao tác database cho Setting model
 */

import { Setting, Prisma } from "@prisma/client";
import {
  BaseRepository,
  NotFoundError,
  ConflictError,
} from "./base.repository";

export type SettingCreateInput = Omit<
  Prisma.SettingCreateInput,
  "id" | "createdAt" | "updatedAt"
>;
export type SettingUpdateInput = Partial<SettingCreateInput>;

export interface SettingSearchOptions {
  search?: string;
  // group?: string; // Setting không có group field
  // isPublic?: boolean; // Setting không có isPublic field
}

export class SettingRepository extends BaseRepository<
  Setting,
  SettingCreateInput,
  SettingUpdateInput
> {
  constructor() {
    super("setting");
  }

  // Tìm setting theo key
  async findByKey(key: string): Promise<Setting | null> {
    return await this.findFirst({ key });
  }

  // Lấy value của setting theo key
  async getValue(key: string, defaultValue?: any): Promise<any> {
    const setting = await this.findByKey(key);
    if (!setting) return defaultValue;

    // setting.value đã là JsonValue, không cần parse
    return setting.value;
  }

  // Set value cho setting
  async setValue(key: string, value: any, _group?: string): Promise<Setting> {
    const stringValue =
      typeof value === "string" ? value : JSON.stringify(value);

    const existingSetting = await this.findByKey(key);

    if (existingSetting) {
      return await this.update(existingSetting.id, { value: stringValue });
    } else {
      return await this.create({
        key,
        value: stringValue,
        type: "string",
      });
    }
  }

  // Tạo setting mới
  async createSetting(data: SettingCreateInput): Promise<Setting> {
    // Kiểm tra key đã tồn tại
    const existingSetting = await this.findByKey(data.key);
    if (existingSetting) {
      throw new ConflictError("Setting key đã tồn tại");
    }

    return await this.create(data);
  }

  // Tìm kiếm settings với filter
  async searchSettings(
    options: SettingSearchOptions & {
      page?: number;
      limit?: number;
    }
  ) {
    const { search, page = 1, limit = 50 } = options;

    const where: Prisma.SettingWhereInput = {};

    if (search) {
      where.OR = [
        { key: { contains: search, mode: "insensitive" } },
        // Setting không có description field và value là JsonValue không support contains
      ];
    }

    // Setting không có group field
    // if (group) {
    //   where.group = group;
    // }

    // Setting không có isPublic field
    // if (typeof isPublic === "boolean") {
    //   where.isPublic = isPublic;
    // }

    return await this.findWithPagination({
      page,
      limit,
      where,
      orderBy: { key: "asc" },
    });
  }

  // Lấy settings theo group (Setting không có group field)
  async getSettingsByGroup(_group: string): Promise<Setting[]> {
    // Setting không có group field trong schema
    return [];
  }

  // Lấy public settings (Setting không có isPublic field)
  async getPublicSettings(): Promise<Record<string, any>> {
    // Setting không có isPublic field, trả về tất cả settings
    const settings = await this.findMany({
      select: { key: true, value: true },
    });

    const result: Record<string, any> = {};
    settings.forEach((setting) => {
      result[setting.key] = setting.value;
    });

    return result;
  }

  // Lấy all settings as object
  async getAllSettingsAsObject(): Promise<Record<string, any>> {
    const settings = await this.findMany({
      select: { key: true, value: true },
    });

    const result: Record<string, any> = {};
    settings.forEach((setting) => {
      result[setting.key] = setting.value;
    });

    return result;
  }

  // Bulk update settings
  async bulkUpdateSettings(
    updates: Array<{ key: string; value: any }>
  ): Promise<void> {
    for (const { key, value } of updates) {
      await this.setValue(key, value);
    }
  }

  // Lấy all groups (Setting không có group field)
  async getAllGroups(): Promise<string[]> {
    // Setting không có group field trong schema
    return [];
  }

  // Reset setting to default (Setting không có defaultValue field)
  async resetToDefault(key: string): Promise<Setting | null> {
    const setting = await this.findByKey(key);
    if (!setting) return null;

    // Không có defaultValue field trong schema, trả về null
    return null;
  }

  // Backup settings
  async backupSettings(): Promise<Record<string, any>> {
    const settings = await this.findMany({
      select: { key: true, value: true, type: true },
    });

    const backup: Record<string, any> = {};
    settings.forEach((setting) => {
      backup[setting.key] = {
        value: setting.value,
        type: setting.type,
      };
    });

    return backup;
  }

  // Restore settings from backup
  async restoreSettings(backup: Record<string, any>): Promise<void> {
    for (const [key, data] of Object.entries(backup)) {
      await this.setValue(key, data.value, data.group);
    }
  }

  // Validate setting value
  validateSettingValue(
    key: string,
    value: any
  ): { valid: boolean; error?: string } {
    // Basic validation rules
    const validationRules: Record<string, (value: any) => boolean> = {
      "site.email": (val) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val),
      "site.phone": (val) => /^[0-9+\-\s()]+$/.test(val),
      "site.url": (val) => /^https?:\/\/.+/.test(val),
      "pagination.limit": (val) =>
        Number.isInteger(val) && val > 0 && val <= 100,
      "cache.ttl": (val) => Number.isInteger(val) && val >= 0,
    };

    const validator = validationRules[key];
    if (validator && !validator(value)) {
      return { valid: false, error: `Invalid value for ${key}` };
    }

    return { valid: true };
  }

  // Get setting with validation
  async getValidatedValue<T>(key: string, defaultValue: T): Promise<T> {
    const value = await this.getValue(key, defaultValue);
    const validation = this.validateSettingValue(key, value);

    if (!validation.valid) {
      console.warn(
        `Invalid setting value for ${key}, using default:`,
        validation.error
      );
      return defaultValue;
    }

    return value;
  }

  // Common setting getters
  async getSiteName(): Promise<string> {
    return await this.getValue("site.name", "NS Shop");
  }

  async getSiteDescription(): Promise<string> {
    return await this.getValue(
      "site.description",
      "Fashion E-commerce Platform"
    );
  }

  async getSiteEmail(): Promise<string> {
    return await this.getValue("site.email", "<EMAIL>");
  }

  async getSitePhone(): Promise<string> {
    return await this.getValue("site.phone", "");
  }

  async getSiteUrl(): Promise<string> {
    return await this.getValue("site.url", "http://localhost:3000");
  }

  async getPaginationLimit(): Promise<number> {
    return await this.getValidatedValue("pagination.limit", 20);
  }

  async getCacheTTL(): Promise<number> {
    return await this.getValidatedValue("cache.ttl", 3600);
  }

  async getMaintenanceMode(): Promise<boolean> {
    return await this.getValue("maintenance.enabled", false);
  }

  async getMaintenanceMessage(): Promise<string> {
    return await this.getValue(
      "maintenance.message",
      "Site is under maintenance"
    );
  }

  // Common setting setters
  async setSiteName(name: string): Promise<Setting> {
    return await this.setValue("site.name", name, "site");
  }

  async setSiteDescription(description: string): Promise<Setting> {
    return await this.setValue("site.description", description, "site");
  }

  async setSiteEmail(email: string): Promise<Setting> {
    const validation = this.validateSettingValue("site.email", email);
    if (!validation.valid) {
      throw new Error(validation.error);
    }
    return await this.setValue("site.email", email, "site");
  }

  async setMaintenanceMode(enabled: boolean, message?: string): Promise<void> {
    await this.setValue("maintenance.enabled", enabled, "maintenance");
    if (message) {
      await this.setValue("maintenance.message", message, "maintenance");
    }
  }

  // Lấy thống kê settings
  async getSettingStats() {
    const [total, groups, publicSettings, privateSettings] = await Promise.all([
      this.count(),
      this.getAllGroups().then((groups) => groups.length),
      this.count({ isPublic: true }),
      this.count({ isPublic: false }),
    ]);

    return {
      total,
      groups,
      publicSettings,
      privateSettings,
    };
  }
}
