/**
 * Product Repository
 * Quản lý các thao tác database cho Product model
 */

import { Product, Prisma, ProductStatus } from "@prisma/client";
import {
  BaseRepository,
  NotFoundError,
  ConflictError,
} from "./base.repository";

// Custom input types that use simple IDs instead of Prisma relations
export interface ProductCreateInput {
  name: string;
  description: string;
  price: number;
  salePrice?: number;
  categoryId: string;
  brandId?: string;
  stock?: number;
  sku: string;
  slug: string;
  featured?: boolean;
  status?: ProductStatus;
  tags?: string[];
  avgRating?: number;
  reviewCount?: number;
}

export type ProductUpdateInput = Partial<ProductCreateInput>;
export type ProductWithRelations = Prisma.ProductGetPayload<{
  include: {
    category: true;
    brand: true;
    media: { include: { media: true } };
    reviews: { include: { user: true } };
    inventoryEntries: true;
    ProductAttribute: { include: { attribute: true; attributeValue: true } };
  };
}>;

export interface ProductSearchOptions {
  search?: string;
  categoryId?: string;
  brandId?: string;
  status?: ProductStatus;
  featured?: boolean;
  priceMin?: number;
  priceMax?: number;
  inStock?: boolean;
  tags?: string[];
}

export class ProductRepository extends BaseRepository<
  Product,
  ProductCreateInput,
  ProductUpdateInput
> {
  constructor() {
    super("product");
  }

  // Override create method để handle relations
  async create(data: ProductCreateInput): Promise<Product> {
    const { categoryId, brandId, ...productData } = data;

    const createData: Prisma.ProductCreateInput = {
      ...productData,
      category: { connect: { id: categoryId } },
      ...(brandId && { brand: { connect: { id: brandId } } }),
    };

    return await this.prisma.product.create({ data: createData });
  }

  // Tìm product theo slug
  async findBySlug(slug: string): Promise<Product | null> {
    return await this.model.findUnique({
      where: { slug },
    });
  }

  // Tìm product theo SKU
  async findBySku(sku: string): Promise<Product | null> {
    return await this.model.findUnique({
      where: { sku },
    });
  }

  // Tìm product với đầy đủ relations
  async findByIdWithRelations(
    id: string
  ): Promise<ProductWithRelations | null> {
    return await this.model.findUnique({
      where: { id },
      include: {
        category: true,
        brand: true,
        media: {
          include: { media: true },
          orderBy: { order: "asc" },
        },
        reviews: {
          include: {
            user: {
              select: { id: true, name: true, avatar: true },
            },
            media: {
              include: { media: true },
            },
          },
          where: { status: "APPROVED" },
          orderBy: { createdAt: "desc" },
        },
        inventoryEntries: true,
        ProductAttribute: {
          include: {
            attribute: true,
            attributeValue: true,
          },
        },
      },
    });
  }

  // Tìm product theo slug với relations
  async findBySlugWithRelations(
    slug: string
  ): Promise<ProductWithRelations | null> {
    return await this.model.findUnique({
      where: { slug },
      include: {
        category: true,
        brand: true,
        media: {
          include: { media: true },
          orderBy: { order: "asc" },
        },
        reviews: {
          include: {
            user: {
              select: { id: true, name: true, avatar: true },
            },
          },
          where: { status: "APPROVED" },
          orderBy: { createdAt: "desc" },
        },
        inventoryEntries: true,
        ProductAttribute: {
          include: {
            attribute: true,
            attributeValue: true,
          },
        },
      },
    });
  }

  // Tạo product mới với slug unique
  async createProduct(data: ProductCreateInput): Promise<Product> {
    // Kiểm tra SKU đã tồn tại
    const existingSku = await this.findBySku(data.sku);
    if (existingSku) {
      throw new ConflictError("SKU đã tồn tại");
    }

    // Kiểm tra slug đã tồn tại
    if (data.slug) {
      const existingSlug = await this.findBySlug(data.slug);
      if (existingSlug) {
        throw new ConflictError("Slug đã tồn tại");
      }
    }

    return await this.create(data);
  }

  // Tạo product với auto-generated slug
  async createProductWithAutoSlug(
    data: Omit<ProductCreateInput, "slug"> & { name: string }
  ): Promise<Product> {
    // Generate slug from name
    const baseSlug = data.name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, "")
      .replace(/\s+/g, "-")
      .replace(/-+/g, "-")
      .trim();

    // Find unique slug
    let finalSlug = baseSlug;
    let counter = 1;
    while (await this.findBySlug(finalSlug)) {
      finalSlug = `${baseSlug}-${counter}`;
      counter++;
    }

    return await this.createProduct({
      ...data,
      slug: finalSlug,
    });
  }

  // Tìm kiếm products với filter
  async searchProducts(
    options: ProductSearchOptions & {
      page?: number;
      limit?: number;
      sortBy?: string;
      sortOrder?: "asc" | "desc";
    }
  ) {
    const {
      search,
      categoryId,
      brandId,
      status,
      featured,
      priceMin,
      priceMax,
      inStock,
      tags,
      page = 1,
      limit = 12,
      sortBy = "createdAt",
      sortOrder = "desc",
    } = options;

    const where: Prisma.ProductWhereInput = {};

    // Only add status filter if it's provided, otherwise show all statuses
    if (status) {
      where.status = status;
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
        { sku: { contains: search, mode: "insensitive" } },
      ];
    }

    if (categoryId) {
      where.categoryId = categoryId;
    }

    if (brandId) {
      where.brandId = brandId;
    }

    if (typeof featured === "boolean") {
      where.featured = featured;
    }

    if (priceMin !== undefined || priceMax !== undefined) {
      where.price = {};
      if (priceMin !== undefined) where.price.gte = priceMin;
      if (priceMax !== undefined) where.price.lte = priceMax;
    }

    if (inStock) {
      where.stock = { gt: 0 };
    }

    if (tags && tags.length > 0) {
      where.tags = { hasSome: tags };
    }

    return await this.findWithPagination({
      page,
      limit,
      where,
      include: {
        category: {
          select: { id: true, name: true, slug: true },
        },
        brand: {
          select: { id: true, name: true, slug: true },
        },
        media: {
          include: { media: true },
          where: { isPrimary: true },
          take: 1,
        },
        _count: {
          select: { reviews: true },
        },
      },
      orderBy: { [sortBy]: sortOrder },
    });
  }

  // Lấy featured products
  async getFeaturedProducts(limit: number = 8): Promise<Product[]> {
    return await this.findMany({
      where: {
        featured: true,
        status: "ACTIVE",
        stock: { gt: 0 },
      },
      include: {
        category: {
          select: { id: true, name: true, slug: true },
        },
        brand: {
          select: { id: true, name: true, slug: true },
        },
        media: {
          include: { media: true },
          where: { isPrimary: true },
          take: 1,
        },
      },
      orderBy: { createdAt: "desc" },
      take: limit,
    });
  }

  // Lấy related products
  async getRelatedProducts(
    productId: string,
    categoryId: string,
    limit: number = 4
  ): Promise<Product[]> {
    return await this.findMany({
      where: {
        id: { not: productId },
        categoryId,
        status: "ACTIVE",
        stock: { gt: 0 },
      },
      include: {
        category: {
          select: { id: true, name: true, slug: true },
        },
        brand: {
          select: { id: true, name: true, slug: true },
        },
        media: {
          include: { media: true },
          where: { isPrimary: true },
          take: 1,
        },
      },
      orderBy: { createdAt: "desc" },
      take: limit,
    });
  }

  // Cập nhật stock
  async updateStock(id: string, quantity: number): Promise<Product> {
    const product = await this.findById(id);
    if (!product) {
      throw new NotFoundError("Product", id);
    }

    return await this.update(id, {
      stock: Math.max(0, product.stock + quantity),
    });
  }

  // Cập nhật rating
  async updateRating(id: string): Promise<Product> {
    const product = await this.model.findUnique({
      where: { id },
      include: {
        reviews: {
          where: { status: "APPROVED" },
          select: { rating: true },
        },
      },
    });

    if (!product) {
      throw new NotFoundError("Product", id);
    }

    const reviews = product.reviews;
    const avgRating =
      reviews.length > 0
        ? reviews.reduce((sum: number, review: any) => sum + review.rating, 0) /
          reviews.length
        : 0;

    return await this.update(id, {
      avgRating: Math.round(avgRating * 10) / 10,
      reviewCount: reviews.length,
    });
  }

  // Lấy thống kê products
  async getProductStats() {
    const [total, active, inactive, outOfStock, featured] = await Promise.all([
      this.count(),
      this.count({ status: "ACTIVE" }),
      this.count({ status: "INACTIVE" }),
      this.count({ stock: 0 }),
      this.count({ featured: true }),
    ]);

    return {
      total,
      active,
      inactive,
      outOfStock,
      featured,
    };
  }

  // Lấy top selling products
  async getTopSellingProducts(limit: number = 10): Promise<any[]> {
    return await this.prisma.$queryRaw`
      SELECT p.*, COUNT(oi.id) as sales_count
      FROM products p
      LEFT JOIN order_items oi ON p.id = oi.product_id
      WHERE p.status = 'ACTIVE'
      GROUP BY p.id
      ORDER BY sales_count DESC
      LIMIT ${limit}
    `;
  }

  // Count products by brand (required by services)
  async countByBrand(brandId: string): Promise<number> {
    return await this.count({ brandId });
  }

  // Soft delete product
  async softDelete(id: string, _deletedBy: string): Promise<Product> {
    return await this.update(id, {
      status: "INACTIVE",
      // Note: Add deletedBy and deletedAt fields to schema if needed
    });
  }

  // Confirm stock for order
  async confirmStock(productId: string, quantity: number): Promise<boolean> {
    const product = await this.findById(productId);
    return product ? product.stock >= quantity : false;
  }

  // Increment view count
  async incrementViewCount(id: string): Promise<void> {
    // Note: Add viewCount field to schema if needed
    // For now, this is a no-op
    console.log(`Incrementing view count for product ${id}`);
  }
}
