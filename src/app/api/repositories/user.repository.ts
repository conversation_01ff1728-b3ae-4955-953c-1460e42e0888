/**
 * User Repository
 * Quản lý các thao tác database cho User model
 */

import { User, Prisma, Gender } from "@prisma/client";
import {
  BaseRepository,
  NotFoundError,
  ConflictError,
} from "./base.repository";
import * as bcrypt from "bcryptjs";

export type UserCreateInput = Omit<
  Prisma.UserCreateInput,
  "id" | "createdAt" | "updatedAt"
>;
export type UserUpdateInput = Partial<UserCreateInput>;
export type UserWithRelations = Prisma.UserGetPayload<{
  include: {
    orders: true;
    addresses: true;
    reviews: true;
    cart: true;
    wishlistItems: true;
    avatar: true;
  };
}>;

export interface UserSearchOptions {
  search?: string;
  isActive?: boolean;
  gender?: string;
  dateFrom?: Date;
  dateTo?: Date;
}

export class UserRepository extends BaseRepository<
  User,
  UserCreateInput,
  UserUpdateInput
> {
  constructor() {
    super("user");
  }

  // Tìm user theo email
  async findByEmail(email: string): Promise<User | null> {
    return await this.model.findUnique({
      where: { email },
    });
  }

  // Tìm user với đầy đủ relations
  async findByIdWithRelations(id: string): Promise<UserWithRelations | null> {
    return await this.model.findUnique({
      where: { id },
      include: {
        orders: {
          orderBy: { createdAt: "desc" },
          take: 10,
        },
        addresses: {
          orderBy: { isDefault: "desc" },
        },
        reviews: {
          include: {
            product: {
              select: { id: true, name: true, slug: true },
            },
          },
          orderBy: { createdAt: "desc" },
        },
        cart: {
          include: {
            items: {
              include: {
                product: {
                  select: { id: true, name: true, price: true, slug: true },
                },
              },
            },
          },
        },
        wishlistItems: {
          include: {
            product: {
              select: { id: true, name: true, price: true, slug: true },
            },
          },
        },
        avatar: true,
      },
    });
  }

  // Tạo user mới với mật khẩu đã hash
  async createUser(
    data: UserCreateInput & { password: string }
  ): Promise<User> {
    // Kiểm tra email đã tồn tại
    const existingUser = await this.findByEmail(data.email);
    if (existingUser) {
      throw new ConflictError("Email đã được sử dụng");
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(data.password, 12);

    return await this.create({
      ...data,
      password: hashedPassword,
    });
  }

  // Cập nhật mật khẩu
  async updatePassword(id: string, newPassword: string): Promise<User> {
    const hashedPassword = await bcrypt.hash(newPassword, 12);

    return await this.update(id, {
      password: hashedPassword,
    });
  }

  // Xác thực mật khẩu
  async verifyPassword(email: string, password: string): Promise<User | null> {
    const user = await this.findByEmail(email);
    if (!user) return null;

    const isValid = await bcrypt.compare(password, user.password);
    return isValid ? user : null;
  }

  // Tìm kiếm users với filter
  async searchUsers(
    options: UserSearchOptions & {
      page?: number;
      limit?: number;
    }
  ) {
    const {
      search,
      isActive,
      gender,
      dateFrom,
      dateTo,
      page = 1,
      limit = 20,
    } = options;

    const where: Prisma.UserWhereInput = {};

    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { email: { contains: search, mode: "insensitive" } },
        { phone: { contains: search, mode: "insensitive" } },
      ];
    }

    if (typeof isActive === "boolean") {
      where.isActive = isActive;
    }

    if (gender) {
      where.gender = gender as Gender;
    }

    if (dateFrom || dateTo) {
      where.createdAt = {};
      if (dateFrom) where.createdAt.gte = dateFrom;
      if (dateTo) where.createdAt.lte = dateTo;
    }

    return await this.findWithPagination({
      page,
      limit,
      where,
      include: {
        avatar: true,
        _count: {
          select: {
            orders: true,
            reviews: true,
            addresses: true,
          },
        },
      },
      orderBy: { createdAt: "desc" },
    });
  }

  // Lấy users hoạt động
  async getActiveUsers(limit: number = 10): Promise<User[]> {
    return await this.findMany({
      where: { isActive: true },
      orderBy: { createdAt: "desc" },
      take: limit,
      include: {
        avatar: true,
      },
    });
  }

  // Deactivate user
  async deactivateUser(id: string): Promise<User> {
    const user = await this.findById(id);
    if (!user) {
      throw new NotFoundError("User", id);
    }

    return await this.update(id, { isActive: false });
  }

  // Activate user
  async activateUser(id: string): Promise<User> {
    const user = await this.findById(id);
    if (!user) {
      throw new NotFoundError("User", id);
    }

    return await this.update(id, { isActive: true });
  }

  // Lấy thống kê user
  async getUserStats() {
    const [total, active, inactive, thisMonth] = await Promise.all([
      this.count(),
      this.count({ isActive: true }),
      this.count({ isActive: false }),
      this.count({
        createdAt: {
          gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
        },
      }),
    ]);

    return {
      total,
      active,
      inactive,
      thisMonth,
    };
  }

  // Soft delete user (deactivate)
  async softDelete(id: string): Promise<User> {
    return await this.deactivateUser(id);
  }
}
