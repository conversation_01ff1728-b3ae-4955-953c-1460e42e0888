/**
 * Inventory Repository
 * Quản lý các thao tác database cho Inventory và StockMovement models
 */

import {
  InventoryEntry,
  StockMovement,
  Prisma,
  StockMovementType,
} from "@prisma/client";
import {
  BaseRepository,
  NotFoundError,
  ConflictError,
} from "./base.repository";

export type InventoryCreateInput = Omit<
  Prisma.InventoryEntryCreateInput,
  "id" | "createdAt" | "updatedAt" | "product"
> & {
  productId: string;
};
export type InventoryUpdateInput = Partial<InventoryCreateInput>;
export type StockMovementCreateInput = Omit<
  Prisma.StockMovementCreateInput,
  "id" | "createdAt" | "inventoryEntry"
> & {
  inventoryEntryId: string;
};

export type InventoryWithProduct = Prisma.InventoryEntryGetPayload<{
  include: {
    product: true;
  };
}>;

export interface InventorySearchOptions {
  search?: string;
  lowStock?: boolean;
  outOfStock?: boolean;
  productId?: string;
}

export class InventoryRepository extends BaseRepository<
  InventoryEntry,
  InventoryCreateInput,
  InventoryUpdateInput
> {
  constructor() {
    super("inventoryEntry");
  }

  // Tìm inventory theo product
  async findByProductId(productId: string): Promise<InventoryEntry | null> {
    return await this.findFirst({ productId });
  }

  // Tìm inventory với product info
  async findByIdWithProduct(id: string): Promise<InventoryWithProduct | null> {
    return await this.model.findUnique({
      where: { id },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            sku: true,
            price: true,
            images: true,
          },
        },
      },
    });
  }

  // Tạo inventory mới
  async createInventory(data: InventoryCreateInput): Promise<InventoryEntry> {
    // Kiểm tra product tồn tại
    const product = await this.prisma.product.findUnique({
      where: { id: data.productId },
    });

    if (!product) {
      throw new NotFoundError("Product", data.productId);
    }

    // Kiểm tra inventory đã tồn tại
    const existingInventory = await this.findByProductId(data.productId);
    if (existingInventory) {
      throw new ConflictError("Inventory cho product này đã tồn tại");
    }

    return await this.create(data);
  }

  // Tìm kiếm inventory với filter
  async searchInventory(
    options: InventorySearchOptions & {
      page?: number;
      limit?: number;
    }
  ) {
    const {
      search,
      lowStock,
      outOfStock,
      productId,
      page = 1,
      limit = 20,
    } = options;

    const where: Prisma.InventoryEntryWhereInput = {};

    if (search) {
      where.product = {
        OR: [
          { name: { contains: search, mode: "insensitive" } },
          { sku: { contains: search, mode: "insensitive" } },
        ],
      };
    }

    if (productId) {
      where.productId = productId;
    }

    if (lowStock) {
      // Use raw SQL for comparing quantity with minStock
      where.AND = [
        { quantity: { lte: 10 } }, // Default low stock threshold
        { quantity: { gt: 0 } },
      ];
    }

    if (outOfStock) {
      where.quantity = { lte: 0 };
    }

    return await this.findWithPagination({
      page,
      limit,
      where,
      include: {
        product: {
          select: {
            id: true,
            name: true,
            sku: true,
            price: true,
            images: true,
          },
        },
      },
      orderBy: { updatedAt: "desc" },
    });
  }

  // Lấy low stock items
  async getLowStockItems(): Promise<InventoryWithProduct[]> {
    return await this.model.findMany({
      where: {
        OR: [
          { quantity: { lte: this.prisma.$queryRaw`low_stock_threshold` } },
          { quantity: { lte: 10 } }, // fallback threshold
        ],
      },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            sku: true,
            price: true,
            images: true,
          },
        },
      },
      orderBy: { quantity: "asc" },
    });
  }

  // Lấy out of stock items
  async getOutOfStockItems(): Promise<InventoryWithProduct[]> {
    return await this.model.findMany({
      where: { quantity: { lte: 0 } },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            sku: true,
            price: true,
            images: true,
          },
        },
      },
      orderBy: { updatedAt: "desc" },
    });
  }

  // Cập nhật stock
  async updateStock(
    productId: string,
    quantity: number,
    reason?: string
  ): Promise<InventoryEntry> {
    const inventory = await this.findByProductId(productId);
    if (!inventory) {
      throw new NotFoundError("Inventory", productId);
    }

    const oldQuantity = inventory.quantity;
    const newQuantity = quantity;

    // Cập nhật inventory
    const updatedInventory = await this.update(inventory.id, {
      quantity: newQuantity,
    });

    // Tạo stock movement record
    await this.createStockMovement({
      inventoryEntryId: inventory.id,
      type: newQuantity > oldQuantity ? "IN" : "OUT",
      quantity: Math.abs(newQuantity - oldQuantity),
      reason: reason || "Manual adjustment",
    });

    return updatedInventory;
  }

  // Tăng stock
  async increaseStock(
    productId: string,
    quantity: number,
    reason?: string
  ): Promise<InventoryEntry> {
    const inventory = await this.findByProductId(productId);
    if (!inventory) {
      throw new NotFoundError("Inventory", productId);
    }

    const newQuantity = inventory.quantity + quantity;
    return await this.updateStock(
      productId,
      newQuantity,
      reason || "Stock increase"
    );
  }

  // Giảm stock
  async decreaseStock(
    productId: string,
    quantity: number,
    reason?: string
  ): Promise<InventoryEntry> {
    const inventory = await this.findByProductId(productId);
    if (!inventory) {
      throw new NotFoundError("Inventory", productId);
    }

    const newQuantity = Math.max(0, inventory.quantity - quantity);
    return await this.updateStock(
      productId,
      newQuantity,
      reason || "Stock decrease"
    );
  }

  // Reserve stock (cho orders)
  async reserveStock(
    productId: string,
    quantity: number
  ): Promise<InventoryEntry> {
    const inventory = await this.findByProductId(productId);
    if (!inventory) {
      throw new NotFoundError("Inventory", productId);
    }

    if (inventory.quantity < quantity) {
      throw new ConflictError("Không đủ stock để reserve");
    }

    return await this.update(inventory.id, {
      quantity: inventory.quantity - quantity,
      reserved: inventory.reserved + quantity,
    });
  }

  // Release reserved stock
  async releaseReservedStock(
    productId: string,
    quantity: number
  ): Promise<InventoryEntry> {
    const inventory = await this.findByProductId(productId);
    if (!inventory) {
      throw new NotFoundError("Inventory", productId);
    }

    return await this.update(inventory.id, {
      quantity: inventory.quantity + quantity,
      reserved: Math.max(0, inventory.reserved - quantity),
    });
  }

  // StockMovement Methods

  // Tạo stock movement
  async createStockMovement(
    data: StockMovementCreateInput
  ): Promise<StockMovement> {
    const { inventoryEntryId, ...createData } = data;
    return await this.prisma.stockMovement.create({
      data: {
        ...createData,
        inventoryEntry: { connect: { id: inventoryEntryId } },
      },
    });
  }

  // Lấy stock movements của product
  async getProductStockMovements(
    productId: string,
    options: {
      page?: number;
      limit?: number;
      type?: StockMovementType;
    } = {}
  ): Promise<any> {
    const { page = 1, limit = 20, type } = options;

    const inventory = await this.findByProductId(productId);
    if (!inventory) {
      throw new NotFoundError("Inventory", productId);
    }

    const where: Prisma.StockMovementWhereInput = {
      inventoryEntryId: inventory.id,
    };
    if (type) where.type = type;

    return await this.prisma.stockMovement.findMany({
      where,
      orderBy: { createdAt: "desc" },
      skip: (page - 1) * limit,
      take: limit,
    });
  }

  // Lấy recent stock movements
  async getRecentStockMovements(limit: number = 20): Promise<StockMovement[]> {
    return await this.prisma.stockMovement.findMany({
      include: {
        inventoryEntry: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                sku: true,
              },
            },
          },
        },
      },
      orderBy: { createdAt: "desc" },
      take: limit,
    });
  }

  // Lấy thống kê inventory
  async getInventoryStats() {
    const [
      totalProducts,
      totalStock,
      totalReserved,
      lowStockCount,
      outOfStockCount,
      totalValue,
    ] = await Promise.all([
      this.count(),
      this.prisma.inventoryEntry.aggregate({
        _sum: { quantity: true },
      }),
      this.prisma.inventoryEntry.aggregate({
        _sum: { reserved: true },
      }),
      this.count({
        quantity: { lte: 10 }, // Assuming 10 as low stock threshold
      }),
      this.count({
        quantity: { lte: 0 },
      }),
      this.prisma.$queryRaw<Array<{ total: number }>>`
        SELECT SUM(i.quantity * p.price) as total
        FROM inventory_entries i
        JOIN products p ON i.product_id = p.id
      `,
    ]);

    return {
      totalProducts,
      totalStock: totalStock._sum.quantity || 0,
      totalReserved: totalReserved._sum.reserved || 0,
      lowStockCount,
      outOfStockCount,
      totalValue: totalValue[0]?.total || 0,
    };
  }

  // Bulk update inventory
  async bulkUpdateInventory(
    updates: Array<{
      productId: string;
      quantity: number;
      reason?: string;
    }>
  ): Promise<void> {
    for (const update of updates) {
      await this.updateStock(update.productId, update.quantity, update.reason);
    }
  }

  // Check stock availability
  async checkStockAvailability(
    productId: string,
    requiredQuantity: number
  ): Promise<{
    available: boolean;
    currentStock: number;
    shortfall: number;
  }> {
    const inventory = await this.findByProductId(productId);

    if (!inventory) {
      return {
        available: false,
        currentStock: 0,
        shortfall: requiredQuantity,
      };
    }

    const availableStock = inventory.quantity - inventory.reserved;
    const available = availableStock >= requiredQuantity;
    const shortfall = available ? 0 : requiredQuantity - availableStock;

    return {
      available,
      currentStock: availableStock,
      shortfall,
    };
  }

  // Get inventory valuation
  async getInventoryValuation(): Promise<
    Array<{
      productId: string;
      productName: string;
      quantity: number;
      unitPrice: number;
      totalValue: number;
    }>
  > {
    return await this.prisma.$queryRaw`
      SELECT 
        i.product_id as "productId",
        p.name as "productName",
        i.quantity,
        p.price as "unitPrice",
        (i.quantity * p.price) as "totalValue"
      FROM inventories i
      JOIN products p ON i.product_id = p.id
      WHERE i.quantity > 0
      ORDER BY "totalValue" DESC
    `;
  }

  // Set low stock threshold
  async setLowStockThreshold(
    productId: string,
    threshold: number
  ): Promise<InventoryEntry> {
    const inventory = await this.findByProductId(productId);
    if (!inventory) {
      throw new NotFoundError("Inventory", productId);
    }

    return await this.update(inventory.id, { minStock: threshold });
  }

  // Find inventory by product and variant (required by services)
  async findByProductAndVariant(
    productId: string,
    variantId?: string
  ): Promise<InventoryEntry | null> {
    return await this.findFirst({
      productId,
      variantId: variantId || null,
    });
  }
}
