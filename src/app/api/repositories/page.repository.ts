/**
 * Page Repository
 * <PERSON><PERSON><PERSON><PERSON> lý các thao tác database cho Page model
 */

import { Page, Prisma, PageStatus } from "@prisma/client";
import {
  BaseRepository,
  NotFoundError,
  ConflictError,
} from "./base.repository";

export type PageCreateInput = Omit<
  Prisma.PageCreateInput,
  "id" | "createdAt" | "updatedAt" | "author" | "featuredImage"
> & {
  authorId: string;
  featuredImageId?: string;
};
export type PageUpdateInput = Partial<PageCreateInput>;
export type PageWithRelations = Prisma.PageGetPayload<{
  include: {
    author: true;
    featuredImage: true;
  };
}>;

export interface PageSearchOptions {
  search?: string;
  authorId?: string;
  status?: PageStatus;
}

export class PageRepository extends BaseRepository<
  Page,
  PageCreateInput,
  PageUpdateInput
> {
  constructor() {
    super("page");
  }

  // Override create method để handle relations
  async create(data: PageCreateInput): Promise<Page> {
    const { authorId, featuredImageId, ...pageData } = data;

    const createData: Prisma.PageCreateInput = {
      ...pageData,
      author: { connect: { id: authorId } },
      ...(featuredImageId && {
        featuredImage: { connect: { id: featuredImageId } },
      }),
    };

    return await this.prisma.page.create({ data: createData });
  }

  // Tìm page theo slug
  async findBySlug(slug: string): Promise<Page | null> {
    return await this.model.findUnique({
      where: { slug },
    });
  }

  // Tìm page với đầy đủ relations
  async findByIdWithRelations(id: string): Promise<PageWithRelations | null> {
    return await this.model.findUnique({
      where: { id },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        featuredImage: true,
      },
    });
  }

  // Tìm page theo slug với relations
  async findBySlugWithRelations(
    slug: string
  ): Promise<PageWithRelations | null> {
    return await this.model.findUnique({
      where: { slug },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
          },
        },
        featuredImage: true,
        parent: {
          select: {
            id: true,
            title: true,
            slug: true,
          },
        },
        children: {
          where: { status: "PUBLISHED" },
          select: {
            id: true,
            title: true,
            slug: true,
            excerpt: true,
          },
          orderBy: { order: "asc" },
        },
      },
    });
  }

  // Tạo page mới với slug unique
  async createPage(data: PageCreateInput): Promise<Page> {
    // Kiểm tra slug đã tồn tại
    if (data.slug) {
      const existingSlug = await this.findBySlug(data.slug);
      if (existingSlug) {
        throw new ConflictError("Slug đã tồn tại");
      }
    }

    // Page không có hierarchical structure

    return await this.create(data);
  }

  // Tìm kiếm pages với filter
  async searchPages(
    options: PageSearchOptions & {
      page?: number;
      limit?: number;
      sortBy?: string;
      sortOrder?: "asc" | "desc";
    }
  ) {
    const {
      search,
      authorId,
      status = "PUBLISHED",
      page = 1,
      limit = 20,
      sortBy = "createdAt",
      sortOrder = "desc",
    } = options;

    const where: Prisma.PageWhereInput = { status };

    if (search) {
      where.OR = [
        { title: { contains: search, mode: "insensitive" } },
        { content: { contains: search, mode: "insensitive" } },
        { excerpt: { contains: search, mode: "insensitive" } },
      ];
    }

    if (authorId) {
      where.authorId = authorId;
    }

    return await this.findWithPagination({
      page,
      limit,
      where,
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
          },
        },
        featuredImage: true,
        parent: {
          select: {
            id: true,
            title: true,
            slug: true,
          },
        },
        _count: {
          select: { children: true },
        },
      },
      orderBy: { [sortBy]: sortOrder },
    });
  }

  // Lấy published pages
  async getPublishedPages(): Promise<Page[]> {
    return await this.findMany({
      where: {
        status: "PUBLISHED",
        publishedAt: { lte: new Date() },
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            avatar: true,
          },
        },
        featuredImage: true,
      },
      orderBy: { order: "asc" },
    });
  }

  // Lấy tất cả published pages (Page không có hierarchical structure)
  async getRootPages(): Promise<Page[]> {
    return await this.findMany({
      where: {
        status: "PUBLISHED",
      },
      include: {
        featuredImage: true,
        author: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: { createdAt: "desc" },
    });
  }

  // Lấy page tree (Page không có hierarchical structure nên trả về flat list)
  async getPageTree(): Promise<Page[]> {
    return await this.getRootPages();
  }

  // Lấy children của một page
  async getChildren(parentId: string): Promise<Page[]> {
    return await this.findMany({
      where: { parentId },
      include: {
        featuredImage: true,
        _count: {
          select: { children: true },
        },
      },
      orderBy: { order: "asc" },
    });
  }

  // Page không có hierarchical structure nên không có breadcrumb
  async getBreadcrumbPath(pageId: string): Promise<Page[]> {
    const page = await this.findById(pageId);
    return page ? [page] : [];
  }

  // Publish page
  async publishPage(id: string): Promise<Page> {
    const page = await this.findById(id);
    if (!page) {
      throw new NotFoundError("Page", id);
    }

    return await this.update(id, {
      status: "PUBLISHED",
    });
  }

  // Unpublish page
  async unpublishPage(id: string): Promise<Page> {
    return await this.update(id, {
      status: "DRAFT",
    });
  }

  // Archive page
  async archivePage(id: string): Promise<Page> {
    return await this.update(id, { status: "ARCHIVED" });
  }

  // Page không có template field
  async getPagesByTemplate(_template: string): Promise<Page[]> {
    return [];
  }

  // Lấy thống kê pages
  async getPageStats() {
    const [total, published, draft, archived, rootPages] = await Promise.all([
      this.count(),
      this.count({ status: "PUBLISHED" }),
      this.count({ status: "DRAFT" }),
      this.count({ status: "ARCHIVED" }),
      this.count({ parentId: null }),
    ]);

    return {
      total,
      published,
      draft,
      archived,
      rootPages,
    };
  }

  // Page không có order field
  async reorderPages(
    _pageOrders: Array<{ id: string; order: number }>
  ): Promise<void> {
    // Page không có order field
  }

  // Page không có hierarchical structure nên không thể di chuyển
  async movePage(id: string, _newParentId: string | null): Promise<Page> {
    const page = await this.findById(id);
    if (!page) {
      throw new NotFoundError("Page", id);
    }
    return page;
  }

  // Duplicate page
  async duplicatePage(id: string, authorId: string): Promise<Page> {
    const page = await this.findById(id);
    if (!page) {
      throw new NotFoundError("Page", id);
    }

    const newPageData: PageCreateInput = {
      title: `${page.title} (Copy)`,
      slug: `${page.slug}-copy-${Date.now()}`,
      content: page.content,
      excerpt: page.excerpt,
      authorId,
      status: "DRAFT",
      metaTitle: page.metaTitle,
      metaDescription: page.metaDescription,
      featuredImageId: page.featuredImageId || undefined,
    };

    return await this.createPage(newPageData);
  }

  // Page không có children nên luôn có thể xóa
  async canDelete(_id: string): Promise<boolean> {
    return true;
  }

  // Xóa page
  async deletePage(id: string): Promise<Page> {
    return await this.delete(id);
  }
}
