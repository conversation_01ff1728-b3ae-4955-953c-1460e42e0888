/**
 * Contact Repository
 * Quản lý các thao tác database cho Contact và ContactInfo models
 */

import { Contact, ContactInfo, Prisma, ContactStatus } from "@prisma/client";
import { BaseRepository, NotFoundError } from "./base.repository";

export type ContactCreateInput = Omit<
  Prisma.ContactCreateInput,
  "id" | "createdAt" | "updatedAt" | "assignedAdmin" | "notes"
> & {
  assignedTo?: string | null;
};
export type ContactUpdateInput = Partial<ContactCreateInput>;
export type ContactInfoCreateInput = Omit<
  Prisma.ContactInfoCreateInput,
  "id" | "createdAt" | "updatedAt"
>;
export type ContactInfoUpdateInput = Partial<ContactInfoCreateInput>;

export interface ContactSearchOptions {
  search?: string;
  status?: ContactStatus;
  dateFrom?: Date;
  dateTo?: Date;
}

export class ContactRepository extends BaseRepository<
  Contact,
  ContactCreateInput,
  ContactUpdateInput
> {
  constructor() {
    super("contact");
  }

  // Tạo contact message
  async createContact(data: ContactCreateInput): Promise<Contact> {
    return await this.create(data);
  }

  // Tìm kiếm contacts với filter
  async searchContacts(
    options: ContactSearchOptions & {
      page?: number;
      limit?: number;
    }
  ) {
    const {
      search,
      status = "PENDING",
      dateFrom,
      dateTo,
      page = 1,
      limit = 20,
    } = options;

    const where: Prisma.ContactWhereInput = {};

    if (status) {
      where.status = status as ContactStatus;
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { email: { contains: search, mode: "insensitive" } },
        { subject: { contains: search, mode: "insensitive" } },
        { message: { contains: search, mode: "insensitive" } },
      ];
    }

    if (dateFrom || dateTo) {
      where.createdAt = {};
      if (dateFrom) where.createdAt.gte = dateFrom;
      if (dateTo) where.createdAt.lte = dateTo;
    }

    return await this.findWithPagination({
      page,
      limit,
      where,
      orderBy: { createdAt: "desc" },
    });
  }

  // Lấy contacts theo status
  async getContactsByStatus(
    status: ContactStatus,
    options: {
      page?: number;
      limit?: number;
    } = {}
  ): Promise<any> {
    const { page = 1, limit = 20 } = options;

    return await this.findWithPagination({
      page,
      limit,
      where: { status },
      orderBy: { createdAt: "desc" },
    });
  }

  // Lấy pending contacts
  async getPendingContacts(): Promise<Contact[]> {
    return await this.findMany({
      where: { status: "PENDING" },
      orderBy: { createdAt: "asc" },
    });
  }

  // Cập nhật status
  async updateStatus(id: string, status: ContactStatus): Promise<Contact> {
    const contact = await this.findById(id);
    if (!contact) {
      throw new NotFoundError("Contact", id);
    }

    return await this.update(id, { status });
  }

  // Reply to contact
  async replyToContact(id: string, reply: string): Promise<Contact> {
    return await this.update(id, {
      status: "RESOLVED",
      responseMessage: reply,
      respondedAt: new Date(),
    });
  }

  // Mark as resolved
  async markAsResolved(id: string): Promise<Contact> {
    return await this.updateStatus(id, "RESOLVED");
  }

  // Lấy thống kê contacts
  async getContactStats() {
    const [total, pending, replied, resolved] = await Promise.all([
      this.count(),
      this.count({ status: "PENDING" }),
      this.count({ status: "REPLIED" }),
      this.count({ status: "RESOLVED" }),
    ]);

    return {
      total,
      pending,
      replied,
      resolved,
    };
  }

  // Lấy recent contacts
  async getRecentContacts(limit: number = 10): Promise<Contact[]> {
    return await this.findMany({
      orderBy: { createdAt: "desc" },
      take: limit,
    });
  }

  // ContactInfo Methods

  // Lấy contact info
  async getContactInfo(): Promise<ContactInfo | null> {
    return await this.prisma.contactInfo.findFirst();
  }

  // Tạo hoặc cập nhật contact info
  async upsertContactInfo(data: ContactInfoCreateInput): Promise<ContactInfo> {
    const existing = await this.getContactInfo();

    if (existing) {
      return await this.prisma.contactInfo.update({
        where: { id: existing.id },
        data,
      });
    } else {
      return await this.prisma.contactInfo.create({ data });
    }
  }

  // Cập nhật contact info
  async updateContactInfo(data: ContactInfoUpdateInput): Promise<ContactInfo> {
    const existing = await this.getContactInfo();

    if (!existing) {
      throw new NotFoundError("ContactInfo", "singleton");
    }

    return await this.prisma.contactInfo.update({
      where: { id: existing.id },
      data,
    });
  }

  // Validate contact data
  validateContactData(data: Partial<ContactCreateInput>): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (!data.name || data.name.trim().length < 2) {
      errors.push("Tên phải có ít nhất 2 ký tự");
    }

    if (!data.email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
      errors.push("Email không hợp lệ");
    }

    if (!data.subject || data.subject.trim().length < 5) {
      errors.push("Tiêu đề phải có ít nhất 5 ký tự");
    }

    if (!data.message || data.message.trim().length < 10) {
      errors.push("Nội dung phải có ít nhất 10 ký tự");
    }

    if (data.phone && !/^[0-9+\-\s()]+$/.test(data.phone)) {
      errors.push("Số điện thoại không hợp lệ");
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  // Validate contact info data
  validateContactInfoData(data: Partial<ContactInfoCreateInput>): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (data.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
      errors.push("Email không hợp lệ");
    }

    if (data.phone && !/^[0-9+\-\s()]+$/.test(data.phone)) {
      errors.push("Số điện thoại không hợp lệ");
    }

    if (data.website && !/^https?:\/\/.+/.test(data.website)) {
      errors.push("Website phải bắt đầu bằng http:// hoặc https://");
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  // Export contacts
  async exportContacts(options: ContactSearchOptions = {}): Promise<Contact[]> {
    const { search, status, dateFrom, dateTo } = options;

    const where: Prisma.ContactWhereInput = {};

    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { email: { contains: search, mode: "insensitive" } },
        { subject: { contains: search, mode: "insensitive" } },
      ];
    }

    if (status) {
      where.status = status;
    }

    if (dateFrom || dateTo) {
      where.createdAt = {};
      if (dateFrom) where.createdAt.gte = dateFrom;
      if (dateTo) where.createdAt.lte = dateTo;
    }

    return await this.findMany({
      where,
      orderBy: { createdAt: "desc" },
    });
  }

  // Bulk update status
  async bulkUpdateStatus(ids: string[], status: ContactStatus): Promise<void> {
    await this.updateMany({ id: { in: ids } }, { status });
  }

  // Delete old contacts
  async deleteOldContacts(olderThanDays: number = 365): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

    const result = await this.deleteMany({
      createdAt: { lt: cutoffDate },
      status: "RESOLVED",
    });

    return result.count;
  }

  // Get contact response time stats
  async getResponseTimeStats(): Promise<{
    avgResponseTime: number;
    totalReplied: number;
  }> {
    const repliedContacts = await this.findMany({
      where: {
        status: "REPLIED",
        repliedAt: { not: null },
      },
      select: {
        createdAt: true,
        repliedAt: true,
      },
    });

    if (repliedContacts.length === 0) {
      return { avgResponseTime: 0, totalReplied: 0 };
    }

    const totalResponseTime = repliedContacts.reduce((sum, contact) => {
      if (contact.respondedAt) {
        const responseTime =
          contact.respondedAt.getTime() - contact.createdAt.getTime();
        return sum + responseTime;
      }
      return sum;
    }, 0);

    const avgResponseTime = totalResponseTime / repliedContacts.length;

    return {
      avgResponseTime: Math.round(avgResponseTime / (1000 * 60 * 60)), // Convert to hours
      totalReplied: repliedContacts.length,
    };
  }
}
