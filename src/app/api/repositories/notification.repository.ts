/**
 * Notification Repository
 * <PERSON>u<PERSON>n lý các thao tác database cho Notification model
 */

import { Notification, Prisma, NotificationType } from "@prisma/client";
import { BaseRepository, NotFoundError } from "./base.repository";

export type NotificationCreateInput = Omit<
  Prisma.NotificationCreateInput,
  "id" | "createdAt" | "updatedAt" | "creator" | "target"
> & {
  createdBy?: string;
  targetId?: string;
};
export type NotificationUpdateInput = Partial<NotificationCreateInput>;
export type NotificationWithRelations = Prisma.NotificationGetPayload<{
  include: {
    creator: true;
    target: true;
  };
}>;

export interface NotificationSearchOptions {
  search?: string;
  targetId?: string;
  type?: NotificationType;
  isRead?: boolean;
  dateFrom?: Date;
  dateTo?: Date;
}

export class NotificationRepository extends BaseRepository<
  Notification,
  NotificationCreateInput,
  NotificationUpdateInput
> {
  constructor() {
    super("notification");
  }

  // Tìm notification với relations
  async findByIdWithRelations(
    id: string
  ): Promise<NotificationWithRelations | null> {
    return await this.model.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
          },
        },
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });
  }

  // Tìm notifications của admin user
  async findUserNotifications(
    targetId: string,
    options: {
      page?: number;
      limit?: number;
      isRead?: boolean;
      type?: NotificationType;
    } = {}
  ): Promise<any> {
    const { page = 1, limit = 20, isRead, type } = options;

    const where: Prisma.NotificationWhereInput = {
      OR: [{ targetId }, { targetType: "ALL_ADMINS" }],
    };

    if (typeof isRead === "boolean") {
      where.isRead = isRead;
    }

    if (type) {
      where.type = type;
    }

    return await this.findWithPagination({
      page,
      limit,
      where,
      include: {
        createdBy: {
          select: {
            id: true,
            name: true,
            avatar: true,
          },
        },
      },
      orderBy: { createdAt: "desc" },
    });
  }

  // Override create method để handle relations
  async create(data: NotificationCreateInput): Promise<Notification> {
    const { createdBy, targetId, ...notificationData } = data;

    const createData: Prisma.NotificationCreateInput = {
      ...notificationData,
      ...(createdBy && { creator: { connect: { id: createdBy } } }),
      ...(targetId && { target: { connect: { id: targetId } } }),
    };

    return await this.prisma.notification.create({ data: createData });
  }

  // Tạo notification
  async createNotification(
    data: NotificationCreateInput
  ): Promise<Notification> {
    return await this.create(data);
  }

  // Tạo notification cho nhiều admin users
  async createBulkNotifications(
    targetIds: string[],
    notificationData: Omit<NotificationCreateInput, "targetId">
  ): Promise<Notification[]> {
    const notifications = await Promise.all(
      targetIds.map((targetId) =>
        this.create({
          ...notificationData,
          targetId,
          targetType: "SPECIFIC_ADMIN" as const,
        })
      )
    );

    return notifications;
  }

  // Đánh dấu đã đọc
  async markAsRead(id: string): Promise<Notification> {
    return await this.update(id, {
      isRead: true,
      readAt: new Date(),
    });
  }

  // Đánh dấu chưa đọc
  async markAsUnread(id: string): Promise<Notification> {
    return await this.update(id, {
      isRead: false,
      readAt: null,
    });
  }

  // Đánh dấu tất cả notifications của user đã đọc
  async markAllAsRead(userId: string): Promise<void> {
    await this.updateMany(
      { userId, isRead: false },
      {
        isRead: true,
        readAt: new Date(),
      }
    );
  }

  // Lấy số lượng notifications chưa đọc
  async getUnreadCount(userId: string): Promise<number> {
    return await this.count({
      userId,
      isRead: false,
    });
  }

  // Tìm kiếm notifications với filter
  async searchNotifications(
    options: NotificationSearchOptions & {
      page?: number;
      limit?: number;
    }
  ) {
    const {
      search,
      targetId,
      type,
      isRead,
      dateFrom,
      dateTo,
      page = 1,
      limit = 20,
    } = options;

    const where: Prisma.NotificationWhereInput = {};

    if (search) {
      where.OR = [
        { title: { contains: search, mode: "insensitive" } },
        { message: { contains: search, mode: "insensitive" } },
      ];
    }

    if (targetId) {
      where.OR = [{ targetId }, { targetType: "ALL_ADMINS" }];
    }

    if (type) {
      where.type = type;
    }

    if (typeof isRead === "boolean") {
      where.isRead = isRead;
    }

    if (dateFrom || dateTo) {
      where.createdAt = {};
      if (dateFrom) where.createdAt.gte = dateFrom;
      if (dateTo) where.createdAt.lte = dateTo;
    }

    return await this.findWithPagination({
      page,
      limit,
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: { createdAt: "desc" },
    });
  }

  // Lấy notifications theo type
  async getNotificationsByType(
    type: NotificationType,
    options: {
      page?: number;
      limit?: number;
    } = {}
  ): Promise<any> {
    const { page = 1, limit = 20 } = options;

    return await this.findWithPagination({
      page,
      limit,
      where: { type },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: { createdAt: "desc" },
    });
  }

  // Lấy recent notifications
  async getRecentNotifications(limit: number = 10): Promise<Notification[]> {
    return await this.findMany({
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: { createdAt: "desc" },
      take: limit,
    });
  }

  // Xóa notifications cũ
  async deleteOldNotifications(olderThanDays: number = 30): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

    const result = await this.deleteMany({
      createdAt: { lt: cutoffDate },
    });

    return result.count;
  }

  // Lấy thống kê notifications
  async getNotificationStats() {
    const [total, unread, read, typeStats] = await Promise.all([
      this.count(),
      this.count({ isRead: false }),
      this.count({ isRead: true }),
      this.prisma.notification.groupBy({
        by: ["type"],
        _count: { type: true },
        orderBy: { _count: { type: "desc" } },
      }),
    ]);

    return {
      total,
      unread,
      read,
      byType: typeStats.map((stat) => ({
        type: stat.type,
        count: stat._count.type,
      })),
    };
  }

  // Notification helpers cho các loại thông báo cụ thể

  // Thông báo đơn hàng mới
  async notifyNewOrder(
    targetId: string,
    orderId: string,
    createdBy?: string
  ): Promise<Notification> {
    return await this.createNotification({
      targetId,
      targetType: "SPECIFIC_ADMIN",
      type: "INFO",
      title: "Đơn hàng mới",
      message: `Có đơn hàng mới #${orderId}`,
      metadata: { orderId },
      createdBy,
    });
  }

  // Thông báo cập nhật đơn hàng
  async notifyOrderUpdate(
    targetId: string,
    orderId: string,
    status: string,
    createdBy?: string
  ): Promise<Notification> {
    return await this.createNotification({
      targetId,
      targetType: "SPECIFIC_ADMIN",
      type: "INFO",
      title: "Cập nhật đơn hàng",
      message: `Đơn hàng #${orderId} đã được cập nhật: ${status}`,
      metadata: { orderId, status },
      createdBy,
    });
  }

  // Thông báo sản phẩm mới
  async notifyNewProduct(
    targetIds: string[],
    productId: string,
    productName: string,
    createdBy?: string
  ): Promise<Notification[]> {
    return await this.createBulkNotifications(targetIds, {
      type: "INFO",
      title: "Sản phẩm mới",
      message: `Sản phẩm mới: ${productName}`,
      metadata: { productId },
      createdBy,
    });
  }

  // Thông báo khuyến mãi
  async notifyPromotion(
    targetIds: string[],
    promotionId: string,
    title: string,
    createdBy?: string
  ): Promise<Notification[]> {
    return await this.createBulkNotifications(targetIds, {
      type: "INFO",
      title: "Khuyến mãi mới",
      message: title,
      metadata: { promotionId },
      createdBy,
    });
  }

  // Thông báo hệ thống
  async notifySystem(
    targetIds: string[],
    title: string,
    message: string,
    createdBy?: string
  ): Promise<Notification[]> {
    return await this.createBulkNotifications(targetIds, {
      type: "SYSTEM",
      title,
      message,
      createdBy,
    });
  }

  // Thông báo review mới
  async notifyNewReview(
    targetId: string,
    reviewId: string,
    productName: string,
    createdBy?: string
  ): Promise<Notification> {
    return await this.createNotification({
      targetId,
      targetType: "SPECIFIC_ADMIN",
      type: "INFO",
      title: "Đánh giá mới",
      message: `Sản phẩm "${productName}" có đánh giá mới`,
      metadata: { reviewId },
      createdBy,
    });
  }

  // Lấy notification preferences của admin user (nếu có bảng preferences)
  async getUserNotificationPreferences(
    _userId: string
  ): Promise<Record<NotificationType, boolean>> {
    // Mặc định tất cả đều bật, có thể mở rộng với bảng preferences riêng
    return {
      INFO: true,
      SUCCESS: true,
      WARNING: true,
      ERROR: true,
      SYSTEM: true,
    };
  }

  // Kiểm tra admin user có muốn nhận loại notification này không
  async shouldNotifyUser(
    userId: string,
    type: NotificationType
  ): Promise<boolean> {
    const preferences = await this.getUserNotificationPreferences(userId);
    return preferences[type] || false;
  }
}
