/**
 * Review Repository
 * <PERSON><PERSON><PERSON><PERSON> lý các thao tác database cho Review model
 */

import { Review, Prisma, ReviewStatus } from "@prisma/client";
import {
  BaseRepository,
  NotFoundError,
  ConflictError,
} from "./base.repository";

export type ReviewCreateInput = Omit<
  Prisma.ReviewCreateInput,
  "id" | "createdAt" | "updatedAt" | "user" | "product"
> & {
  userId: string;
  productId: string;
};
export type ReviewUpdateInput = Partial<
  Omit<ReviewCreateInput, "userId" | "productId">
>;
export type ReviewWithRelations = Prisma.ReviewGetPayload<{
  include: {
    user: true;
    product: true;
    media: { include: { media: true } };
  };
}>;

export interface ReviewSearchOptions {
  search?: string;
  userId?: string;
  productId?: string;
  status?: ReviewStatus;
  rating?: number;
  dateFrom?: Date;
  dateTo?: Date;
}

export class ReviewRepository extends BaseRepository<
  Review,
  ReviewCreateInput,
  ReviewUpdateInput
> {
  constructor() {
    super("review");
  }

  // Override create method để handle relations
  async create(data: ReviewCreateInput): Promise<Review> {
    const { userId, productId, ...reviewData } = data;

    const createData: Prisma.ReviewCreateInput = {
      ...reviewData,
      user: { connect: { id: userId } },
      product: { connect: { id: productId } },
    };

    return await this.prisma.review.create({ data: createData });
  }

  // Tìm review với đầy đủ relations
  async findByIdWithRelations(id: string): Promise<ReviewWithRelations | null> {
    return await this.model.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
          },
        },
        product: {
          select: {
            id: true,
            name: true,
            slug: true,
            media: {
              include: { media: true },
              where: { isPrimary: true },
              take: 1,
            },
          },
        },
        media: {
          include: { media: true },
          orderBy: { order: "asc" },
        },
      },
    });
  }

  // Tìm review của user cho product
  async findUserProductReview(
    userId: string,
    productId: string
  ): Promise<Review | null> {
    return await this.findFirst({
      userId,
      productId,
    });
  }

  // Tìm reviews của product
  async findProductReviews(
    productId: string,
    options: {
      status?: ReviewStatus;
      page?: number;
      limit?: number;
      sortBy?: "createdAt" | "rating";
      sortOrder?: "asc" | "desc";
    } = {}
  ): Promise<any> {
    const {
      status = "APPROVED",
      page = 1,
      limit = 10,
      sortBy = "createdAt",
      sortOrder = "desc",
    } = options;

    return await this.findWithPagination({
      page,
      limit,
      where: {
        productId,
        status,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            avatar: true,
          },
        },
        media: {
          include: { media: true },
          orderBy: { order: "asc" },
        },
      },
      orderBy: { [sortBy]: sortOrder },
    });
  }

  // Tìm reviews của user
  async findUserReviews(
    userId: string,
    options: {
      page?: number;
      limit?: number;
    } = {}
  ): Promise<any> {
    const { page = 1, limit = 10 } = options;

    return await this.findWithPagination({
      page,
      limit,
      where: { userId },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            slug: true,
            media: {
              include: { media: true },
              where: { isPrimary: true },
              take: 1,
            },
          },
        },
        media: {
          include: { media: true },
          orderBy: { order: "asc" },
        },
      },
      orderBy: { createdAt: "desc" },
    });
  }

  // Tạo review mới
  async createReview(data: ReviewCreateInput): Promise<Review> {
    // Kiểm tra user đã review product này chưa
    const existingReview = await this.findUserProductReview(
      data.userId,
      data.productId
    );
    if (existingReview) {
      throw new ConflictError("Bạn đã đánh giá sản phẩm này rồi");
    }

    // Validate rating
    if (data.rating && (data.rating < 1 || data.rating > 5)) {
      throw new Error("Rating phải từ 1 đến 5");
    }

    const review = await this.create(data);

    // Cập nhật rating của product
    await this.updateProductRating(data.productId);

    return review;
  }

  // Cập nhật review
  async updateReview(id: string, data: ReviewUpdateInput): Promise<Review> {
    const review = await this.findById(id);
    if (!review) {
      throw new NotFoundError("Review", id);
    }

    // Validate rating nếu có
    if (data.rating && (data.rating < 1 || data.rating > 5)) {
      throw new Error("Rating phải từ 1 đến 5");
    }

    const updatedReview = await this.update(id, data);

    // Cập nhật rating của product nếu rating thay đổi
    if (data.rating) {
      await this.updateProductRating(review.productId);
    }

    return updatedReview;
  }

  // Xóa review
  async deleteReview(id: string): Promise<Review> {
    const review = await this.findById(id);
    if (!review) {
      throw new NotFoundError("Review", id);
    }

    const deletedReview = await this.delete(id);

    // Cập nhật rating của product
    await this.updateProductRating(review.productId);

    return deletedReview;
  }

  // Approve review
  async approveReview(id: string): Promise<Review> {
    const review = await this.updateReview(id, { status: "APPROVED" });

    // Cập nhật rating của product
    await this.updateProductRating(review.productId);

    return review;
  }

  // Reject review
  async rejectReview(id: string): Promise<Review> {
    return await this.updateReview(id, { status: "REJECTED" });
  }

  // Tìm kiếm reviews với filter
  async searchReviews(
    options: ReviewSearchOptions & {
      page?: number;
      limit?: number;
    }
  ) {
    const {
      search,
      userId,
      productId,
      status,
      rating,
      dateFrom,
      dateTo,
      page = 1,
      limit = 20,
    } = options;

    const where: Prisma.ReviewWhereInput = {};

    if (search) {
      where.OR = [
        { comment: { contains: search, mode: "insensitive" } },
        {
          user: {
            OR: [
              { name: { contains: search, mode: "insensitive" } },
              { email: { contains: search, mode: "insensitive" } },
            ],
          },
        },
        { product: { name: { contains: search, mode: "insensitive" } } },
      ];
    }

    if (userId) {
      where.userId = userId;
    }

    if (productId) {
      where.productId = productId;
    }

    if (status) {
      where.status = status;
    }

    if (rating) {
      where.rating = rating;
    }

    if (dateFrom || dateTo) {
      where.createdAt = {};
      if (dateFrom) where.createdAt.gte = dateFrom;
      if (dateTo) where.createdAt.lte = dateTo;
    }

    return await this.findWithPagination({
      page,
      limit,
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
          },
        },
        product: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        media: {
          include: { media: true },
        },
      },
      orderBy: { createdAt: "desc" },
    });
  }

  // Lấy pending reviews
  async getPendingReviews(limit?: number): Promise<Review[]> {
    return await this.findMany({
      where: { status: "PENDING" },
      include: {
        user: {
          select: { id: true, name: true, email: true },
        },
        product: {
          select: { id: true, name: true, slug: true },
        },
      },
      orderBy: { createdAt: "asc" },
      take: limit,
    });
  }

  // Lấy recent reviews
  async getRecentReviews(limit: number = 10): Promise<Review[]> {
    return await this.findMany({
      where: { status: "APPROVED" },
      include: {
        user: {
          select: { id: true, name: true, avatar: true },
        },
        product: {
          select: { id: true, name: true, slug: true },
        },
      },
      orderBy: { createdAt: "desc" },
      take: limit,
    });
  }

  // Lấy thống kê reviews
  async getReviewStats() {
    const [total, pending, approved, rejected, avgRating] = await Promise.all([
      this.count(),
      this.count({ status: "PENDING" }),
      this.count({ status: "APPROVED" }),
      this.count({ status: "REJECTED" }),
      this.prisma.review.aggregate({
        where: { status: "APPROVED" },
        _avg: { rating: true },
      }),
    ]);

    // Thống kê theo rating
    const ratingStats = await this.prisma.review.groupBy({
      by: ["rating"],
      where: { status: "APPROVED" },
      _count: { rating: true },
      orderBy: { rating: "desc" },
    });

    return {
      total,
      pending,
      approved,
      rejected,
      avgRating: avgRating._avg.rating || 0,
      ratingDistribution: ratingStats.map((stat) => ({
        rating: stat.rating,
        count: stat._count.rating,
      })),
    };
  }

  // Cập nhật rating của product
  private async updateProductRating(productId: string): Promise<void> {
    const reviews = await this.findMany({
      where: {
        productId,
        status: "APPROVED",
      },
      select: { rating: true },
    });

    const avgRating =
      reviews.length > 0
        ? reviews.reduce((sum, review) => sum + review.rating, 0) /
          reviews.length
        : 0;

    await this.prisma.product.update({
      where: { id: productId },
      data: {
        avgRating: Math.round(avgRating * 10) / 10,
        reviewCount: reviews.length,
      },
    });
  }

  // Lấy rating distribution của product
  async getProductRatingDistribution(productId: string): Promise<any[]> {
    const result = await this.prisma.review.groupBy({
      by: ["rating"],
      where: {
        productId,
        status: "APPROVED",
      },
      _count: { rating: true },
    });

    return result.sort((a, b) => b.rating - a.rating);
  }

  // Kiểm tra user có thể review product không
  async canUserReviewProduct(
    userId: string,
    productId: string
  ): Promise<{
    canReview: boolean;
    reason?: string;
  }> {
    // Kiểm tra đã review chưa
    const existingReview = await this.findUserProductReview(userId, productId);
    if (existingReview) {
      return { canReview: false, reason: "Bạn đã đánh giá sản phẩm này rồi" };
    }

    // Kiểm tra đã mua sản phẩm chưa
    const hasPurchased = await this.prisma.orderItem.findFirst({
      where: {
        productId,
        order: {
          userId,
          status: "DELIVERED",
        },
      },
    });

    if (!hasPurchased) {
      return {
        canReview: false,
        reason: "Bạn cần mua sản phẩm này trước khi đánh giá",
      };
    }

    return { canReview: true };
  }
}
