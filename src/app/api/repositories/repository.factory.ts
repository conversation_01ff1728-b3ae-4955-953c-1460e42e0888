/**
 * Repository Factory
 * Factory pattern để quản lý và tạo instances của repositories
 */

import { UserRepository } from './user.repository';
import { AdminUserRepository } from './admin-user.repository';
import { ProductRepository } from './product.repository';
import { CategoryRepository } from './category.repository';
import { BrandRepository } from './brand.repository';
import { OrderRepository } from './order.repository';
import { CartRepository } from './cart.repository';
import { AddressRepository } from './address.repository';
import { ReviewRepository } from './review.repository';
import { WishlistRepository } from './wishlist.repository';
import { PostRepository } from './post.repository';
import { PageRepository } from './page.repository';
import { MediaRepository } from './media.repository';
import { MenuRepository } from './menu.repository';
import { SettingRepository } from './setting.repository';
import { NotificationRepository } from './notification.repository';
import { AuditLogRepository } from './audit-log.repository';
import { ContactRepository } from './contact.repository';
import { AttributeRepository } from './attribute.repository';
import { InventoryRepository } from './inventory.repository';
import { PromotionRepository } from './promotion.repository';

// Repository types
export type RepositoryType = 
  | 'user'
  | 'adminUser'
  | 'product'
  | 'category'
  | 'brand'
  | 'order'
  | 'cart'
  | 'address'
  | 'review'
  | 'wishlist'
  | 'post'
  | 'page'
  | 'media'
  | 'menu'
  | 'setting'
  | 'notification'
  | 'auditLog'
  | 'contact'
  | 'attribute'
  | 'inventory'
  | 'promotion';

// Repository instances cache
const repositoryInstances = new Map<RepositoryType, any>();

/**
 * Repository Factory Class
 */
export class RepositoryFactory {
  /**
   * Lấy repository instance (singleton pattern)
   */
  static getRepository<T>(type: RepositoryType): T {
    if (!repositoryInstances.has(type)) {
      repositoryInstances.set(type, this.createRepository(type));
    }
    return repositoryInstances.get(type) as T;
  }

  /**
   * Tạo repository instance mới
   */
  private static createRepository(type: RepositoryType): any {
    switch (type) {
      case 'user':
        return new UserRepository();
      case 'adminUser':
        return new AdminUserRepository();
      case 'product':
        return new ProductRepository();
      case 'category':
        return new CategoryRepository();
      case 'brand':
        return new BrandRepository();
      case 'order':
        return new OrderRepository();
      case 'cart':
        return new CartRepository();
      case 'address':
        return new AddressRepository();
      case 'review':
        return new ReviewRepository();
      case 'wishlist':
        return new WishlistRepository();
      case 'post':
        return new PostRepository();
      case 'page':
        return new PageRepository();
      case 'media':
        return new MediaRepository();
      case 'menu':
        return new MenuRepository();
      case 'setting':
        return new SettingRepository();
      case 'notification':
        return new NotificationRepository();
      case 'auditLog':
        return new AuditLogRepository();
      case 'contact':
        return new ContactRepository();
      case 'attribute':
        return new AttributeRepository();
      case 'inventory':
        return new InventoryRepository();
      case 'promotion':
        return new PromotionRepository();
      default:
        throw new Error(`Unknown repository type: ${type}`);
    }
  }

  /**
   * Clear repository cache (useful for testing)
   */
  static clearCache(): void {
    repositoryInstances.clear();
  }

  /**
   * Get all repository instances
   */
  static getAllRepositories(): Record<RepositoryType, any> {
    const repositories: Partial<Record<RepositoryType, any>> = {};
    
    const types: RepositoryType[] = [
      'user', 'adminUser', 'product', 'category', 'brand', 'order',
      'cart', 'address', 'review', 'wishlist', 'post', 'page',
      'media', 'menu', 'setting', 'notification', 'auditLog',
      'contact', 'attribute', 'inventory', 'promotion'
    ];

    types.forEach(type => {
      repositories[type] = this.getRepository(type);
    });

    return repositories as Record<RepositoryType, any>;
  }
}

/**
 * Repository Service Container
 * Dependency injection container cho repositories
 */
export class RepositoryContainer {
  private static instance: RepositoryContainer;
  private repositories: Map<string, any> = new Map();

  private constructor() {}

  static getInstance(): RepositoryContainer {
    if (!RepositoryContainer.instance) {
      RepositoryContainer.instance = new RepositoryContainer();
    }
    return RepositoryContainer.instance;
  }

  /**
   * Register repository
   */
  register<T>(name: string, repository: T): void {
    this.repositories.set(name, repository);
  }

  /**
   * Resolve repository
   */
  resolve<T>(name: string): T {
    const repository = this.repositories.get(name);
    if (!repository) {
      throw new Error(`Repository ${name} not found`);
    }
    return repository as T;
  }

  /**
   * Check if repository exists
   */
  has(name: string): boolean {
    return this.repositories.has(name);
  }

  /**
   * Initialize all repositories
   */
  initializeRepositories(): void {
    // Core repositories
    this.register('userRepository', RepositoryFactory.getRepository<UserRepository>('user'));
    this.register('adminUserRepository', RepositoryFactory.getRepository<AdminUserRepository>('adminUser'));
    this.register('productRepository', RepositoryFactory.getRepository<ProductRepository>('product'));
    this.register('categoryRepository', RepositoryFactory.getRepository<CategoryRepository>('category'));
    this.register('brandRepository', RepositoryFactory.getRepository<BrandRepository>('brand'));
    this.register('orderRepository', RepositoryFactory.getRepository<OrderRepository>('order'));

    // E-commerce repositories
    this.register('cartRepository', RepositoryFactory.getRepository<CartRepository>('cart'));
    this.register('addressRepository', RepositoryFactory.getRepository<AddressRepository>('address'));
    this.register('reviewRepository', RepositoryFactory.getRepository<ReviewRepository>('review'));
    this.register('wishlistRepository', RepositoryFactory.getRepository<WishlistRepository>('wishlist'));

    // Content management repositories
    this.register('postRepository', RepositoryFactory.getRepository<PostRepository>('post'));
    this.register('pageRepository', RepositoryFactory.getRepository<PageRepository>('page'));
    this.register('mediaRepository', RepositoryFactory.getRepository<MediaRepository>('media'));
    this.register('menuRepository', RepositoryFactory.getRepository<MenuRepository>('menu'));

    // System repositories
    this.register('settingRepository', RepositoryFactory.getRepository<SettingRepository>('setting'));
    this.register('notificationRepository', RepositoryFactory.getRepository<NotificationRepository>('notification'));
    this.register('auditLogRepository', RepositoryFactory.getRepository<AuditLogRepository>('auditLog'));
    this.register('contactRepository', RepositoryFactory.getRepository<ContactRepository>('contact'));

    // Advanced repositories
    this.register('attributeRepository', RepositoryFactory.getRepository<AttributeRepository>('attribute'));
    this.register('inventoryRepository', RepositoryFactory.getRepository<InventoryRepository>('inventory'));
    this.register('promotionRepository', RepositoryFactory.getRepository<PromotionRepository>('promotion'));
  }
}

/**
 * Helper functions để sử dụng repositories
 */

// Core repositories
export const getUserRepository = (): UserRepository => 
  RepositoryFactory.getRepository<UserRepository>('user');

export const getAdminUserRepository = (): AdminUserRepository => 
  RepositoryFactory.getRepository<AdminUserRepository>('adminUser');

export const getProductRepository = (): ProductRepository => 
  RepositoryFactory.getRepository<ProductRepository>('product');

export const getCategoryRepository = (): CategoryRepository => 
  RepositoryFactory.getRepository<CategoryRepository>('category');

export const getBrandRepository = (): BrandRepository => 
  RepositoryFactory.getRepository<BrandRepository>('brand');

export const getOrderRepository = (): OrderRepository => 
  RepositoryFactory.getRepository<OrderRepository>('order');

// E-commerce repositories
export const getCartRepository = (): CartRepository => 
  RepositoryFactory.getRepository<CartRepository>('cart');

export const getAddressRepository = (): AddressRepository => 
  RepositoryFactory.getRepository<AddressRepository>('address');

export const getReviewRepository = (): ReviewRepository => 
  RepositoryFactory.getRepository<ReviewRepository>('review');

export const getWishlistRepository = (): WishlistRepository => 
  RepositoryFactory.getRepository<WishlistRepository>('wishlist');

// Content management repositories
export const getPostRepository = (): PostRepository => 
  RepositoryFactory.getRepository<PostRepository>('post');

export const getPageRepository = (): PageRepository => 
  RepositoryFactory.getRepository<PageRepository>('page');

export const getMediaRepository = (): MediaRepository => 
  RepositoryFactory.getRepository<MediaRepository>('media');

export const getMenuRepository = (): MenuRepository => 
  RepositoryFactory.getRepository<MenuRepository>('menu');

// System repositories
export const getSettingRepository = (): SettingRepository => 
  RepositoryFactory.getRepository<SettingRepository>('setting');

export const getNotificationRepository = (): NotificationRepository => 
  RepositoryFactory.getRepository<NotificationRepository>('notification');

export const getAuditLogRepository = (): AuditLogRepository => 
  RepositoryFactory.getRepository<AuditLogRepository>('auditLog');

export const getContactRepository = (): ContactRepository => 
  RepositoryFactory.getRepository<ContactRepository>('contact');

// Advanced repositories
export const getAttributeRepository = (): AttributeRepository => 
  RepositoryFactory.getRepository<AttributeRepository>('attribute');

export const getInventoryRepository = (): InventoryRepository => 
  RepositoryFactory.getRepository<InventoryRepository>('inventory');

export const getPromotionRepository = (): PromotionRepository => 
  RepositoryFactory.getRepository<PromotionRepository>('promotion');

/**
 * Initialize repository container
 */
export const initializeRepositories = (): void => {
  const container = RepositoryContainer.getInstance();
  container.initializeRepositories();
};

/**
 * Get repository container instance
 */
export const getRepositoryContainer = (): RepositoryContainer => {
  return RepositoryContainer.getInstance();
};
