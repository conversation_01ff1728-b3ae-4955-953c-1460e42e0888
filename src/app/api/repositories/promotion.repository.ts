/**
 * Promotion Repository
 * <PERSON>u<PERSON>n lý các thao tác database cho Promotion model
 */

import { Promotion, Prisma, PromotionType } from "@prisma/client";
import {
  BaseRepository,
  NotFoundError,
  ConflictError,
} from "./base.repository";

export type PromotionCreateInput = Omit<
  Prisma.PromotionCreateInput,
  "id" | "createdAt" | "updatedAt"
>;
export type PromotionUpdateInput = Partial<PromotionCreateInput>;

export interface PromotionSearchOptions {
  search?: string;
  type?: PromotionType;
  isActive?: boolean;
  dateFrom?: Date;
  dateTo?: Date;
}

export class PromotionRepository extends BaseRepository<
  Promotion,
  PromotionCreateInput,
  PromotionUpdateInput
> {
  constructor() {
    super("promotion");
  }

  // Tìm promotion theo code
  async findByCode(code: string): Promise<Promotion | null> {
    return await this.findFirst({ code });
  }

  // Tạo promotion mới
  async createPromotion(data: PromotionCreateInput): Promise<Promotion> {
    // Kiểm tra code đã tồn tại
    if (data.code) {
      const existingPromotion = await this.findByCode(data.code);
      if (existingPromotion) {
        throw new ConflictError("Promotion code đã tồn tại");
      }
    }

    return await this.create(data);
  }

  // Tìm kiếm promotions với filter
  async searchPromotions(
    options: PromotionSearchOptions & {
      page?: number;
      limit?: number;
    }
  ) {
    const {
      search,
      type,
      // _discountType,
      isActive,
      dateFrom,
      dateTo,
      page = 1,
      limit = 20,
    } = options;

    const where: Prisma.PromotionWhereInput = {};

    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { code: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
      ];
    }

    if (type) {
      where.type = type;
    }

    // discountType không tồn tại trong schema, sử dụng type thay thế
    // if (discountType) {
    //   where.discountType = discountType;
    // }

    if (typeof isActive === "boolean") {
      where.isActive = isActive;
    }

    if (dateFrom || dateTo) {
      where.startDate = {};
      if (dateFrom) where.startDate.gte = dateFrom;
      if (dateTo) where.endDate = { lte: dateTo };
    }

    return await this.findWithPagination({
      page,
      limit,
      where,
      orderBy: { createdAt: "desc" },
    });
  }

  // Lấy active promotions
  async getActivePromotions(): Promise<Promotion[]> {
    const now = new Date();

    return await this.findMany({
      where: {
        isActive: true,
        startDate: { lte: now },
        endDate: { gte: now },
      },
      orderBy: { startDate: "desc" },
    });
  }

  // Lấy promotions theo type
  async getPromotionsByType(type: PromotionType): Promise<Promotion[]> {
    return await this.findMany({
      where: { type },
      orderBy: { createdAt: "desc" },
    });
  }

  // Validate promotion code
  async validatePromotionCode(
    code: string,
    orderTotal?: number
  ): Promise<{
    valid: boolean;
    promotion?: Promotion;
    error?: string;
  }> {
    const promotion = await this.findByCode(code);

    if (!promotion) {
      return { valid: false, error: "Mã khuyến mãi không tồn tại" };
    }

    if (!promotion.isActive) {
      return { valid: false, error: "Mã khuyến mãi đã bị vô hiệu hóa" };
    }

    const now = new Date();

    if (promotion.startDate > now) {
      return { valid: false, error: "Mã khuyến mãi chưa có hiệu lực" };
    }

    if (promotion.endDate && promotion.endDate < now) {
      return { valid: false, error: "Mã khuyến mãi đã hết hạn" };
    }

    if (promotion.usageLimit && promotion.usageCount >= promotion.usageLimit) {
      return { valid: false, error: "Mã khuyến mãi đã hết lượt sử dụng" };
    }

    if (
      orderTotal &&
      promotion.minOrderAmount &&
      orderTotal < promotion.minOrderAmount
    ) {
      return {
        valid: false,
        error: `Đơn hàng tối thiểu ${promotion.minOrderAmount.toLocaleString()}đ để sử dụng mã này`,
      };
    }

    // maxOrderAmount không tồn tại trong schema
    // if (
    //   orderTotal &&
    //   promotion.maxOrderAmount &&
    //   orderTotal > promotion.maxOrderAmount
    // ) {
    //   return {
    //     valid: false,
    //     error: `Đơn hàng tối đa ${promotion.maxOrderAmount.toLocaleString()}đ để sử dụng mã này`,
    //   };
    // }

    return { valid: true, promotion };
  }

  // Calculate discount
  calculateDiscount(promotion: Promotion, orderTotal: number): number {
    if (promotion.type === "PERCENTAGE") {
      const discount = (orderTotal * promotion.value) / 100;
      return promotion.maxDiscountAmount
        ? Math.min(discount, promotion.maxDiscountAmount)
        : discount;
    } else {
      return Math.min(promotion.value, orderTotal);
    }
  }

  // Apply promotion
  async applyPromotion(
    code: string,
    orderTotal: number
  ): Promise<{
    success: boolean;
    discount: number;
    promotion?: Promotion;
    error?: string;
  }> {
    const validation = await this.validatePromotionCode(code, orderTotal);

    if (!validation.valid || !validation.promotion) {
      return {
        success: false,
        discount: 0,
        error: validation.error,
      };
    }

    const discount = this.calculateDiscount(validation.promotion, orderTotal);

    // Tăng usage count
    await this.update(validation.promotion.id, {
      usageCount: validation.promotion.usageCount + 1,
    });

    return {
      success: true,
      discount,
      promotion: validation.promotion,
    };
  }

  // Activate promotion
  async activatePromotion(id: string): Promise<Promotion> {
    return await this.update(id, { isActive: true });
  }

  // Deactivate promotion
  async deactivatePromotion(id: string): Promise<Promotion> {
    return await this.update(id, { isActive: false });
  }

  // Lấy expired promotions
  async getExpiredPromotions(): Promise<Promotion[]> {
    const now = new Date();

    return await this.findMany({
      where: {
        endDate: { lt: now },
        isActive: true,
      },
      orderBy: { endDate: "desc" },
    });
  }

  // Lấy upcoming promotions
  async getUpcomingPromotions(): Promise<Promotion[]> {
    const now = new Date();

    return await this.findMany({
      where: {
        startDate: { gt: now },
        isActive: true,
      },
      orderBy: { startDate: "asc" },
    });
  }

  // Lấy thống kê promotions
  async getPromotionStats() {
    const [total, active, expired, upcoming, totalUsage, typeStats] =
      await Promise.all([
        this.count(),
        this.count({
          isActive: true,
          startDate: { lte: new Date() },
          endDate: { gte: new Date() },
        }),
        this.count({
          endDate: { lt: new Date() },
        }),
        this.count({
          startDate: { gt: new Date() },
        }),
        this.prisma.promotion.aggregate({
          _sum: { usageCount: true },
        }),
        this.prisma.promotion.groupBy({
          by: ["type"],
          _count: { type: true },
          _sum: { usageCount: true },
        }),
      ]);

    return {
      total,
      active,
      expired,
      upcoming,
      totalUsage: totalUsage._sum.usageCount || 0,
      byType: typeStats.map((stat) => ({
        type: stat.type,
        count: stat._count.type,
        usage: stat._sum.usageCount || 0,
      })),
    };
  }

  // Generate unique promotion code
  generatePromotionCode(prefix: string = "PROMO"): string {
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.random().toString(36).substring(2, 6).toUpperCase();
    return `${prefix}${timestamp}${random}`;
  }

  // Duplicate promotion
  async duplicatePromotion(id: string, createdBy: string): Promise<Promotion> {
    const promotion = await this.findById(id);
    if (!promotion) {
      throw new NotFoundError("Promotion", id);
    }

    const newCode = this.generatePromotionCode();

    const newPromotionData: PromotionCreateInput = {
      name: `${promotion.name} (Copy)`,
      code: newCode,
      description: promotion.description,
      type: promotion.type,
      value: promotion.value,
      maxDiscountAmount: promotion.maxDiscountAmount,
      minOrderAmount: promotion.minOrderAmount,
      usageLimit: promotion.usageLimit,
      startDate: new Date(),
      endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      isActive: false,
      createdBy,
    };

    return await this.create(newPromotionData);
  }

  // Validate promotion data
  validatePromotionData(data: Partial<PromotionCreateInput>): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (!data.name || data.name.trim().length < 3) {
      errors.push("Tên khuyến mãi phải có ít nhất 3 ký tự");
    }

    if (data.code && data.code.trim().length < 3) {
      errors.push("Mã khuyến mãi phải có ít nhất 3 ký tự");
    }

    if (!data.value || data.value <= 0) {
      errors.push("Giá trị giảm giá phải lớn hơn 0");
    }

    if (data.type === "PERCENTAGE" && data.value && data.value > 100) {
      errors.push("Phần trăm giảm giá không được vượt quá 100%");
    }

    if (data.startDate && data.endDate && data.startDate >= data.endDate) {
      errors.push("Ngày kết thúc phải sau ngày bắt đầu");
    }

    // maxOrderAmount không tồn tại trong schema
    // if (
    //   data.minOrderAmount &&
    //   data.maxOrderAmount &&
    //   data.minOrderAmount > data.maxOrderAmount
    // ) {
    //   errors.push("Giá trị đơn hàng tối thiểu không được lớn hơn tối đa");
    // }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  // Auto deactivate expired promotions
  async autoDeactivateExpiredPromotions(): Promise<number> {
    const now = new Date();

    const result = await this.updateMany(
      {
        endDate: { lt: now },
        isActive: true,
      },
      { isActive: false }
    );

    return result.count;
  }
}
