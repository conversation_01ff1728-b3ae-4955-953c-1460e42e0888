/**
 * Cart Repository
 * Quản lý các thao tác database cho Cart và CartItem models
 */

import { Cart, CartItem, Prisma } from "@prisma/client";
import { BaseRepository, NotFoundError } from "./base.repository";

export type CartCreateInput = Omit<
  Prisma.CartCreateInput,
  "id" | "createdAt" | "updatedAt" | "user"
> & {
  userId: string;
};
export type CartUpdateInput = Partial<CartCreateInput>;
export type CartItemCreateInput = Omit<
  Prisma.CartItemCreateInput,
  "id" | "createdAt" | "updatedAt"
>;
export type CartItemUpdateInput = Partial<CartItemCreateInput>;

export type CartWithItems = Prisma.CartGetPayload<{
  include: {
    items: {
      include: {
        product: {
          include: {
            media: { include: { media: true } };
            brand: true;
            category: true;
          };
        };
      };
    };
  };
}>;

export class CartRepository extends BaseRepository<
  Cart,
  CartCreateInput,
  CartUpdateInput
> {
  constructor() {
    super("cart");
  }

  // Tìm cart theo userId
  async findByUserId(userId: string): Promise<Cart | null> {
    return await this.model.findUnique({
      where: { userId },
    });
  }

  // Tìm cart với items
  async findByUserIdWithItems(userId: string): Promise<CartWithItems | null> {
    return await this.model.findUnique({
      where: { userId },
      include: {
        items: {
          include: {
            product: {
              include: {
                media: {
                  include: { media: true },
                  where: { isPrimary: true },
                  take: 1,
                },
                brand: {
                  select: { id: true, name: true, slug: true },
                },
                category: {
                  select: { id: true, name: true, slug: true },
                },
              },
            },
          },
          orderBy: { createdAt: "asc" },
        },
      },
    });
  }

  // Tạo cart cho user
  async createUserCart(userId: string): Promise<Cart> {
    return await this.model.create({
      data: {
        user: { connect: { id: userId } },
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        items: {
          include: {
            product: {
              include: {
                media: { include: { media: true } },
                category: {
                  select: { id: true, name: true, slug: true },
                },
              },
            },
          },
          orderBy: { createdAt: "asc" },
        },
      },
    });
  }

  // Lấy hoặc tạo cart cho user
  async getOrCreateUserCart(userId: string): Promise<Cart> {
    let cart = await this.findByUserId(userId);
    if (!cart) {
      cart = await this.createUserCart(userId);
    }
    return cart;
  }

  // Thêm item vào cart
  async addItem(
    userId: string,
    productId: string,
    quantity: number,
    price: number
  ): Promise<CartItem> {
    const cart = await this.getOrCreateUserCart(userId);

    // Kiểm tra item đã tồn tại trong cart
    const existingItem = await this.prisma.cartItem.findUnique({
      where: {
        cartId_productId: {
          cartId: cart.id,
          productId,
        },
      },
    });

    if (existingItem) {
      // Cập nhật quantity nếu item đã tồn tại
      return await this.prisma.cartItem.update({
        where: { id: existingItem.id },
        data: {
          quantity: existingItem.quantity + quantity,
          price, // Cập nhật giá mới nhất
        },
      });
    } else {
      // Tạo item mới
      return await this.prisma.cartItem.create({
        data: {
          cartId: cart.id,
          productId,
          quantity,
          price,
        },
      });
    }
  }

  // Cập nhật quantity của item
  async updateItemQuantity(
    userId: string,
    productId: string,
    quantity: number
  ): Promise<CartItem> {
    const cart = await this.findByUserId(userId);
    if (!cart) {
      throw new NotFoundError("Cart", userId);
    }

    const item = await this.prisma.cartItem.findUnique({
      where: {
        cartId_productId: {
          cartId: cart.id,
          productId,
        },
      },
    });

    if (!item) {
      throw new NotFoundError("CartItem", `${cart.id}-${productId}`);
    }

    if (quantity <= 0) {
      // Xóa item nếu quantity <= 0
      await this.prisma.cartItem.delete({
        where: { id: item.id },
      });
      throw new Error("Item removed from cart");
    }

    return await this.prisma.cartItem.update({
      where: { id: item.id },
      data: { quantity },
    });
  }

  // Xóa item khỏi cart
  async removeItem(userId: string, productId: string): Promise<void> {
    const cart = await this.findByUserId(userId);
    if (!cart) {
      throw new NotFoundError("Cart", userId);
    }

    await this.prisma.cartItem.deleteMany({
      where: {
        cartId: cart.id,
        productId,
      },
    });
  }

  // Xóa tất cả items trong cart
  async clearCart(userId: string): Promise<void> {
    const cart = await this.findByUserId(userId);
    if (!cart) return;

    await this.prisma.cartItem.deleteMany({
      where: { cartId: cart.id },
    });
  }

  // Lấy tổng số items trong cart
  async getCartItemCount(userId: string): Promise<number> {
    const cart = await this.findByUserId(userId);
    if (!cart) return 0;

    const result = await this.prisma.cartItem.aggregate({
      where: { cartId: cart.id },
      _sum: { quantity: true },
    });

    return result._sum.quantity || 0;
  }

  // Lấy tổng giá trị cart
  async getCartTotal(userId: string): Promise<number> {
    const cart = await this.findByUserId(userId);
    if (!cart) return 0;

    const items = await this.prisma.cartItem.findMany({
      where: { cartId: cart.id },
      select: { quantity: true, price: true },
    });

    return items.reduce((total, item) => total + item.quantity * item.price, 0);
  }

  // Kiểm tra product có trong cart không
  async hasProduct(userId: string, productId: string): Promise<boolean> {
    const cart = await this.findByUserId(userId);
    if (!cart) return false;

    const item = await this.prisma.cartItem.findUnique({
      where: {
        cartId_productId: {
          cartId: cart.id,
          productId,
        },
      },
    });

    return !!item;
  }

  // Merge cart (khi user login)
  async mergeCart(fromUserId: string, toUserId: string): Promise<void> {
    const fromCart = await this.findByUserIdWithItems(fromUserId);
    if (!fromCart || !fromCart.items.length) return;

    const toCart = await this.getOrCreateUserCart(toUserId);

    // Merge từng item
    for (const item of fromCart.items) {
      const existingItem = await this.prisma.cartItem.findUnique({
        where: {
          cartId_productId: {
            cartId: toCart.id,
            productId: item.productId,
          },
        },
      });

      if (existingItem) {
        // Cộng dồn quantity
        await this.prisma.cartItem.update({
          where: { id: existingItem.id },
          data: {
            quantity: existingItem.quantity + item.quantity,
            price: item.price, // Sử dụng giá mới nhất
          },
        });
      } else {
        // Tạo item mới
        await this.prisma.cartItem.create({
          data: {
            cartId: toCart.id,
            productId: item.productId,
            quantity: item.quantity,
            price: item.price,
          },
        });
      }
    }

    // Xóa cart cũ
    await this.delete(fromCart.id);
  }

  // Validate cart items (kiểm tra stock, giá...)
  async validateCartItems(userId: string): Promise<{
    valid: boolean;
    issues: Array<{
      productId: string;
      issue: string;
      currentStock?: number;
      currentPrice?: number;
    }>;
  }> {
    const cart = await this.findByUserIdWithItems(userId);
    if (!cart) return { valid: true, issues: [] };

    const issues: any[] = [];

    for (const item of cart.items) {
      const product = item.product;

      // Kiểm tra product còn active không
      if (product.status !== "ACTIVE") {
        issues.push({
          productId: product.id,
          issue: "Product is no longer available",
        });
        continue;
      }

      // Kiểm tra stock
      if (product.stock < item.quantity) {
        issues.push({
          productId: product.id,
          issue: "Insufficient stock",
          currentStock: product.stock,
        });
      }

      // Kiểm tra giá có thay đổi không
      if (product.price !== item.price) {
        issues.push({
          productId: product.id,
          issue: "Price changed",
          currentPrice: product.price,
        });
      }
    }

    return {
      valid: issues.length === 0,
      issues,
    };
  }

  // Cập nhật giá của tất cả items trong cart
  async updateCartPrices(userId: string): Promise<void> {
    const cart = await this.findByUserIdWithItems(userId);
    if (!cart) return;

    for (const item of cart.items) {
      if (item.product.price !== item.price) {
        await this.prisma.cartItem.update({
          where: { id: item.id },
          data: { price: item.product.price },
        });
      }
    }
  }

  // Lấy thống kê cart
  async getCartStats() {
    const [totalCarts, cartsWithItems, totalItems, avgItemsPerCart] =
      await Promise.all([
        this.count(),
        this.prisma.cart.count({
          where: {
            items: { some: {} },
          },
        }),
        this.prisma.cartItem.count(),
        this.prisma.cartItem.aggregate({
          _avg: { quantity: true },
        }),
      ]);

    return {
      totalCarts,
      cartsWithItems,
      totalItems,
      avgItemsPerCart: avgItemsPerCart._avg.quantity || 0,
    };
  }
}
