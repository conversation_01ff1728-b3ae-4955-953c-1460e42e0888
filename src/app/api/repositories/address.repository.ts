/**
 * Address Repository
 * Quản lý các thao tác database cho Address model
 */

import { Address, Prisma } from "@prisma/client";
import { BaseRepository, NotFoundError } from "./base.repository";

export type AddressCreateInput = Omit<
  Prisma.AddressCreateInput,
  "id" | "createdAt" | "updatedAt" | "user"
> & {
  userId: string;
};
export type AddressUpdateInput = Partial<AddressCreateInput>;

export interface AddressSearchOptions {
  search?: string;
  userId?: string;
  province?: string;
  district?: string;
  isDefault?: boolean;
}

export class AddressRepository extends BaseRepository<
  Address,
  AddressCreateInput,
  AddressUpdateInput
> {
  constructor() {
    super("address");
  }

  // Tìm addresses của user
  async findUserAddresses(userId: string): Promise<Address[]> {
    return await this.findMany({
      where: { userId },
      orderBy: [{ isDefault: "desc" }, { createdAt: "desc" }],
    });
  }

  // Alias for findUserAddresses (required by services)
  async findByUser(userId: string): Promise<Address[]> {
    return await this.findUserAddresses(userId);
  }

  // Tìm default address của user
  async findUserDefaultAddress(userId: string): Promise<Address | null> {
    return await this.findFirst({
      userId,
      isDefault: true,
    });
  }

  // Alias for findUserDefaultAddress (required by services)
  async findDefaultByUser(userId: string): Promise<Address | null> {
    return await this.findUserDefaultAddress(userId);
  }

  // Count addresses by user (required by services)
  async countByUser(userId: string): Promise<number> {
    return await this.count({ userId });
  }

  // Unset default for user (required by services)
  async unsetDefaultForUser(userId: string): Promise<void> {
    await this.prisma.address.updateMany({
      where: {
        userId,
        isDefault: true,
      },
      data: { isDefault: false },
    });
  }

  // Tạo address mới
  async createAddress(data: AddressCreateInput): Promise<Address> {
    // Nếu đây là address đầu tiên của user hoặc được đặt làm default
    if (data.isDefault) {
      // Bỏ default của các address khác
      await this.prisma.address.updateMany({
        where: {
          userId: data.userId,
          isDefault: true,
        },
        data: { isDefault: false },
      });
    } else {
      // Kiểm tra xem user có address nào chưa, nếu chưa thì đặt làm default
      const existingAddresses = await this.count({ userId: data.userId });
      if (existingAddresses === 0) {
        data.isDefault = true;
      }
    }

    // Convert userId to user relation for Prisma
    const { userId, ...addressData } = data;
    return await this.model.create({
      data: {
        ...addressData,
        user: { connect: { id: userId } },
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });
  }

  // Cập nhật address
  async updateAddress(id: string, data: AddressUpdateInput): Promise<Address> {
    const address = await this.findById(id);
    if (!address) {
      throw new NotFoundError("Address", id);
    }

    // Nếu đặt làm default
    if (data.isDefault) {
      // Bỏ default của các address khác cùng user
      await this.prisma.address.updateMany({
        where: {
          userId: address.userId,
          id: { not: id },
          isDefault: true,
        },
        data: { isDefault: false },
      });
    }

    return await this.update(id, data);
  }

  // Đặt address làm default
  async setAsDefault(id: string): Promise<Address> {
    const address = await this.findById(id);
    if (!address) {
      throw new NotFoundError("Address", id);
    }

    // Bỏ default của các address khác cùng user
    await this.prisma.address.updateMany({
      where: {
        userId: address.userId,
        id: { not: id },
        isDefault: true,
      },
      data: { isDefault: false },
    });

    return await this.update(id, { isDefault: true });
  }

  // Xóa address
  async deleteAddress(id: string): Promise<Address> {
    const address = await this.findById(id);
    if (!address) {
      throw new NotFoundError("Address", id);
    }

    const deletedAddress = await this.delete(id);

    // Nếu xóa default address, đặt address khác làm default
    if (address.isDefault) {
      const nextAddress = await this.findFirst({
        userId: address.userId,
      });

      if (nextAddress) {
        await this.update(nextAddress.id, { isDefault: true });
      }
    }

    return deletedAddress;
  }

  // Tìm kiếm addresses với filter
  async searchAddresses(
    options: AddressSearchOptions & {
      page?: number;
      limit?: number;
    }
  ) {
    const {
      search,
      userId,
      province,
      district,
      isDefault,
      page = 1,
      limit = 20,
    } = options;

    const where: Prisma.AddressWhereInput = {};

    if (search) {
      where.OR = [
        { fullName: { contains: search, mode: "insensitive" } },
        { phone: { contains: search, mode: "insensitive" } },
        { address: { contains: search, mode: "insensitive" } },
        { ward: { contains: search, mode: "insensitive" } },
        { district: { contains: search, mode: "insensitive" } },
        { province: { contains: search, mode: "insensitive" } },
      ];
    }

    if (userId) {
      where.userId = userId;
    }

    if (province) {
      where.province = { contains: province, mode: "insensitive" };
    }

    if (district) {
      where.district = { contains: district, mode: "insensitive" };
    }

    if (typeof isDefault === "boolean") {
      where.isDefault = isDefault;
    }

    return await this.findWithPagination({
      page,
      limit,
      where,
      include: {
        user: {
          select: { id: true, name: true, email: true },
        },
      },
      orderBy: [{ isDefault: "desc" }, { createdAt: "desc" }],
    });
  }

  // Lấy addresses theo tỉnh/thành phố
  async getAddressesByProvince(province: string): Promise<Address[]> {
    return await this.findMany({
      where: { province: { contains: province, mode: "insensitive" } },
      include: {
        user: {
          select: { id: true, name: true, email: true },
        },
      },
      orderBy: { createdAt: "desc" },
    });
  }

  // Lấy thống kê addresses
  async getAddressStats() {
    const [total, defaultAddresses, provinces] = await Promise.all([
      this.count(),
      this.count({ isDefault: true }),
      this.prisma.address.groupBy({
        by: ["province"],
        _count: { province: true },
        orderBy: { _count: { province: "desc" } },
      }),
    ]);

    return {
      total,
      defaultAddresses,
      provinces: provinces.map((p) => ({
        province: p.province,
        count: p._count.province,
      })),
    };
  }

  // Validate address format
  validateAddressFormat(address: Partial<AddressCreateInput>): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (!address.fullName || address.fullName.trim().length < 2) {
      errors.push("Họ tên phải có ít nhất 2 ký tự");
    }

    if (
      !address.phone ||
      !/^[0-9]{10,11}$/.test(address.phone.replace(/\s/g, ""))
    ) {
      errors.push("Số điện thoại không hợp lệ");
    }

    if (!address.address || address.address.trim().length < 5) {
      errors.push("Địa chỉ phải có ít nhất 5 ký tự");
    }

    if (!address.ward || address.ward.trim().length < 2) {
      errors.push("Phường/xã không hợp lệ");
    }

    if (!address.district || address.district.trim().length < 2) {
      errors.push("Quận/huyện không hợp lệ");
    }

    if (!address.province || address.province.trim().length < 2) {
      errors.push("Tỉnh/thành phố không hợp lệ");
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  // Format address string
  formatAddressString(address: Address): string {
    return [address.address, address.ward, address.district, address.province]
      .filter(Boolean)
      .join(", ");
  }

  // Lấy full address info
  async getFullAddressInfo(id: string): Promise<
    | (Address & {
        formattedAddress: string;
        user: { id: string; name: string; email: string } | null;
      })
    | null
  > {
    const address = await this.model.findUnique({
      where: { id },
      include: {
        user: {
          select: { id: true, name: true, email: true },
        },
      },
    });

    if (!address) return null;

    return {
      ...address,
      formattedAddress: this.formatAddressString(address),
    };
  }

  // Duplicate address
  async duplicateAddress(id: string, userId?: string): Promise<Address> {
    const address = await this.findById(id);
    if (!address) {
      throw new NotFoundError("Address", id);
    }

    const newAddressData: AddressCreateInput = {
      userId: userId || address.userId,
      fullName: address.fullName,
      phone: address.phone,
      address: address.address,
      ward: address.ward,
      district: address.district,
      province: address.province,
      isDefault: false, // Không đặt làm default khi duplicate
    };

    return await this.createAddress(newAddressData);
  }
}
