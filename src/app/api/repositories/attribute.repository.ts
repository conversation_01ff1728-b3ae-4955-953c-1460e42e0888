/**
 * Attribute Repository
 * Quản lý các thao tác database cho Attribute, AttributeValue, và ProductAttribute models
 */

import {
  Attribute,
  AttributeValue,
  ProductAttribute,
  Prisma,
  AttributeType,
} from "@prisma/client";
import {
  BaseRepository,
  NotFoundError,
  ConflictError,
} from "./base.repository";

export type AttributeCreateInput = Omit<
  Prisma.AttributeCreateInput,
  "id" | "createdAt" | "updatedAt"
>;
export type AttributeUpdateInput = Partial<AttributeCreateInput>;
export type AttributeValueCreateInput = Omit<
  Prisma.AttributeValueCreateInput,
  "id" | "createdAt" | "updatedAt" | "attribute"
> & {
  attributeId: string;
};
export type AttributeValueUpdateInput = Partial<AttributeValueCreateInput>;
export type ProductAttributeCreateInput = Omit<
  Prisma.ProductAttributeCreateInput,
  "id" | "createdAt" | "updatedAt" | "product" | "attribute" | "attributeValue"
> & {
  productId: string;
  attributeId: string;
  attributeValueId?: string;
};

export type AttributeWithValues = Prisma.AttributeGetPayload<{
  include: {
    values: true;
  };
}>;

export interface AttributeSearchOptions {
  search?: string;
  type?: AttributeType;
  isRequired?: boolean;
  isFilterable?: boolean;
}

export class AttributeRepository extends BaseRepository<
  Attribute,
  AttributeCreateInput,
  AttributeUpdateInput
> {
  constructor() {
    super("attribute");
  }

  // Tìm attribute với values
  async findByIdWithValues(id: string): Promise<AttributeWithValues | null> {
    return await this.model.findUnique({
      where: { id },
      include: {
        values: {
          orderBy: { order: "asc" },
        },
      },
    });
  }

  // Tìm attribute theo name
  async findByName(name: string): Promise<Attribute | null> {
    return await this.findFirst({ name });
  }

  // Tìm attribute theo slug (required by services)
  async findBySlug(slug: string): Promise<Attribute | null> {
    return await this.findFirst({ slug });
  }

  // Check if attribute is used in products (required by services)
  async isAttributeUsed(attributeId: string): Promise<boolean> {
    const count = await this.prisma.productAttribute.count({
      where: { attributeId },
    });
    return count > 0;
  }

  // Check if attribute value is used in products (required by services)
  async isAttributeValueUsed(
    attributeId: string,
    value: string
  ): Promise<boolean> {
    const attributeValue = await this.prisma.attributeValue.findFirst({
      where: {
        attributeId,
        value,
      },
    });

    if (!attributeValue) return false;

    const count = await this.prisma.productAttribute.count({
      where: { attributeValueId: attributeValue.id },
    });
    return count > 0;
  }

  // Get attribute usage statistics (required by services)
  async getAttributeUsageStats(attributeId: string): Promise<{
    totalProducts: number;
    valueUsage: { value: string; count: number }[];
  }> {
    const [totalProducts, valueUsage] = await Promise.all([
      this.prisma.productAttribute.count({
        where: { attributeId },
      }),
      this.prisma.attributeValue.findMany({
        where: { attributeId },
        include: {
          _count: {
            select: { products: true },
          },
        },
      }),
    ]);

    return {
      totalProducts,
      valueUsage: valueUsage.map((av) => ({
        value: av.value,
        count: av._count.products,
      })),
    };
  }

  // Tạo attribute mới
  async createAttribute(data: AttributeCreateInput): Promise<Attribute> {
    // Kiểm tra name đã tồn tại
    const existingAttribute = await this.findByName(data.name);
    if (existingAttribute) {
      throw new ConflictError("Attribute name đã tồn tại");
    }

    return await this.create(data);
  }

  // Tìm kiếm attributes với filter
  async searchAttributes(
    options: AttributeSearchOptions & {
      page?: number;
      limit?: number;
    }
  ) {
    const {
      search,
      type,
      isRequired,
      isFilterable,
      page = 1,
      limit = 20,
    } = options;

    const where: Prisma.AttributeWhereInput = {};

    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
      ];
    }

    if (type) {
      where.type = type;
    }

    if (typeof isRequired === "boolean") {
      where.isRequired = isRequired;
    }

    if (typeof isFilterable === "boolean") {
      where.isFilterable = isFilterable;
    }

    return await this.findWithPagination({
      page,
      limit,
      where,
      include: {
        _count: {
          select: { values: true },
        },
      },
      orderBy: { name: "asc" },
    });
  }

  // Lấy attributes theo type
  async getAttributesByType(
    type: AttributeType
  ): Promise<AttributeWithValues[]> {
    return await this.model.findMany({
      where: { type },
      include: {
        values: {
          orderBy: { sortOrder: "asc" },
        },
      },
      orderBy: { name: "asc" },
    });
  }

  // Lấy filterable attributes
  async getFilterableAttributes(): Promise<AttributeWithValues[]> {
    return await this.model.findMany({
      where: { isFilterable: true },
      include: {
        values: {
          orderBy: { sortOrder: "asc" },
        },
      },
      orderBy: { name: "asc" },
    });
  }

  // Lấy required attributes
  async getRequiredAttributes(): Promise<AttributeWithValues[]> {
    return await this.model.findMany({
      where: { isRequired: true },
      include: {
        values: {
          orderBy: { sortOrder: "asc" },
        },
      },
      orderBy: { name: "asc" },
    });
  }

  // AttributeValue Methods

  // Tạo attribute value
  async createAttributeValue(
    data: AttributeValueCreateInput
  ): Promise<AttributeValue> {
    // Kiểm tra attribute tồn tại
    const attribute = await this.findById(data.attributeId);
    if (!attribute) {
      throw new NotFoundError("Attribute", data.attributeId);
    }

    // Kiểm tra value đã tồn tại trong attribute
    const existingValue = await this.prisma.attributeValue.findFirst({
      where: {
        attributeId: data.attributeId,
        value: data.value,
      },
    });

    if (existingValue) {
      throw new ConflictError("Attribute value đã tồn tại");
    }

    // Convert attributeId to attribute relation for Prisma
    const { attributeId, ...valueData } = data;
    return await this.prisma.attributeValue.create({
      data: {
        ...valueData,
        attribute: { connect: { id: attributeId } },
      },
      include: {
        attribute: {
          select: {
            id: true,
            name: true,
            type: true,
          },
        },
      },
    });
  }

  // Cập nhật attribute value
  async updateAttributeValue(
    id: string,
    data: AttributeValueUpdateInput
  ): Promise<AttributeValue> {
    const value = await this.prisma.attributeValue.findUnique({
      where: { id },
    });

    if (!value) {
      throw new NotFoundError("AttributeValue", id);
    }

    return await this.prisma.attributeValue.update({
      where: { id },
      data,
    });
  }

  // Xóa attribute value
  async deleteAttributeValue(id: string): Promise<AttributeValue> {
    const value = await this.prisma.attributeValue.findUnique({
      where: { id },
      include: {
        products: true,
      },
    });

    if (!value) {
      throw new NotFoundError("AttributeValue", id);
    }

    // Kiểm tra có products đang sử dụng value này không
    if (value.products.length > 0) {
      throw new ConflictError(
        "Không thể xóa attribute value đang được sử dụng"
      );
    }

    return await this.prisma.attributeValue.delete({
      where: { id },
    });
  }

  // Lấy values của attribute
  async getAttributeValues(attributeId: string): Promise<AttributeValue[]> {
    return await this.prisma.attributeValue.findMany({
      where: { attributeId },
      orderBy: { sortOrder: "asc" },
    });
  }

  // Reorder attribute values
  async reorderAttributeValues(
    valueOrders: Array<{ id: string; order: number }>
  ): Promise<void> {
    for (const { id, order } of valueOrders) {
      await this.prisma.attributeValue.update({
        where: { id },
        data: { sortOrder: order },
      });
    }
  }

  // ProductAttribute Methods

  // Tạo product attribute
  async createProductAttribute(
    data: ProductAttributeCreateInput
  ): Promise<ProductAttribute> {
    // Kiểm tra product và attribute tồn tại
    const [product, attribute] = await Promise.all([
      this.prisma.product.findUnique({ where: { id: data.productId } }),
      this.findById(data.attributeId),
    ]);

    if (!product) {
      throw new NotFoundError("Product", data.productId);
    }

    if (!attribute) {
      throw new NotFoundError("Attribute", data.attributeId);
    }

    // Kiểm tra product attribute đã tồn tại
    const existing = await this.prisma.productAttribute.findUnique({
      where: {
        productId_attributeId: {
          productId: data.productId,
          attributeId: data.attributeId,
        },
      },
    });

    if (existing) {
      throw new ConflictError("Product attribute đã tồn tại");
    }

    // Convert IDs to relations for Prisma
    const { productId, attributeId, attributeValueId, ...attributeData } = data;

    if (!attributeValueId) {
      throw new Error("attributeValueId is required");
    }

    const createData: Prisma.ProductAttributeCreateInput = {
      ...attributeData,
      product: { connect: { id: productId } },
      attribute: { connect: { id: attributeId } },
      attributeValue: { connect: { id: attributeValueId } },
    };

    return await this.prisma.productAttribute.create({
      data: createData,
      include: {
        product: {
          select: {
            id: true,
            name: true,
            sku: true,
          },
        },
        attribute: {
          select: {
            id: true,
            name: true,
            type: true,
          },
        },
      },
    });
  }

  // Cập nhật product attribute
  async updateProductAttribute(
    productId: string,
    attributeId: string,
    attributeValueId: string
  ): Promise<ProductAttribute> {
    const productAttribute = await this.prisma.productAttribute.findUnique({
      where: {
        productId_attributeId: {
          productId,
          attributeId,
        },
      },
    });

    if (!productAttribute) {
      throw new NotFoundError(
        "ProductAttribute",
        `${productId}-${attributeId}`
      );
    }

    return await this.prisma.productAttribute.update({
      where: {
        productId_attributeId: {
          productId,
          attributeId,
        },
      },
      data: { attributeValueId },
    });
  }

  // Xóa product attribute
  async deleteProductAttribute(
    productId: string,
    attributeId: string
  ): Promise<ProductAttribute> {
    const productAttribute = await this.prisma.productAttribute.findUnique({
      where: {
        productId_attributeId: {
          productId,
          attributeId,
        },
      },
    });

    if (!productAttribute) {
      throw new NotFoundError(
        "ProductAttribute",
        `${productId}-${attributeId}`
      );
    }

    return await this.prisma.productAttribute.delete({
      where: {
        productId_attributeId: {
          productId,
          attributeId,
        },
      },
    });
  }

  // Lấy product attributes
  async getProductAttributes(productId: string): Promise<any[]> {
    return await this.prisma.productAttribute.findMany({
      where: { productId },
      include: {
        attribute: true,
        attributeValue: true,
      },
    });
  }

  // Bulk update product attributes
  async bulkUpdateProductAttributes(
    productId: string,
    attributes: Array<{ attributeId: string; attributeValueId: string }>
  ): Promise<void> {
    // Xóa tất cả attributes hiện tại của product
    await this.prisma.productAttribute.deleteMany({
      where: { productId },
    });

    // Tạo mới các attributes
    for (const attr of attributes) {
      await this.createProductAttribute({
        productId,
        attributeId: attr.attributeId,
        attributeValueId: attr.attributeValueId,
      });
    }
  }

  // Lấy thống kê attributes
  async getAttributeStats() {
    const [
      totalAttributes,
      totalValues,
      requiredAttributes,
      filterableAttributes,
      typeStats,
    ] = await Promise.all([
      this.count(),
      this.prisma.attributeValue.count(),
      this.count({ isRequired: true }),
      this.count({ isFilterable: true }),
      this.prisma.attribute.groupBy({
        by: ["type"],
        _count: { type: true },
      }),
    ]);

    return {
      totalAttributes,
      totalValues,
      requiredAttributes,
      filterableAttributes,
      byType: typeStats.map((stat) => ({
        type: stat.type,
        count: stat._count.type,
      })),
    };
  }

  // Validate attribute data
  validateAttributeData(data: Partial<AttributeCreateInput>): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (!data.name || data.name.trim().length < 2) {
      errors.push("Tên attribute phải có ít nhất 2 ký tự");
    }

    // Label validation removed as Attribute model doesn't have label field

    if (!data.type) {
      errors.push("Type là bắt buộc");
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }
}
