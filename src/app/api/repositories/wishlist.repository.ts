/**
 * Wishlist Repository
 * Quản lý các thao tác database cho WishlistItem model
 */

import { WishlistItem, Prisma } from "@prisma/client";
import {
  BaseRepository,
  NotFoundError,
  ConflictError,
} from "./base.repository";

export type WishlistItemCreateInput = Omit<
  Prisma.WishlistItemCreateInput,
  "id" | "createdAt" | "updatedAt" | "user" | "product"
> & {
  userId: string;
  productId: string;
};
export type WishlistItemUpdateInput = Partial<WishlistItemCreateInput>;
export type WishlistItemWithProduct = Prisma.WishlistItemGetPayload<{
  include: {
    product: {
      include: {
        media: { include: { media: true } };
        brand: true;
        category: true;
      };
    };
  };
}>;

export interface WishlistSearchOptions {
  search?: string;
  userId?: string;
  categoryId?: string;
  brandId?: string;
  priceMin?: number;
  priceMax?: number;
}

export class WishlistRepository extends BaseRepository<
  WishlistItem,
  WishlistItemCreateInput,
  WishlistItemUpdateInput
> {
  constructor() {
    super("wishlistItem");
  }

  // Tìm wishlist items của user
  async findUserWishlist(
    userId: string,
    options: {
      page?: number;
      limit?: number;
    } = {}
  ): Promise<any> {
    const { page = 1, limit = 12 } = options;

    return await this.findWithPagination({
      page,
      limit,
      where: { userId },
      include: {
        product: {
          include: {
            media: {
              include: { media: true },
              where: { isPrimary: true },
              take: 1,
            },
            brand: {
              select: { id: true, name: true, slug: true },
            },
            category: {
              select: { id: true, name: true, slug: true },
            },
          },
        },
      },
      orderBy: { createdAt: "desc" },
    });
  }

  // Kiểm tra product có trong wishlist của user không
  async isInWishlist(userId: string, productId: string): Promise<boolean> {
    const item = await this.findFirst({
      userId,
      productId,
    });
    return !!item;
  }

  // Thêm product vào wishlist
  async addToWishlist(
    userId: string,
    productId: string
  ): Promise<WishlistItem> {
    // Kiểm tra đã có trong wishlist chưa
    const existingItem = await this.findFirst({
      userId,
      productId,
    });

    if (existingItem) {
      throw new ConflictError("Sản phẩm đã có trong wishlist");
    }

    // Kiểm tra product tồn tại và active
    const product = await this.prisma.product.findUnique({
      where: { id: productId },
      select: { id: true, status: true },
    });

    if (!product) {
      throw new NotFoundError("Product", productId);
    }

    if (product.status !== "ACTIVE") {
      throw new Error("Không thể thêm sản phẩm không hoạt động vào wishlist");
    }

    return await this.model.create({
      data: {
        user: { connect: { id: userId } },
        product: { connect: { id: productId } },
      },
      include: {
        product: {
          include: {
            media: { include: { media: true } },
            brand: true,
            category: true,
          },
        },
      },
    });
  }

  // Xóa product khỏi wishlist
  async removeFromWishlist(userId: string, productId: string): Promise<void> {
    const item = await this.findFirst({
      userId,
      productId,
    });

    if (!item) {
      throw new NotFoundError("WishlistItem", `${userId}-${productId}`);
    }

    await this.delete(item.id);
  }

  // Toggle product trong wishlist
  async toggleWishlist(
    userId: string,
    productId: string
  ): Promise<{
    action: "added" | "removed";
    item?: WishlistItem;
  }> {
    const existingItem = await this.findFirst({
      userId,
      productId,
    });

    if (existingItem) {
      await this.delete(existingItem.id);
      return { action: "removed" };
    } else {
      const item = await this.addToWishlist(userId, productId);
      return { action: "added", item };
    }
  }

  // Xóa tất cả items trong wishlist của user
  async clearWishlist(userId: string): Promise<void> {
    await this.deleteMany({ userId });
  }

  // Lấy số lượng items trong wishlist
  async getWishlistCount(userId: string): Promise<number> {
    return await this.count({ userId });
  }

  // Tìm kiếm trong wishlist với filter
  async searchWishlist(
    options: WishlistSearchOptions & {
      page?: number;
      limit?: number;
    }
  ) {
    const {
      search,
      userId,
      categoryId,
      brandId,
      priceMin,
      priceMax,
      page = 1,
      limit = 12,
    } = options;

    const where: Prisma.WishlistItemWhereInput = {};

    if (userId) {
      where.userId = userId;
    }

    if (
      search ||
      categoryId ||
      brandId ||
      priceMin !== undefined ||
      priceMax !== undefined
    ) {
      where.product = {};

      if (search) {
        where.product.OR = [
          { name: { contains: search, mode: "insensitive" } },
          { description: { contains: search, mode: "insensitive" } },
        ];
      }

      if (categoryId) {
        where.product.categoryId = categoryId;
      }

      if (brandId) {
        where.product.brandId = brandId;
      }

      if (priceMin !== undefined || priceMax !== undefined) {
        where.product.price = {};
        if (priceMin !== undefined) where.product.price.gte = priceMin;
        if (priceMax !== undefined) where.product.price.lte = priceMax;
      }

      // Chỉ lấy products active
      where.product.status = "ACTIVE";
    }

    return await this.findWithPagination({
      page,
      limit,
      where,
      include: {
        product: {
          include: {
            media: {
              include: { media: true },
              where: { isPrimary: true },
              take: 1,
            },
            brand: {
              select: { id: true, name: true, slug: true },
            },
            category: {
              select: { id: true, name: true, slug: true },
            },
          },
        },
      },
      orderBy: { createdAt: "desc" },
    });
  }

  // Lấy popular wishlist items (được thêm nhiều nhất)
  async getPopularWishlistItems(limit: number = 10): Promise<any[]> {
    return await this.prisma.$queryRaw`
      SELECT 
        p.*,
        COUNT(w.id) as wishlist_count
      FROM products p
      INNER JOIN wishlist_items w ON p.id = w.product_id
      WHERE p.status = 'ACTIVE'
      GROUP BY p.id
      ORDER BY wishlist_count DESC
      LIMIT ${limit}
    `;
  }

  // Move wishlist items to cart
  async moveToCart(
    userId: string,
    productIds?: string[]
  ): Promise<{
    moved: number;
    failed: Array<{ productId: string; reason: string }>;
  }> {
    let wishlistItems: WishlistItemWithProduct[];

    if (productIds && productIds.length > 0) {
      wishlistItems = (await this.findMany({
        where: {
          userId,
          productId: { in: productIds },
        },
        include: { product: true },
      })) as WishlistItemWithProduct[];
    } else {
      wishlistItems = (await this.findMany({
        where: { userId },
        include: { product: true },
      })) as WishlistItemWithProduct[];
    }

    const moved: string[] = [];
    const failed: Array<{ productId: string; reason: string }> = [];

    for (const item of wishlistItems) {
      try {
        const product = item.product;

        // Kiểm tra product còn active và có stock
        if (product.status !== "ACTIVE") {
          failed.push({
            productId: product.id,
            reason: "Sản phẩm không còn hoạt động",
          });
          continue;
        }

        if (product.stock <= 0) {
          failed.push({
            productId: product.id,
            reason: "Sản phẩm hết hàng",
          });
          continue;
        }

        // Thêm vào cart (sử dụng CartRepository)
        // Tạm thời skip logic này, cần import CartRepository

        // Xóa khỏi wishlist
        await this.delete(item.id);
        moved.push(product.id);
      } catch (_error) {
        failed.push({
          productId: item.productId,
          reason: "Lỗi khi thêm vào giỏ hàng",
        });
      }
    }

    return {
      moved: moved.length,
      failed,
    };
  }

  // Lấy thống kê wishlist
  async getWishlistStats() {
    const [totalItems, totalUsers, avgItemsPerUser] = await Promise.all([
      this.count(),
      this.prisma.wishlistItem
        .groupBy({
          by: ["userId"],
          _count: { userId: true },
        })
        .then((result) => result.length),
      this.prisma.wishlistItem
        .groupBy({
          by: ["userId"],
          _count: { userId: true },
        })
        .then((result) => {
          if (result.length === 0) return 0;
          const total = result.reduce(
            (sum, item) => sum + item._count.userId,
            0
          );
          return total / result.length;
        }),
    ]);

    return {
      totalItems,
      totalUsers,
      avgItemsPerUser: Math.round(avgItemsPerUser * 10) / 10,
    };
  }

  // Lấy wishlist items sắp hết hàng
  async getLowStockWishlistItems(
    userId: string,
    threshold: number = 5
  ): Promise<WishlistItemWithProduct[]> {
    return await this.model.findMany({
      where: {
        userId,
        product: {
          status: "ACTIVE",
          stock: { lte: threshold },
        },
      },
      include: {
        product: {
          include: {
            media: {
              include: { media: true },
              where: { isPrimary: true },
              take: 1,
            },
            brand: {
              select: { id: true, name: true, slug: true },
            },
            category: {
              select: { id: true, name: true, slug: true },
            },
          },
        },
      },
      orderBy: { product: { stock: "asc" } },
    });
  }

  // Cleanup wishlist (xóa items của products không active)
  async cleanupWishlist(): Promise<number> {
    const result = await this.prisma.wishlistItem.deleteMany({
      where: {
        product: {
          status: { not: "ACTIVE" },
        },
      },
    });

    return result.count;
  }
}
