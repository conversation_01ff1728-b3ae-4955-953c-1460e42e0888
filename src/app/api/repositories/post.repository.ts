/**
 * Post Repository
 * <PERSON><PERSON><PERSON><PERSON> lý các thao tác database cho Post model
 */

import { Post, Prisma, PostStatus } from "@prisma/client";
import {
  BaseRepository,
  NotFoundError,
  ConflictError,
} from "./base.repository";

export type PostCreateInput = Omit<
  Prisma.PostCreateInput,
  "id" | "createdAt" | "updatedAt" | "author" | "category" | "featuredImage"
> & {
  authorId: string;
  categoryId?: string;
  featuredImageId?: string;
};
export type PostUpdateInput = Partial<PostCreateInput>;
export type PostWithRelations = Prisma.PostGetPayload<{
  include: {
    author: true;
    category: true;
    featuredImage: true;
  };
}>;

export interface PostSearchOptions {
  search?: string;
  authorId?: string;
  status?: PostStatus;
  featured?: boolean;
  dateFrom?: Date;
  dateTo?: Date;
  tags?: string[];
}

export class PostRepository extends BaseRepository<
  Post,
  PostCreateInput,
  PostUpdateInput
> {
  constructor() {
    super("post");
  }

  // Override create method để handle relations
  async create(data: PostCreateInput): Promise<Post> {
    const { authorId, categoryId, featuredImageId, ...postData } = data;

    const createData: Prisma.PostCreateInput = {
      ...postData,
      author: { connect: { id: authorId } },
      ...(categoryId && { category: { connect: { id: categoryId } } }),
      ...(featuredImageId && {
        featuredImage: { connect: { id: featuredImageId } },
      }),
    };

    return await this.prisma.post.create({ data: createData });
  }

  // Tìm post theo slug
  async findBySlug(slug: string): Promise<Post | null> {
    return await this.model.findUnique({
      where: { slug },
    });
  }

  // Tìm post với đầy đủ relations
  async findByIdWithRelations(id: string): Promise<PostWithRelations | null> {
    return await this.model.findUnique({
      where: { id },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
          },
        },
        featuredImage: true,
      },
    });
  }

  // Tìm post theo slug với relations
  async findBySlugWithRelations(
    slug: string
  ): Promise<PostWithRelations | null> {
    return await this.model.findUnique({
      where: { slug },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
          },
        },
        featuredImage: true,
      },
    });
  }

  // Tạo post mới với slug unique
  async createPost(data: PostCreateInput): Promise<Post> {
    // Kiểm tra slug đã tồn tại
    if (data.slug) {
      const existingSlug = await this.findBySlug(data.slug);
      if (existingSlug) {
        throw new ConflictError("Slug đã tồn tại");
      }
    }

    return await this.create(data);
  }

  // Tìm kiếm posts với filter
  async searchPosts(
    options: PostSearchOptions & {
      page?: number;
      limit?: number;
      sortBy?: string;
      sortOrder?: "asc" | "desc";
    }
  ) {
    const {
      search,
      authorId,
      status = "PUBLISHED",
      featured,
      dateFrom,
      dateTo,
      tags,
      page = 1,
      limit = 10,
      sortBy = "createdAt",
      sortOrder = "desc",
    } = options;

    const where: Prisma.PostWhereInput = { status };

    if (search) {
      where.OR = [
        { title: { contains: search, mode: "insensitive" } },
        { content: { contains: search, mode: "insensitive" } },
        { excerpt: { contains: search, mode: "insensitive" } },
      ];
    }

    if (authorId) {
      where.authorId = authorId;
    }

    if (typeof featured === "boolean") {
      where.featured = featured;
    }

    if (dateFrom || dateTo) {
      where.createdAt = {};
      if (dateFrom) where.createdAt.gte = dateFrom;
      if (dateTo) where.createdAt.lte = dateTo;
    }

    if (tags && tags.length > 0) {
      where.tags = { hasSome: tags };
    }

    return await this.findWithPagination({
      page,
      limit,
      where,
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
          },
        },
        featuredImage: true,
      },
      orderBy: { [sortBy]: sortOrder },
    });
  }

  // Lấy published posts
  async getPublishedPosts(
    options: {
      page?: number;
      limit?: number;
      featured?: boolean;
    } = {}
  ): Promise<any> {
    const { page = 1, limit = 10, featured } = options;

    const where: Prisma.PostWhereInput = {
      status: "PUBLISHED",
    };

    if (typeof featured === "boolean") {
      where.featured = featured;
    }

    return await this.findWithPagination({
      page,
      limit,
      where,
      include: {
        author: {
          select: {
            id: true,
            name: true,
            avatar: true,
          },
        },
        featuredImage: true,
      },
      orderBy: { publishedAt: "desc" },
    });
  }

  // Lấy featured posts
  async getFeaturedPosts(limit: number = 5): Promise<Post[]> {
    return await this.findMany({
      where: {
        featured: true,
        status: "PUBLISHED",
        publishedAt: { lte: new Date() },
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            avatar: true,
          },
        },
        featuredImage: true,
      },
      orderBy: { publishedAt: "desc" },
      take: limit,
    });
  }

  // Lấy recent posts
  async getRecentPosts(limit: number = 10): Promise<Post[]> {
    return await this.findMany({
      where: {
        status: "PUBLISHED",
        publishedAt: { lte: new Date() },
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            avatar: true,
          },
        },
        featuredImage: true,
      },
      orderBy: { publishedAt: "desc" },
      take: limit,
    });
  }

  // Lấy posts của author
  async getAuthorPosts(
    authorId: string,
    options: {
      page?: number;
      limit?: number;
      status?: PostStatus;
    } = {}
  ): Promise<any> {
    const { page = 1, limit = 10, status } = options;

    const where: Prisma.PostWhereInput = { authorId };
    if (status) {
      where.status = status;
    }

    return await this.findWithPagination({
      page,
      limit,
      where,
      include: {
        featuredImage: true,
      },
      orderBy: { createdAt: "desc" },
    });
  }

  // Publish post
  async publishPost(id: string): Promise<Post> {
    const post = await this.findById(id);
    if (!post) {
      throw new NotFoundError("Post", id);
    }

    return await this.update(id, {
      status: "PUBLISHED",
    });
  }

  // Unpublish post
  async unpublishPost(id: string): Promise<Post> {
    return await this.update(id, {
      status: "DRAFT",
    });
  }

  // Archive post
  async archivePost(id: string): Promise<Post> {
    return await this.update(id, { status: "ARCHIVED" });
  }

  // Lấy posts theo tags
  async getPostsByTags(
    tags: string[],
    options: {
      page?: number;
      limit?: number;
    } = {}
  ): Promise<any> {
    const { page = 1, limit = 10 } = options;

    return await this.findWithPagination({
      page,
      limit,
      where: {
        status: "PUBLISHED",
        publishedAt: { lte: new Date() },
        tags: { hasSome: tags },
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            avatar: true,
          },
        },
        featuredImage: true,
      },
      orderBy: { publishedAt: "desc" },
    });
  }

  // Lấy all tags
  async getAllTags(): Promise<string[]> {
    const posts = await this.findMany({
      where: { status: "PUBLISHED" },
      select: { tags: true },
    });

    const allTags = posts.flatMap((post) => post.tags || []);
    const uniqueTags = Array.from(new Set(allTags));
    return uniqueTags.sort();
  }

  // Lấy popular tags
  async getPopularTags(
    limit: number = 20
  ): Promise<Array<{ tag: string; count: number }>> {
    const posts = await this.findMany({
      where: { status: "PUBLISHED" },
      select: { tags: true },
    });

    const tagCounts: Record<string, number> = {};
    posts.forEach((post) => {
      (post.tags || []).forEach((tag) => {
        tagCounts[tag] = (tagCounts[tag] || 0) + 1;
      });
    });

    return Object.entries(tagCounts)
      .map(([tag, count]) => ({ tag, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, limit);
  }

  // Lấy thống kê posts
  async getPostStats() {
    const [total, published, draft, archived, featured] = await Promise.all([
      this.count(),
      this.count({ status: "PUBLISHED" }),
      this.count({ status: "DRAFT" }),
      this.count({ status: "ARCHIVED" }),
      this.count({ featured: true }),
    ]);

    return {
      total,
      published,
      draft,
      archived,
      featured,
    };
  }

  // Lấy posts theo tháng
  async getPostsByMonth(year: number, month: number): Promise<Post[]> {
    const startDate = new Date(year, month - 1, 1);
    const endDate = new Date(year, month, 0, 23, 59, 59);

    return await this.findMany({
      where: {
        status: "PUBLISHED",
        publishedAt: {
          gte: startDate,
          lte: endDate,
        },
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            avatar: true,
          },
        },
        featuredImage: true,
      },
      orderBy: { publishedAt: "desc" },
    });
  }

  // Duplicate post
  async duplicatePost(id: string, authorId: string): Promise<Post> {
    const post = await this.findById(id);
    if (!post) {
      throw new NotFoundError("Post", id);
    }

    const newPostData: PostCreateInput = {
      title: `${post.title} (Copy)`,
      slug: `${post.slug}-copy-${Date.now()}`,
      content: post.content,
      excerpt: post.excerpt,
      authorId,
      status: "DRAFT",
      featured: false,
      tags: post.tags,
      featuredImageId: post.featuredImageId || undefined,
      categoryId: post.categoryId || undefined,
    };

    return await this.create(newPostData);
  }
}
