/**
 * Admin Authentication Utilities
 * Shared auth functions for admin API routes
 */

import { NextRequest } from 'next/server';
import { jwtVerify } from 'jose';

export interface AdminAuthResult {
  success: boolean;
  admin?: {
    id: string;
    role: string;
    type: 'admin';
    email?: string;
    name?: string;
  };
  error?: string;
}

/**
 * Verify admin authentication from request
 */
export async function verifyAdminAuth(request: NextRequest): Promise<AdminAuthResult> {
  try {
    const adminToken = request.cookies.get("admin-session")?.value;

    if (!adminToken) {
      return {
        success: false,
        error: "No admin session found"
      };
    }

    const secret = new TextEncoder().encode(
      process.env.NEXTAUTH_SECRET || "fallback-secret"
    );

    const { payload } = await jwtVerify(adminToken, secret);

    if (payload.role !== "ADMIN" && payload.role !== "MODERATOR") {
      return {
        success: false,
        error: "Insufficient permissions"
      };
    }

    return {
      success: true,
      admin: {
        id: payload.id as string,
        role: payload.role as string,
        type: "admin" as const,
        email: payload.email as string,
        name: payload.name as string,
      }
    };
  } catch (error) {
    console.error('Admin auth verification failed:', error);
    return {
      success: false,
      error: "Invalid admin session"
    };
  }
}

/**
 * Verify admin authentication and require specific role
 */
export async function verifyAdminAuthWithRole(
  request: NextRequest, 
  requiredRole: 'ADMIN' | 'MODERATOR' = 'MODERATOR'
): Promise<AdminAuthResult> {
  const authResult = await verifyAdminAuth(request);
  
  if (!authResult.success) {
    return authResult;
  }

  if (requiredRole === 'ADMIN' && authResult.admin?.role !== 'ADMIN') {
    return {
      success: false,
      error: "Admin role required"
    };
  }

  return authResult;
}

/**
 * Extract admin ID from request (for logging/audit purposes)
 */
export async function getAdminIdFromRequest(request: NextRequest): Promise<string | null> {
  const authResult = await verifyAdminAuth(request);
  return authResult.success ? authResult.admin!.id : null;
}
