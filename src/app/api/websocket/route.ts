/**
 * WebSocket API Route
 * Handle WebSocket connections for real-time notifications
 */

import { NextRequest } from "next/server";
import { WebSocketServer, WebSocket } from "ws";
import { webSocketService } from "../services/websocket.service";
import { jwtVerify } from "jose";

// Global WebSocket server instance
let wss: WebSocketServer | null = null;

/**
 * Initialize WebSocket server
 */
function initializeWebSocketServer(): WebSocketServer {
  if (wss) {
    return wss;
  }

  wss = new WebSocketServer({
    port: 8080,
    path: "/api/websocket",
  });

  wss.on("connection", async (ws: WebSocket, request) => {
    console.log("New WebSocket connection attempt");

    try {
      // Extract auth token from query params or headers
      const url = new URL(request.url || "", `http://${request.headers.host}`);
      const token =
        url.searchParams.get("token") ||
        request.headers.authorization?.replace("Bearer ", "");

      if (!token) {
        ws.close(1008, "Authentication required");
        return;
      }

      // Verify token
      const authResult = await verifyToken(token);
      if (!authResult.success) {
        ws.close(1008, "Invalid token");
        return;
      }

      // Add connection to service
      const connectionId = webSocketService.addConnection(
        ws,
        authResult.userId,
        authResult.adminId
      );

      console.log(`WebSocket connection established: ${connectionId}`);
    } catch (error) {
      console.error("WebSocket connection error:", error);
      ws.close(1011, "Internal server error");
    }
  });

  wss.on("error", (error) => {
    console.error("WebSocket server error:", error);
  });

  console.log("WebSocket server initialized on port 8080");
  return wss;
}

/**
 * Verify JWT token
 */
async function verifyToken(token: string): Promise<{
  success: boolean;
  userId?: string;
  adminId?: string;
  type?: "user" | "admin";
}> {
  try {
    const secret = new TextEncoder().encode(
      process.env.NEXTAUTH_SECRET || "fallback-secret"
    );

    const { payload } = await jwtVerify(token, secret);

    if (payload.type === "admin") {
      return {
        success: true,
        adminId: payload.id as string,
        type: "admin",
      };
    } else {
      return {
        success: true,
        userId: payload.id as string,
        type: "user",
      };
    }
  } catch (error) {
    console.error("Token verification failed:", error);
    return { success: false };
  }
}

/**
 * GET endpoint to initialize WebSocket server
 */
export async function GET(_request: NextRequest) {
  try {
    // Initialize WebSocket server if not already done
    const _server = initializeWebSocketServer();

    const stats = webSocketService.getStats();

    return new Response(
      JSON.stringify({
        success: true,
        message: "WebSocket server is running",
        stats,
        endpoint: "ws://localhost:8080/api/websocket",
      }),
      {
        status: 200,
        headers: {
          "Content-Type": "application/json",
        },
      }
    );
  } catch (error) {
    console.error("WebSocket initialization error:", error);
    return new Response(
      JSON.stringify({
        success: false,
        error: "Failed to initialize WebSocket server",
      }),
      {
        status: 500,
        headers: {
          "Content-Type": "application/json",
        },
      }
    );
  }
}

/**
 * POST endpoint to send test notification
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { type: _type, userId, adminOnly, message } = body;

    if (adminOnly) {
      const sentCount = webSocketService.sendNotificationToAdmins({
        id: `test_${Date.now()}`,
        title: "Test Admin Notification",
        message: message || "This is a test notification for admins",
        category: "info",
      });

      return new Response(
        JSON.stringify({
          success: true,
          message: `Notification sent to ${sentCount} admin connections`,
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" },
        }
      );
    }

    if (userId) {
      const sentCount = webSocketService.sendNotificationToUser(userId, {
        id: `test_${Date.now()}`,
        title: "Test User Notification",
        message: message || "This is a test notification",
        category: "info",
      });

      return new Response(
        JSON.stringify({
          success: true,
          message: `Notification sent to ${sentCount} user connections`,
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" },
        }
      );
    }

    // Broadcast to all
    const sentCount = webSocketService.broadcast({
      type: "notification",
      data: {
        id: `test_${Date.now()}`,
        title: "Broadcast Notification",
        message: message || "This is a broadcast notification",
        category: "info",
      },
    });

    return new Response(
      JSON.stringify({
        success: true,
        message: `Notification broadcast to ${sentCount} connections`,
      }),
      {
        status: 200,
        headers: { "Content-Type": "application/json" },
      }
    );
  } catch (error) {
    console.error("Send notification error:", error);
    return new Response(
      JSON.stringify({
        success: false,
        error: "Failed to send notification",
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" },
      }
    );
  }
}

// Cleanup on process exit
process.on("SIGTERM", () => {
  console.log("Shutting down WebSocket server...");
  webSocketService.shutdown();
  if (wss) {
    wss.close();
  }
});

process.on("SIGINT", () => {
  console.log("Shutting down WebSocket server...");
  webSocketService.shutdown();
  if (wss) {
    wss.close();
  }
  process.exit(0);
});
