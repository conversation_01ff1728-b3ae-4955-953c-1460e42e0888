import { NextRequest, NextResponse } from "next/server";
import { container } from "../di-container";
import { SETTING_SERVICE } from "../services/service-identifiers";
import type { SettingService } from "../services/setting.service";
// Ensure DI container is initialized
import "../di-setup";

// GET /api/settings - Lấy settings công khai (không cần auth)
export async function GET(_request: NextRequest) {
  try {
    const settingService = container.resolve<SettingService>(SETTING_SERVICE);

    // Get public settings from service
    const settingsObject = await settingService.getPublicSettings();

    return NextResponse.json(settingsObject);
  } catch (error) {
    console.error("Get public settings error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi lấy cài đặt" },
      { status: 500 }
    );
  }
}
