/**
 * Contact Service
 * Business logic cho Contact/Support management
 */

import { BaseService } from "./base.service";
import { Injectable } from "../di-container";
import { ContactRepository, UserRepository } from "../repositories";
import {
  ContactEntity,
  CreateContactData,
  UpdateContactData,
  ContactBusinessRules,
  ContactStatus,
  ContactPriority,
} from "../../models/contact.model";
import {
  PaginatedResult,
  SearchFilters,
  NotFoundError,
  ValidationError,
  ForbiddenError,
} from "../../models/common.model";
import { UserEntity } from "../../models/user.model";

// Service identifier
export const CONTACT_SERVICE = Symbol("ContactService");


export class ContactService extends BaseService {
  private contactRepository: ContactRepository;
  private userRepository: UserRepository;

  constructor(
    contactRepository: ContactRepository,
    userRepository: UserRepository
  ) {
    super();
    this.contactRepository = contactRepository;
    this.userRepository = userRepository;
  }

  /**
   * Tạo contact message mới
   */
  async createContact(
    data: CreateContactData,
    createdBy?: UserEntity
  ): Promise<ContactEntity> {
    return this.executeWithErrorHandling(async () => {
      // Validate input
      this.validateRequired(data, ["name", "email", "subject", "message"]);

      // Validate contact data
      const validation = ContactBusinessRules.validateContact(data);
      if (!validation.valid) {
        throw new ValidationError(validation.errors.join(", "));
      }

      // Create contact
      const contactData = {
        ...data,
        userId: createdBy?.id || null,
        status: ContactStatus.NEW,
        priority: data.priority || ContactPriority.NORMAL,
      };

      const contact = (await this.contactRepository.create(
        contactData
      )) as unknown as ContactEntity;

      // Log activity
      if (createdBy) {
        await this.logActivity("CONTACT_CREATED", createdBy.id, {
          contactId: contact.id,
          subject: contact.subject,
        });
      }

      return contact;
    }, "createContact");
  }

  /**
   * Lấy contact theo ID
   */
  async getContactById(
    id: string,
    requestedBy: UserEntity
  ): Promise<ContactEntity> {
    return this.executeWithErrorHandling(async () => {
      const contact = await this.contactRepository.findById(id);
      if (!contact) {
        throw new NotFoundError("Contact", id);
      }

      // Check permission
      if (
        !this.canViewContact(requestedBy, contact as unknown as ContactEntity)
      ) {
        throw new ForbiddenError("Cannot access this contact");
      }

      return contact as unknown as ContactEntity;
    }, "getContactById");
  }

  /**
   * Cập nhật contact
   */
  async updateContact(
    id: string,
    data: UpdateContactData,
    updatedBy: UserEntity
  ): Promise<ContactEntity> {
    return this.executeWithErrorHandling(async () => {
      // Check if contact exists
      const existingContact = await this.contactRepository.findById(id);
      if (!existingContact) {
        throw new NotFoundError("Contact", id);
      }

      // Check permission
      if (
        !this.canEditContact(
          updatedBy,
          existingContact as unknown as ContactEntity
        )
      ) {
        throw new ForbiddenError("Cannot update this contact");
      }

      // Update contact
      const updatedContact = (await this.contactRepository.update(
        id,
        data
      )) as unknown as ContactEntity;

      // Log activity
      await this.logActivity("CONTACT_UPDATED", updatedBy.id, {
        contactId: id,
        changes: Object.keys(data),
      });

      return updatedContact;
    }, "updateContact");
  }

  /**
   * Xóa contact
   */
  async deleteContact(id: string, deletedBy: UserEntity): Promise<void> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(deletedBy)) {
        throw new ForbiddenError("Only admins can delete contacts");
      }

      // Check if contact exists
      const contact = await this.contactRepository.findById(id);
      if (!contact) {
        throw new NotFoundError("Contact", id);
      }

      // Delete contact
      await this.contactRepository.delete(id);

      // Log activity
      await this.logActivity("CONTACT_DELETED", deletedBy.id, {
        contactId: id,
        subject: contact.subject,
      });
    }, "deleteContact");
  }

  /**
   * Lấy danh sách contacts
   */
  async getContacts(
    filters: SearchFilters & {
      status?: string;
      priority?: string;
      category?: string;
      userId?: string;
    } = {},
    requestedBy: UserEntity
  ): Promise<PaginatedResult<ContactEntity>> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission for viewing all contacts
      if (!this.isAdmin(requestedBy)) {
        // Non-admin users can only see their own contacts
        filters.userId = requestedBy.id;
      }

      // Validate pagination
      const page = filters.page || 1;
      const limit = filters.limit || 20;
      this.validatePagination(page, limit);

      // Build search conditions
      const searchConditions: any = {};

      if (filters.status) {
        searchConditions.status = filters.status;
      }

      if (filters.priority) {
        searchConditions.priority = filters.priority;
      }

      if (filters.category) {
        searchConditions.category = filters.category;
      }

      if (filters.userId) {
        searchConditions.userId = filters.userId;
      }

      if (filters.search) {
        const searchQuery = this.sanitizeSearchQuery(filters.search);
        searchConditions.OR = [
          { name: { contains: searchQuery, mode: "insensitive" } },
          { email: { contains: searchQuery, mode: "insensitive" } },
          { subject: { contains: searchQuery, mode: "insensitive" } },
          { message: { contains: searchQuery, mode: "insensitive" } },
        ];
      }

      // Get contacts with pagination
      const result = await this.contactRepository.findWithPagination({
        page,
        limit,
        where: searchConditions,
        orderBy: {
          [filters.sortBy || "createdAt"]: filters.sortOrder || "desc",
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      return {
        data: result.data.map((contact) => contact as unknown as ContactEntity),
        total: result.total,
        page: result.page,
        limit: result.limit,
        totalPages: result.totalPages,
        hasNext: result.hasNext,
        hasPrev: result.hasPrev,
      };
    }, "getContacts");
  }

  /**
   * Update contact status
   */
  async updateContactStatus(
    id: string,
    status: ContactStatus,
    updatedBy: UserEntity
  ): Promise<ContactEntity> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(updatedBy)) {
        throw new ForbiddenError("Only admins can update contact status");
      }

      const contact = await this.updateContact(id, { status }, updatedBy);

      // Log activity
      await this.logActivity("CONTACT_STATUS_UPDATED", updatedBy.id, {
        contactId: id,
        newStatus: status,
      });

      return contact;
    }, "updateContactStatus");
  }

  /**
   * Update contact priority
   */
  async updateContactPriority(
    id: string,
    priority: ContactPriority,
    updatedBy: UserEntity
  ): Promise<ContactEntity> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(updatedBy)) {
        throw new ForbiddenError("Only admins can update contact priority");
      }

      const contact = await this.updateContact(id, { priority }, updatedBy);

      // Log activity
      await this.logActivity("CONTACT_PRIORITY_UPDATED", updatedBy.id, {
        contactId: id,
        newPriority: priority,
      });

      return contact;
    }, "updateContactPriority");
  }

  /**
   * Assign contact to admin user
   */
  async assignContact(
    id: string,
    assignedToId: string,
    assignedBy: UserEntity
  ): Promise<ContactEntity> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(assignedBy)) {
        throw new ForbiddenError("Only admins can assign contacts");
      }

      // Check if assigned user exists and is admin
      const assignedUser = await this.userRepository.findById(assignedToId);
      if (
        !assignedUser ||
        !this.isAdmin(assignedUser as unknown as UserEntity)
      ) {
        throw new ValidationError("Can only assign to admin users");
      }

      const contact = await this.updateContact(
        id,
        {
          assignedTo: assignedToId,
          status: ContactStatus.IN_PROGRESS,
        },
        assignedBy
      );

      // Log activity
      await this.logActivity("CONTACT_ASSIGNED", assignedBy.id, {
        contactId: id,
        assignedToId,
      });

      return contact;
    }, "assignContact");
  }

  /**
   * Add response to contact
   */
  async addResponse(
    id: string,
    response: string,
    respondedBy: UserEntity
  ): Promise<ContactEntity> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(respondedBy)) {
        throw new ForbiddenError("Only admins can respond to contacts");
      }

      // Get existing contact
      const existingContact = await this.contactRepository.findById(id);
      if (!existingContact) {
        throw new NotFoundError("Contact", id);
      }

      const contact = await this.updateContact(
        id,
        {
          response,
          status: ContactStatus.IN_PROGRESS,
        },
        respondedBy
      );

      // Log activity
      await this.logActivity("CONTACT_RESPONSE_ADDED", respondedBy.id, {
        contactId: id,
      });

      return contact;
    }, "addResponse");
  }

  /**
   * Get contact statistics
   */
  async getContactStats(requestedBy: UserEntity): Promise<{
    total: number;
    byStatus: Record<string, number>;
    byPriority: Record<string, number>;
    byCategory: Record<string, number>;
    responseTime: {
      average: number;
      median: number;
    };
  }> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(requestedBy)) {
        throw new ForbiddenError("Only admins can view contact statistics");
      }

      const stats = await this.contactRepository.getContactStats();

      // Transform to expected format
      return {
        total: stats.total,
        byStatus: {
          pending: stats.pending,
          replied: stats.replied,
          resolved: stats.resolved,
        },
        byPriority: {},
        byCategory: {},
        responseTime: {
          average: 0,
          median: 0,
        },
      };
    }, "getContactStats");
  }

  /**
   * Get contact notes
   */
  async getContactNotes(
    contactId: string,
    requestedBy: UserEntity
  ): Promise<any[]> {
    return this.executeWithErrorHandling(async () => {
      // Check if contact exists and user has permission
      await this.getContactById(contactId, requestedBy);

      // For now, return empty array as notes functionality would need a separate table
      // In a real implementation, this would query a contact_notes table
      return [];
    }, "getContactNotes");
  }

  /**
   * Create contact note
   */
  async createContactNote(
    contactId: string,
    noteData: { note: string; isInternal?: boolean },
    createdBy: UserEntity
  ): Promise<any> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(createdBy)) {
        throw new ForbiddenError("Only admins can create contact notes");
      }

      // Check if contact exists
      await this.getContactById(contactId, createdBy);

      // For now, return a mock note object
      // In a real implementation, this would create a record in a contact_notes table
      const note = {
        id: `note_${Date.now()}`,
        contactId,
        note: noteData.note,
        isInternal: noteData.isInternal || true,
        createdBy: createdBy.id,
        createdAt: new Date(),
      };

      // Log activity
      await this.logActivity("CONTACT_NOTE_CREATED", createdBy.id, {
        contactId,
        noteId: note.id,
      });

      return note;
    }, "createContactNote");
  }

  /**
   * Export contacts
   */
  async exportContacts(
    filters: SearchFilters & {
      status?: string;
      priority?: string;
      category?: string;
      format?: "csv" | "json";
    },
    requestedBy: UserEntity
  ): Promise<string> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(requestedBy)) {
        throw new ForbiddenError("Only admins can export contacts");
      }

      // Get all matching contacts (without pagination)
      const allFilters = { ...filters, limit: 10000 }; // Large limit for export
      const result = await this.getContacts(allFilters, requestedBy);

      const format = filters.format || "csv";
      let exportData: string;

      if (format === "csv") {
        exportData = this.convertToCSV(result.data);
      } else {
        exportData = JSON.stringify(result.data, null, 2);
      }

      // Log export activity
      await this.logActivity("CONTACTS_EXPORTED", requestedBy.id, {
        format,
        recordCount: result.data.length,
        filters,
      });

      return exportData;
    }, "exportContacts");
  }

  /**
   * Helper method to convert contacts to CSV
   */
  private convertToCSV(contacts: ContactEntity[]): string {
    const headers = [
      "ID",
      "Name",
      "Email",
      "Phone",
      "Subject",
      "Category",
      "Priority",
      "Status",
      "Created At",
      "Responded At",
    ];

    const rows = contacts.map((contact) => [
      contact.id,
      contact.name,
      contact.email,
      contact.phone || "",
      contact.subject,
      "",
      contact.priority,
      contact.status,
      contact.createdAt.toISOString(),
      contact.respondedAt?.toISOString() || "",
    ]);

    const csvContent = [headers, ...rows]
      .map((row) => row.map((field) => `"${field}"`).join(","))
      .join("\n");

    return csvContent;
  }

  /**
   * Permission helper methods
   */
  private canViewContact(user: UserEntity, _contact: ContactEntity): boolean {
    return this.isAdmin(user);
  }

  private canEditContact(user: UserEntity, _contact: ContactEntity): boolean {
    return this.isAdmin(user);
  }

  private isAdmin(user: UserEntity): boolean {
    return ["ADMIN", "SUPER_ADMIN"].includes(user.role as string);
  }
}
