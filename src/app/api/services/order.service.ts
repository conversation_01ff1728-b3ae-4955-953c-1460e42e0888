/**
 * Order Service
 * Business logic cho Order management
 */

import { SERVICE_IDENTIFIERS } from "../di-container";
import { BaseService } from "./base.service";
import {
  OrderRepository,
  ProductRepository,
  UserRepository,
  CartRepository,
} from "../repositories";
import {
  OrderEntity,
  OrderWithRelations,
  CreateOrderData,
  UpdateOrderData,
  OrderBusinessRules,
} from "../../models/order.model";
import {
  PaginatedResult,
  SearchFilters,
  OrderStatus,
  PaymentStatus,
} from "../../models/common.model";
import { UserEntity } from "../../models/user.model";

// Service identifier
export const ORDER_SERVICE = Symbol("OrderService");

export class OrderService extends BaseService {
  private orderRepository: OrderRepository;
  private productRepository: ProductRepository;
  private userRepository: UserRepository;
  private cartRepository: CartRepository;

  constructor() {
    super();
    this.orderRepository = this.getRepository<OrderRepository>(
      SERVICE_IDENTIFIERS.ORDER_REPOSITORY
    );
    this.productRepository = this.getRepository<ProductRepository>(
      SERVICE_IDENTIFIERS.PRODUCT_REPOSITORY
    );
    this.userRepository = this.getRepository<UserRepository>(
      SERVICE_IDENTIFIERS.USER_REPOSITORY
    );
    this.cartRepository = this.getRepository<CartRepository>(
      SERVICE_IDENTIFIERS.CART_REPOSITORY
    );
  }

  /**
   * Tạo đơn hàng mới
   */
  async createOrder(
    data: CreateOrderData,
    createdBy: UserEntity
  ): Promise<OrderEntity> {
    try {
      // Validate required fields
      this.validateRequired(data, ["userId", "items", "shippingAddress"]);

      // Validate user exists
      const user = await this.userRepository.findById(data.userId);
      if (!user) {
        throw new Error("User not found");
      }

      // Validate items and calculate totals
      let subtotal = 0;
      const validatedItems = [];

      for (const item of data.items) {
        const product = await this.productRepository.findById(item.productId);
        if (!product) {
          throw new Error(`Product not found: ${item.productId}`);
        }

        // Check stock availability
        if (product.stock < item.quantity) {
          throw new Error(`Insufficient stock for product: ${product.name}`);
        }

        // Validate price
        if (item.price !== product.price) {
          throw new Error(`Price mismatch for product: ${product.name}`);
        }

        const itemTotal = item.price * item.quantity;
        subtotal += itemTotal;

        validatedItems.push({
          ...item,
          name: product.name,
          total: itemTotal,
        });
      }

      // Calculate totals
      const tax = OrderBusinessRules.calculateTax(subtotal);
      const shipping = OrderBusinessRules.calculateShipping(
        subtotal,
        data.shippingAddress
      );
      const discount = data.discountCode
        ? await this.calculateDiscount(data.discountCode, subtotal)
        : 0;
      const total = subtotal + tax + shipping - discount;

      // Generate order number
      const orderNumber = OrderBusinessRules.generateOrderNumber();

      // Create order
      const orderData = {
        ...data,
        orderNumber,
        items: validatedItems,
        subtotal,
        tax,
        shipping,
        discount,
        total,
        status: OrderStatus.PENDING,
        paymentStatus: PaymentStatus.PENDING,
        createdBy: createdBy.id,
      };

      const order = await this.orderRepository.create(orderData);

      // Reserve stock for order items
      for (const item of validatedItems) {
        await this.productRepository.reserveStock(
          item.productId,
          item.quantity
        );
      }

      // Clear cart if order created from cart
      if (data.cartId) {
        await this.cartRepository.clear(data.cartId);
      }

      // Log activity
      await this.logActivity("order:created", createdBy.id, {
        orderId: order.id,
        orderNumber: order.orderNumber,
        total: order.total,
      });

      // Send notifications
      await this.sendNotification(user.id, "order:created", {
        orderNumber: order.orderNumber,
        total: order.total,
      });

      return order;
    } catch (error) {
      this.handleError(error, "OrderService.createOrder");
    }
  }

  /**
   * Cập nhật đơn hàng
   */
  async updateOrder(
    id: string,
    data: UpdateOrderData,
    updatedBy: UserEntity
  ): Promise<OrderEntity> {
    try {
      // Validate permissions
      this.validatePermission(updatedBy, "order:update");

      // Get existing order
      const existingOrder = await this.orderRepository.findById(id);
      if (!existingOrder) {
        throw new Error("Order not found");
      }

      // Validate status transitions
      if (
        data.status &&
        !OrderBusinessRules.canTransitionStatus(
          existingOrder.status,
          data.status
        )
      ) {
        throw new Error(
          `Cannot transition from ${existingOrder.status} to ${data.status}`
        );
      }

      // Handle status-specific logic
      if (data.status) {
        await this.handleStatusChange(existingOrder, data.status, updatedBy);
      }

      // Update order
      const updateData = {
        ...data,
        updatedBy: updatedBy.id,
        updatedAt: new Date(),
      };

      const order = await this.orderRepository.update(id, updateData);

      // Log activity
      await this.logActivity("order:updated", updatedBy.id, {
        orderId: order.id,
        orderNumber: order.orderNumber,
        changes: Object.keys(data),
      });

      return order;
    } catch (error) {
      this.handleError(error, "OrderService.updateOrder");
    }
  }

  /**
   * Hủy đơn hàng
   */
  async cancelOrder(
    id: string,
    reason: string,
    cancelledBy: UserEntity
  ): Promise<OrderEntity> {
    try {
      // Get existing order
      const existingOrder = await this.orderRepository.findById(id);
      if (!existingOrder) {
        throw new Error("Order not found");
      }

      // Check if order can be cancelled
      if (!OrderBusinessRules.canCancel(existingOrder)) {
        throw new Error("Order cannot be cancelled");
      }

      // Cancel order
      const order = await this.orderRepository.update(id, {
        status: OrderStatus.CANCELLED,
        cancelReason: reason,
        cancelledAt: new Date(),
        cancelledBy: cancelledBy.id,
      });

      // Release reserved stock
      for (const item of existingOrder.items) {
        await this.productRepository.releaseStock(
          item.productId,
          item.quantity
        );
      }

      // Process refund if payment was made
      if (existingOrder.paymentStatus === PaymentStatus.PAID) {
        await this.processRefund(order);
      }

      // Log activity
      await this.logActivity("order:cancelled", cancelledBy.id, {
        orderId: order.id,
        orderNumber: order.orderNumber,
        reason,
      });

      return order;
    } catch (error) {
      this.handleError(error, "OrderService.cancelOrder");
    }
  }

  /**
   * Lấy đơn hàng theo ID
   */
  async getOrderById(
    id: string,
    includeRelations = false
  ): Promise<OrderEntity | OrderWithRelations | null> {
    try {
      if (includeRelations) {
        return await this.orderRepository.findByIdWithRelations(id);
      }
      return await this.orderRepository.findById(id);
    } catch (error) {
      this.handleError(error, "OrderService.getOrderById");
    }
  }

  /**
   * Lấy đơn hàng theo order number
   */
  async getOrderByNumber(orderNumber: string): Promise<OrderEntity | null> {
    try {
      return await this.orderRepository.findByOrderNumber(orderNumber);
    } catch (error) {
      this.handleError(error, "OrderService.getOrderByNumber");
    }
  }

  /**
   * Lấy đơn hàng của user
   */
  async getUserOrders(
    userId: string,
    filters?: SearchFilters
  ): Promise<PaginatedResult<OrderEntity>> {
    try {
      return await this.orderRepository.findByUserId(userId, filters);
    } catch (error) {
      this.handleError(error, "OrderService.getUserOrders");
    }
  }

  /**
   * Tìm kiếm đơn hàng
   */
  async searchOrders(
    filters: SearchFilters
  ): Promise<PaginatedResult<OrderEntity>> {
    try {
      return await this.orderRepository.search(filters);
    } catch (error) {
      this.handleError(error, "OrderService.searchOrders");
    }
  }

  /**
   * Xử lý thay đổi trạng thái
   */
  private async handleStatusChange(
    order: OrderEntity,
    newStatus: OrderStatus,
    updatedBy: UserEntity
  ): Promise<void> {
    switch (newStatus) {
      case OrderStatus.CONFIRMED:
        // Confirm stock reservation
        for (const item of order.items) {
          await this.productRepository.confirmStock(
            item.productId,
            item.quantity
          );
        }
        break;

      case OrderStatus.SHIPPED:
        // Generate tracking number if not provided
        if (!order.trackingNumber) {
          order.trackingNumber = OrderBusinessRules.generateTrackingNumber();
        }
        break;

      case OrderStatus.DELIVERED:
        // Mark as delivered
        order.deliveredAt = new Date();
        break;
    }
  }

  /**
   * Tính toán discount
   */
  private async calculateDiscount(
    discountCode: string,
    subtotal: number
  ): Promise<number> {
    // Implementation for discount calculation
    // This would typically involve checking discount codes/coupons
    return 0;
  }

  /**
   * Xử lý refund
   */
  private async processRefund(order: OrderEntity): Promise<void> {
    // Implementation for refund processing
    // This would typically involve payment gateway integration
  }

  /**
   * Get order count with filters
   */
  async getOrderCount(
    filters: {
      status?: OrderStatus;
      paymentStatus?: PaymentStatus;
      userId?: string;
      startDate?: Date;
      endDate?: Date;
    } = {}
  ): Promise<number> {
    return this.executeWithErrorHandling(async () => {
      const where: any = {};

      if (filters.status) {
        where.status = filters.status;
      }

      if (filters.paymentStatus) {
        where.paymentStatus = filters.paymentStatus;
      }

      if (filters.userId) {
        where.userId = filters.userId;
      }

      if (filters.startDate || filters.endDate) {
        where.createdAt = {};
        if (filters.startDate) {
          where.createdAt.gte = filters.startDate;
        }
        if (filters.endDate) {
          where.createdAt.lte = filters.endDate;
        }
      }

      return await this.orderRepository.count(where);
    }, "getOrderCount");
  }

  /**
   * Get revenue by date range
   */
  async getRevenueByDateRange(startDate: Date, endDate: Date): Promise<number> {
    return this.executeWithErrorHandling(async () => {
      const orders = await this.orderRepository.findMany({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
          paymentStatus: PaymentStatus.PAID,
        },
        select: { total: true },
      });

      return orders.reduce(
        (sum: number, order: { total: number }) => sum + order.total,
        0
      );
    }, "getRevenueByDateRange");
  }

  /**
   * Get recent orders
   */
  async getRecentOrders(limit: number = 10): Promise<OrderEntity[]> {
    return this.executeWithErrorHandling(async () => {
      const orders = await this.orderRepository.findMany({
        orderBy: { createdAt: "desc" },
        take: limit,
        include: {
          user: {
            select: { name: true, email: true },
          },
        },
      });

      return orders as unknown as OrderEntity[];
    }, "getRecentOrders");
  }

  /**
   * Get total revenue
   */
  async getTotalRevenue(): Promise<number> {
    return this.executeWithErrorHandling(async () => {
      const orders = await this.orderRepository.findMany({
        where: {
          paymentStatus: PaymentStatus.PAID,
        },
        select: { total: true },
      });

      return orders.reduce(
        (sum: number, order: { total: number }) => sum + order.total,
        0
      );
    }, "getTotalRevenue");
  }
}
