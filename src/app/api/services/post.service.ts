/**
 * Post Service
 * Business logic cho Post/Blog management
 */

import { BaseService } from "./base.service";
import { Injectable } from "../di-container";
import {
  PostRepository,
  UserRepository,
  CategoryRepository,
} from "../repositories";
import {
  PostEntity,
  CreatePostData,
  UpdatePostData,
  PostBusinessRules,
} from "../../models/post.model";
import {
  PaginatedResult,
  SearchFilters,
  NotFoundError,
  ValidationError,
  ConflictError,
  ForbiddenError,
  PostStatus,
} from "../../models/common.model";
import { UserEntity } from "../../models/user.model";

// Service identifier
export const POST_SERVICE = Symbol("PostService");


export class PostService extends BaseService {
  private postRepository: PostRepository;
  private userRepository: UserRepository;
  private categoryRepository: CategoryRepository;

  constructor(
    postRepository: PostRepository,
    userRepository: UserRepository,
    categoryRepository: CategoryRepository
  ) {
    super();
    this.postRepository = postRepository;
    this.userRepository = userRepository;
    this.categoryRepository = categoryRepository;
  }

  /**
   * Tạo post mới
   */
  async createPost(
    data: CreatePostData,
    createdBy: UserEntity
  ): Promise<PostEntity> {
    return this.executeWithErrorHandling(async () => {
      // Check permission
      if (!this.canCreatePost(createdBy)) {
        throw new ForbiddenError("Only admins and editors can create posts");
      }

      // Validate input
      this.validateRequired(data, ["title", "content"]);

      // Validate post data
      const validation = PostBusinessRules.validatePost(data);
      if (!validation.valid) {
        throw new ValidationError(validation.errors.join(", "));
      }

      // Check slug uniqueness if provided
      if (data.slug) {
        const existingPost = await this.postRepository.findBySlug(data.slug);
        if (existingPost) {
          throw new ConflictError(
            `Post with slug '${data.slug}' already exists`
          );
        }
      }

      // Generate slug if not provided
      const slug = data.slug || PostBusinessRules.generateSlug(data.title);

      // Validate category if provided
      if (data.categoryId) {
        const category = await this.categoryRepository.findById(
          data.categoryId
        );
        if (!category) {
          throw new NotFoundError("Category", data.categoryId);
        }
      }

      // Create post
      const postData = {
        ...data,
        slug,
        authorId: createdBy.id,
        status: data.status || PostStatus.DRAFT,
        publishedAt: data.status === PostStatus.PUBLISHED ? new Date() : null,
      };

      const post = (await this.postRepository.create(
        postData
      )) as unknown as PostEntity;

      // Log activity
      await this.logActivity("POST_CREATED", createdBy.id, {
        postId: post.id,
        title: post.title,
        status: post.status,
      });

      return post;
    }, "createPost");
  }

  /**
   * Lấy post theo ID
   */
  async getPostById(id: string, requestedBy?: UserEntity): Promise<PostEntity> {
    return this.executeWithErrorHandling(async () => {
      const post = await this.postRepository.findById(id);
      if (!post) {
        throw new NotFoundError("Post", id);
      }

      // Check if post is published or user has permission
      if (
        post.status !== "PUBLISHED" &&
        (!requestedBy ||
          !this.canViewDraft(requestedBy, post as unknown as PostEntity))
      ) {
        throw new ForbiddenError("Cannot access unpublished post");
      }

      return post as unknown as PostEntity;
    }, "getPostById");
  }

  /**
   * Lấy post theo slug
   */
  async getPostBySlug(
    slug: string,
    requestedBy?: UserEntity
  ): Promise<PostEntity> {
    return this.executeWithErrorHandling(async () => {
      const post = await this.postRepository.findBySlug(slug);
      if (!post) {
        throw new NotFoundError("Post", slug);
      }

      // Check if post is published or user has permission
      if (
        post.status !== PostStatus.PUBLISHED &&
        (!requestedBy ||
          !this.canViewDraft(requestedBy, post as unknown as PostEntity))
      ) {
        throw new ForbiddenError("Cannot access unpublished post");
      }

      // Increment view count for published posts
      if (post.status === PostStatus.PUBLISHED) {
        // Note: incrementViewCount method needs to be implemented in repository
        // await this.postRepository.incrementViewCount(post.id);
      }

      return post as unknown as PostEntity;
    }, "getPostBySlug");
  }

  /**
   * Cập nhật post
   */
  async updatePost(
    id: string,
    data: UpdatePostData,
    updatedBy: UserEntity
  ): Promise<PostEntity> {
    return this.executeWithErrorHandling(async () => {
      // Check if post exists
      const existingPost = await this.postRepository.findById(id);
      if (!existingPost) {
        throw new NotFoundError("Post", id);
      }

      // Check permission
      if (!this.canEditPost(updatedBy, existingPost as unknown as PostEntity)) {
        throw new ForbiddenError("Cannot edit this post");
      }

      // Validate slug if provided
      if (data.slug && data.slug !== existingPost.slug) {
        const postWithSlug = await this.postRepository.findBySlug(data.slug);
        if (postWithSlug) {
          throw new ConflictError(
            `Post with slug '${data.slug}' already exists`
          );
        }
      }

      // Validate category if provided
      if (data.categoryId) {
        const category = await this.categoryRepository.findById(
          data.categoryId
        );
        if (!category) {
          throw new NotFoundError("Category", data.categoryId);
        }
      }

      // Handle status change
      let updateData = { ...data };
      if (data.status && data.status !== existingPost.status) {
        if (
          data.status === PostStatus.PUBLISHED &&
          existingPost.status !== PostStatus.PUBLISHED
        ) {
          updateData.publishedAt = new Date();
        } else if (data.status !== PostStatus.PUBLISHED) {
          updateData.publishedAt = undefined;
        }
      }

      // Update post
      const updatedPost = (await this.postRepository.update(
        id,
        updateData
      )) as unknown as PostEntity;

      // Log activity
      await this.logActivity("POST_UPDATED", updatedBy.id, {
        postId: id,
        changes: Object.keys(data),
      });

      return updatedPost;
    }, "updatePost");
  }

  /**
   * Xóa post
   */
  async deletePost(id: string, deletedBy: UserEntity): Promise<void> {
    return this.executeWithErrorHandling(async () => {
      // Check if post exists
      const post = await this.postRepository.findById(id);
      if (!post) {
        throw new NotFoundError("Post", id);
      }

      // Check permission
      if (!this.canDeletePost(deletedBy, post as unknown as PostEntity)) {
        throw new ForbiddenError("Cannot delete this post");
      }

      // Delete post
      await this.postRepository.delete(id);

      // Log activity
      await this.logActivity("POST_DELETED", deletedBy.id, {
        postId: id,
        title: post.title,
      });
    }, "deletePost");
  }

  /**
   * Lấy danh sách posts
   */
  async getPosts(
    filters: SearchFilters & {
      status?: string;
      categoryId?: string;
      authorId?: string;
      featured?: boolean;
    } = {},
    requestedBy?: UserEntity
  ): Promise<PaginatedResult<PostEntity>> {
    return this.executeWithErrorHandling(async () => {
      // Validate pagination
      const page = filters.page || 1;
      const limit = filters.limit || 20;
      this.validatePagination(page, limit);

      // Build search conditions
      const searchConditions: any = {};

      // Non-admin users can only see published posts
      if (!requestedBy || !this.isAdmin(requestedBy)) {
        searchConditions.status = PostStatus.PUBLISHED;
      } else if (filters.status) {
        searchConditions.status = filters.status;
      }

      if (filters.categoryId) {
        searchConditions.categoryId = filters.categoryId;
      }

      if (filters.authorId) {
        searchConditions.authorId = filters.authorId;
      }

      if (filters.featured !== undefined) {
        searchConditions.featured = filters.featured;
      }

      if (filters.search) {
        const searchQuery = this.sanitizeSearchQuery(filters.search);
        searchConditions.OR = [
          { title: { contains: searchQuery, mode: "insensitive" } },
          { content: { contains: searchQuery, mode: "insensitive" } },
          { excerpt: { contains: searchQuery, mode: "insensitive" } },
        ];
      }

      // Get posts with pagination
      const result = await this.postRepository.findWithPagination({
        page,
        limit,
        where: searchConditions,
        orderBy: {
          [filters.sortBy || "publishedAt"]: filters.sortOrder || "desc",
        },
        include: {
          author: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          category: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
        },
      });

      return {
        data: result.data.map((post) => post as unknown as PostEntity),
        total: result.total,
        page: result.page,
        limit: result.limit,
        totalPages: result.totalPages,
        hasNext: result.hasNext,
        hasPrev: result.hasPrev,
      };
    }, "getPosts");
  }

  /**
   * Publish post
   */
  async publishPost(id: string, publishedBy: UserEntity): Promise<PostEntity> {
    return this.executeWithErrorHandling(async () => {
      const post = await this.updatePost(
        id,
        {
          status: PostStatus.PUBLISHED,
          publishedAt: new Date(),
        },
        publishedBy
      );

      // Log activity
      await this.logActivity("POST_PUBLISHED", publishedBy.id, {
        postId: id,
      });

      return post;
    }, "publishPost");
  }

  /**
   * Unpublish post
   */
  async unpublishPost(
    id: string,
    unpublishedBy: UserEntity
  ): Promise<PostEntity> {
    return this.executeWithErrorHandling(async () => {
      const post = await this.updatePost(
        id,
        {
          status: PostStatus.DRAFT,
          publishedAt: undefined,
        },
        unpublishedBy
      );

      // Log activity
      await this.logActivity("POST_UNPUBLISHED", unpublishedBy.id, {
        postId: id,
      });

      return post;
    }, "unpublishPost");
  }

  /**
   * Toggle featured status
   */
  async toggleFeatured(id: string, updatedBy: UserEntity): Promise<PostEntity> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(updatedBy)) {
        throw new ForbiddenError("Only admins can feature posts");
      }

      const existingPost = await this.postRepository.findById(id);
      if (!existingPost) {
        throw new NotFoundError("Post", id);
      }

      const post = await this.updatePost(
        id,
        { featured: !existingPost.featured },
        updatedBy
      );

      // Log activity
      await this.logActivity("POST_FEATURED_TOGGLED", updatedBy.id, {
        postId: id,
        featured: post.featured,
      });

      return post;
    }, "toggleFeatured");
  }

  /**
   * Get related posts
   */
  async getRelatedPosts(
    postId: string,
    limit: number = 5
  ): Promise<PostEntity[]> {
    return this.executeWithErrorHandling(async () => {
      const post = await this.postRepository.findById(postId);
      if (!post) {
        throw new NotFoundError("Post", postId);
      }

      // For now, return featured posts as related posts
      // TODO: Implement proper related posts logic based on category/tags
      const relatedPosts = await this.postRepository.getFeaturedPosts(limit);

      return relatedPosts.map((p: any) => p as unknown as PostEntity);
    }, "getRelatedPosts");
  }

  /**
   * Permission helper methods
   */
  private canCreatePost(user: UserEntity): boolean {
    return ["ADMIN", "SUPER_ADMIN", "EDITOR"].includes(user.role as string);
  }

  private canViewDraft(user: UserEntity, post: PostEntity): boolean {
    return this.isAdmin(user) || user.id === post.authorId;
  }

  private canEditPost(user: UserEntity, post: PostEntity): boolean {
    return this.isAdmin(user) || user.id === post.authorId;
  }

  private canDeletePost(user: UserEntity, post: PostEntity): boolean {
    return this.isAdmin(user) || user.id === post.authorId;
  }

  private isAdmin(user: UserEntity): boolean {
    return ["ADMIN", "SUPER_ADMIN"].includes(user.role as string);
  }
}
