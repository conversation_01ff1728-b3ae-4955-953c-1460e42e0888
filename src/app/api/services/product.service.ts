/**
 * Product Service
 * Business logic cho Product management
 */

import { SERVICE_IDENTIFIERS } from "../di-container";
import { BaseService } from "./base.service";
import { CacheService } from "./cache.service";
import {
  ProductRepository,
  CategoryRepository,
  BrandRepository,
} from "../repositories";
import { eventService, EVENT_TYPES, ProductEventData } from "./event.service";
import {
  ProductEntity,
  ProductWithRelations,
  CreateProductData,
  UpdateProductData,
  ProductBusinessRules,
} from "../../models/product.model";
import { ProductStatus } from "../../models/common.model";
import {
  PaginatedResult,
  SearchFilters,
  ForbiddenError,
  NotFoundError,
} from "../../models/common.model";
import { UserEntity } from "../../models/user.model";
import {
  transformPrismaProductToEntity,
  transformPrismaProductsToEntities,
  transformPrismaProductWithRelationsToEntity,
} from "../../dto/transforms/product.transform";

// Service identifier
export const PRODUCT_SERVICE = Symbol("ProductService");

export class ProductService extends BaseService {
  private productRepository: ProductRepository;
  private categoryRepository: CategoryRepository;
  private brandRepository: BrandRepository;
  private cacheService: CacheService;

  constructor() {
    super();
    this.productRepository = this.getRepository<ProductRepository>(
      SERVICE_IDENTIFIERS.PRODUCT_REPOSITORY
    );
    this.categoryRepository = this.getRepository<CategoryRepository>(
      SERVICE_IDENTIFIERS.CATEGORY_REPOSITORY
    );
    this.brandRepository = this.getRepository<BrandRepository>(
      SERVICE_IDENTIFIERS.BRAND_REPOSITORY
    );
    this.cacheService = this.getService<CacheService>(
      SERVICE_IDENTIFIERS.CACHE_SERVICE
    );
  }

  /**
   * Tạo sản phẩm mới
   */
  async createProduct(
    data: CreateProductData,
    createdBy: UserEntity
  ): Promise<ProductEntity> {
    try {
      // Validate permissions
      this.validatePermission(createdBy, "product:create");

      // Validate required fields
      this.validateRequired(data, ["name", "price", "categoryId"]);

      // Business rules validation
      if (!ProductBusinessRules.validatePrice(data.price)) {
        throw new Error("Invalid product price");
      }

      if (
        data.salePrice &&
        !ProductBusinessRules.validateSalePrice(data.price, data.salePrice)
      ) {
        throw new Error("Sale price must be less than regular price");
      }

      // Generate slug if not provided
      const slug = data.slug || ProductBusinessRules.generateSlug(data.name);

      // Check slug uniqueness
      const existingProduct = await this.productRepository.findBySlug(slug);
      if (existingProduct) {
        throw new Error("Product slug already exists");
      }

      // Validate category exists
      if (data.categoryId) {
        const category = await this.categoryRepository.findById(
          data.categoryId
        );
        if (!category) {
          throw new Error("Category not found");
        }
      }

      // Validate brand exists
      if (data.brandId) {
        const brand = await this.brandRepository.findById(data.brandId);
        if (!brand) {
          throw new Error("Brand not found");
        }
      }

      // Create product
      const productData = {
        ...data,
        slug,
        // Note: createdBy is not part of Prisma schema, handle separately if needed
      };

      const product = await this.productRepository.create(productData);

      // Log activity
      await this.logActivity("product:created", createdBy.id, {
        productId: product.id,
        productName: product.name,
      });

      // Emit product created event
      await eventService.emit<ProductEventData>(EVENT_TYPES.PRODUCT_CREATED, {
        productId: product.id,
        productName: product.name,
        userId: createdBy.id,
      });

      // Invalidate cache
      await this.cacheService.invalidateByTags([CacheService.TAGS.PRODUCTS]);

      return transformPrismaProductToEntity(product);
    } catch (error) {
      this.handleError(error, "ProductService.createProduct");
    }
  }

  /**
   * Cập nhật sản phẩm
   */
  async updateProduct(
    id: string,
    data: UpdateProductData,
    updatedBy: UserEntity
  ): Promise<ProductEntity> {
    try {
      // Validate permissions
      this.validatePermission(updatedBy, "product:update");

      // Get existing product
      const existingProduct = await this.productRepository.findById(id);
      if (!existingProduct) {
        throw new Error("Product not found");
      }

      // Business rules validation
      if (data.price && !ProductBusinessRules.validatePrice(data.price)) {
        throw new Error("Invalid product price");
      }

      if (
        data.salePrice &&
        !ProductBusinessRules.validateSalePrice(
          data.price || existingProduct.price,
          data.salePrice
        )
      ) {
        throw new Error("Sale price must be less than regular price");
      }

      // Handle slug update
      if (data.name && data.name !== existingProduct.name) {
        const newSlug = ProductBusinessRules.generateSlug(data.name);
        if (newSlug !== existingProduct.slug) {
          const existingSlug = await this.productRepository.findBySlug(newSlug);
          if (existingSlug && existingSlug.id !== id) {
            throw new Error("Product slug already exists");
          }
          data.slug = newSlug;
        }
      }

      // Validate category if changed
      if (data.categoryId && data.categoryId !== existingProduct.categoryId) {
        const category = await this.categoryRepository.findById(
          data.categoryId
        );
        if (!category) {
          throw new Error("Category not found");
        }
      }

      // Validate brand if changed
      if (data.brandId && data.brandId !== existingProduct.brandId) {
        const brand = await this.brandRepository.findById(data.brandId);
        if (!brand) {
          throw new Error("Brand not found");
        }
      }

      // Update product
      const updateData = {
        ...data,
        updatedBy: updatedBy.id,
        updatedAt: new Date(),
      };

      const product = await this.productRepository.update(id, updateData);

      // Log activity
      await this.logActivity("product:updated", updatedBy.id, {
        productId: product.id,
        productName: product.name,
        changes: Object.keys(data),
      });

      return transformPrismaProductToEntity(product);
    } catch (error) {
      this.handleError(error, "ProductService.updateProduct");
    }
  }

  /**
   * Bulk update products
   */
  async bulkUpdateProducts(
    productIds: string[],
    updateData: Partial<UpdateProductData>,
    updatedBy: UserEntity
  ): Promise<{ count: number; products: ProductEntity[] }> {
    try {
      // Check admin permission
      if (updatedBy.role !== "ADMIN" && updatedBy.role !== "SUPER_ADMIN") {
        throw new ForbiddenError("Only admins can bulk update products");
      }

      const updatedProducts: ProductEntity[] = [];

      for (const productId of productIds) {
        try {
          const product = await this.updateProduct(
            productId,
            updateData,
            updatedBy
          );
          updatedProducts.push(product);
        } catch (error) {
          // Continue with other products if one fails
          console.error(`Failed to update product ${productId}:`, error);
        }
      }

      // Log bulk activity
      await this.logActivity("product:bulk_updated", updatedBy.id, {
        productIds,
        updateData,
        successCount: updatedProducts.length,
      });

      return {
        count: updatedProducts.length,
        products: updatedProducts,
      };
    } catch (error) {
      this.handleError(error, "ProductService.bulkUpdateProducts");
    }
  }

  /**
   * Xóa sản phẩm
   */
  async deleteProduct(id: string, deletedBy: UserEntity): Promise<void> {
    try {
      // Validate permissions
      this.validatePermission(deletedBy, "product:delete");

      // Get existing product
      const existingProduct = await this.productRepository.findById(id);
      if (!existingProduct) {
        throw new Error("Product not found");
      }

      // Check if product can be deleted
      const productEntity = transformPrismaProductToEntity(existingProduct);
      if (!ProductBusinessRules.canDelete(productEntity)) {
        throw new Error("Product cannot be deleted");
      }

      // Soft delete
      await this.productRepository.softDelete(id, deletedBy.id);

      // Log activity
      await this.logActivity("product:deleted", deletedBy.id, {
        productId: id,
        productName: existingProduct.name,
      });
    } catch (error) {
      this.handleError(error, "ProductService.deleteProduct");
    }
  }

  /**
   * Lấy sản phẩm theo ID (with caching)
   */
  async getProductById(
    id: string,
    includeRelations = false
  ): Promise<ProductEntity | ProductWithRelations | null> {
    try {
      const cacheKey = CacheService.generateKey(
        "product",
        id,
        includeRelations ? "with-relations" : "basic"
      );

      return await this.cacheService.getOrSet(
        cacheKey,
        async () => {
          if (includeRelations) {
            const product =
              await this.productRepository.findByIdWithRelations(id);
            return product
              ? transformPrismaProductWithRelationsToEntity(product)
              : null;
          }
          const product = await this.productRepository.findById(id);
          return product ? transformPrismaProductToEntity(product) : null;
        },
        {
          ttl: CacheService.TTL.MEDIUM,
          tags: [CacheService.TAGS.PRODUCTS],
        }
      );
    } catch (error) {
      this.handleError(error, "ProductService.getProductById");
      return null;
    }
  }

  /**
   * Lấy sản phẩm theo slug
   */
  async getProductBySlug(
    slug: string,
    includeRelations = false
  ): Promise<ProductEntity | ProductWithRelations | null> {
    try {
      if (includeRelations) {
        const product =
          await this.productRepository.findBySlugWithRelations(slug);
        return product
          ? transformPrismaProductWithRelationsToEntity(product)
          : null;
      }
      const product = await this.productRepository.findBySlug(slug);
      return product ? transformPrismaProductToEntity(product) : null;
    } catch (error) {
      this.handleError(error, "ProductService.getProductBySlug");
    }
  }

  /**
   * Tìm kiếm sản phẩm
   */
  async searchProducts(
    filters: SearchFilters
  ): Promise<PaginatedResult<ProductEntity>> {
    try {
      // Transform SearchFilters to ProductSearchOptions
      const searchOptions = {
        search: filters.search,
        categoryId: filters.category,
        brandId: filters.brand,
        status: filters.status,
        featured: filters.featured,
        priceMin: filters.minPrice,
        priceMax: filters.maxPrice,
        page: filters.page,
        limit: filters.limit,
        sortBy: filters.sortBy,
        sortOrder: filters.sortOrder,
      };

      const result = await this.productRepository.searchProducts(searchOptions);
      return {
        ...result,
        data: transformPrismaProductsToEntities(result.data),
      };
    } catch (error) {
      this.handleError(error, "ProductService.searchProducts");
    }
  }

  /**
   * Lấy sản phẩm nổi bật
   */
  async getFeaturedProducts(limit = 10): Promise<ProductEntity[]> {
    try {
      const products = await this.productRepository.getFeaturedProducts(limit);
      return transformPrismaProductsToEntities(products);
    } catch (error) {
      this.handleError(error, "ProductService.getFeaturedProducts");
    }
  }

  /**
   * Lấy sản phẩm theo danh mục
   */
  async getProductsByCategory(
    categoryId: string,
    filters?: SearchFilters
  ): Promise<PaginatedResult<ProductEntity>> {
    try {
      // Transform SearchFilters to ProductSearchOptions and add categoryId
      const searchOptions = {
        search: filters?.search,
        categoryId: categoryId,
        brandId: filters?.brand,
        status: filters?.status,
        featured: filters?.featured,
        priceMin: filters?.minPrice,
        priceMax: filters?.maxPrice,
        page: filters?.page,
        limit: filters?.limit,
        sortBy: filters?.sortBy,
        sortOrder: filters?.sortOrder,
      };

      const result = await this.productRepository.searchProducts(searchOptions);
      return {
        ...result,
        data: transformPrismaProductsToEntities(result.data),
      };
    } catch (error) {
      this.handleError(error, "ProductService.getProductsByCategory");
    }
  }

  /**
   * Cập nhật stock sản phẩm
   */
  async updateStock(
    id: string,
    quantity: number,
    updatedBy: UserEntity
  ): Promise<ProductEntity> {
    try {
      // Validate permissions
      this.validatePermission(updatedBy, "product:update_stock");

      // Get existing product
      const existingProduct = await this.productRepository.findById(id);
      if (!existingProduct) {
        throw new Error("Product not found");
      }

      // Validate stock quantity
      if (quantity < 0) {
        throw new Error("Stock quantity cannot be negative");
      }

      // Update stock
      const product = await this.productRepository.updateStock(id, quantity);

      // Log activity
      await this.logActivity("product:stock_updated", updatedBy.id, {
        productId: id,
        oldStock: existingProduct.stock,
        newStock: quantity,
      });

      return transformPrismaProductToEntity(product);
    } catch (error) {
      this.handleError(error, "ProductService.updateStock");
    }
  }

  /**
   * Get related products based on category, brand, price, and tags
   */
  /**
   * Get related products based on category, brand, price, and tags
   */
  async getRelatedProducts(
    productId: string,
    limit: number = 4
  ): Promise<ProductEntity[]> {
    return this.executeWithErrorHandling(async () => {
      // Get the current product to find related products
      const currentProduct = await this.productRepository.findById(productId);
      if (!currentProduct) {
        throw new NotFoundError("Product", productId);
      }

      // Use repository method to get related products
      const relatedProducts = await this.productRepository.getRelatedProducts(
        productId,
        currentProduct.categoryId,
        limit
      );

      return transformPrismaProductsToEntities(relatedProducts);
    }, "getRelatedProducts");
  }

  /**
   * Tăng view count
   */
  async incrementViewCount(id: string): Promise<void> {
    try {
      await this.productRepository.incrementViewCount(id);
    } catch (error) {
      // Don't throw error for view count increment
      console.error("Failed to increment view count:", error);
    }
  }

  /**
   * Get product count with filters
   */
  async getProductCount(
    filters: {
      status?: ProductStatus;
      categoryId?: string;
      brandId?: string;
      featured?: boolean;
      hasImages?: boolean;
    } = {}
  ): Promise<number> {
    return this.executeWithErrorHandling(async () => {
      const where: any = {};

      if (filters.status) {
        where.status = filters.status;
      }

      if (filters.categoryId) {
        where.categoryId = filters.categoryId;
      }

      if (filters.brandId) {
        where.brandId = filters.brandId;
      }

      if (filters.featured !== undefined) {
        where.featured = filters.featured;
      }

      if (filters.hasImages === false) {
        where.images = { none: {} };
      }

      return await this.productRepository.count(where);
    }, "getProductCount");
  }

  /**
   * Get low stock products
   */
  async getLowStockProducts(
    limit: number = 10,
    threshold: number = 5
  ): Promise<ProductEntity[]> {
    return this.executeWithErrorHandling(async () => {
      const where = {
        stock: { lte: threshold },
        status: ProductStatus.ACTIVE,
      };

      const products = await this.productRepository.findMany({
        where,
        orderBy: { stock: "asc" },
        take: limit,
      });

      return products as unknown as ProductEntity[];
    }, "getLowStockProducts");
  }

  /**
   * Get top selling products
   */
  async getTopSellingProducts(limit: number = 10): Promise<ProductEntity[]> {
    return this.executeWithErrorHandling(async () => {
      // This would typically require order item data
      // For now, we'll return featured products as a placeholder
      const products = await this.productRepository.findMany({
        where: {
          status: ProductStatus.ACTIVE,
          featured: true,
        },
        orderBy: { createdAt: "desc" },
        take: limit,
      });

      return products as unknown as ProductEntity[];
    }, "getTopSellingProducts");
  }
}
