/**
 * Notification Handlers Service
 * Event handlers for notifications
 */

import {
  eventService,
  EVENT_TYPES,
  ProductEventData,
  OrderEventData,
  UserEventData,
  AdminEventData,
  NotificationEventData,
} from "./event.service";
import { webSocketService } from "./websocket.service";
import { emailService } from "./email.service";

export class NotificationHandlersService {
  private static initialized = false;

  /**
   * Initialize all notification handlers
   */
  static initialize(): void {
    if (this.initialized) {
      return;
    }

    this.setupProductHandlers();
    this.setupOrderHandlers();
    this.setupUserHandlers();
    this.setupAdminHandlers();
    this.setupSystemHandlers();

    this.initialized = true;
    console.log("Notification handlers initialized");
  }

  /**
   * Setup product event handlers
   */
  private static setupProductHandlers(): void {
    // Product created notification
    eventService.subscribe<ProductEventData>(
      EVENT_TYPES.PRODUCT_CREATED,
      async (data) => {
        await this.sendNotification({
          type: "product_created",
          title: "<PERSON>ản phẩm mới",
          message: `<PERSON><PERSON>n phẩm "${data.productName}" đã được tạo thành công`,
          data: { productId: data.productId },
        });
      },
      10
    );

    // Low stock alert
    eventService.subscribe<ProductEventData>(
      EVENT_TYPES.PRODUCT_STOCK_LOW,
      async (data) => {
        await this.sendAdminNotification({
          type: "stock_alert",
          title: "Cảnh báo tồn kho",
          message: `Sản phẩm "${data.productName}" sắp hết hàng`,
          data: { productId: data.productId, level: "warning" },
        });
      },
      20
    );

    // Out of stock alert
    eventService.subscribe<ProductEventData>(
      EVENT_TYPES.PRODUCT_OUT_OF_STOCK,
      async (data) => {
        await this.sendAdminNotification({
          type: "stock_alert",
          title: "Hết hàng",
          message: `Sản phẩm "${data.productName}" đã hết hàng`,
          data: { productId: data.productId, level: "critical" },
        });
      },
      30
    );
  }

  /**
   * Setup order event handlers
   */
  private static setupOrderHandlers(): void {
    // Order created notification
    eventService.subscribe<OrderEventData>(
      EVENT_TYPES.ORDER_CREATED,
      async (data) => {
        // Notify customer
        await this.sendUserNotification(data.userId, {
          type: "order_created",
          title: "Đơn hàng đã được tạo",
          message: `Đơn hàng #${data.orderId} của bạn đã được tạo thành công`,
          data: { orderId: data.orderId },
        });

        // Notify admin
        await this.sendAdminNotification({
          type: "new_order",
          title: "Đơn hàng mới",
          message: `Có đơn hàng mới #${data.orderId} với giá trị ${data.total?.toLocaleString("vi-VN")}đ`,
          data: { orderId: data.orderId, total: data.total },
        });
      },
      10
    );

    // Order status changed
    eventService.subscribe<OrderEventData>(
      EVENT_TYPES.ORDER_UPDATED,
      async (data) => {
        if (data.status) {
          const statusMessages = {
            CONFIRMED: "Đơn hàng đã được xác nhận",
            PROCESSING: "Đơn hàng đang được xử lý",
            SHIPPED: "Đơn hàng đã được giao cho đơn vị vận chuyển",
            DELIVERED: "Đơn hàng đã được giao thành công",
            CANCELLED: "Đơn hàng đã bị hủy",
          };

          const message =
            statusMessages[data.status as keyof typeof statusMessages] ||
            "Trạng thái đơn hàng đã thay đổi";

          await this.sendUserNotification(data.userId, {
            type: "order_status",
            title: "Cập nhật đơn hàng",
            message: `${message} - Đơn hàng #${data.orderId}`,
            data: { orderId: data.orderId, status: data.status },
          });
        }
      },
      10
    );
  }

  /**
   * Setup user event handlers
   */
  private static setupUserHandlers(): void {
    // User registered welcome notification
    eventService.subscribe<UserEventData>(
      EVENT_TYPES.USER_REGISTERED,
      async (data) => {
        // Send real-time notification
        await this.sendUserNotification(data.userId, {
          type: "welcome",
          title: "Chào mừng đến với NS Shop!",
          message: `Xin chào ${data.name || "bạn"}! Cảm ơn bạn đã đăng ký tài khoản tại NS Shop.`,
          data: { userId: data.userId },
        });

        // Send welcome email
        if (data.email) {
          await emailService.queueEmail({
            to: data.email,
            subject: "Chào mừng đến với NS Shop!",
            templateId: "welcome",
            templateData: {
              name: data.name || "bạn",
            },
            priority: "normal",
            tags: ["welcome", "user_registration"],
          });
        }
      },
      10
    );

    // Profile updated notification
    eventService.subscribe<UserEventData>(
      EVENT_TYPES.USER_PROFILE_UPDATED,
      async (data) => {
        await this.sendUserNotification(data.userId, {
          type: "profile_updated",
          title: "Thông tin cá nhân đã được cập nhật",
          message: "Thông tin cá nhân của bạn đã được cập nhật thành công.",
          data: { userId: data.userId, changes: data.changes },
        });
      },
      5
    );
  }

  /**
   * Setup admin event handlers
   */
  private static setupAdminHandlers(): void {
    // Admin login notification
    eventService.subscribe<AdminEventData>(
      EVENT_TYPES.ADMIN_LOGIN,
      async (data) => {
        await this.logAdminActivity({
          type: "admin_login",
          title: "Đăng nhập admin",
          message: `Admin đã đăng nhập hệ thống`,
          data: { adminId: data.adminId, timestamp: new Date() },
        });
      },
      10
    );

    // Admin action logging
    eventService.subscribe<AdminEventData>(
      EVENT_TYPES.ADMIN_ACTION,
      async (data) => {
        await this.logAdminActivity({
          type: "admin_action",
          title: "Hành động admin",
          message: `Admin thực hiện: ${data.action}`,
          data: {
            adminId: data.adminId,
            action: data.action,
            resource: data.resource,
            resourceId: data.resourceId,
            changes: data.changes,
          },
        });
      },
      10
    );
  }

  /**
   * Setup system event handlers
   */
  private static setupSystemHandlers(): void {
    // Cache invalidation logging
    eventService.subscribe(
      EVENT_TYPES.CACHE_INVALIDATED,
      async (data) => {
        console.log("Cache invalidated:", data);
      },
      5
    );

    // Error logging
    eventService.subscribe(
      EVENT_TYPES.ERROR_OCCURRED,
      async (data) => {
        console.error("System error occurred:", data);
        // Could send to error tracking service like Sentry
      },
      20
    );
  }

  /**
   * Send notification to specific user
   */
  private static async sendUserNotification(
    userId: string,
    notification: NotificationEventData
  ): Promise<void> {
    try {
      console.log(`[USER NOTIFICATION] ${userId}:`, notification);

      // Send real-time notification via WebSocket
      const sentCount = webSocketService.sendNotificationToUser(userId, {
        id: `notif_${Date.now()}`,
        title: notification.title,
        message: notification.message,
        category: "info",
        userId,
        data: notification.data,
      });

      if (sentCount > 0) {
        console.log(
          `Real-time notification sent to ${sentCount} connections for user ${userId}`
        );
      }

      // Could also save to database for offline users
      // await notificationRepository.create({
      //   userId,
      //   type: notification.type,
      //   title: notification.title,
      //   message: notification.message,
      //   data: notification.data
      // });
    } catch (error) {
      console.error("Failed to send user notification:", error);
    }
  }

  /**
   * Send notification to all admins
   */
  private static async sendAdminNotification(
    notification: NotificationEventData
  ): Promise<void> {
    try {
      console.log("[ADMIN NOTIFICATION]:", notification);

      // Send real-time notification to all connected admins
      const sentCount = webSocketService.sendNotificationToAdmins({
        id: `admin_notif_${Date.now()}`,
        title: notification.title,
        message: notification.message,
        category: "info",
        data: notification.data,
      });

      if (sentCount > 0) {
        console.log(
          `Admin notification sent to ${sentCount} admin connections`
        );
      }

      // Could also send to all admin users via database
      // const admins = await adminUserRepository.findAll();
      // for (const admin of admins) {
      //   await this.sendUserNotification(admin.id, notification);
      // }
    } catch (error) {
      console.error("Failed to send admin notification:", error);
    }
  }

  /**
   * Log admin activity
   */
  private static async logAdminActivity(
    activity: NotificationEventData
  ): Promise<void> {
    try {
      console.log("[ADMIN ACTIVITY]:", activity);

      // Could save to audit log
      // await auditLogRepository.create({
      //   action: activity.type,
      //   userId: activity.data?.adminId,
      //   details: activity.data
      // });
    } catch (error) {
      console.error("Failed to log admin activity:", error);
    }
  }

  /**
   * Send general notification
   */
  private static async sendNotification(
    notification: NotificationEventData
  ): Promise<void> {
    try {
      console.log("[NOTIFICATION]:", notification);
    } catch (error) {
      console.error("Failed to send notification:", error);
    }
  }
}
