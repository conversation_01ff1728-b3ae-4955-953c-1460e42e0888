/**
 * Cart Service
 * Business logic cho Cart management
 */

import { SERVICE_IDENTIFIERS } from "../di-container";
import { BaseService } from "./base.service";
import {
  CartRepository,
  ProductRepository,
  UserRepository,
} from "../repositories";
import {
  CartEntity,
  CartWithRelations,
  CreateCartData,
  UpdateCartData,
  CartItemEntity,
  CartBusinessRules,
  CartStatus,
} from "../../models/cart.model";
import { UserEntity } from "../../models/user.model";

// Service identifier
export const CART_SERVICE = Symbol("CartService");

export class CartService extends BaseService {
  private cartRepository: CartRepository;
  private productRepository: ProductRepository;
  private userRepository: UserRepository;

  constructor() {
    super();
    this.cartRepository = this.getRepository<CartRepository>(
      SERVICE_IDENTIFIERS.CART_REPOSITORY
    );
    this.productRepository = this.getRepository<ProductRepository>(
      SERVICE_IDENTIFIERS.PRODUCT_REPOSITORY
    );
    this.userRepository = this.getRepository<UserRepository>(
      SERVICE_IDENTIFIERS.USER_REPOSITORY
    );
  }

  /**
   * Lấy hoặc tạo giỏ hàng cho user
   */
  async getOrCreateCart(userId: string): Promise<CartEntity> {
    try {
      // Try to get existing active cart
      let cart = await this.cartRepository.findActiveByUserId(userId);

      if (!cart) {
        // Create new cart
        const cartData: CreateCartData = {
          userId,
          status: CartStatus.ACTIVE,
          expiresAt: CartBusinessRules.calculateExpiryDate(),
        };

        cart = await this.cartRepository.create(cartData);
      }

      return cart;
    } catch (error) {
      this.handleError(error, "CartService.getOrCreateCart");
    }
  }

  /**
   * Thêm sản phẩm vào giỏ hàng
   */
  async addToCart(
    userId: string,
    productId: string,
    quantity: number,
    variantId?: string
  ): Promise<CartEntity> {
    try {
      // Validate product exists and is available
      const product = await this.productRepository.findById(productId);
      if (!product) {
        throw new Error("Product not found");
      }

      if (!CartBusinessRules.isProductAvailable(product)) {
        throw new Error("Product is not available");
      }

      // Validate quantity
      if (!CartBusinessRules.isValidQuantity(quantity)) {
        throw new Error("Invalid quantity");
      }

      if (quantity > product.stock) {
        throw new Error("Insufficient stock");
      }

      // Get or create cart
      const cart = await this.getOrCreateCart(userId);

      // Check if item already exists in cart
      const existingItem = await this.cartRepository.findCartItem(
        cart.id,
        productId,
        variantId
      );

      if (existingItem) {
        // Update existing item
        const newQuantity = existingItem.quantity + quantity;

        // Validate total quantity
        if (newQuantity > CartBusinessRules.MAX_QUANTITY_PER_ITEM) {
          throw new Error(
            `Maximum quantity per item is ${CartBusinessRules.MAX_QUANTITY_PER_ITEM}`
          );
        }

        if (newQuantity > product.stock) {
          throw new Error("Insufficient stock");
        }

        await this.cartRepository.updateCartItem(existingItem.id, {
          quantity: newQuantity,
          price: product.price, // Update price in case it changed
        });
      } else {
        // Add new item
        const itemCount = await this.cartRepository.getItemCount(cart.id);
        if (itemCount >= CartBusinessRules.MAX_ITEMS_PER_CART) {
          throw new Error(
            `Maximum ${CartBusinessRules.MAX_ITEMS_PER_CART} items allowed per cart`
          );
        }

        await this.cartRepository.addCartItem({
          cartId: cart.id,
          productId,
          variantId,
          quantity,
          price: product.price,
        });
      }

      // Recalculate cart totals
      const updatedCart = await this.recalculateCart(cart.id);

      // Log activity
      await this.logActivity("cart:item_added", userId, {
        cartId: cart.id,
        productId,
        quantity,
      });

      return updatedCart;
    } catch (error) {
      this.handleError(error, "CartService.addToCart");
    }
  }

  /**
   * Cập nhật số lượng sản phẩm trong giỏ hàng
   */
  async updateCartItem(
    userId: string,
    itemId: string,
    quantity: number
  ): Promise<CartEntity> {
    try {
      // Validate quantity
      if (quantity < 0) {
        throw new Error("Quantity cannot be negative");
      }

      // Get cart item
      const cartItem = await this.cartRepository.findCartItemById(itemId);
      if (!cartItem) {
        throw new Error("Cart item not found");
      }

      // Verify ownership
      const cart = await this.cartRepository.findById(cartItem.cartId);
      if (!cart || cart.userId !== userId) {
        throw new Error("Cart not found or access denied");
      }

      if (quantity === 0) {
        // Remove item
        await this.cartRepository.removeCartItem(itemId);
      } else {
        // Validate quantity limits
        if (quantity > CartBusinessRules.MAX_QUANTITY_PER_ITEM) {
          throw new Error(
            `Maximum quantity per item is ${CartBusinessRules.MAX_QUANTITY_PER_ITEM}`
          );
        }

        // Check stock availability
        const product = await this.productRepository.findById(
          cartItem.productId
        );
        if (!product || quantity > product.stock) {
          throw new Error("Insufficient stock");
        }

        // Update item
        await this.cartRepository.updateCartItem(itemId, {
          quantity,
          price: product.price, // Update price in case it changed
        });
      }

      // Recalculate cart totals
      const updatedCart = await this.recalculateCart(cart.id);

      // Log activity
      await this.logActivity("cart:item_updated", userId, {
        cartId: cart.id,
        itemId,
        quantity,
      });

      return updatedCart;
    } catch (error) {
      this.handleError(error, "CartService.updateCartItem");
    }
  }

  /**
   * Xóa sản phẩm khỏi giỏ hàng
   */
  async removeFromCart(userId: string, itemId: string): Promise<CartEntity> {
    try {
      return await this.updateCartItem(userId, itemId, 0);
    } catch (error) {
      this.handleError(error, "CartService.removeFromCart");
    }
  }

  /**
   * Xóa toàn bộ giỏ hàng
   */
  async clearCart(userId: string): Promise<void> {
    try {
      const cart = await this.cartRepository.findActiveByUserId(userId);
      if (cart) {
        await this.cartRepository.clearCartItems(cart.id);
        await this.recalculateCart(cart.id);

        // Log activity
        await this.logActivity("cart:cleared", userId, {
          cartId: cart.id,
        });
      }
    } catch (error) {
      this.handleError(error, "CartService.clearCart");
    }
  }

  /**
   * Lấy giỏ hàng của user
   */
  async getUserCart(
    userId: string,
    includeItems = true
  ): Promise<CartEntity | CartWithRelations | null> {
    try {
      if (includeItems) {
        return await this.cartRepository.findActiveByUserIdWithItems(userId);
      }
      return await this.cartRepository.findActiveByUserId(userId);
    } catch (error) {
      this.handleError(error, "CartService.getUserCart");
    }
  }

  /**
   * Tính toán lại tổng tiền giỏ hàng
   */
  async recalculateCart(cartId: string): Promise<CartEntity> {
    try {
      const cartItems = await this.cartRepository.getCartItems(cartId);

      let subtotal = 0;
      for (const item of cartItems) {
        subtotal += item.price * item.quantity;
      }

      const tax = CartBusinessRules.calculateTax(subtotal);
      const shipping = CartBusinessRules.calculateShipping(subtotal);
      const total = subtotal + tax + shipping;

      const updatedCart = await this.cartRepository.update(cartId, {
        subtotal,
        tax,
        shipping,
        total,
      });

      return updatedCart;
    } catch (error) {
      this.handleError(error, "CartService.recalculateCart");
    }
  }

  /**
   * Chuyển đổi giỏ hàng thành đơn hàng
   */
  async convertToOrder(
    userId: string,
    shippingAddress: any,
    paymentMethod: string
  ): Promise<any> {
    try {
      const cart =
        await this.cartRepository.findActiveByUserIdWithItems(userId);
      if (!cart || !cart.items || cart.items.length === 0) {
        throw new Error("Cart is empty");
      }

      // Validate all items are still available
      for (const item of cart.items) {
        const product = await this.productRepository.findById(item.productId);
        if (!product || !CartBusinessRules.isProductAvailable(product)) {
          throw new Error(`Product ${item.productId} is no longer available`);
        }

        if (item.quantity > product.stock) {
          throw new Error(`Insufficient stock for product ${item.productId}`);
        }
      }

      // Create order data
      const orderData = {
        userId,
        cartId: cart.id,
        items: cart.items.map((item: any) => ({
          productId: item.productId,
          variantId: item.variantId,
          quantity: item.quantity,
          price: item.price,
        })),
        subtotal: cart.subtotal,
        tax: cart.tax,
        shipping: cart.shipping,
        total: cart.total,
        shippingAddress,
        paymentMethod,
      };

      return orderData;
    } catch (error) {
      this.handleError(error, "CartService.convertToOrder");
    }
  }

  /**
   * Dọn dẹp giỏ hàng hết hạn
   */
  async cleanupExpiredCarts(): Promise<void> {
    try {
      await this.cartRepository.deleteExpiredCarts();
    } catch (error) {
      console.error("Failed to cleanup expired carts:", error);
    }
  }
}
