/**
 * Inventory Service
 * Business logic cho Inventory/Stock management
 */

import { BaseService } from "./base.service";
import { Injectable } from "../di-container";
import { InventoryRepository, ProductRepository } from "../repositories";
import {
  InventoryEntity,
  CreateInventoryData,
  UpdateInventoryData,
  InventoryBusinessRules,
} from "../../models/inventory.model";
import {
  PaginatedResult,
  SearchFilters,
  NotFoundError,
  ValidationError,
  ConflictError,
  ForbiddenError,
} from "../../models/common.model";
import { UserEntity } from "../../models/user.model";

// Service identifier
export const INVENTORY_SERVICE = Symbol("InventoryService");


export class InventoryService extends BaseService {
  private inventoryRepository: InventoryRepository;
  private productRepository: ProductRepository;

  constructor(
    inventoryRepository: InventoryRepository,
    productRepository: ProductRepository
  ) {
    super();
    this.inventoryRepository = inventoryRepository;
    this.productRepository = productRepository;
  }

  /**
   * Tạo inventory record mới
   */
  async createInventory(
    data: CreateInventoryData,
    createdBy: UserEntity
  ): Promise<InventoryEntity> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(createdBy)) {
        throw new ForbiddenError("Only admins can create inventory records");
      }

      // Validate input
      this.validateRequired(data, ["productId", "quantity"]);

      // Check if product exists
      const product = await this.productRepository.findById(data.productId);
      if (!product) {
        throw new NotFoundError("Product", data.productId);
      }

      // Validate inventory data
      const validation = InventoryBusinessRules.validateInventory(data);
      if (!validation.valid) {
        throw new ValidationError(validation.errors.join(", "));
      }

      // Check if inventory already exists for this product/variant
      const existingInventory =
        await this.inventoryRepository.findByProductAndVariant(
          data.productId,
          data.variantId
        );
      if (existingInventory) {
        throw new ConflictError(
          "Inventory record already exists for this product/variant"
        );
      }

      // Create inventory
      const inventoryData = {
        ...data,
        reservedQuantity: 0,
        lastUpdatedBy: createdBy.id,
      };

      const inventory = (await this.inventoryRepository.create(
        inventoryData
      )) as unknown as InventoryEntity;

      // Log activity
      await this.logActivity("INVENTORY_CREATED", createdBy.id, {
        inventoryId: inventory.id,
        productId: data.productId,
        quantity: data.quantity,
      });

      return inventory;
    }, "createInventory");
  }

  /**
   * Lấy inventory theo ID
   */
  async getInventoryById(
    id: string,
    requestedBy: UserEntity
  ): Promise<InventoryEntity> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(requestedBy)) {
        throw new ForbiddenError("Only admins can access inventory details");
      }

      const inventory = await this.inventoryRepository.findById(id);
      if (!inventory) {
        throw new NotFoundError("Inventory", id);
      }

      return inventory as unknown as InventoryEntity;
    }, "getInventoryById");
  }

  /**
   * Lấy inventory theo product
   */
  async getInventoryByProduct(
    productId: string,
    variantId?: string
  ): Promise<InventoryEntity | null> {
    return this.executeWithErrorHandling(async () => {
      const inventory = await this.inventoryRepository.findByProductAndVariant(
        productId,
        variantId
      );

      return inventory ? (inventory as unknown as InventoryEntity) : null;
    }, "getInventoryByProduct");
  }

  /**
   * Cập nhật inventory
   */
  async updateInventory(
    id: string,
    data: UpdateInventoryData,
    updatedBy: UserEntity
  ): Promise<InventoryEntity> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(updatedBy)) {
        throw new ForbiddenError("Only admins can update inventory");
      }

      // Check if inventory exists
      const existingInventory = await this.inventoryRepository.findById(id);
      if (!existingInventory) {
        throw new NotFoundError("Inventory", id);
      }

      // Update inventory
      const updateData = {
        ...data,
        lastUpdatedBy: updatedBy.id,
        updatedAt: new Date(),
      };

      const updatedInventory = (await this.inventoryRepository.update(
        id,
        updateData
      )) as unknown as InventoryEntity;

      // Log activity
      await this.logActivity("INVENTORY_UPDATED", updatedBy.id, {
        inventoryId: id,
        changes: Object.keys(data),
      });

      return updatedInventory;
    }, "updateInventory");
  }

  /**
   * Xóa inventory
   */
  async deleteInventory(id: string, deletedBy: UserEntity): Promise<void> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(deletedBy)) {
        throw new ForbiddenError("Only admins can delete inventory records");
      }

      // Check if inventory exists
      const inventory = await this.inventoryRepository.findById(id);
      if (!inventory) {
        throw new NotFoundError("Inventory", id);
      }

      // Check if inventory has reserved quantity
      if (inventory.reserved > 0) {
        throw new ValidationError(
          "Cannot delete inventory with reserved quantity"
        );
      }

      // Delete inventory
      await this.inventoryRepository.delete(id);

      // Log activity
      await this.logActivity("INVENTORY_DELETED", deletedBy.id, {
        inventoryId: id,
        productId: inventory.productId,
      });
    }, "deleteInventory");
  }

  /**
   * Lấy danh sách inventory
   */
  async getInventories(
    filters: SearchFilters & {
      productId?: string;
      lowStock?: boolean;
      outOfStock?: boolean;
      location?: string;
    } = {},
    requestedBy: UserEntity
  ): Promise<PaginatedResult<InventoryEntity>> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(requestedBy)) {
        throw new ForbiddenError("Only admins can access inventory list");
      }

      // Validate pagination
      const page = filters.page || 1;
      const limit = filters.limit || 50;
      this.validatePagination(page, limit);

      // Build search conditions
      const searchConditions: any = {};

      if (filters.productId) {
        searchConditions.productId = filters.productId;
      }

      if (filters.location) {
        searchConditions.location = filters.location;
      }

      if (filters.lowStock) {
        searchConditions.quantity = { lte: 10 }; // Low stock threshold
      }

      if (filters.outOfStock) {
        searchConditions.quantity = { lte: 0 };
      }

      if (filters.search) {
        const searchQuery = this.sanitizeSearchQuery(filters.search);
        searchConditions.OR = [
          { product: { name: { contains: searchQuery, mode: "insensitive" } } },
          { product: { sku: { contains: searchQuery, mode: "insensitive" } } },
          { location: { contains: searchQuery, mode: "insensitive" } },
        ];
      }

      // Get inventories with pagination
      const result = await this.inventoryRepository.findWithPagination({
        page,
        limit,
        where: searchConditions,
        orderBy: {
          [filters.sortBy || "updatedAt"]: filters.sortOrder || "desc",
        },
        include: {
          product: {
            select: {
              id: true,
              name: true,
              sku: true,
              price: true,
            },
          },
        },
      });

      return {
        data: result.data.map(
          (inventory) => inventory as unknown as InventoryEntity
        ),
        total: result.total,
        page: result.page,
        limit: result.limit,
        totalPages: result.totalPages,
        hasNext: result.hasNext,
        hasPrev: result.hasPrev,
      };
    }, "getInventories");
  }

  /**
   * Reserve inventory
   */
  async reserveInventory(
    productId: string,
    quantity: number,
    variantId?: string,
    reservedBy?: UserEntity
  ): Promise<boolean> {
    return this.executeWithErrorHandling(async () => {
      const inventory = await this.inventoryRepository.findByProductAndVariant(
        productId,
        variantId
      );

      if (!inventory) {
        throw new NotFoundError(
          "Inventory",
          `${productId}${variantId ? `-${variantId}` : ""}`
        );
      }

      // Check available quantity
      const availableQuantity = inventory.quantity - inventory.reserved;
      if (availableQuantity < quantity) {
        return false; // Not enough stock
      }

      // Reserve inventory
      await this.inventoryRepository.update(inventory.id, {
        reserved: inventory.reserved + quantity,
      });

      // Log activity
      if (reservedBy) {
        await this.logActivity("INVENTORY_RESERVED", reservedBy.id, {
          inventoryId: inventory.id,
          productId,
          quantity,
          variantId,
        });
      }

      return true;
    }, "reserveInventory");
  }

  /**
   * Release reserved inventory
   */
  async releaseReservedInventory(
    productId: string,
    quantity: number,
    variantId?: string,
    releasedBy?: UserEntity
  ): Promise<void> {
    return this.executeWithErrorHandling(async () => {
      const inventory = await this.inventoryRepository.findByProductAndVariant(
        productId,
        variantId
      );

      if (!inventory) {
        throw new NotFoundError(
          "Inventory",
          `${productId}${variantId ? `-${variantId}` : ""}`
        );
      }

      // Release reserved inventory
      const newReservedQuantity = Math.max(0, inventory.reserved - quantity);
      await this.inventoryRepository.update(inventory.id, {
        reserved: newReservedQuantity,
      });

      // Log activity
      if (releasedBy) {
        await this.logActivity("INVENTORY_RELEASED", releasedBy.id, {
          inventoryId: inventory.id,
          productId,
          quantity,
          variantId,
        });
      }
    }, "releaseReservedInventory");
  }

  /**
   * Deduct inventory (for completed orders)
   */
  async deductInventory(
    productId: string,
    quantity: number,
    variantId?: string,
    deductedBy?: UserEntity
  ): Promise<void> {
    return this.executeWithErrorHandling(async () => {
      const inventory = await this.inventoryRepository.findByProductAndVariant(
        productId,
        variantId
      );

      if (!inventory) {
        throw new NotFoundError(
          "Inventory",
          `${productId}${variantId ? `-${variantId}` : ""}`
        );
      }

      // Deduct from both quantity and reserved quantity
      const newQuantity = Math.max(0, inventory.quantity - quantity);
      const newReservedQuantity = Math.max(0, inventory.reserved - quantity);

      await this.inventoryRepository.update(inventory.id, {
        quantity: newQuantity,
        reserved: newReservedQuantity,
      });

      // Log activity
      if (deductedBy) {
        await this.logActivity("INVENTORY_DEDUCTED", deductedBy.id, {
          inventoryId: inventory.id,
          productId,
          quantity,
          variantId,
        });
      }
    }, "deductInventory");
  }

  /**
   * Add inventory (for restocking)
   */
  async addInventory(
    productId: string,
    quantity: number,
    variantId?: string,
    addedBy?: UserEntity
  ): Promise<void> {
    return this.executeWithErrorHandling(async () => {
      const inventory = await this.inventoryRepository.findByProductAndVariant(
        productId,
        variantId
      );

      if (!inventory) {
        // Create new inventory record if doesn't exist
        const createData: CreateInventoryData = {
          productId,
          variantId,
          quantity,
          location: "default",
          minStock: 0,
        };
        await this.createInventory(createData, addedBy!);
      } else {
        // Add to existing inventory
        await this.inventoryRepository.update(inventory.id, {
          quantity: inventory.quantity + quantity,
        });

        // Log activity
        if (addedBy) {
          await this.logActivity("INVENTORY_ADDED", addedBy.id, {
            inventoryId: inventory.id,
            productId,
            quantity,
            variantId,
          });
        }
      }
    }, "addInventory");
  }

  /**
   * Get low stock products
   */
  async getLowStockProducts(
    threshold: number = 10,
    requestedBy: UserEntity
  ): Promise<InventoryEntity[]> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(requestedBy)) {
        throw new ForbiddenError("Only admins can access low stock report");
      }

      const inventories = await this.inventoryRepository.findMany({
        where: {
          quantity: { lte: threshold },
        },
        include: {
          product: {
            select: {
              id: true,
              name: true,
              sku: true,
              price: true,
            },
          },
        },
        orderBy: { quantity: "asc" },
      });

      return inventories.map((inv) => inv as unknown as InventoryEntity);
    }, "getLowStockProducts");
  }

  /**
   * Get inventory statistics
   */
  async getInventoryStats(requestedBy: UserEntity): Promise<{
    totalProducts: number;
    totalQuantity: number;
    totalReserved: number;
    lowStockCount: number;
    outOfStockCount: number;
    totalValue: number;
  }> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(requestedBy)) {
        throw new ForbiddenError("Only admins can access inventory statistics");
      }

      const stats = await this.inventoryRepository.getInventoryStats();
      return {
        totalProducts: stats.totalProducts,
        totalQuantity: stats.totalStock,
        totalReserved: stats.totalReserved,
        lowStockCount: stats.lowStockCount,
        outOfStockCount: stats.outOfStockCount,
        totalValue: stats.totalValue,
      };
    }, "getInventoryStats");
  }

  /**
   * Bulk update inventory
   */
  async bulkUpdateInventory(
    updates: { productId: string; variantId?: string; quantity: number }[],
    updatedBy: UserEntity
  ): Promise<{
    success: string[];
    failed: { productId: string; reason: string }[];
  }> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(updatedBy)) {
        throw new ForbiddenError("Only admins can bulk update inventory");
      }

      const success: string[] = [];
      const failed: { productId: string; reason: string }[] = [];

      for (const update of updates) {
        try {
          const inventory =
            await this.inventoryRepository.findByProductAndVariant(
              update.productId,
              update.variantId
            );

          if (inventory) {
            await this.updateInventory(
              inventory.id,
              { quantity: update.quantity },
              updatedBy
            );
          } else {
            await this.createInventory(
              {
                productId: update.productId,
                variantId: update.variantId,
                quantity: update.quantity,
                location: "default",
                minStock: 0,
              },
              updatedBy
            );
          }
          success.push(update.productId);
        } catch (error) {
          failed.push({
            productId: update.productId,
            reason: error instanceof Error ? error.message : "Unknown error",
          });
        }
      }

      // Log activity
      await this.logActivity("INVENTORY_BULK_UPDATED", updatedBy.id, {
        successCount: success.length,
        failedCount: failed.length,
      });

      return { success, failed };
    }, "bulkUpdateInventory");
  }

  /**
   * Helper method to check if user is admin
   */
  private isAdmin(user: UserEntity): boolean {
    return ["ADMIN", "SUPER_ADMIN"].includes(user.role as string);
  }
}
