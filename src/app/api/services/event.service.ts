/**
 * Event Service
 * Event-driven architecture implementation
 */

export interface EventData {
  [key: string]: any;
}

export interface EventHandler<T = EventData> {
  (data: T): Promise<void> | void;
}

export interface EventSubscription {
  id: string;
  eventType: string;
  handler: EventHandler;
  priority: number;
}

export class EventService {
  private static instance: EventService;
  private subscriptions = new Map<string, EventSubscription[]>();
  private eventHistory: Array<{
    type: string;
    data: EventData;
    timestamp: Date;
  }> = [];
  private readonly maxHistorySize = 1000;

  private constructor() {}

  static getInstance(): EventService {
    if (!EventService.instance) {
      EventService.instance = new EventService();
    }
    return EventService.instance;
  }

  /**
   * Subscribe to an event
   */
  subscribe<T = EventData>(
    eventType: string,
    handler: EventHandler<T>,
    priority: number = 0
  ): string {
    const subscription: EventSubscription = {
      id: this.generateId(),
      eventType,
      handler: handler as EventHandler,
      priority,
    };

    if (!this.subscriptions.has(eventType)) {
      this.subscriptions.set(eventType, []);
    }

    const handlers = this.subscriptions.get(eventType)!;
    handlers.push(subscription);

    // Sort by priority (higher priority first)
    handlers.sort((a, b) => b.priority - a.priority);

    return subscription.id;
  }

  /**
   * Unsubscribe from an event
   */
  unsubscribe(subscriptionId: string): boolean {
    for (const [eventType, handlers] of this.subscriptions.entries()) {
      const index = handlers.findIndex((h) => h.id === subscriptionId);
      if (index !== -1) {
        handlers.splice(index, 1);
        if (handlers.length === 0) {
          this.subscriptions.delete(eventType);
        }
        return true;
      }
    }
    return false;
  }

  /**
   * Emit an event
   */
  async emit<T extends EventData = EventData>(
    eventType: string,
    data: T
  ): Promise<void> {
    // Add to history
    this.addToHistory(eventType, data as EventData);

    const handlers = this.subscriptions.get(eventType);
    if (!handlers || handlers.length === 0) {
      return;
    }

    // Execute handlers in parallel
    const promises = handlers.map(async (subscription) => {
      try {
        await subscription.handler(data);
      } catch (error) {
        console.error(`Error in event handler for ${eventType}:`, error);
      }
    });

    await Promise.all(promises);
  }

  /**
   * Emit an event synchronously (handlers executed one by one)
   */
  async emitSync<T extends EventData = EventData>(
    eventType: string,
    data: T
  ): Promise<void> {
    // Add to history
    this.addToHistory(eventType, data as EventData);

    const handlers = this.subscriptions.get(eventType);
    if (!handlers || handlers.length === 0) {
      return;
    }

    // Execute handlers sequentially
    for (const subscription of handlers) {
      try {
        await subscription.handler(data);
      } catch (error) {
        console.error(`Error in event handler for ${eventType}:`, error);
      }
    }
  }

  /**
   * Get event history
   */
  getHistory(
    eventType?: string
  ): Array<{ type: string; data: EventData; timestamp: Date }> {
    if (eventType) {
      return this.eventHistory.filter((event) => event.type === eventType);
    }
    return [...this.eventHistory];
  }

  /**
   * Clear event history
   */
  clearHistory(): void {
    this.eventHistory = [];
  }

  /**
   * Get all subscriptions
   */
  getSubscriptions(): Map<string, EventSubscription[]> {
    return new Map(this.subscriptions);
  }

  /**
   * Check if there are handlers for an event type
   */
  hasHandlers(eventType: string): boolean {
    const handlers = this.subscriptions.get(eventType);
    return handlers !== undefined && handlers.length > 0;
  }

  private addToHistory(type: string, data: EventData): void {
    this.eventHistory.push({
      type,
      data,
      timestamp: new Date(),
    });

    // Keep history size under limit
    if (this.eventHistory.length > this.maxHistorySize) {
      this.eventHistory.shift();
    }
  }

  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * Event types constants
 */
export const EVENT_TYPES = {
  // Product events
  PRODUCT_CREATED: "product.created",
  PRODUCT_UPDATED: "product.updated",
  PRODUCT_DELETED: "product.deleted",
  PRODUCT_STOCK_LOW: "product.stock.low",
  PRODUCT_OUT_OF_STOCK: "product.out_of_stock",

  // Order events
  ORDER_CREATED: "order.created",
  ORDER_UPDATED: "order.updated",
  ORDER_CANCELLED: "order.cancelled",
  ORDER_SHIPPED: "order.shipped",
  ORDER_DELIVERED: "order.delivered",

  // User events
  USER_REGISTERED: "user.registered",
  USER_LOGIN: "user.login",
  USER_LOGOUT: "user.logout",
  USER_PROFILE_UPDATED: "user.profile.updated",

  // Admin events
  ADMIN_LOGIN: "admin.login",
  ADMIN_ACTION: "admin.action",
  ADMIN_PERMISSION_CHANGED: "admin.permission.changed",

  // System events
  CACHE_INVALIDATED: "cache.invalidated",
  EMAIL_SENT: "email.sent",
  NOTIFICATION_SENT: "notification.sent",
  ERROR_OCCURRED: "error.occurred",

  // Payment events
  PAYMENT_SUCCESS: "payment.success",
  PAYMENT_FAILED: "payment.failed",
  PAYMENT_REFUNDED: "payment.refunded",
} as const;

/**
 * Event data interfaces
 */
export interface ProductEventData {
  productId: string;
  productName: string;
  userId?: string;
  changes?: Record<string, any>;
}

export interface OrderEventData {
  orderId: string;
  userId: string;
  status?: string;
  total?: number;
  items?: Array<{ productId: string; quantity: number; price: number }>;
}

export interface UserEventData {
  userId: string;
  email?: string;
  name?: string;
  changes?: Record<string, any>;
}

export interface AdminEventData {
  adminId: string;
  action: string;
  resource?: string;
  resourceId?: string;
  changes?: Record<string, any>;
}

export interface NotificationEventData {
  type: string;
  userId?: string;
  title: string;
  message: string;
  data?: Record<string, any>;
}

/**
 * Global event service instance
 */
export const eventService = EventService.getInstance();
