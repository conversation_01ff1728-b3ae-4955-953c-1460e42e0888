/**
 * Wishlist Service
 * Business logic cho Wishlist management
 */

import { BaseService } from "./base.service";
import { Injectable } from "../di-container";
import {
  WishlistRepository,
  ProductRepository,
  UserRepository,
} from "../repositories";
import {
  WishlistEntity,
  CreateWishlistData,
  WishlistBusinessRules,
} from "../../models/wishlist.model";
import {
  PaginatedResult,
  SearchFilters,
  NotFoundError,
  ValidationError,
  ForbiddenError,
  ConflictError,
} from "../../models/common.model";
import { UserEntity } from "../../models/user.model";
import { ProductEntity } from "../../models/product.model";

// Service identifier
export const WISHLIST_SERVICE = Symbol("WishlistService");


export class WishlistService extends BaseService {
  private wishlistRepository: WishlistRepository;
  private productRepository: ProductRepository;
  private userRepository: UserRepository;

  constructor(
    wishlistRepository: WishlistRepository,
    productRepository: ProductRepository,
    userRepository: UserRepository
  ) {
    super();
    this.wishlistRepository = wishlistRepository;
    this.productRepository = productRepository;
    this.userRepository = userRepository;
  }

  /**
   * Thêm product vào wishlist
   */
  async addToWishlist(
    data: CreateWishlistData,
    createdBy: UserEntity
  ): Promise<WishlistEntity> {
    return this.executeWithErrorHandling(async () => {
      // Validate input
      this.validateRequired(data, ["userId", "productId"]);

      // Check permission
      if (data.userId !== createdBy.id && !this.isAdmin(createdBy)) {
        throw new ForbiddenError("Cannot add to wishlist for another user");
      }

      // Check if user exists
      const user = await this.userRepository.findById(data.userId);
      if (!user) {
        throw new NotFoundError("User", data.userId);
      }

      // Check if product exists and is active
      if (!data.productId) {
        throw new ValidationError("Product ID is required");
      }
      const product = await this.productRepository.findById(data.productId);
      if (!product) {
        throw new NotFoundError("Product", data.productId);
      }

      if (product.status !== "ACTIVE") {
        throw new ValidationError("Cannot add inactive product to wishlist");
      }

      // Check if product already in wishlist
      const existingItem = await this.wishlistRepository.findFirst({
        where: { userId: data.userId, productId: data.productId },
      });
      if (existingItem) {
        throw new ConflictError("Product already in wishlist");
      }

      // Check wishlist limit
      const userWishlistCount = await this.wishlistRepository.count({
        where: { userId: data.userId },
      });
      if (userWishlistCount >= WishlistBusinessRules.MAX_ITEMS_PER_WISHLIST) {
        throw new ValidationError(
          `Maximum ${WishlistBusinessRules.MAX_ITEMS_PER_WISHLIST} items allowed in wishlist`
        );
      }

      // Create wishlist item
      const wishlistItem = (await this.wishlistRepository.create({
        userId: data.userId,
        productId: data.productId!,
      } as any)) as unknown as WishlistEntity;

      // Log activity
      await this.logActivity("WISHLIST_ITEM_ADDED", createdBy.id, {
        wishlistItemId: wishlistItem.id,
        productId: data.productId,
        userId: data.userId,
      });

      return wishlistItem;
    }, "addToWishlist");
  }

  /**
   * Xóa product khỏi wishlist
   */
  async removeFromWishlist(
    userId: string,
    productId: string,
    requestedBy: UserEntity
  ): Promise<void> {
    return this.executeWithErrorHandling(async () => {
      // Check permission
      if (userId !== requestedBy.id && !this.isAdmin(requestedBy)) {
        throw new ForbiddenError(
          "Cannot remove from wishlist for another user"
        );
      }

      // Find wishlist item
      const wishlistItem = await this.wishlistRepository.findFirst({
        where: { userId, productId },
      });
      if (!wishlistItem) {
        throw new NotFoundError("Wishlist item not found");
      }

      // Remove from wishlist
      await this.wishlistRepository.delete(wishlistItem.id);

      // Log activity
      await this.logActivity("WISHLIST_ITEM_REMOVED", requestedBy.id, {
        wishlistItemId: wishlistItem.id,
        productId,
        userId,
      });
    }, "removeFromWishlist");
  }

  /**
   * Lấy wishlist của user
   */
  async getUserWishlist(
    userId: string,
    requestedBy: UserEntity,
    filters: SearchFilters = {}
  ): Promise<PaginatedResult<WishlistEntity>> {
    return this.executeWithErrorHandling(async () => {
      // Check permission
      if (userId !== requestedBy.id && !this.isAdmin(requestedBy)) {
        throw new ForbiddenError("Cannot access wishlist of another user");
      }

      // Validate pagination
      const page = filters.page || 1;
      const limit = filters.limit || 20;
      this.validatePagination(page, limit);

      // Build search conditions
      const searchConditions: any = { userId };

      // Get wishlist with pagination
      const result = await this.wishlistRepository.findWithPagination({
        page,
        limit,
        where: searchConditions,
        orderBy: {
          [filters.sortBy || "createdAt"]: filters.sortOrder || "desc",
        },
        include: {
          product: {
            include: {
              media: {
                include: { media: true },
                where: { isPrimary: true },
                take: 1,
              },
              category: true,
              brand: true,
            },
          },
        },
      });

      return {
        data: result.data.map((item) => item as unknown as WishlistEntity),
        total: result.total,
        page: result.page,
        limit: result.limit,
        totalPages: result.totalPages,
        hasNext: result.hasNext,
        hasPrev: result.hasPrev,
      };
    }, "getUserWishlist");
  }

  /**
   * Kiểm tra product có trong wishlist không
   */
  async isInWishlist(
    userId: string,
    productId: string,
    requestedBy: UserEntity
  ): Promise<boolean> {
    return this.executeWithErrorHandling(async () => {
      // Check permission
      if (userId !== requestedBy.id && !this.isAdmin(requestedBy)) {
        throw new ForbiddenError("Cannot check wishlist of another user");
      }

      const wishlistItem = await this.wishlistRepository.findFirst({
        where: { userId, productId },
      });
      return !!wishlistItem;
    }, "isInWishlist");
  }

  /**
   * Lấy wishlist count của user
   */
  async getUserWishlistCount(
    userId: string,
    requestedBy: UserEntity
  ): Promise<number> {
    return this.executeWithErrorHandling(async () => {
      // Check permission
      if (userId !== requestedBy.id && !this.isAdmin(requestedBy)) {
        throw new ForbiddenError(
          "Cannot access wishlist count of another user"
        );
      }

      return await this.wishlistRepository.count({
        where: { userId },
      });
    }, "getUserWishlistCount");
  }

  /**
   * Clear toàn bộ wishlist của user
   */
  async clearWishlist(userId: string, requestedBy: UserEntity): Promise<void> {
    return this.executeWithErrorHandling(async () => {
      // Check permission
      if (userId !== requestedBy.id && !this.isAdmin(requestedBy)) {
        throw new ForbiddenError("Cannot clear wishlist of another user");
      }

      // Get all wishlist items
      const wishlistItems = await this.wishlistRepository.findMany({
        where: { userId },
      });

      // Delete all items
      await Promise.all(
        wishlistItems.map((item: any) =>
          this.wishlistRepository.delete(item.id)
        )
      );

      // Log activity
      await this.logActivity("WISHLIST_CLEARED", requestedBy.id, {
        userId,
        itemCount: wishlistItems.length,
      });
    }, "clearWishlist");
  }

  /**
   * Move wishlist items to cart
   */
  async moveToCart(
    userId: string,
    productIds: string[],
    requestedBy: UserEntity
  ): Promise<{
    success: string[];
    failed: { productId: string; reason: string }[];
  }> {
    return this.executeWithErrorHandling(async () => {
      // Check permission
      if (userId !== requestedBy.id && !this.isAdmin(requestedBy)) {
        throw new ForbiddenError("Cannot move wishlist items for another user");
      }

      const success: string[] = [];
      const failed: { productId: string; reason: string }[] = [];

      for (const productId of productIds) {
        try {
          // Check if product is in wishlist
          const wishlistItem = await this.wishlistRepository.findFirst({
            where: { userId, productId },
          });
          if (!wishlistItem) {
            failed.push({ productId, reason: "Product not in wishlist" });
            continue;
          }

          // Check if product is still available
          const product = await this.productRepository.findById(productId);
          if (!product || product.status !== "ACTIVE") {
            failed.push({ productId, reason: "Product not available" });
            continue;
          }

          // TODO: Add to cart logic (requires CartService integration)
          // For now, just remove from wishlist
          await this.wishlistRepository.delete(wishlistItem.id);
          success.push(productId);
        } catch (error) {
          failed.push({
            productId,
            reason: error instanceof Error ? error.message : "Unknown error",
          });
        }
      }

      // Log activity
      await this.logActivity("WISHLIST_MOVED_TO_CART", requestedBy.id, {
        userId,
        successCount: success.length,
        failedCount: failed.length,
      });

      return { success, failed };
    }, "moveToCart");
  }

  /**
   * Lấy wishlist items theo product IDs
   */
  async getWishlistItemsByProducts(
    userId: string,
    productIds: string[],
    requestedBy: UserEntity
  ): Promise<WishlistEntity[]> {
    return this.executeWithErrorHandling(async () => {
      // Check permission
      if (userId !== requestedBy.id && !this.isAdmin(requestedBy)) {
        throw new ForbiddenError("Cannot access wishlist of another user");
      }

      const items = await this.wishlistRepository.findMany({
        where: {
          userId,
          productId: { in: productIds },
        },
      });
      return items.map((item: any) => item as unknown as WishlistEntity);
    }, "getWishlistItemsByProducts");
  }

  /**
   * Lấy popular wishlist products
   */
  async getPopularWishlistProducts(
    limit: number = 10
  ): Promise<ProductEntity[]> {
    return this.executeWithErrorHandling(async () => {
      // Get popular products from wishlist (simplified)
      const items = await this.wishlistRepository.findMany({
        take: limit,
        include: { product: true },
        orderBy: { createdAt: "desc" },
      });
      return items.map((item: any) => item.product as unknown as ProductEntity);
    }, "getPopularWishlistProducts");
  }

  /**
   * Sync wishlist between guest and user
   */
  async syncGuestWishlist(
    userId: string,
    guestWishlistItems: string[],
    requestedBy: UserEntity
  ): Promise<{
    added: string[];
    skipped: string[];
  }> {
    return this.executeWithErrorHandling(async () => {
      // Check permission
      if (userId !== requestedBy.id && !this.isAdmin(requestedBy)) {
        throw new ForbiddenError("Cannot sync wishlist for another user");
      }

      const added: string[] = [];
      const skipped: string[] = [];

      for (const productId of guestWishlistItems) {
        try {
          // Check if already in wishlist
          const existingItem = await this.wishlistRepository.findFirst({
            where: { userId, productId },
          });
          if (existingItem) {
            skipped.push(productId);
            continue;
          }

          // Add to wishlist
          await this.addToWishlist({ userId, productId }, requestedBy);
          added.push(productId);
        } catch {
          skipped.push(productId);
        }
      }

      // Log activity
      await this.logActivity("WISHLIST_SYNCED", requestedBy.id, {
        userId,
        addedCount: added.length,
        skippedCount: skipped.length,
      });

      return { added, skipped };
    }, "syncGuestWishlist");
  }

  /**
   * Helper method to check if user is admin
   */
  private isAdmin(user: UserEntity): boolean {
    return ["ADMIN", "SUPER_ADMIN"].includes(user.role as string);
  }
}
