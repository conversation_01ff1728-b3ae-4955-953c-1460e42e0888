/**
 * Email Service
 * Multi-provider email service with templates and queue support
 */

export interface EmailProvider {
  name: string;
  send(email: EmailData): Promise<EmailResult>;
  isConfigured(): boolean;
}

export interface EmailData {
  to: string | string[];
  cc?: string | string[];
  bcc?: string | string[];
  subject: string;
  html?: string;
  text?: string;
  attachments?: EmailAttachment[];
  templateId?: string;
  templateData?: Record<string, any>;
  priority?: 'low' | 'normal' | 'high';
  tags?: string[];
}

export interface EmailAttachment {
  filename: string;
  content: Buffer | string;
  contentType?: string;
  cid?: string; // Content-ID for inline images
}

export interface EmailResult {
  success: boolean;
  messageId?: string;
  error?: string;
  provider: string;
}

export interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  html: string;
  text?: string;
  variables: string[];
}

/**
 * SendGrid Email Provider
 */
export class SendGridProvider implements EmailProvider {
  name = 'SendGrid';
  private apiKey: string;

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async send(email: EmailData): Promise<EmailResult> {
    try {
      // Mock SendGrid implementation
      console.log(`[SendGrid] Sending email to: ${email.to}`);
      console.log(`[SendGrid] Subject: ${email.subject}`);
      
      // In real implementation:
      // const sgMail = require('@sendgrid/mail');
      // sgMail.setApiKey(this.apiKey);
      // const result = await sgMail.send(email);
      
      return {
        success: true,
        messageId: `sg_${Date.now()}`,
        provider: this.name
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        provider: this.name
      };
    }
  }

  isConfigured(): boolean {
    return !!this.apiKey;
  }
}

/**
 * AWS SES Email Provider
 */
export class SESProvider implements EmailProvider {
  name = 'AWS SES';
  private region: string;
  private accessKeyId: string;
  private secretAccessKey: string;

  constructor(region: string, accessKeyId: string, secretAccessKey: string) {
    this.region = region;
    this.accessKeyId = accessKeyId;
    this.secretAccessKey = secretAccessKey;
  }

  async send(email: EmailData): Promise<EmailResult> {
    try {
      console.log(`[AWS SES] Sending email to: ${email.to}`);
      console.log(`[AWS SES] Subject: ${email.subject}`);
      
      // In real implementation:
      // const AWS = require('aws-sdk');
      // const ses = new AWS.SES({ region: this.region });
      // const result = await ses.sendEmail(params).promise();
      
      return {
        success: true,
        messageId: `ses_${Date.now()}`,
        provider: this.name
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        provider: this.name
      };
    }
  }

  isConfigured(): boolean {
    return !!(this.region && this.accessKeyId && this.secretAccessKey);
  }
}

/**
 * SMTP Email Provider
 */
export class SMTPProvider implements EmailProvider {
  name = 'SMTP';
  private config: {
    host: string;
    port: number;
    secure: boolean;
    auth: {
      user: string;
      pass: string;
    };
  };

  constructor(config: SMTPProvider['config']) {
    this.config = config;
  }

  async send(email: EmailData): Promise<EmailResult> {
    try {
      console.log(`[SMTP] Sending email to: ${email.to}`);
      console.log(`[SMTP] Subject: ${email.subject}`);
      
      // In real implementation:
      // const nodemailer = require('nodemailer');
      // const transporter = nodemailer.createTransporter(this.config);
      // const result = await transporter.sendMail(email);
      
      return {
        success: true,
        messageId: `smtp_${Date.now()}`,
        provider: this.name
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        provider: this.name
      };
    }
  }

  isConfigured(): boolean {
    return !!(this.config.host && this.config.auth.user && this.config.auth.pass);
  }
}

/**
 * Email Service with multiple providers and failover
 */
export class EmailService {
  private static instance: EmailService;
  private providers: EmailProvider[] = [];
  private templates = new Map<string, EmailTemplate>();
  private queue: EmailData[] = [];
  private isProcessing = false;

  private constructor() {
    this.initializeProviders();
    this.loadTemplates();
  }

  static getInstance(): EmailService {
    if (!EmailService.instance) {
      EmailService.instance = new EmailService();
    }
    return EmailService.instance;
  }

  /**
   * Initialize email providers based on environment variables
   */
  private initializeProviders(): void {
    // SendGrid
    if (process.env.SENDGRID_API_KEY) {
      this.providers.push(new SendGridProvider(process.env.SENDGRID_API_KEY));
    }

    // AWS SES
    if (process.env.AWS_REGION && process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY) {
      this.providers.push(new SESProvider(
        process.env.AWS_REGION,
        process.env.AWS_ACCESS_KEY_ID,
        process.env.AWS_SECRET_ACCESS_KEY
      ));
    }

    // SMTP
    if (process.env.SMTP_HOST && process.env.SMTP_USER && process.env.SMTP_PASS) {
      this.providers.push(new SMTPProvider({
        host: process.env.SMTP_HOST,
        port: parseInt(process.env.SMTP_PORT || '587'),
        secure: process.env.SMTP_SECURE === 'true',
        auth: {
          user: process.env.SMTP_USER,
          pass: process.env.SMTP_PASS
        }
      }));
    }

    console.log(`Email service initialized with ${this.providers.length} providers`);
  }

  /**
   * Load email templates
   */
  private loadTemplates(): void {
    // Default templates
    this.templates.set('welcome', {
      id: 'welcome',
      name: 'Welcome Email',
      subject: 'Chào mừng đến với NS Shop!',
      html: `
        <h1>Xin chào {{name}}!</h1>
        <p>Cảm ơn bạn đã đăng ký tài khoản tại NS Shop.</p>
        <p>Chúng tôi rất vui mừng được phục vụ bạn!</p>
      `,
      text: 'Xin chào {{name}}! Cảm ơn bạn đã đăng ký tài khoản tại NS Shop.',
      variables: ['name']
    });

    this.templates.set('order_confirmation', {
      id: 'order_confirmation',
      name: 'Order Confirmation',
      subject: 'Xác nhận đơn hàng #{{orderNumber}}',
      html: `
        <h1>Đơn hàng của bạn đã được xác nhận</h1>
        <p>Mã đơn hàng: <strong>{{orderNumber}}</strong></p>
        <p>Tổng tiền: <strong>{{total}}đ</strong></p>
        <p>Cảm ơn bạn đã mua sắm tại NS Shop!</p>
      `,
      variables: ['orderNumber', 'total']
    });

    this.templates.set('password_reset', {
      id: 'password_reset',
      name: 'Password Reset',
      subject: 'Đặt lại mật khẩu',
      html: `
        <h1>Đặt lại mật khẩu</h1>
        <p>Bạn đã yêu cầu đặt lại mật khẩu.</p>
        <p><a href="{{resetLink}}">Nhấn vào đây để đặt lại mật khẩu</a></p>
        <p>Link này sẽ hết hạn sau 1 giờ.</p>
      `,
      variables: ['resetLink']
    });
  }

  /**
   * Send email with failover support
   */
  async sendEmail(email: EmailData): Promise<EmailResult> {
    const configuredProviders = this.providers.filter(p => p.isConfigured());
    
    if (configuredProviders.length === 0) {
      return {
        success: false,
        error: 'No email providers configured',
        provider: 'none'
      };
    }

    // Process template if specified
    if (email.templateId) {
      const processedEmail = await this.processTemplate(email);
      if (!processedEmail) {
        return {
          success: false,
          error: 'Template processing failed',
          provider: 'template'
        };
      }
      email = processedEmail;
    }

    // Try each provider until one succeeds
    for (const provider of configuredProviders) {
      try {
        const result = await provider.send(email);
        if (result.success) {
          console.log(`Email sent successfully via ${provider.name}`);
          return result;
        }
        console.warn(`Email failed via ${provider.name}: ${result.error}`);
      } catch (error) {
        console.error(`Provider ${provider.name} error:`, error);
      }
    }

    return {
      success: false,
      error: 'All email providers failed',
      provider: 'all'
    };
  }

  /**
   * Add email to queue for batch processing
   */
  async queueEmail(email: EmailData): Promise<void> {
    this.queue.push(email);
    
    if (!this.isProcessing) {
      this.processQueue();
    }
  }

  /**
   * Process email queue
   */
  private async processQueue(): Promise<void> {
    if (this.isProcessing || this.queue.length === 0) {
      return;
    }

    this.isProcessing = true;
    console.log(`Processing ${this.queue.length} emails in queue`);

    while (this.queue.length > 0) {
      const email = this.queue.shift()!;
      try {
        await this.sendEmail(email);
        // Add delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 100));
      } catch (error) {
        console.error('Queue processing error:', error);
      }
    }

    this.isProcessing = false;
  }

  /**
   * Process email template
   */
  private async processTemplate(email: EmailData): Promise<EmailData | null> {
    if (!email.templateId) return email;

    const template = this.templates.get(email.templateId);
    if (!template) {
      console.error(`Template not found: ${email.templateId}`);
      return null;
    }

    const data = email.templateData || {};
    
    // Replace variables in template
    let html = template.html;
    let text = template.text || '';
    let subject = template.subject;

    for (const variable of template.variables) {
      const value = data[variable] || '';
      const regex = new RegExp(`{{${variable}}}`, 'g');
      html = html.replace(regex, value);
      text = text.replace(regex, value);
      subject = subject.replace(regex, value);
    }

    return {
      ...email,
      subject: email.subject || subject,
      html: email.html || html,
      text: email.text || text
    };
  }

  /**
   * Get available templates
   */
  getTemplates(): EmailTemplate[] {
    return Array.from(this.templates.values());
  }

  /**
   * Add custom template
   */
  addTemplate(template: EmailTemplate): void {
    this.templates.set(template.id, template);
  }

  /**
   * Get provider status
   */
  getProviderStatus(): Array<{ name: string; configured: boolean }> {
    return this.providers.map(p => ({
      name: p.name,
      configured: p.isConfigured()
    }));
  }
}

// Global instance
export const emailService = EmailService.getInstance();
