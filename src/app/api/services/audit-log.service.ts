/**
 * Audit Log Service
 * Business logic cho Audit Log management
 */

import { BaseService } from "./base.service";
import { Injectable } from "../di-container";
import { AuditLogRepository, UserRepository } from "../repositories";
import {
  AuditLogEntity,
  CreateAuditLogData,
  AuditLogBusinessRules,
} from "../../models/audit-log.model";
import {
  PaginatedResult,
  SearchFilters,
  NotFoundError,
  ValidationError,
  ForbiddenError,
} from "../../models/common.model";
import { UserEntity } from "../../models/user.model";

// Service identifier
export const AUDIT_LOG_SERVICE = Symbol("AuditLogService");


export class AuditLogService extends BaseService {
  private auditLogRepository: AuditLogRepository;
  private userRepository: UserRepository;

  constructor(
    auditLogRepository: AuditLogRepository,
    userRepository: UserRepository
  ) {
    super();
    this.auditLogRepository = auditLogRepository;
    this.userRepository = userRepository;
  }

  /**
   * Tạo audit log entry
   */
  async createAuditLog(data: CreateAuditLogData): Promise<AuditLogEntity> {
    return this.executeWithErrorHandling(async () => {
      // Validate input
      this.validateRequired(data, ["action", "resource", "adminId"]);

      // Validate audit log data
      const validation = AuditLogBusinessRules.validateAuditLog(data);
      if (!validation.valid) {
        throw new ValidationError(validation.errors.join(", "));
      }

      // Create audit log
      const auditLogData = {
        ...data,
        timestamp: new Date(),
        ipAddress: data.ipAddress || "unknown",
        userAgent: data.userAgent || "unknown",
      };

      const auditLog = (await this.auditLogRepository.create(
        auditLogData
      )) as unknown as AuditLogEntity;

      return auditLog;
    }, "createAuditLog");
  }

  /**
   * Lấy audit log theo ID
   */
  async getAuditLogById(
    id: string,
    requestedBy: UserEntity
  ): Promise<AuditLogEntity> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(requestedBy)) {
        throw new ForbiddenError("Only admins can access audit logs");
      }

      const auditLog = await this.auditLogRepository.findById(id);
      if (!auditLog) {
        throw new NotFoundError("Audit log", id);
      }

      return auditLog as unknown as AuditLogEntity;
    }, "getAuditLogById");
  }

  /**
   * Lấy audit logs với filters
   */
  async getAuditLogs(
    filters: SearchFilters & {
      userId?: string;
      action?: string;
      entityType?: string;
      entityId?: string;
      startDate?: Date;
      endDate?: Date;
    } = {},
    requestedBy: UserEntity
  ): Promise<PaginatedResult<AuditLogEntity>> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(requestedBy)) {
        throw new ForbiddenError("Only admins can access audit logs");
      }

      // Validate pagination
      const page = filters.page || 1;
      const limit = filters.limit || 50;
      this.validatePagination(page, limit);

      // Build search conditions
      const searchConditions: any = {};

      if (filters.userId) {
        searchConditions.userId = filters.userId;
      }

      if (filters.action) {
        searchConditions.action = filters.action;
      }

      if (filters.entityType) {
        searchConditions.entityType = filters.entityType;
      }

      if (filters.entityId) {
        searchConditions.entityId = filters.entityId;
      }

      if (filters.startDate || filters.endDate) {
        searchConditions.timestamp = {};
        if (filters.startDate) {
          searchConditions.timestamp.gte = filters.startDate;
        }
        if (filters.endDate) {
          searchConditions.timestamp.lte = filters.endDate;
        }
      }

      if (filters.search) {
        const searchQuery = this.sanitizeSearchQuery(filters.search);
        searchConditions.OR = [
          { action: { contains: searchQuery, mode: "insensitive" } },
          { entityType: { contains: searchQuery, mode: "insensitive" } },
          { details: { path: ["description"], string_contains: searchQuery } },
        ];
      }

      // Get audit logs with pagination
      const result = await this.auditLogRepository.findWithPagination({
        page,
        limit,
        where: searchConditions,
        orderBy: {
          [filters.sortBy || "timestamp"]: filters.sortOrder || "desc",
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      return {
        data: result.data.map((log) => log as unknown as AuditLogEntity),
        total: result.total,
        page: result.page,
        limit: result.limit,
        totalPages: result.totalPages,
        hasNext: result.page < result.totalPages,
        hasPrev: result.page > 1,
      };
    }, "getAuditLogs");
  }

  /**
   * Lấy audit logs của user
   */
  async getUserAuditLogs(
    userId: string,
    requestedBy: UserEntity,
    filters: SearchFilters = {}
  ): Promise<PaginatedResult<AuditLogEntity>> {
    return this.executeWithErrorHandling(async () => {
      // Check permission
      if (userId !== requestedBy.id && !this.isAdmin(requestedBy)) {
        throw new ForbiddenError("Cannot access audit logs of another user");
      }

      const auditFilters = {
        ...filters,
        userId,
      };

      return await this.getAuditLogs(auditFilters, requestedBy);
    }, "getUserAuditLogs");
  }

  /**
   * Lấy audit logs cho entity
   */
  async getEntityAuditLogs(
    entityType: string,
    entityId: string,
    requestedBy: UserEntity,
    filters: SearchFilters = {}
  ): Promise<PaginatedResult<AuditLogEntity>> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(requestedBy)) {
        throw new ForbiddenError("Only admins can access entity audit logs");
      }

      const auditFilters = {
        ...filters,
        entityType,
        entityId,
      };

      return await this.getAuditLogs(auditFilters, requestedBy);
    }, "getEntityAuditLogs");
  }

  /**
   * Log user activity
   */
  async logUserActivity(
    action: string,
    userId: string,
    entityType?: string,
    entityId?: string,
    details?: any,
    ipAddress?: string,
    userAgent?: string
  ): Promise<AuditLogEntity> {
    return this.executeWithErrorHandling(async () => {
      const auditLogData: CreateAuditLogData = {
        action,
        adminId: userId,
        resource: entityType || "unknown",
        resourceId: entityId,
        metadata: details,
        ipAddress,
        userAgent,
      };

      return await this.createAuditLog(auditLogData);
    }, "logActivity");
  }

  /**
   * Log system event
   */
  async logSystemEvent(
    action: string,
    details?: any,
    entityType?: string,
    entityId?: string
  ): Promise<AuditLogEntity> {
    return this.executeWithErrorHandling(async () => {
      const auditLogData: CreateAuditLogData = {
        action,
        adminId: "system", // System event
        resource: entityType || "unknown",
        resourceId: entityId,
        metadata: details,
        ipAddress: "system",
        userAgent: "system",
      };

      return await this.createAuditLog(auditLogData);
    }, "logSystemEvent");
  }

  /**
   * Get audit log statistics
   */
  async getAuditLogStats(
    requestedBy: UserEntity,
    _filters: {
      startDate?: Date;
      endDate?: Date;
      userId?: string;
    } = {}
  ): Promise<{
    totalLogs: number;
    uniqueUsers: number;
    topActions: { action: string; count: number }[];
    topEntityTypes: { entityType: string; count: number }[];
    activityByDay: { date: string; count: number }[];
  }> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(requestedBy)) {
        throw new ForbiddenError("Only admins can access audit log statistics");
      }

      const stats = await this.auditLogRepository.getAuditLogStats();

      // Transform to match expected return type
      return {
        totalLogs: stats.total,
        uniqueUsers: stats.topAdmins.length,
        topActions: stats.topActions,
        topEntityTypes: stats.topResources.map((r) => ({
          entityType: r.resource,
          count: r.count,
        })),
        activityByDay: [], // TODO: Implement activity by day
      };
    }, "getAuditLogStats");
  }

  /**
   * Export audit logs
   */
  async exportAuditLogs(
    filters: SearchFilters & {
      userId?: string;
      action?: string;
      entityType?: string;
      startDate?: Date;
      endDate?: Date;
      format?: "csv" | "json";
    },
    requestedBy: UserEntity
  ): Promise<string> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(requestedBy)) {
        throw new ForbiddenError("Only admins can export audit logs");
      }

      // Get all matching logs (without pagination)
      const allFilters = { ...filters, limit: 10000 }; // Large limit for export
      const result = await this.getAuditLogs(allFilters, requestedBy);

      const format = filters.format || "csv";
      let exportData: string;

      if (format === "csv") {
        exportData = this.convertToCSV(result.data);
      } else {
        exportData = JSON.stringify(result.data, null, 2);
      }

      // Log export activity
      await this.logActivity("AUDIT_LOGS_EXPORTED", requestedBy.id, {
        format,
        recordCount: result.data.length,
        filters,
      });

      return exportData;
    }, "exportAuditLogs");
  }

  /**
   * Clean up old audit logs
   */
  async cleanupOldLogs(
    olderThanDays: number = 365,
    requestedBy: UserEntity
  ): Promise<number> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(requestedBy)) {
        throw new ForbiddenError("Only admins can cleanup audit logs");
      }

      const deletedCount =
        await this.auditLogRepository.deleteOldLogs(olderThanDays);

      // Log cleanup activity
      await this.logActivity("AUDIT_LOGS_CLEANUP", requestedBy.id, {
        deletedCount,
        olderThanDays,
      });

      return deletedCount;
    }, "cleanupOldLogs");
  }

  /**
   * Search audit logs by content
   */
  async searchAuditLogs(
    query: string,
    requestedBy: UserEntity,
    filters: SearchFilters = {}
  ): Promise<PaginatedResult<AuditLogEntity>> {
    return this.executeWithErrorHandling(async () => {
      const searchFilters = {
        ...filters,
        search: query,
      };
      return await this.getAuditLogs(searchFilters, requestedBy);
    }, "searchAuditLogs");
  }

  /**
   * Helper method to convert audit logs to CSV
   */
  private convertToCSV(logs: AuditLogEntity[]): string {
    const headers = [
      "Timestamp",
      "User ID",
      "User Name",
      "Action",
      "Entity Type",
      "Entity ID",
      "IP Address",
      "User Agent",
      "Details",
    ];

    const rows = logs.map((log) => [
      log.createdAt.toISOString(),
      log.userId || "",
      "", // User name - would need to join with user table
      log.action,
      log.resource || "",
      log.resourceId || "",
      log.ipAddress || "",
      log.userAgent || "",
      JSON.stringify(log.metadata || {}),
    ]);

    const csvContent = [headers, ...rows]
      .map((row) => row.map((field) => `"${field}"`).join(","))
      .join("\n");

    return csvContent;
  }

  /**
   * Helper method to check if user is admin
   */
  private isAdmin(user: UserEntity): boolean {
    return ["ADMIN", "SUPER_ADMIN"].includes(user.role as string);
  }
}
