/**
 * WebSocket Service
 * Real-time communication service for notifications
 */

import { WebSocket } from "ws";

export interface WebSocketConnection {
  id: string;
  socket: WebSocket;
  userId?: string;
  adminId?: string;
  isAdmin: boolean;
  connectedAt: Date;
  lastActivity: Date;
}

export interface WebSocketMessage {
  type: string;
  data: any;
  timestamp: Date;
  id: string;
}

export interface NotificationMessage {
  type: "notification";
  data: {
    id: string;
    title: string;
    message: string;
    category: "info" | "success" | "warning" | "error";
    userId?: string;
    data?: any;
  };
}

export interface SystemMessage {
  type: "system";
  data: {
    action: "ping" | "pong" | "auth_required" | "auth_success" | "auth_failed";
    message?: string;
  };
}

export class WebSocketService {
  private static instance: WebSocketService;
  private connections = new Map<string, WebSocketConnection>();
  private userConnections = new Map<string, Set<string>>(); // userId -> connectionIds
  private adminConnections = new Set<string>(); // connectionIds for admins
  private heartbeatInterval: NodeJS.Timeout | null = null;

  private constructor() {
    this.startHeartbeat();
  }

  static getInstance(): WebSocketService {
    if (!WebSocketService.instance) {
      WebSocketService.instance = new WebSocketService();
    }
    return WebSocketService.instance;
  }

  /**
   * Add new WebSocket connection
   */
  addConnection(socket: WebSocket, userId?: string, adminId?: string): string {
    const connectionId = this.generateConnectionId();
    const isAdmin = !!adminId;

    const connection: WebSocketConnection = {
      id: connectionId,
      socket,
      userId: userId || adminId,
      adminId,
      isAdmin,
      connectedAt: new Date(),
      lastActivity: new Date(),
    };

    this.connections.set(connectionId, connection);

    // Track user connections
    if (userId) {
      if (!this.userConnections.has(userId)) {
        this.userConnections.set(userId, new Set());
      }
      this.userConnections.get(userId)!.add(connectionId);
    }

    // Track admin connections
    if (isAdmin) {
      this.adminConnections.add(connectionId);
    }

    // Setup socket event handlers
    this.setupSocketHandlers(connection);

    console.log(
      `WebSocket connection added: ${connectionId} (${isAdmin ? "admin" : "user"})`
    );

    // Send welcome message
    this.sendToConnection(connectionId, {
      type: "system",
      data: {
        action: "auth_success",
        message: "Connected successfully",
      },
    });

    return connectionId;
  }

  /**
   * Remove WebSocket connection
   */
  removeConnection(connectionId: string): void {
    const connection = this.connections.get(connectionId);
    if (!connection) return;

    // Remove from user connections
    if (connection.userId) {
      const userConnections = this.userConnections.get(connection.userId);
      if (userConnections) {
        userConnections.delete(connectionId);
        if (userConnections.size === 0) {
          this.userConnections.delete(connection.userId);
        }
      }
    }

    // Remove from admin connections
    if (connection.isAdmin) {
      this.adminConnections.delete(connectionId);
    }

    // Close socket if still open
    if (connection.socket.readyState === WebSocket.OPEN) {
      connection.socket.close();
    }

    this.connections.delete(connectionId);
    console.log(`WebSocket connection removed: ${connectionId}`);
  }

  /**
   * Send message to specific connection
   */
  sendToConnection(
    connectionId: string,
    message: Partial<WebSocketMessage>
  ): boolean {
    const connection = this.connections.get(connectionId);
    if (!connection || connection.socket.readyState !== WebSocket.OPEN) {
      return false;
    }

    const fullMessage: WebSocketMessage = {
      type: message.type || "message",
      data: message.data,
      timestamp: new Date(),
      id: this.generateMessageId(),
    };

    try {
      connection.socket.send(JSON.stringify(fullMessage));
      connection.lastActivity = new Date();
      return true;
    } catch (error) {
      console.error(
        `Failed to send message to connection ${connectionId}:`,
        error
      );
      this.removeConnection(connectionId);
      return false;
    }
  }

  /**
   * Send notification to specific user
   */
  sendNotificationToUser(
    userId: string,
    notification: NotificationMessage["data"]
  ): number {
    const userConnections = this.userConnections.get(userId);
    if (!userConnections || userConnections.size === 0) {
      return 0;
    }

    let sentCount = 0;
    for (const connectionId of userConnections) {
      const success = this.sendToConnection(connectionId, {
        type: "notification",
        data: notification,
      });
      if (success) sentCount++;
    }

    return sentCount;
  }

  /**
   * Send notification to all admins
   */
  sendNotificationToAdmins(notification: NotificationMessage["data"]): number {
    let sentCount = 0;
    for (const connectionId of this.adminConnections) {
      const success = this.sendToConnection(connectionId, {
        type: "notification",
        data: notification,
      });
      if (success) sentCount++;
    }
    return sentCount;
  }

  /**
   * Broadcast message to all connections
   */
  broadcast(
    message: Partial<WebSocketMessage>,
    excludeConnectionId?: string
  ): number {
    let sentCount = 0;
    for (const [connectionId, _connection] of this.connections) {
      if (excludeConnectionId && connectionId === excludeConnectionId) {
        continue;
      }
      const success = this.sendToConnection(connectionId, message);
      if (success) sentCount++;
    }
    return sentCount;
  }

  /**
   * Get connection statistics
   */
  getStats(): {
    totalConnections: number;
    userConnections: number;
    adminConnections: number;
    activeUsers: number;
  } {
    return {
      totalConnections: this.connections.size,
      userConnections: this.connections.size - this.adminConnections.size,
      adminConnections: this.adminConnections.size,
      activeUsers: this.userConnections.size,
    };
  }

  /**
   * Setup socket event handlers
   */
  private setupSocketHandlers(connection: WebSocketConnection): void {
    connection.socket.on("message", (data) => {
      try {
        const message = JSON.parse(data.toString());
        this.handleMessage(connection, message);
      } catch (error) {
        console.error("Invalid WebSocket message:", error);
      }
    });

    connection.socket.on("close", () => {
      this.removeConnection(connection.id);
    });

    connection.socket.on("error", (error) => {
      console.error(`WebSocket error for connection ${connection.id}:`, error);
      this.removeConnection(connection.id);
    });

    connection.socket.on("pong", () => {
      connection.lastActivity = new Date();
    });
  }

  /**
   * Handle incoming messages
   */
  private handleMessage(connection: WebSocketConnection, message: any): void {
    connection.lastActivity = new Date();

    switch (message.type) {
      case "ping":
        this.sendToConnection(connection.id, {
          type: "system",
          data: { action: "pong" },
        });
        break;

      case "heartbeat":
        // Just update last activity
        break;

      default:
        console.log(`Unhandled message type: ${message.type}`);
    }
  }

  /**
   * Start heartbeat to check connection health
   */
  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      const now = new Date();
      const timeout = 30000; // 30 seconds

      for (const [connectionId, connection] of this.connections) {
        const timeSinceActivity =
          now.getTime() - connection.lastActivity.getTime();

        if (timeSinceActivity > timeout) {
          console.log(`Connection ${connectionId} timed out`);
          this.removeConnection(connectionId);
        } else if (connection.socket.readyState === WebSocket.OPEN) {
          // Send ping
          connection.socket.ping();
        }
      }
    }, 15000); // Check every 15 seconds
  }

  /**
   * Generate unique connection ID
   */
  private generateConnectionId(): string {
    return `ws_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate unique message ID
   */
  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Cleanup on shutdown
   */
  shutdown(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }

    for (const connection of this.connections.values()) {
      if (connection.socket.readyState === WebSocket.OPEN) {
        connection.socket.close();
      }
    }

    this.connections.clear();
    this.userConnections.clear();
    this.adminConnections.clear();
  }
}

// Global instance
export const webSocketService = WebSocketService.getInstance();
