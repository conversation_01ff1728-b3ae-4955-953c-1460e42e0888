/**
 * Base Service
 * Service cơ sở cho tất cả services khác
 */

import { container, SERVICE_IDENTIFIERS } from "../di-container";
import {
  BusinessError,
  ValidationError,
  NotFoundError,
  UnauthorizedError,
  ForbiddenError,
} from "../../models/common.model";

/**
 * Base Service Class
 */
export abstract class BaseService {
  protected container = container;

  /**
   * Get repository từ DI container
   */
  protected getRepository<T>(identifier: symbol): T {
    return this.container.resolve<T>(identifier);
  }

  /**
   * Get service từ DI container
   */
  protected getService<T>(identifier: symbol): T {
    return this.container.resolve<T>(identifier);
  }

  /**
   * Validate required fields
   */
  protected validateRequired(
    data: Record<string, any>,
    fields: string[]
  ): void {
    const missing = fields.filter((field) => !data[field]);
    if (missing.length > 0) {
      throw new ValidationError(
        `Missing required fields: ${missing.join(", ")}`
      );
    }
  }

  /**
   * Validate permissions
   */
  protected validatePermission(
    user: any,
    permission: string,
    resource?: any
  ): void {
    // Basic permission check - can be extended
    if (!user) {
      throw new UnauthorizedError("Authentication required");
    }

    if (!user.isActive) {
      throw new ForbiddenError("Account is inactive");
    }

    // Check if user has required permission
    if (user.permissions && !user.permissions.includes(permission)) {
      throw new ForbiddenError(`Permission '${permission}' required`);
    }
  }

  /**
   * Validate ownership
   */
  protected validateOwnership(
    user: any,
    resource: any,
    userIdField: string = "userId"
  ): void {
    if (!user) {
      throw new UnauthorizedError("Authentication required");
    }

    // Admin can access everything
    if (user.role === "ADMIN" || user.role === "SUPER_ADMIN") {
      return;
    }

    // Check ownership
    if (resource[userIdField] !== user.id) {
      throw new ForbiddenError("Access denied: not the owner");
    }
  }

  /**
   * Handle service errors
   */
  protected handleError(error: any, context?: string): never {
    console.error(`Service error${context ? ` in ${context}` : ""}:`, error);

    // Re-throw known business errors
    if (error instanceof BusinessError) {
      throw error;
    }

    // Handle Prisma errors
    if (error.code === "P2002") {
      throw new ValidationError("Duplicate entry", error.meta);
    }

    if (error.code === "P2025") {
      throw new NotFoundError("Resource");
    }

    // Handle validation errors
    if (error.name === "ZodError") {
      throw new ValidationError("Validation failed", error.errors);
    }

    // Generic error
    throw new BusinessError(
      error.message || "An unexpected error occurred",
      "INTERNAL_ERROR",
      500
    );
  }

  /**
   * Execute with error handling
   */
  protected async executeWithErrorHandling<T>(
    operation: () => Promise<T>,
    context?: string
  ): Promise<T> {
    try {
      return await operation();
    } catch (error) {
      this.handleError(error, context);
    }
  }

  /**
   * Validate pagination parameters
   */
  protected validatePagination(page: number, limit: number): void {
    if (page < 1) {
      throw new ValidationError("Page must be greater than 0");
    }

    if (limit < 1 || limit > 100) {
      throw new ValidationError("Limit must be between 1 and 100");
    }
  }

  /**
   * Sanitize search query
   */
  protected sanitizeSearchQuery(query?: string): string | undefined {
    if (!query) return undefined;

    return query
      .trim()
      .replace(/[<>]/g, "") // Remove potential XSS characters
      .substring(0, 100); // Limit length
  }

  /**
   * Build where clause for search
   */
  protected buildSearchWhere(
    search?: string,
    searchFields: string[] = ["name"]
  ): any {
    if (!search) return {};

    const sanitizedSearch = this.sanitizeSearchQuery(search);
    if (!sanitizedSearch) return {};

    return {
      OR: searchFields.map((field) => ({
        [field]: {
          contains: sanitizedSearch,
          mode: "insensitive",
        },
      })),
    };
  }

  /**
   * Log service action
   */
  protected logAction(
    action: string,
    user?: any,
    resource?: any,
    metadata?: Record<string, any>
  ): void {
    console.log("Service Action:", {
      action,
      userId: user?.id,
      userEmail: user?.email,
      resourceId: resource?.id,
      resourceType: resource?.constructor?.name,
      metadata,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Check rate limit (placeholder - implement with Redis)
   */
  protected async checkRateLimit(
    key: string,
    limit: number,
    window: number
  ): Promise<void> {
    // TODO: Implement rate limiting with Redis
    // For now, just log the attempt
    console.log(`Rate limit check: ${key}, limit: ${limit}, window: ${window}`);
  }

  /**
   * Cache result (placeholder - implement with Redis)
   */
  protected async cacheResult<T>(
    key: string,
    data: T,
    ttl: number = 300
  ): Promise<void> {
    // TODO: Implement caching with Redis
    console.log(`Cache set: ${key}, ttl: ${ttl}`);
  }

  /**
   * Get cached result (placeholder - implement with Redis)
   */
  protected async getCachedResult<T>(key: string): Promise<T | null> {
    // TODO: Implement caching with Redis
    console.log(`Cache get: ${key}`);
    return null;
  }

  /**
   * Invalidate cache (placeholder - implement with Redis)
   */
  protected async invalidateCache(pattern: string): Promise<void> {
    // TODO: Implement cache invalidation with Redis
    console.log(`Cache invalidate: ${pattern}`);
  }

  /**
   * Send notification (placeholder)
   */
  protected async sendNotification(
    type: string,
    recipient: string,
    data: any
  ): Promise<void> {
    // TODO: Implement notification service
    console.log(`Notification: ${type} to ${recipient}`, data);
  }

  /**
   * Emit event (placeholder)
   */
  protected async emitEvent(
    eventType: string,
    data: any,
    metadata?: Record<string, any>
  ): Promise<void> {
    // TODO: Implement event system
    console.log(`Event: ${eventType}`, { data, metadata });
  }

  /**
   * Generate unique identifier
   */
  protected generateId(): string {
    return crypto.randomUUID();
  }

  /**
   * Generate slug from text
   */
  protected generateSlug(text: string): string {
    return text
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, "")
      .replace(/\s+/g, "-")
      .replace(/-+/g, "-")
      .trim();
  }

  /**
   * Validate email format
   */
  protected validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Hash password (placeholder)
   */
  protected async hashPassword(password: string): Promise<string> {
    // TODO: Implement proper password hashing
    return `hashed_${password}`;
  }

  /**
   * Verify password (placeholder)
   */
  protected async verifyPassword(
    password: string,
    hashedPassword: string
  ): Promise<boolean> {
    // TODO: Implement proper password verification
    return hashedPassword === `hashed_${password}`;
  }

  /**
   * Log activity (placeholder)
   */
  protected async logActivity(
    action: string,
    userId: string,
    data: any
  ): Promise<void> {
    // Implementation for logging activities
    // This would typically integrate with audit log service
    console.log(`Activity logged: ${action} by ${userId}`, data);
  }
}
