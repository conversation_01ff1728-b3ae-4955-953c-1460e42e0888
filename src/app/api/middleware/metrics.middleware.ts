/**
 * Metrics Middleware
 * Automatically track API performance metrics
 */

import { NextRequest, NextResponse } from 'next/server';
import { metricsService } from '../services/metrics.service';

export interface MetricsMiddlewareOptions {
  trackResponseTime?: boolean;
  trackUserAgent?: boolean;
  trackIP?: boolean;
  excludePaths?: string[];
  includeBody?: boolean;
}

/**
 * Metrics middleware function
 */
export function withMetrics(
  handler: (request: NextRequest, ...args: any[]) => Promise<NextResponse>,
  options: MetricsMiddlewareOptions = {}
) {
  return async (request: NextRequest, ...args: any[]): Promise<NextResponse> => {
    const startTime = Date.now();
    const path = new URL(request.url).pathname;
    const method = request.method;

    // Skip tracking for excluded paths
    if (options.excludePaths?.some(excludePath => path.includes(excludePath))) {
      return handler(request, ...args);
    }

    // Extract user info if available
    let userId: string | undefined;
    try {
      // Try to extract user ID from authorization header or session
      const authHeader = request.headers.get('authorization');
      if (authHeader) {
        // This would need to be implemented based on your auth system
        // userId = await extractUserIdFromToken(authHeader);
      }
    } catch (error) {
      // Ignore auth extraction errors
    }

    let response: NextResponse;
    let error: Error | null = null;

    try {
      // Execute the handler
      response = await handler(request, ...args);
    } catch (err) {
      error = err instanceof Error ? err : new Error('Unknown error');
      // Create error response
      response = NextResponse.json(
        { error: 'Internal Server Error' },
        { status: 500 }
      );
    }

    const endTime = Date.now();
    const responseTime = endTime - startTime;
    const statusCode = response.status;

    // Record metrics
    metricsService.recordApiRequest(
      method,
      path,
      statusCode,
      responseTime,
      userId
    );

    // Record additional metrics based on options
    if (options.trackUserAgent) {
      const userAgent = request.headers.get('user-agent');
      if (userAgent) {
        metricsService.counter('requests_by_user_agent', 1, {
          user_agent: userAgent.split(' ')[0] // First part of user agent
        });
      }
    }

    if (options.trackIP) {
      const ip = getClientIP(request);
      if (ip) {
        metricsService.counter('requests_by_ip', 1, { ip });
      }
    }

    // Log error if occurred
    if (error) {
      console.error(`API Error in ${method} ${path}:`, error);
      metricsService.counter('api_errors_by_path', 1, {
        method,
        path,
        error_type: error.constructor.name
      });
    }

    return response;
  };
}

/**
 * Metrics decorator for API routes
 */
export function Metrics(options: MetricsMiddlewareOptions = {}) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const request = args[0] as NextRequest;
      const startTime = Date.now();
      
      try {
        const result = await method.apply(this, args);
        const responseTime = Date.now() - startTime;
        
        // Record successful execution
        metricsService.histogram('method_execution_time', responseTime, {
          class: target.constructor.name,
          method: propertyName
        });

        return result;
      } catch (error) {
        const responseTime = Date.now() - startTime;
        
        // Record error
        metricsService.counter('method_errors', 1, {
          class: target.constructor.name,
          method: propertyName,
          error_type: error instanceof Error ? error.constructor.name : 'unknown'
        });

        metricsService.histogram('method_execution_time', responseTime, {
          class: target.constructor.name,
          method: propertyName,
          status: 'error'
        });

        throw error;
      }
    };
  };
}

/**
 * Database query metrics decorator
 */
export function DatabaseMetrics(operation: string, table: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const timer = metricsService.timer('db_query_duration', {
        operation,
        table,
        method: propertyName
      });

      try {
        const result = await method.apply(this, args);
        timer.end();
        
        metricsService.recordDatabaseQuery(operation, table, timer.duration, true);
        return result;
      } catch (error) {
        timer.end();
        
        metricsService.recordDatabaseQuery(operation, table, timer.duration, false);
        throw error;
      }
    };
  };
}

/**
 * Cache metrics decorator
 */
export function CacheMetrics(keyPrefix: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const timer = metricsService.timer('cache_operation_duration', {
        operation: propertyName,
        key_prefix: keyPrefix
      });

      try {
        const result = await method.apply(this, args);
        const duration = timer.end();
        
        // Determine if it was a hit or miss based on result
        const operation = result !== null && result !== undefined ? 'hit' : 'miss';
        metricsService.recordCacheOperation(operation as any, keyPrefix, duration);
        
        return result;
      } catch (error) {
        timer.end();
        
        metricsService.counter('cache_errors', 1, {
          operation: propertyName,
          key_prefix: keyPrefix
        });
        
        throw error;
      }
    };
  };
}

/**
 * Get client IP address
 */
function getClientIP(request: NextRequest): string | null {
  // Check various headers for client IP
  const forwarded = request.headers.get('x-forwarded-for');
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }

  const realIP = request.headers.get('x-real-ip');
  if (realIP) {
    return realIP;
  }

  const cfConnectingIP = request.headers.get('cf-connecting-ip');
  if (cfConnectingIP) {
    return cfConnectingIP;
  }

  // Fallback to connection remote address (may not be available in serverless)
  return null;
}

/**
 * Create metrics dashboard data
 */
export function createMetricsDashboard() {
  const summary = metricsService.getSummary();
  const alerts = metricsService.getAlerts(10);

  return {
    summary,
    alerts,
    charts: {
      responseTime: metricsService.getMetrics('api_response_time')
        .slice(-50)
        .map(m => ({ timestamp: m.timestamp, value: m.value })),
      
      requestsPerMinute: metricsService.getMetrics('api_requests_total')
        .slice(-60)
        .reduce((acc, m) => {
          const minute = new Date(m.timestamp).toISOString().slice(0, 16);
          acc[minute] = (acc[minute] || 0) + m.value;
          return acc;
        }, {} as Record<string, number>),
      
      errorRate: metricsService.getMetrics('api_errors_total')
        .slice(-50)
        .map(m => ({ timestamp: m.timestamp, value: m.value })),
      
      memoryUsage: metricsService.getMetrics('memory_used')
        .slice(-50)
        .map(m => ({ timestamp: m.timestamp, value: m.value }))
    }
  };
}

/**
 * Middleware to track active connections
 */
export function trackActiveConnections() {
  let activeConnections = 0;

  return {
    increment: () => {
      activeConnections++;
      metricsService.gauge('active_connections', activeConnections);
    },
    
    decrement: () => {
      activeConnections = Math.max(0, activeConnections - 1);
      metricsService.gauge('active_connections', activeConnections);
    },
    
    getCount: () => activeConnections
  };
}

// Global connection tracker
export const connectionTracker = trackActiveConnections();
