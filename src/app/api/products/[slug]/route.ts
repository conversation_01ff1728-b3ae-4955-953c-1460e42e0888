import { NextRequest, NextResponse } from "next/server";
import { container } from "../../di-container";
import { PRODUCT_SERVICE } from "../../services/service-identifiers";
import type { ProductService } from "../../services/product.service";

// GET /api/products/[slug] - <PERSON><PERSON><PERSON> chi tiết sản phẩm theo slug
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const productService = container.resolve<ProductService>(PRODUCT_SERVICE);
    const { slug } = await params;

    if (!slug) {
      return NextResponse.json({ error: "Slug không hợp lệ" }, { status: 400 });
    }

    // Get product by slug with all related data
    const product = await productService.getProductBySlug(slug, true);

    if (!product) {
      return NextResponse.json(
        { error: "Không tìm thấy sản phẩm" },
        { status: 404 }
      );
    }

    return NextResponse.json(product);
  } catch (error) {
    console.error("Get product by slug error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi lấy thông tin sản phẩm" },
      { status: 500 }
    );
  }
}
