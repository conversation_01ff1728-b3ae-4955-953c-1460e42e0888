/**
 * System Initialization
 * Initialize all services, repositories, and event handlers
 */

import { setupRepositories, setupServices } from './di-setup';
import { NotificationHandlersService } from './services/notification-handlers.service';

let initialized = false;

/**
 * Initialize the entire system
 */
export function initializeSystem(): void {
  if (initialized) {
    return;
  }

  try {
    console.log('🚀 Initializing NS Shop system...');

    // 1. Setup repositories
    console.log('📦 Setting up repositories...');
    setupRepositories();

    // 2. Setup services
    console.log('⚙️ Setting up services...');
    setupServices();

    // 3. Initialize notification handlers
    console.log('🔔 Setting up notification handlers...');
    NotificationHandlersService.initialize();

    initialized = true;
    console.log('✅ NS Shop system initialized successfully!');
  } catch (error) {
    console.error('❌ Failed to initialize system:', error);
    throw error;
  }
}

/**
 * Check if system is initialized
 */
export function isSystemInitialized(): boolean {
  return initialized;
}

/**
 * Force re-initialization (for testing purposes)
 */
export function forceReinitialize(): void {
  initialized = false;
  initializeSystem();
}
