import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../auth/[...nextauth]/route';

export async function GET(request: NextRequest) {
	try {
		const session = await getServerSession(authOptions);
		
		console.log('Debug session:', JSON.stringify(session, null, 2));
		
		return NextResponse.json({
			session,
			hasSession: !!session,
			hasUser: !!session?.user,
			hasUserId: !!session?.user?.id,
			userId: session?.user?.id,
		});
	} catch (error) {
		console.error('Debug session error:', error);
		return NextResponse.json(
			{ error: 'Error getting session' },
			{ status: 500 }
		);
	}
}
