import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "../auth/[...nextauth]/route";
import { z } from "zod";
import { container } from "../di-container";
import { REVIEW_SERVICE } from "../services/service-identifiers";
import { ReviewService } from "../services/review.service";
import { initializeSystem } from "../initialize";
import { UserRole } from "@/app/models/common.model";

const createReviewSchema = z.object({
  productId: z.string().min(1, "Product ID là bắt buộc"),
  rating: z.number().min(1, "Rating phải từ 1-5").max(5, "Rating phải từ 1-5"),
  comment: z.string().optional(),
  images: z.array(z.string()).optional(),
});

// GET /api/reviews - <PERSON><PERSON><PERSON> s<PERSON>ch reviews
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const productId = searchParams.get("productId");
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};

    if (productId) {
      where.productId = productId;
    }

    // Initialize system and get reviews from service
    initializeSystem();
    const reviewService = container.resolve<ReviewService>(REVIEW_SERVICE);

    let result;
    if (productId) {
      result = await reviewService.getProductReviews(productId, {
        page,
        limit,
      });
    } else {
      // For general review listing, we'll create a simple method
      // For now, just get product reviews for the first product
      result = {
        data: [],
        total: 0,
        page,
        limit,
        totalPages: 0,
      };
    }

    return NextResponse.json({
      reviews: result.data,
      pagination: {
        page: result.page,
        limit: result.limit,
        total: result.total,
        pages: result.totalPages,
      },
    });
  } catch (error) {
    console.error("Get reviews error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi lấy danh sách đánh giá" },
      { status: 500 }
    );
  }
}

// POST /api/reviews - Tạo review mới
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Vui lòng đăng nhập" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const data = createReviewSchema.parse(body);

    // Initialize system and create review using service
    initializeSystem();
    const reviewService = container.resolve<ReviewService>(REVIEW_SERVICE);

    // Create user entity for the service
    const user = {
      id: session.user.id,
      email: session.user.email || "",
      name: session.user.name || "",
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      role: (session.user.role || "USER") as UserRole,
    };

    const review = await reviewService.createReview(
      {
        userId: session.user.id,
        productId: data.productId,
        rating: data.rating,
        comment: data.comment || "",
      },
      user
    );

    return NextResponse.json(
      {
        message: "Đánh giá thành công",
        review,
      },
      { status: 201 }
    );
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      );
    }

    console.error("Create review error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi tạo đánh giá" },
      { status: 500 }
    );
  }
}
