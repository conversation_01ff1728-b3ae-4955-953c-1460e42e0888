import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { container } from "@/app/api/di-container";
import { USER_SERVICE } from "@/app/api/services/service-identifiers";
import {
  ValidationError,
  NotFoundError,
} from "@/app/api/repositories/base.repository";
import { UserService } from "@/app/api/services/user.service";

const resetPasswordSchema = z.object({
  token: z.string().min(1, "Token là bắt buộc"),
  password: z.string().min(6, "Mật khẩu phải có ít nhất 6 ký tự"),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { token, password } = resetPasswordSchema.parse(body);

    // Get UserService from DI container
    const userService = container.resolve<UserService>(USER_SERVICE);

    // Use service to reset password
    await userService.resetPassword(token, password);

    return NextResponse.json(
      {
        message: "Đặt lại mật khẩu thành công",
        success: true,
      },
      { status: 200 }
    );
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      );
    }

    if (error instanceof ValidationError) {
      return NextResponse.json({ error: error.message }, { status: 400 });
    }

    if (error instanceof NotFoundError) {
      return NextResponse.json(
        { error: "Token không hợp lệ hoặc đã hết hạn" },
        { status: 400 }
      );
    }

    console.error("Reset password error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi đặt lại mật khẩu" },
      { status: 500 }
    );
  }
}
