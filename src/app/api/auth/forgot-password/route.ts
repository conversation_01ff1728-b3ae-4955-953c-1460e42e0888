import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { container } from "@/app/api/di-container";
import { USER_SERVICE } from "@/app/api/services/service-identifiers";
import { UserService } from "@/app/api/services/user.service";

const forgotPasswordSchema = z.object({
  email: z.string().email("Email không hợp lệ"),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email } = forgotPasswordSchema.parse(body);

    // Get UserService from DI container
    const userService = container.resolve<UserService>(USER_SERVICE);

    // Use service to send password reset email
    await userService.sendPasswordResetEmail(email);

    // Always return success to prevent email enumeration attacks
    return NextResponse.json(
      {
        message:
          "Nếu email tồn tại trong hệ thống, bạn sẽ nhận được link đặt lại mật khẩu.",
        success: true,
      },
      { status: 200 }
    );
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      );
    }

    console.error("Forgot password error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi xử lý yêu cầu" },
      { status: 500 }
    );
  }
}
