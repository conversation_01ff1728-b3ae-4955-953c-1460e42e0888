import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { emailService } from "@/lib/email-service";
import type { WelcomeEmailData } from "@/lib/email/templates";
import { container } from "@/app/api/di-container";
import { USER_SERVICE } from "@/app/api/services/service-identifiers";
import {
  ConflictError,
  ValidationError,
} from "@/app/api/repositories/base.repository";
import { UserService } from "@/app/api/services/user.service";

const registerSchema = z.object({
  name: z.string().min(2, "Tên phải có ít nhất 2 ký tự"),
  email: z.string().email("Email không hợp lệ"),
  password: z.string().min(6, "Mật khẩu phải có ít nhất 6 ký tự"),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, email, password } = registerSchema.parse(body);

    // Get UserService from DI container
    const userService = container.resolve<UserService>(USER_SERVICE);

    // Create user using service
    const user = await userService.createUser({
      name,
      email,
      password,
    });

    // Send welcome email
    try {
      await emailService.initialize();

      const welcomeEmailData: WelcomeEmailData = {
        recipientName: user.name,
        recipientEmail: user.email,
        loginUrl: `${process.env.NEXT_PUBLIC_APP_URL}/auth/signin`,
        supportEmail:
          process.env.ADMIN_EMAIL ||
          process.env.EMAIL_FROM ||
          "<EMAIL>",
      };

      await emailService.sendWelcomeEmail(welcomeEmailData);
      console.log(`Welcome email sent to ${user.email}`);
    } catch (error) {
      console.error("Failed to send welcome email:", error);
      // Don't fail registration if email fails
    }

    return NextResponse.json(
      {
        message: "Đăng ký thành công",
        user: {
          id: user.id,
          name: user.name,
          email: user.email,
          createdAt: user.createdAt,
        },
      },
      { status: 201 }
    );
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      );
    }

    // Handle service-specific errors
    if (error instanceof ConflictError) {
      return NextResponse.json(
        { error: "Email đã được sử dụng" },
        { status: 400 }
      );
    }

    if (error instanceof ValidationError) {
      return NextResponse.json({ error: error.message }, { status: 400 });
    }

    console.error("Register error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi đăng ký" },
      { status: 500 }
    );
  }
}
