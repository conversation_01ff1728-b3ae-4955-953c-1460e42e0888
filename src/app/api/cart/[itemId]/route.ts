import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "../../auth/[...nextauth]/route";
import { z } from "zod";
import { container } from "../../di-container";
import { CART_SERVICE } from "../../services/service-identifiers";
import type { CartService } from "../../services/cart.service";

const updateCartItemSchema = z.object({
  quantity: z.number().int().min(0, "Số lượng không được âm"),
});

// PUT /api/cart/[itemId] - Cập nhật số lượng sản phẩm trong giỏ hàng
export async function PUT(
  request: NextRequest,
  { params }: { params: { itemId: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "<PERSON><PERSON> lòng đăng nhập" },
        { status: 401 }
      );
    }

    const cartService = container.resolve<CartService>(CART_SERVICE);
    const body = await request.json();
    const { quantity } = updateCartItemSchema.parse(body);

    // Update cart item using service
    const result = await cartService.updateCartItem(
      params.itemId,
      session.user.id,
      quantity
    );

    return NextResponse.json({
      message: "Cập nhật giỏ hàng thành công",
      item: result,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      );
    }

    console.error("Update cart item error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi cập nhật giỏ hàng" },
      { status: 500 }
    );
  }
}

// DELETE /api/cart/[itemId] - Xóa sản phẩm khỏi giỏ hàng
export async function DELETE(
  _request: NextRequest,
  { params }: { params: { itemId: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Vui lòng đăng nhập" },
        { status: 401 }
      );
    }

    const cartService = container.resolve<CartService>(CART_SERVICE);

    // Remove cart item using service
    await cartService.removeFromCart(session.user.id, params.itemId);

    return NextResponse.json({
      message: "Xóa sản phẩm khỏi giỏ hàng thành công",
    });
  } catch (error) {
    console.error("Delete cart item error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi xóa sản phẩm khỏi giỏ hàng" },
      { status: 500 }
    );
  }
}
