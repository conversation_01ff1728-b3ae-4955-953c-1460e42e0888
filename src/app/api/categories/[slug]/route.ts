import { NextRequest, NextResponse } from "next/server";
import { container } from "../../di-container";
import { CATEGORY_SERVICE } from "../../services/service-identifiers";
import { CategoryService } from "../../services/category.service";
import { initializeSystem } from "../../initialize";

// GET /api/categories/[slug] - <PERSON><PERSON><PERSON> chi tiết danh mục theo slug
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;
    
    // Initialize system and get category using service
    initializeSystem();
    const categoryService = container.resolve<CategoryService>(CATEGORY_SERVICE);
    
    try {
      const category = await categoryService.getCategoryBySlug(slug);
      return NextResponse.json({ data: category });
    } catch (error) {
      return NextResponse.json(
        { error: "<PERSON>hông tìm thấy danh mục" },
        { status: 404 }
      );
    }
  } catch (error) {
    console.error("Get category error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi lấy thông tin danh mục" },
      { status: 500 }
    );
  }
}
