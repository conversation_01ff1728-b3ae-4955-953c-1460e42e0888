import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "../auth/[...nextauth]/route";
import { z } from "zod";
import { container } from "../di-container";
import { CATEGORY_SERVICE } from "../services/service-identifiers";
import { CategoryService } from "../services/category.service";
import { initializeSystem } from "../initialize";
import { UserRole } from "../../models/common.model";

const categorySchema = z.object({
  name: z.string().min(1, "Tên danh mục là bắt buộc"),
  description: z.string().optional(),
  imageId: z.string().optional().nullable(),
  parentId: z.string().optional().nullable(),
});

// GET /api/categories - L<PERSON>y danh sách danh mục
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const parentId = searchParams.get("parentId");

    // Initialize system and get category service
    initializeSystem();
    const categoryService =
      container.resolve<CategoryService>(CATEGORY_SERVICE);

    let categories;
    if (parentId) {
      // Get children of specific parent
      categories = await categoryService.getChildCategories(parentId);
    } else if (parentId === null) {
      // Get root categories only
      categories = await categoryService.getRootCategories();
    } else {
      // Get all categories
      categories = await categoryService.getAllCategories();
    }

    return NextResponse.json(categories);
  } catch (error) {
    console.error("Get categories error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi lấy danh sách danh mục" },
      { status: 500 }
    );
  }
}

// POST /api/categories - Tạo danh mục mới (Admin only)
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    const body = await request.json();
    const data = categorySchema.parse(body);

    // Initialize system and get category service
    initializeSystem();
    const categoryService =
      container.resolve<CategoryService>(CATEGORY_SERVICE);

    // Create admin user object for service
    const adminUser = {
      id: session.user.id,
      email: session.user.email || "",
      name: session.user.name || "",
      role: (session.user.role as UserRole) || UserRole.ADMIN,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Use service method to create category
    const category = await categoryService.createCategory(
      {
        name: data.name,
        description: data.description,
        image: data.imageId || undefined,
        parentId: data.parentId || undefined,
      },
      adminUser
    );

    return NextResponse.json(
      {
        message: "Tạo danh mục thành công",
        category,
      },
      { status: 201 }
    );
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      );
    }

    console.error("Create category error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi tạo danh mục" },
      { status: 500 }
    );
  }
}
