import { NextRequest, NextResponse } from "next/server";
import { container } from "@/app/api/di-container";
import { AUDIT_LOG_SERVICE } from "@/app/api/services/service-identifiers";
import type { AuditLogService } from "@/app/api/services/audit-log.service";
import { jwtVerify } from "jose";
import type { UserEntity } from "@/app/models/user.model";
import { UserRole } from "@/app/models/common.model";

// Helper function to verify admin JWT token
async function verifyAdminToken(request: NextRequest) {
  try {
    const adminToken = request.cookies.get("admin-session")?.value;
    if (!adminToken) return null;

    const secret = new TextEncoder().encode(
      process.env.NEXTAUTH_SECRET || "fallback-secret"
    );
    const { payload } = await jwtVerify(adminToken, secret);
    return payload;
  } catch {
    return null;
  }
}

// GET /api/admin/logs/export - Export audit logs to CSV
export async function GET(request: NextRequest) {
  try {
    const adminToken = await verifyAdminToken(request);

    if (!adminToken || adminToken.type !== "admin") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    // Only ADMIN can export audit logs
    if (adminToken.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Chỉ Admin mới có quyền xuất logs" },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get("startDate");
    const endDate = searchParams.get("endDate");
    const action = searchParams.get("action");
    const resource = searchParams.get("resource");
    const adminId = searchParams.get("adminId");

    // Resolve audit log service
    const auditLogService =
      container.resolve<AuditLogService>(AUDIT_LOG_SERVICE);

    // Create admin user entity for permission check
    const adminUserEntity: UserEntity = {
      id: adminToken.id as string,
      email: adminToken.email as string,
      name: adminToken.name as string,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      role: adminToken.role as UserRole,
    };

    // Build export filters
    const exportFilters = {
      action: action || undefined,
      resource: resource || undefined,
      userId: adminId || undefined,
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined,
      format: "csv" as const,
    };

    // Export audit logs using service
    const csvContent = await auditLogService.exportAuditLogs(
      exportFilters,
      adminUserEntity
    );

    // Add BOM for proper UTF-8 encoding in Excel
    const bom = "\uFEFF";
    const csvWithBom = bom + csvContent;

    const filename = `audit-logs-${new Date().toISOString().split("T")[0]}.csv`;

    return new NextResponse(csvWithBom, {
      headers: {
        "Content-Type": "text/csv; charset=utf-8",
        "Content-Disposition": `attachment; filename="${filename}"`,
      },
    });
  } catch (error) {
    console.error("Export audit logs error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi xuất logs" },
      { status: 500 }
    );
  }
}
