import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { verifyAdminToken } from "@/lib/admin-auth";
import { container, SERVICE_IDENTIFIERS } from "../../di-container";
import type { MenuRepository } from "../../repositories/menu.repository";
import { initializeSystem } from "../../initialize";

// Validation schemas
const createMenuItemSchema = z.object({
  menuId: z.string().min(1, "Menu ID là bắt buộc"),
  parentId: z.string().optional(),
  title: z.string().min(1, "Tiêu đề là bắt buộc"),
  url: z.string().optional(),
  type: z.enum(["LINK", "PAGE", "CATEGORY", "PRODUCT", "CUSTOM", "SEPARATOR"]),
  target: z.string().optional(),
  icon: z.string().optional(),
  cssClass: z.string().optional(),
  order: z.number().int().min(0).default(0),
  isActive: z.boolean().default(true),
});

const _updateMenuItemSchema = createMenuItemSchema
  .partial()
  .omit({ menuId: true });

// GET /api/admin/menu-items - Get menu items for a specific menu
export async function GET(request: NextRequest) {
  try {
    const adminToken = await verifyAdminToken(request);
    if (!adminToken) {
      return NextResponse.json(
        { success: false, error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const menuId = searchParams.get("menuId");
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "50");
    const _search = searchParams.get("search") || "";

    if (!menuId) {
      return NextResponse.json(
        { success: false, error: "Menu ID là bắt buộc" },
        { status: 400 }
      );
    }

    // Initialize system and get repository
    initializeSystem();
    const _menuRepository = container.resolve<MenuRepository>(
      SERVICE_IDENTIFIERS.MENU_REPOSITORY
    );

    // TODO: Implement menu item methods in MenuRepository
    // For now, return placeholder response
    const result = {
      data: [],
      pagination: {
        total: 0,
        page,
        limit,
        totalPages: 0,
        hasNext: false,
        hasPrev: false,
      },
    };

    return NextResponse.json({
      success: true,
      data: result.data,
      pagination: result.pagination,
    });
  } catch (error) {
    console.error("Error fetching menu items:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi lấy danh sách menu items" },
      { status: 500 }
    );
  }
}

// POST /api/admin/menu-items - Create new menu item
export async function POST(request: NextRequest) {
  try {
    const adminToken = await verifyAdminToken(request);
    if (!adminToken) {
      return NextResponse.json(
        { success: false, error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const validatedData = createMenuItemSchema.parse(body);

    // Initialize system and get repository
    initializeSystem();
    const _menuRepository = container.resolve<MenuRepository>(
      SERVICE_IDENTIFIERS.MENU_REPOSITORY
    );

    // TODO: Implement menu item creation in MenuRepository
    // For now, return placeholder response
    const menuItem = {
      id: "placeholder-menu-item-id",
      title: validatedData.title,
      menuId: validatedData.menuId,
      createdAt: new Date(),
    };

    return NextResponse.json({
      success: true,
      data: menuItem,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: error.errors[0].message },
        { status: 400 }
      );
    }

    console.error("Error creating menu item:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi tạo menu item" },
      { status: 500 }
    );
  }
}

// PUT /api/admin/menu-items/reorder - Reorder menu items
export async function PUT(request: NextRequest) {
  try {
    const adminToken = await verifyAdminToken(request);
    if (!adminToken) {
      return NextResponse.json(
        { success: false, error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { items } = body;

    if (!items || !Array.isArray(items)) {
      return NextResponse.json(
        { success: false, error: "Danh sách items không hợp lệ" },
        { status: 400 }
      );
    }

    // Initialize system and get repository
    initializeSystem();
    const _menuRepository = container.resolve<MenuRepository>(
      SERVICE_IDENTIFIERS.MENU_REPOSITORY
    );

    // TODO: Implement menu item reordering in MenuRepository
    // For now, skip the update

    return NextResponse.json({
      success: true,
      message: "Đã cập nhật thứ tự menu items",
    });
  } catch (error) {
    console.error("Error reordering menu items:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi sắp xếp menu items" },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/menu-items - Bulk delete menu items
export async function DELETE(request: NextRequest) {
  try {
    const adminToken = await verifyAdminToken(request);
    if (!adminToken) {
      return NextResponse.json(
        { success: false, error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const idsParam = searchParams.get("ids");

    if (!idsParam) {
      return NextResponse.json(
        { success: false, error: "Danh sách ID không hợp lệ" },
        { status: 400 }
      );
    }

    const ids = idsParam.split(",");

    // Initialize system and get repository
    initializeSystem();
    const _menuRepository = container.resolve<MenuRepository>(
      SERVICE_IDENTIFIERS.MENU_REPOSITORY
    );

    // TODO: Implement menu item deletion in MenuRepository
    // For now, skip the deletion

    return NextResponse.json({
      success: true,
      message: `Đã xóa ${ids.length} menu item`,
    });
  } catch (error) {
    console.error("Error deleting menu items:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi xóa menu items" },
      { status: 500 }
    );
  }
}
