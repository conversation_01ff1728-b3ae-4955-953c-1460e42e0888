import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { verifyAdminToken } from "@/lib/admin-auth";
import { container } from "../../../di-container";
import { MENU_SERVICE } from "../../../services/service-identifiers";
import { MenuService } from "../../../services/menu.service";
import { initializeSystem } from "../../../initialize";

// Validation schema
const updateMenuItemSchema = z.object({
  parentId: z.string().optional(),
  title: z.string().min(1, "Tiêu đề là bắt buộc").optional(),
  url: z.string().optional(),
  type: z
    .enum(["LINK", "PAGE", "CATEGORY", "PRODUCT", "CUSTOM", "SEPARATOR"])
    .optional(),
  target: z.string().optional(),
  icon: z.string().optional(),
  cssClass: z.string().optional(),
  order: z.number().int().min(0).optional(),
  isActive: z.boolean().optional(),
});

// GET /api/admin/menu-items/[id] - Get single menu item
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const adminToken = await verifyAdminToken(request);
    if (!adminToken) {
      return NextResponse.json(
        { success: false, error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    // Initialize system and resolve services
    initializeSystem();
    const menuService = container.resolve<MenuService>(MENU_SERVICE);

    // TODO: Implement getMenuItemById method in MenuService
    // For now, return placeholder response
    const menuItem = null;

    if (!menuItem) {
      return NextResponse.json(
        { success: false, error: "Menu item không tồn tại" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: menuItem,
    });
  } catch (error) {
    console.error("Error fetching menu item:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi lấy thông tin menu item" },
      { status: 500 }
    );
  }
}

// PUT /api/admin/menu-items/[id] - Update menu item
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const adminToken = await verifyAdminToken(request);
    if (!adminToken) {
      return NextResponse.json(
        { success: false, error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const validatedData = updateMenuItemSchema.parse(body);

    // Initialize system and resolve services
    initializeSystem();
    const _menuService = container.resolve<MenuService>(MENU_SERVICE);

    // TODO: Implement updateMenuItem method in MenuService
    // For now, return placeholder response
    return NextResponse.json(
      {
        success: false,
        error:
          "MenuItem update not implemented yet - MenuService needs updateMenuItem method",
      },
      { status: 501 }
    );
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: error.errors[0].message },
        { status: 400 }
      );
    }

    console.error("Error updating menu item:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi cập nhật menu item" },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/menu-items/[id] - Delete menu item
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const adminToken = await verifyAdminToken(request);
    if (!adminToken) {
      return NextResponse.json(
        { success: false, error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    // Initialize system and resolve services
    initializeSystem();
    const _menuService = container.resolve<MenuService>(MENU_SERVICE);

    // TODO: Implement deleteMenuItem method in MenuService
    // For now, return placeholder response
    return NextResponse.json(
      {
        success: false,
        error:
          "MenuItem delete not implemented yet - MenuService needs deleteMenuItem method",
      },
      { status: 501 }
    );
  } catch (error) {
    console.error("Error deleting menu item:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi xóa menu item" },
      { status: 500 }
    );
  }
}
