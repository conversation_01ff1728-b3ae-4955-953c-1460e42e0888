import { NextRequest, NextResponse } from "next/server";
import { container } from "@/app/api/di-container";
import { BRAND_SERVICE } from "@/app/api/services/service-identifiers";
import type { BrandService } from "@/app/api/services/brand.service";
import { z } from "zod";

// Validation schema for brand creation/update
const brandSchema = z.object({
  name: z.string().min(1, "Tên thương hiệu là bắt buộc"),
  description: z.string().optional(),
  slug: z.string().min(1, "Slug là bắt buộc"),
  logoId: z.string().optional().nullable(),
  website: z
    .string()
    .url("Website phải là URL hợp lệ")
    .optional()
    .or(z.literal("")),
  isActive: z.boolean().default(true),
});

// GET /api/admin/brands - Get all brands with pagination and filters
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const search = searchParams.get("search") || "";
    const isActive = searchParams.get("isActive");
    const sortBy = searchParams.get("sortBy") || "createdAt";
    const sortOrder = searchParams.get("sortOrder") || "desc";

    const brandService = container.resolve<BrandService>(BRAND_SERVICE);

    // Get brands with pagination using service
    const result = await brandService.getBrands({
      page,
      limit,
      search,
      filters: {
        isActive:
          isActive !== null && isActive !== undefined && isActive !== ""
            ? isActive === "true"
            : undefined,
      },
      sortBy,
      sortOrder: sortOrder as "asc" | "desc",
    });

    const totalPages = Math.ceil(result.total / limit);

    return NextResponse.json({
      success: true,
      data: result.data,
      pagination: {
        page,
        limit,
        total: result.total,
        totalPages,
      },
    });
  } catch (error) {
    console.error("Get brands error:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi lấy danh sách thương hiệu" },
      { status: 500 }
    );
  }
}

// POST /api/admin/brands - Create new brand
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate input
    const validatedData = brandSchema.parse(body);

    const brandService = container.resolve<BrandService>(BRAND_SERVICE);

    // Create admin user entity for audit
    const adminUserEntity = {
      id: "admin",
      email: "<EMAIL>",
      name: "Admin User",
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    } as any;

    // Create brand using service
    const brand = await brandService.createBrand(
      validatedData,
      adminUserEntity
    );

    return NextResponse.json({
      success: true,
      data: brand,
      message: "Thương hiệu đã được tạo thành công",
    });
  } catch (error) {
    console.error("Create brand error:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: error.errors[0].message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi tạo thương hiệu" },
      { status: 500 }
    );
  }
}
