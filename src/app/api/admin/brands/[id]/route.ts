import { NextRequest, NextResponse } from "next/server";
import { container } from "@/app/api/di-container";
import { BRAND_SERVICE } from "@/app/api/services/service-identifiers";
import type { BrandService } from "@/app/api/services/brand.service";
import { z } from "zod";

// Validation schema for brand update
const brandUpdateSchema = z.object({
  name: z.string().min(1, "Tên thương hiệu là bắt buộc").optional(),
  description: z.string().optional(),
  slug: z.string().min(1, "Slug là bắt buộc").optional(),
  logoId: z.string().optional().nullable(),
  website: z
    .string()
    .url("Website phải là URL hợp lệ")
    .optional()
    .or(z.literal("")),
  isActive: z.boolean().optional(),
});

// GET /api/admin/brands/[id] - Get single brand
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const brandService = container.resolve<BrandService>(BRAND_SERVICE);
    const brand = await brandService.getBrandById(params.id);

    if (!brand) {
      return NextResponse.json(
        { success: false, error: "Không tìm thấy thương hiệu" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: brand,
    });
  } catch (error) {
    console.error("Get brand error:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi lấy thông tin thương hiệu" },
      { status: 500 }
    );
  }
}

// PUT /api/admin/brands/[id] - Update brand
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();

    // Validate input
    const validatedData = brandUpdateSchema.parse(body);

    const brandService = container.resolve<BrandService>(BRAND_SERVICE);

    // Create admin user entity for audit
    const adminUserEntity = {
      id: "admin",
      email: "<EMAIL>",
      name: "Admin User",
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    } as any;

    // Update brand using service
    const brand = await brandService.updateBrand(
      params.id,
      validatedData,
      adminUserEntity
    );

    return NextResponse.json({
      success: true,
      data: brand,
      message: "Thương hiệu đã được cập nhật thành công",
    });
  } catch (error) {
    console.error("Update brand error:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: error.errors[0].message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi cập nhật thương hiệu" },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/brands/[id] - Delete brand
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const brandService = container.resolve<BrandService>(BRAND_SERVICE);

    // Create admin user entity for audit
    const adminUserEntity = {
      id: "admin",
      email: "<EMAIL>",
      name: "Admin User",
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    } as any;

    // Delete brand using service
    await brandService.deleteBrand(params.id, adminUserEntity);

    return NextResponse.json({
      success: true,
      message: "Thương hiệu đã được xóa thành công",
    });
  } catch (error) {
    console.error("Delete brand error:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi xóa thương hiệu" },
      { status: 500 }
    );
  }
}
