import { NextRequest, NextResponse } from "next/server";
import { verifyAdminAuth } from "../../../utils/admin-auth";
import { container } from "@/app/api/di-container";
import { CONTACT_SERVICE } from "@/app/api/services/service-identifiers";
import type { ContactService } from "@/app/api/services/contact.service";
import { initializeSystem } from "@/app/api/initialize";

// GET /api/admin/contacts/stats - Get contact statistics
export async function GET(request: NextRequest) {
  try {
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: 401 });
    }

    // Initialize system and resolve services
    initializeSystem();
    const contactService = container.resolve<ContactService>(CONTACT_SERVICE);

    // Create admin user for service calls
    const adminUser = {
      id: authResult.admin?.id || "admin",
      role: "admin",
      type: "admin",
    } as any;

    // Get contact statistics using service
    const stats = await contactService.getContactStats(adminUser);

    // For now, use simplified stats since the service method doesn't provide all the detailed breakdowns
    const totalContacts = stats.total;
    const newContacts = stats.byStatus.pending || 0;
    const openContacts = 0; // TODO: Add to service method
    const resolvedContacts = stats.byStatus.resolved || 0;
    const todayContacts = 0; // TODO: Add time-based filtering to service
    const weekContacts = 0; // TODO: Add time-based filtering to service
    const monthContacts = 0; // TODO: Add time-based filtering to service
    const urgentContacts = 0; // TODO: Add priority filtering to service

    // Get status distribution - placeholder
    // TODO: Implement detailed stats methods in ContactService
    const statusStats = [
      { status: "NEW", _count: { status: newContacts } },
      { status: "RESOLVED", _count: { status: resolvedContacts } },
    ];

    // Get priority distribution - placeholder
    const priorityStats = [
      { priority: "NORMAL", _count: { priority: 0 } },
      { priority: "URGENT", _count: { priority: urgentContacts } },
    ];

    // Get source distribution - placeholder
    const sourceStats = [
      { source: "WEBSITE", _count: { source: 0 } },
      { source: "EMAIL", _count: { source: 0 } },
    ];

    // Get daily contacts - placeholder
    const dailyContacts = Array.from({ length: 30 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - (29 - i));
      return {
        date: date.toISOString().split("T")[0],
        count: 0,
      };
    });

    // Get top services - placeholder
    const serviceStats = [
      { service: "SUPPORT", _count: { service: 0 } },
      { service: "SALES", _count: { service: 0 } },
    ];

    // Get admin assignment stats - placeholder
    const assignmentStatsWithNames = [
      {
        adminId: authResult.admin?.id || "admin",
        adminName: authResult.admin?.name || "Admin",
        count: 0,
      },
    ];

    // Response time calculation would need computed fields
    // For now, we'll skip this complex calculation

    return NextResponse.json({
      overview: {
        total: totalContacts,
        new: newContacts,
        open: openContacts,
        resolved: resolvedContacts,
        urgent: urgentContacts,
      },
      timeframe: {
        today: todayContacts,
        week: weekContacts,
        month: monthContacts,
      },
      distribution: {
        status: statusStats.map((stat) => ({
          status: stat.status,
          count: stat._count.status,
        })),
        priority: priorityStats.map((stat) => ({
          priority: stat.priority,
          count: stat._count.priority,
        })),
        source: sourceStats.map((stat) => ({
          source: stat.source,
          count: stat._count.source,
        })),
      },
      trends: {
        daily: dailyContacts.map((day) => ({
          date: day.date,
          count: Number(day.count),
        })),
      },
      services: serviceStats.map((stat) => ({
        service: stat.service,
        count: stat._count.service,
      })),
      assignments: assignmentStatsWithNames,
    });
  } catch (error) {
    console.error("Error fetching contact stats:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
