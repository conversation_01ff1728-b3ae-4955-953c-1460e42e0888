import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { emailService } from "@/lib/email-service";
import type { ContactFormEmailData } from "@/lib/email/templates";

const testEmailSchema = z.object({
  recipientEmail: z.string().email("Email không hợp lệ"),
  recipientName: z.string().min(1, "Tên không được để trống"),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { recipientEmail, recipientName } = testEmailSchema.parse(body);

    // Initialize email service
    const initialized = await emailService.initialize();
    if (!initialized) {
      return NextResponse.json(
        { error: "Email service not configured" },
        { status: 503 }
      );
    }

    // Create test contact form data
    const testContactData: ContactFormEmailData = {
      recipientName,
      recipientEmail,
      senderName: "Nguyễn <PERSON>",
      senderEmail: "<EMAIL>",
      senderPhone: "0123456789",
      company: "Công ty Test ABC",
      service: "May gia công số lượng ít",
      subject: "Test liên hệ từ website",
      message: "Đây là tin nhắn test để kiểm tra hệ thống email notification.\n\nTôi muốn tìm hiểu về dịch vụ may gia công của NS Shop. Vui lòng liên hệ lại với tôi để tư vấn chi tiết.\n\nCảm ơn!",
      submittedAt: new Date().toISOString(),
      contactId: "test-contact-id-123",
      source: "website",
      ipAddress: "*************",
      userAgent: "Mozilla/5.0 (Test Browser)",
    };

    // Send test email
    const success = await emailService.sendContactFormEmail(testContactData);

    if (success) {
      return NextResponse.json({
        message: "Test email sent successfully",
        success: true,
        recipient: {
          name: recipientName,
          email: recipientEmail,
        },
      });
    } else {
      return NextResponse.json(
        { error: "Failed to send test email" },
        { status: 500 }
      );
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      );
    }

    console.error("Test email error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// GET endpoint to check email service status
export async function GET() {
  try {
    const initialized = await emailService.initialize();
    
    if (!initialized) {
      return NextResponse.json({
        configured: false,
        connected: false,
        message: "Email service not configured",
      });
    }

    const connected = await emailService.testConnection();

    return NextResponse.json({
      configured: true,
      connected,
      message: connected 
        ? "Email service is working properly" 
        : "Email service configured but connection failed",
    });
  } catch (error) {
    console.error("Email service status check error:", error);
    return NextResponse.json({
      configured: false,
      connected: false,
      message: "Error checking email service status",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
}
