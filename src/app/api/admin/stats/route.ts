import { NextRequest, NextResponse } from "next/server";
import { jwtVerify } from "jose";
import { container } from "../../di-container";
import {
  PRODUCT_SERVICE,
  CATEGORY_SERVICE,
  ORDER_SERVICE,
  USER_SERVICE,
} from "../../services/service-identifiers";
import type { ProductService } from "../../services/product.service";
import type { CategoryService } from "../../services/category.service";
import type { OrderService } from "../../services/order.service";
import type { UserService } from "../../services/user.service";
import { initializeSystem } from "../../initialize";

// Helper function to verify admin JWT token
async function verifyAdminToken(request: NextRequest) {
  try {
    const adminToken = request.cookies.get("admin-session")?.value;
    if (!adminToken) return null;

    const secret = new TextEncoder().encode(
      process.env.NEXTAUTH_SECRET || "fallback-secret"
    );
    const { payload } = await jwtVerify(adminToken, secret);
    return payload;
  } catch {
    return null;
  }
}

// GET /api/admin/stats - Lấy thống kê tổng quan cho admin dashboard
export async function GET(request: NextRequest) {
  try {
    const adminToken = await verifyAdminToken(request);

    if (
      !adminToken ||
      (adminToken.role !== "ADMIN" && adminToken.role !== "MODERATOR")
    ) {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    // Initialize system and get services
    initializeSystem();
    const _productService = container.resolve<ProductService>(PRODUCT_SERVICE);
    const _categoryService =
      container.resolve<CategoryService>(CATEGORY_SERVICE);
    const _orderService = container.resolve<OrderService>(ORDER_SERVICE);
    const _userService = container.resolve<UserService>(USER_SERVICE);

    // Get basic counts using services - placeholder values
    // TODO: Implement proper service methods for statistics
    const [
      totalProducts,
      activeProducts,
      totalCategories,
      totalOrders,
      totalUsers,
      totalRevenue,
      recentOrders,
      topProducts,
      lowStockProducts,
    ] = await Promise.all([
      // Total products - placeholder
      Promise.resolve(0),

      // Active products - placeholder
      Promise.resolve(0),

      // Total categories - placeholder
      Promise.resolve(0),

      // Total orders - placeholder
      Promise.resolve(0),

      // Total users - placeholder
      Promise.resolve(0),

      // Total revenue (from delivered orders) - placeholder
      Promise.resolve({ _sum: { total: 0 } }),

      // Recent orders (last 10) - placeholder
      Promise.resolve([]),

      // Top selling products - placeholder
      Promise.resolve([]),

      // Low stock products (stock < 10) - placeholder
      Promise.resolve([]),
    ]);

    // Calculate growth rates (compared to last month)
    const lastMonth = new Date();
    lastMonth.setMonth(lastMonth.getMonth() - 1);

    // Last month stats - placeholder values
    const [
      lastMonthProducts,
      lastMonthOrders,
      lastMonthUsers,
      lastMonthRevenue,
    ] = await Promise.all([
      // Last month products - placeholder
      Promise.resolve(0),

      // Last month orders - placeholder
      Promise.resolve(0),

      // Last month users - placeholder
      Promise.resolve(0),

      // Last month revenue - placeholder
      Promise.resolve({ _sum: { total: 0 } }),
    ]);

    // Calculate growth percentages
    const calculateGrowth = (current: number, previous: number) => {
      if (previous === 0) return current > 0 ? 100 : 0;
      return Math.round(((current - previous) / previous) * 100);
    };

    const stats = {
      overview: {
        totalProducts: {
          value: totalProducts,
          growth: calculateGrowth(totalProducts, lastMonthProducts),
        },
        activeProducts: {
          value: activeProducts,
          percentage:
            totalProducts > 0
              ? Math.round((activeProducts / totalProducts) * 100)
              : 0,
        },
        totalCategories: {
          value: totalCategories,
        },
        totalOrders: {
          value: totalOrders,
          growth: calculateGrowth(totalOrders, lastMonthOrders),
        },
        totalUsers: {
          value: totalUsers,
          growth: calculateGrowth(totalUsers, lastMonthUsers),
        },
        totalRevenue: {
          value: totalRevenue._sum?.total || 0,
          growth: calculateGrowth(
            totalRevenue._sum?.total || 0,
            lastMonthRevenue._sum?.total || 0
          ),
        },
      },
      // Placeholder empty arrays since services don't have these methods yet
      recentOrders: [],
      topProducts: [],
      lowStockProducts: [],
    };

    return NextResponse.json(stats);
  } catch (error) {
    console.error("Get admin stats error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi lấy thống kê" },
      { status: 500 }
    );
  }
}
