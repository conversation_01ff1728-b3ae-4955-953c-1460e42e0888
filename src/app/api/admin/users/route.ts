import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { adminAuthOptions } from "@/lib/admin-auth";
import { container } from "../../di-container";
import { USER_SERVICE } from "../../services/service-identifiers";
import { UserService } from "../../services/user.service";

// GET /api/admin/users - L<PERSON>y danh sách users cho admin
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.type !== "admin") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    // Both ADMIN and MODERATOR can manage regular users
    if (session.user.role !== "ADMIN" && session.user.role !== "MODERATOR") {
      return NextResponse.json(
        { error: "<PERSON>hông có quyền quản lý người dùng" },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const search = searchParams.get("search");
    const role = searchParams.get("role");

    // Get UserService from DI container
    const userService = container.resolve<UserService>(USER_SERVICE);

    // Search users using service
    const result = await userService.searchUsers({
      search: search || undefined,
      role: role as any,
      page,
      limit,
    });

    return NextResponse.json({
      success: true,
      data: result.data,
      pagination: {
        total: result.total,
        page: result.page,
        limit: result.limit,
        totalPages: result.totalPages,
        hasNext: result.hasNext,
        hasPrev: result.hasPrev,
      },
    });
  } catch (error) {
    console.error("Get admin users error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi lấy danh sách người dùng" },
      { status: 500 }
    );
  }
}
