import { NextRequest, NextResponse } from "next/server";
import { container } from "../../di-container";
import { CATEGORY_SERVICE } from "../../services/service-identifiers";
import { CategoryService } from "../../services/category.service";
import { initializeSystem } from "../../initialize";
import { verifyAdminAuth } from "../../utils/admin-auth";

// GET /api/admin/categories - Lấy danh sách categories cho admin
export async function GET(request: NextRequest) {
  try {
    const authResult = await verifyAdminAuth(request);

    if (!authResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: authResult.error || "Không có quyền truy cập",
        },
        { status: 403 }
      );
    }

    // Initialize system and get CategoryService from DI container
    initializeSystem();
    const categoryService =
      container.resolve<CategoryService>(CATEGORY_SERVICE);

    // Get all categories with counts
    const categories = await categoryService.getAllCategories();

    return NextResponse.json({
      success: true,
      data: categories,
    });
  } catch (error) {
    console.error("Get admin categories error:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi lấy danh sách danh mục" },
      { status: 500 }
    );
  }
}

// POST /api/admin/categories - Tạo danh mục mới
export async function POST(request: NextRequest) {
  try {
    const authResult = await verifyAdminAuth(request);

    if (!authResult.success || !authResult.admin) {
      return NextResponse.json(
        {
          success: false,
          error: authResult.error || "Không có quyền truy cập",
        },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { name, description, slug, parentId } = body;

    // Initialize system and get CategoryService from DI container
    initializeSystem();
    const categoryService =
      container.resolve<CategoryService>(CATEGORY_SERVICE);

    // Create admin user entity for the service
    const adminUser = {
      id: authResult.admin.id,
      role: authResult.admin.role as any,
      type: "admin" as const,
    };

    // Create category using service
    const category = await categoryService.createCategory(
      {
        name,
        description: description || undefined,
        slug,
        parentId: parentId || undefined,
      },
      adminUser as any
    );

    return NextResponse.json(
      {
        success: true,
        data: category,
        message: "Danh mục đã được tạo thành công",
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Create category error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi tạo danh mục" },
      { status: 500 }
    );
  }
}
