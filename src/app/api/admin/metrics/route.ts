/**
 * Admin Metrics API Route
 * Performance monitoring dashboard
 */

import { NextRequest, NextResponse } from 'next/server';
import { initializeSystem } from '../../initialize';
import { container } from '../../di-container';
import { METRICS_SERVICE, WEBSOCKET_SERVICE, EMAIL_SERVICE, RATE_LIMIT_SERVICE } from '../../services/service-identifiers';
import { MetricsService } from '../../services/metrics.service';
import { WebSocketService } from '../../services/websocket.service';
import { EmailService } from '../../services/email.service';
import { RateLimitService } from '../../services/rate-limit.service';
import { createMetricsDashboard } from '../../middleware/metrics.middleware';

// Initialize system
initializeSystem();

/**
 * GET /api/admin/metrics
 * Get metrics dashboard data
 */
export async function GET(request: NextRequest) {
  try {
    const metricsService = container.resolve<MetricsService>(METRICS_SERVICE);
    const webSocketService = container.resolve<WebSocketService>(WEBSOCKET_SERVICE);
    const emailService = container.resolve<EmailService>(EMAIL_SERVICE);

    // Get dashboard data
    const dashboardData = createMetricsDashboard();
    
    // Get WebSocket stats
    const wsStats = webSocketService.getStats();
    
    // Get email provider status
    const emailProviders = emailService.getProviderStatus();

    // Get recent alerts
    const alerts = metricsService.getAlerts(20);

    // Get system metrics
    const summary = metricsService.getSummary();

    return NextResponse.json({
      success: true,
      data: {
        dashboard: dashboardData,
        websocket: wsStats,
        email: {
          providers: emailProviders,
          templates: emailService.getTemplates().map(t => ({
            id: t.id,
            name: t.name,
            subject: t.subject
          }))
        },
        alerts,
        summary,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Metrics API error:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch metrics data'
    }, { status: 500 });
  }
}

/**
 * POST /api/admin/metrics/test
 * Test various services
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, data } = body;

    const metricsService = container.resolve<MetricsService>(METRICS_SERVICE);
    const webSocketService = container.resolve<WebSocketService>(WEBSOCKET_SERVICE);
    const emailService = container.resolve<EmailService>(EMAIL_SERVICE);
    const rateLimitService = container.resolve<RateLimitService>(RATE_LIMIT_SERVICE);

    let result: any = {};

    switch (action) {
      case 'test_websocket':
        // Test WebSocket notification
        const sentCount = webSocketService.sendNotificationToAdmins({
          id: `test_${Date.now()}`,
          title: 'Test Notification',
          message: 'This is a test notification from metrics dashboard',
          category: 'info'
        });
        result = { sentCount, message: `Notification sent to ${sentCount} admin connections` };
        break;

      case 'test_email':
        // Test email sending
        const emailResult = await emailService.sendEmail({
          to: data.email || '<EMAIL>',
          subject: 'Test Email from NS Shop',
          html: '<h1>Test Email</h1><p>This is a test email from the metrics dashboard.</p>',
          priority: 'normal',
          tags: ['test', 'metrics']
        });
        result = emailResult;
        break;

      case 'test_rate_limit':
        // Test rate limiting
        const rateLimitResult = await rateLimitService.checkRateLimit(
          data.identifier || 'test_user',
          {
            windowMs: 60000, // 1 minute
            maxRequests: 5,
            strategy: 'sliding_window',
            message: 'Test rate limit exceeded'
          }
        );
        result = rateLimitResult;
        break;

      case 'record_metric':
        // Record custom metric
        const { name, value, type, tags } = data;
        switch (type) {
          case 'counter':
            metricsService.counter(name, value, tags);
            break;
          case 'gauge':
            metricsService.gauge(name, value, tags);
            break;
          case 'histogram':
            metricsService.histogram(name, value, tags);
            break;
          default:
            throw new Error(`Unknown metric type: ${type}`);
        }
        result = { message: `Metric ${name} recorded successfully` };
        break;

      case 'simulate_load':
        // Simulate API load for testing
        const requests = data.requests || 10;
        const promises = [];
        
        for (let i = 0; i < requests; i++) {
          promises.push(
            metricsService.timeFunction(
              'simulated_request',
              async () => {
                // Simulate some work
                await new Promise(resolve => setTimeout(resolve, Math.random() * 100));
                
                // Record metrics
                metricsService.recordApiRequest(
                  'GET',
                  '/api/test',
                  200,
                  Math.random() * 200,
                  `user_${i}`
                );
              },
              { simulation: 'true' }
            )
          );
        }
        
        await Promise.all(promises);
        result = { message: `Simulated ${requests} API requests` };
        break;

      default:
        throw new Error(`Unknown action: ${action}`);
    }

    return NextResponse.json({
      success: true,
      action,
      result,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Metrics test error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 400 });
  }
}

/**
 * DELETE /api/admin/metrics/cleanup
 * Cleanup old metrics data
 */
export async function DELETE(request: NextRequest) {
  try {
    const metricsService = container.resolve<MetricsService>(METRICS_SERVICE);
    const rateLimitService = container.resolve<RateLimitService>(RATE_LIMIT_SERVICE);

    // Cleanup metrics
    metricsService.cleanup();
    await rateLimitService.cleanup();

    return NextResponse.json({
      success: true,
      message: 'Metrics cleanup completed',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Metrics cleanup error:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to cleanup metrics'
    }, { status: 500 });
  }
}
