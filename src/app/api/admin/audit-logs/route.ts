import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { jwtVerify } from "jose";
import { container } from "@/app/api/di-container";
import { AUDIT_LOG_SERVICE } from "@/app/api/services/service-identifiers";
import { ForbiddenError } from "@/app/models/common.model";
import { AuditLogService } from "@/app/api/services/audit-log.service";

// Helper function to verify admin JWT token
async function verifyAdminToken(request: NextRequest) {
  try {
    const adminToken = request.cookies.get("admin-session")?.value;
    if (!adminToken) return null;

    const secret = new TextEncoder().encode(
      process.env.NEXTAUTH_SECRET || "fallback-secret"
    );
    const { payload } = await jwtVerify(adminToken, secret);
    return payload;
  } catch {
    return null;
  }
}

// Validation schema for query parameters
const auditLogsQuerySchema = z.object({
  page: z.string().optional().default("1"),
  limit: z.string().optional().default("20"),
  action: z.string().optional(),
  resource: z.string().optional(),
  adminId: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  search: z.string().optional(),
  sortBy: z
    .enum(["createdAt", "action", "resource", "admin"])
    .optional()
    .default("createdAt"),
  sortOrder: z.enum(["asc", "desc"]).optional().default("desc"),
});

export async function GET(request: NextRequest) {
  try {
    // Check admin authentication
    const adminToken = await verifyAdminToken(request);
    if (!adminToken || adminToken.type !== "admin") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Parse and validate query parameters
    const url = new URL(request.url);
    const queryParams = Object.fromEntries(url.searchParams.entries());
    const validatedParams = auditLogsQuerySchema.parse(queryParams);

    const {
      page,
      limit,
      action,
      resource,
      adminId,
      startDate,
      endDate,
      search,
      sortBy,
      sortOrder,
    } = validatedParams;

    // Get AuditLogService from DI container
    const auditLogService =
      container.resolve<AuditLogService>(AUDIT_LOG_SERVICE);

    // Build filters for the service
    const filters: any = {
      page: parseInt(page),
      limit: parseInt(limit),
      sortBy,
      sortOrder,
    };

    if (action) filters.action = action;
    if (resource) filters.entityType = resource;
    if (adminId) filters.userId = adminId;
    if (search) filters.search = search;
    if (startDate) filters.startDate = new Date(startDate);
    if (endDate) filters.endDate = new Date(endDate);

    // Create a mock admin user for permission checking
    const requestedBy = {
      id: adminToken.id as string,
      role: "ADMIN",
    } as any;

    // Get audit logs using service
    const result = await auditLogService.getAuditLogs(filters, requestedBy);

    return NextResponse.json({
      success: true,
      data: result.data,
      pagination: {
        page: result.page,
        limit: result.limit,
        total: result.total,
        totalPages: result.totalPages,
        hasNext: result.hasNext,
        hasPrev: result.hasPrev,
      },
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: "Invalid query parameters",
          details: error.errors.map((e) => `${e.path.join(".")}: ${e.message}`),
        },
        { status: 400 }
      );
    }

    if (error instanceof ForbiddenError) {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    console.error("Get audit logs error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi lấy audit logs" },
      { status: 500 }
    );
  }
}

// Get unique values for filters
export async function POST(request: NextRequest) {
  try {
    // Check admin authentication
    const adminToken = await verifyAdminToken(request);
    if (!adminToken || adminToken.type !== "admin") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { type } = body;

    if (type === "filter-options") {
      // Get filter options - simplified implementation
      // TODO: Implement these methods in services if needed
      const actions = ["CREATE", "UPDATE", "DELETE", "LOGIN", "LOGOUT"];
      const resources = ["USER", "PRODUCT", "ORDER", "CATEGORY", "BRAND"];
      const admins: any[] = []; // TODO: Get from admin service

      return NextResponse.json({
        actions,
        resources,
        admins,
      });
    }

    return NextResponse.json(
      { error: "Invalid request type" },
      { status: 400 }
    );
  } catch (error) {
    console.error("Error processing audit logs request:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
