import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { adminAuthOptions } from "@/lib/admin-auth";
import {
  getNotificationPreferences,
  updateNotificationPreferences,
  NotificationPreferences,
} from "@/lib/notification-preferences";
import { z } from "zod";

// Validation schema for preferences update
const updatePreferencesSchema = z.object({
  adminId: z.string().min(1, "Admin ID is required"),
  preferences: z.object({
    emailNotifications: z.object({
      enabled: z.boolean(),
      types: z.object({
        INFO: z.boolean(),
        SUCCESS: z.boolean(),
        WARNING: z.boolean(),
        ERROR: z.boolean(),
        SYSTEM: z.boolean(),
      }),
      priorities: z.object({
        LOW: z.boolean(),
        NORMAL: z.boolean(),
        HIGH: z.boolean(),
        URGENT: z.boolean(),
      }),
      frequency: z.enum(["immediate", "hourly", "daily", "weekly"]),
      quietHours: z.object({
        enabled: z.boolean(),
        start: z.string(),
        end: z.string(),
        timezone: z.string(),
      }),
    }),
    browserNotifications: z.object({
      enabled: z.boolean(),
      types: z.object({
        INFO: z.boolean(),
        SUCCESS: z.boolean(),
        WARNING: z.boolean(),
        ERROR: z.boolean(),
        SYSTEM: z.boolean(),
      }),
      priorities: z.object({
        LOW: z.boolean(),
        NORMAL: z.boolean(),
        HIGH: z.boolean(),
        URGENT: z.boolean(),
      }),
    }),
    autoMarkAsRead: z.object({
      enabled: z.boolean(),
      afterDays: z.number().min(1).max(365),
    }),
  }).partial(),
});

export async function GET(request: NextRequest) {
  try {
    // Check admin authentication
    const session = await getServerSession(adminAuthOptions);
    if (!session?.user || session.user.type !== "admin") {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const url = new URL(request.url);
    const adminId = url.searchParams.get("adminId");

    // If no adminId provided, use current user's ID
    const targetAdminId = adminId || session.user.id;

    // Only allow users to access their own preferences unless they're ADMIN
    if (targetAdminId !== session.user.id && session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Forbidden" },
        { status: 403 }
      );
    }

    const preferences = await getNotificationPreferences(targetAdminId);

    return NextResponse.json({
      preferences,
      adminId: targetAdminId,
    });
  } catch (error) {
    console.error("Error fetching notification preferences:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check admin authentication
    const session = await getServerSession(adminAuthOptions);
    if (!session?.user || session.user.type !== "admin") {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const validatedData = updatePreferencesSchema.parse(body);

    const { adminId, preferences } = validatedData;

    // Only allow users to update their own preferences unless they're ADMIN
    if (adminId !== session.user.id && session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Forbidden" },
        { status: 403 }
      );
    }

    const updatedPreferences = await updateNotificationPreferences(
      adminId,
      preferences
    );

    return NextResponse.json({
      message: "Preferences updated successfully",
      preferences: updatedPreferences,
      adminId,
    });
  } catch (error) {
    console.error("Error updating notification preferences:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid preferences data", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
