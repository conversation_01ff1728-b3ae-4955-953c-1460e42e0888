import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { adminAuthOptions } from "@/lib/admin-auth";
import { notificationRulesEngine } from "@/lib/notification-rules";
import { NotificationRule } from "@/types/notification";
import { z } from "zod";

// Validation schema for creating/updating rules
const notificationRuleSchema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string().min(1, "Description is required"),
  eventType: z.string().min(1, "Event type is required"),
  conditions: z.record(z.any()).default({}),
  notificationTemplate: z.object({
    title: z.string().min(1, "Title is required"),
    message: z.string().min(1, "Message is required"),
    type: z.enum(["INFO", "SUCCESS", "WARNING", "ERROR", "SYSTEM"]),
    priority: z.enum(["LOW", "NORMAL", "HIGH", "URGENT"]),
    targetType: z.enum([
      "ALL_ADMINS",
      "SPECIFIC_ADMIN",
      "ROLE_ADMIN",
      "ROLE_MODERATOR",
    ]),
    targetId: z.string().optional(),
    actionUrl: z.string().optional(),
  }),
  isActive: z.boolean().default(true),
});

// Validation schema for testing rules
const testRuleSchema = z.object({
  ruleId: z.string().optional(),
  eventType: z.string().min(1, "Event type is required"),
  eventData: z.record(z.any()).default({}),
});

export async function GET(request: NextRequest) {
  try {
    // Check admin authentication
    const session = await getServerSession(adminAuthOptions);
    if (!session?.user || session.user.type !== "admin") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const url = new URL(request.url);
    const eventType = url.searchParams.get("eventType");

    // Initialize rules engine
    await notificationRulesEngine.initialize();

    let rules: NotificationRule[];
    if (eventType) {
      rules = notificationRulesEngine.getRulesForEvent(eventType);
    } else {
      rules = notificationRulesEngine.getAllRules();
    }

    // Get available event types
    const eventTypes = [
      "product.stock.low",
      "product.stock.empty",
      "order.created",
      "order.status.changed",
      "payment.failed",
      "user.registered",
      "system.error",
    ];

    return NextResponse.json({
      rules,
      eventTypes,
      totalRules: rules.length,
      activeRules: rules.filter((r) => r.isActive).length,
    });
  } catch (error) {
    console.error("Error fetching notification rules:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check admin authentication
    const session = await getServerSession(adminAuthOptions);
    if (!session?.user || session.user.type !== "admin") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Only ADMIN role can create rules
    if (session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Only admins can create notification rules" },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { action } = body;

    if (action === "test") {
      return handleTestRule(body);
    } else if (action === "create") {
      return handleCreateRule(body);
    } else {
      return NextResponse.json(
        { error: "Invalid action. Use 'create' or 'test'" },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error("Error processing notification rules request:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

async function handleCreateRule(body: any) {
  try {
    const validatedData = notificationRuleSchema.parse(body);

    // Initialize rules engine
    await notificationRulesEngine.initialize();

    // Add the new rule
    const newRule = await notificationRulesEngine.addRule(validatedData);

    return NextResponse.json(
      {
        message: "Notification rule created successfully",
        rule: newRule,
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error creating notification rule:", error);
    throw error;
  }
}

async function handleTestRule(body: any) {
  try {
    const { ruleId, eventType, eventData } = testRuleSchema.parse(body);

    // Initialize rules engine
    await notificationRulesEngine.initialize();

    if (ruleId) {
      // Test specific rule
      const rules = notificationRulesEngine.getAllRules();
      const rule = rules.find((r) => r.id === ruleId);

      if (!rule) {
        return NextResponse.json({ error: "Rule not found" }, { status: 404 });
      }

      // Simulate the event
      await notificationRulesEngine.processEvent({
        type: eventType,
        data: eventData,
        timestamp: new Date(),
      });

      return NextResponse.json({
        message: "Test notification triggered successfully",
        ruleId,
        eventType,
        eventData,
      });
    } else {
      // Test all rules for event type
      await notificationRulesEngine.processEvent({
        type: eventType,
        data: eventData,
        timestamp: new Date(),
      });

      const matchingRules = notificationRulesEngine.getRulesForEvent(eventType);

      return NextResponse.json({
        message: "Test event processed successfully",
        eventType,
        eventData,
        matchingRules: matchingRules.length,
        rules: matchingRules.map((r) => ({
          id: r.id,
          name: r.name,
          isActive: r.isActive,
        })),
      });
    }
  } catch (error) {
    console.error("Error testing notification rule:", error);
    throw error;
  }
}

// PUT endpoint for updating rules
export async function PUT(request: NextRequest) {
  try {
    // Check admin authentication
    const session = await getServerSession(adminAuthOptions);
    if (!session?.user || session.user.type !== "admin") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Only ADMIN role can update rules
    if (session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Only admins can update notification rules" },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { ruleId, ...updateData } = body;

    if (!ruleId) {
      return NextResponse.json(
        { error: "Rule ID is required" },
        { status: 400 }
      );
    }

    // For now, we'll just return a success message
    // In a real implementation, you'd update the rule in the database
    return NextResponse.json({
      message: "Rule update functionality not implemented yet",
      ruleId,
      updateData,
    });
  } catch (error) {
    console.error("Error updating notification rule:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
