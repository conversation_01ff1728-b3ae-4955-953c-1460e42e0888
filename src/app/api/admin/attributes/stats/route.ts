import { NextRequest, NextResponse } from "next/server";
import { jwtVerify } from "jose";
import { container, SERVICE_IDENTIFIERS } from "../../../di-container";
import type { AttributeRepository } from "../../../repositories/attribute.repository";
import { initializeSystem } from "../../../initialize";

// Helper function to verify admin authentication
async function verifyAdminAuth(request: NextRequest) {
  try {
    const adminToken = request.cookies.get("admin-session")?.value;

    if (!adminToken) {
      return null;
    }

    const secret = new TextEncoder().encode(
      process.env.NEXTAUTH_SECRET || "fallback-secret"
    );

    const { payload } = await jwtVerify(adminToken, secret);

    if (payload.role !== "ADMIN" && payload.role !== "MODERATOR") {
      return null;
    }

    return {
      id: payload.id as string,
      role: payload.role as string,
      type: "admin" as const,
    };
  } catch {
    return null;
  }
}

// GET /api/admin/attributes/stats - <PERSON><PERSON><PERSON> thống kê chi tiết về attributes
export async function GET(request: NextRequest) {
  try {
    const adminUser = await verifyAdminAuth(request);

    if (!adminUser) {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    // Initialize system and get repository
    initializeSystem();
    const attributeRepository = container.resolve<AttributeRepository>(
      SERVICE_IDENTIFIERS.ATTRIBUTE_REPOSITORY
    );

    // Get comprehensive stats from repository first
    const attributeStats = await attributeRepository.getAttributeStats();

    // Get all attributes with values for detailed analysis
    const allAttributes = await attributeRepository.findMany({
      include: {
        values: true,
      },
    });

    // Get basic counts
    const [
      totalAttributes,
      requiredAttributes,
      filterableAttributes,
      attributesWithValues,
      attributesInUse,
    ] = await Promise.all([
      attributeRepository.count(),
      attributeRepository.count({ isRequired: true }),
      attributeRepository.count({ isFilterable: true }),
      attributeRepository.count({
        values: {
          some: {},
        },
      }),
      attributeRepository.count({
        products: {
          some: {},
        },
      }),
    ]);

    // Use values from attributeStats
    const totalValues = attributeStats.totalValues;
    const totalProductAttributes = 0; // This would need a separate method

    // Get attributes by type from stats
    const attributesByType = attributeStats.byType.map((stat) => ({
      type: stat.type,
      _count: { _all: stat.count },
    }));

    // Get top attributes by usage - use allAttributes data
    const topAttributesByUsage = allAttributes
      .map((attr: any) => ({
        ...attr,
        productCount: 0, // Would need separate query for product usage
        valueCount: attr.values.length,
      }))
      .sort((a: any, b: any) => b.valueCount - a.valueCount)
      .slice(0, 10);

    // Get attributes with most values - use allAttributes data
    const attributesWithMostValues = allAttributes
      .map((attr: any) => ({
        ...attr,
        valueCount: attr.values.length,
      }))
      .sort((a: any, b: any) => b.valueCount - a.valueCount)
      .slice(0, 5);

    // Get recent attributes - use allAttributes data
    const recentAttributes = allAttributes
      .sort(
        (a, b) =>
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      )
      .slice(0, 5)
      .map((attr: any) => ({
        ...attr,
        valueCount: attr.values.length,
        productCount: 0, // Would need separate query
      }));

    // Get value distribution - use existing allAttributes data
    const valueDistribution = allAttributes
      .map((attr: any) => ({
        attributeId: attr.id,
        _count: { _all: attr.values.length },
      }))
      .sort((a: any, b: any) => b._count._all - a._count._all)
      .slice(0, 10);

    // Get attribute names for value distribution
    const attributeIds = valueDistribution.map((item: any) => item.attributeId);
    const attributeNames = await attributeRepository.findMany({
      where: {
        id: { in: attributeIds },
      },
      select: {
        id: true,
        name: true,
      },
    });

    const valueDistributionWithNames = valueDistribution.map((item) => {
      const attribute = attributeNames.find(
        (attr) => attr.id === item.attributeId
      );
      return {
        attributeId: item.attributeId,
        attributeName: attribute?.name || "Unknown",
        valueCount: item._count._all,
      };
    });

    // Get monthly growth data (last 12 months)
    const twelveMonthsAgo = new Date();
    twelveMonthsAgo.setMonth(twelveMonthsAgo.getMonth() - 12);

    // Get monthly growth - simplified approach
    const attributesForGrowth = await attributeRepository.findMany({
      where: {
        createdAt: {
          gte: twelveMonthsAgo,
        },
      },
      select: {
        createdAt: true,
      },
    });

    // Group by month manually
    const monthlyGrowth = attributesForGrowth.reduce((acc: any[], attr) => {
      const monthKey = attr.createdAt.toISOString().substring(0, 7); // YYYY-MM
      const existing = acc.find((item) => item.month === monthKey);
      if (existing) {
        existing._count._all++;
      } else {
        acc.push({
          createdAt: attr.createdAt,
          month: monthKey,
          _count: { _all: 1 },
        });
      }
      return acc;
    }, []);

    // Process monthly growth data
    const monthlyGrowthProcessed = monthlyGrowth.reduce(
      (acc, item) => {
        const month = item.createdAt.toISOString().slice(0, 7); // YYYY-MM format
        acc[month] = (acc[month] || 0) + item._count._all;
        return acc;
      },
      {} as Record<string, number>
    );

    // Get unused attributes - simplified approach
    const unusedAttributes = allAttributes
      .filter((attr: any) => attr.values.length === 0) // Attributes with no values as proxy for unused
      .sort(
        (a: any, b: any) =>
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      )
      .slice(0, 10)
      .map((attr: any) => ({
        ...attr,
        valueCount: attr.values.length,
      }));

    // Calculate percentages
    const stats = {
      overview: {
        total: totalAttributes,
        required: requiredAttributes,
        filterable: filterableAttributes,
        withValues: attributesWithValues,
        inUse: attributesInUse,
        unused: totalAttributes - attributesInUse,
        totalValues,
        totalProductAttributes,
      },
      percentages: {
        requiredPercentage:
          totalAttributes > 0
            ? (requiredAttributes / totalAttributes) * 100
            : 0,
        filterablePercentage:
          totalAttributes > 0
            ? (filterableAttributes / totalAttributes) * 100
            : 0,
        withValuesPercentage:
          totalAttributes > 0
            ? (attributesWithValues / totalAttributes) * 100
            : 0,
        inUsePercentage:
          totalAttributes > 0 ? (attributesInUse / totalAttributes) * 100 : 0,
      },
      byType: attributesByType.reduce(
        (acc, item) => {
          acc[item.type] = item._count._all;
          return acc;
        },
        {} as Record<string, number>
      ),
      topByUsage: topAttributesByUsage.map((attr: any) => ({
        id: attr.id,
        name: attr.name,
        type: attr.type,
        productCount: attr.productCount,
        valueCount: attr.valueCount,
        isRequired: attr.isRequired,
        isFilterable: attr.isFilterable,
      })),
      mostValues: attributesWithMostValues.map((attr: any) => ({
        id: attr.id,
        name: attr.name,
        type: attr.type,
        valueCount: attr.valueCount,
      })),
      recent: recentAttributes.map((attr: any) => ({
        id: attr.id,
        name: attr.name,
        type: attr.type,
        createdAt: attr.createdAt,
        valueCount: attr.valueCount,
        productCount: attr.productCount,
      })),
      valueDistribution: valueDistributionWithNames,
      monthlyGrowth: monthlyGrowthProcessed,
      unused: unusedAttributes.map((attr: any) => ({
        id: attr.id,
        name: attr.name,
        type: attr.type,
        createdAt: attr.createdAt,
        valueCount: attr.valueCount,
      })),
    };

    return NextResponse.json(stats);
  } catch (error) {
    console.error("Error fetching attribute statistics:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi tải thống kê" },
      { status: 500 }
    );
  }
}
