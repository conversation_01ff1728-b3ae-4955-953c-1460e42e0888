import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { adminAuthOptions } from "@/lib/admin-auth";
import { z } from "zod";
import { container, SERVICE_IDENTIFIERS } from "../../../../../di-container";
import type { AttributeRepository } from "../../../../../repositories/attribute.repository";
import { initializeSystem } from "../../../../../initialize";

// Validation schema for reordering values
const reorderValuesSchema = z.object({
  values: z.array(
    z.object({
      id: z.string(),
      sortOrder: z.number(),
    })
  ),
});

// PUT /api/admin/attributes/[id]/values/reorder - Sắp xếp lại thứ tự values
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    // Initialize system and get repository
    initializeSystem();
    const attributeRepository = container.resolve<AttributeRepository>(
      SERVICE_IDENTIFIERS.ATTRIBUTE_REPOSITORY
    );

    const body = await request.json();
    const validatedData = reorderValuesSchema.parse(body);

    // Check if attribute exists
    const attribute = await attributeRepository.findById(params.id);

    if (!attribute) {
      return NextResponse.json(
        { error: "Không tìm thấy thuộc tính" },
        { status: 404 }
      );
    }

    // Verify all values belong to this attribute
    const valueIds = validatedData.values.map((v) => v.id);
    const allAttributeValues = await attributeRepository.getAttributeValues(
      params.id
    );
    const existingValues = allAttributeValues.filter((v) =>
      valueIds.includes(v.id)
    );

    if (existingValues.length !== valueIds.length) {
      return NextResponse.json(
        { error: "Một số giá trị không thuộc về thuộc tính này" },
        { status: 400 }
      );
    }

    // Update sort orders (replace transaction with individual updates)
    for (const value of validatedData.values) {
      await attributeRepository.updateAttributeValue(value.id, {
        sortOrder: value.sortOrder,
      });
    }

    // Return updated values
    const updatedValues = await attributeRepository.getAttributeValues(
      params.id
    );

    return NextResponse.json({
      message: "Cập nhật thứ tự thành công",
      data: updatedValues,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Dữ liệu không hợp lệ", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Error reordering attribute values:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi sắp xếp lại thứ tự" },
      { status: 500 }
    );
  }
}
