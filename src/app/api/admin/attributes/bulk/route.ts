import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { adminAuthOptions } from "@/lib/admin-auth";
import { z } from "zod";
import { container, SERVICE_IDENTIFIERS } from "../../../di-container";
import type { AttributeRepository } from "../../../repositories/attribute.repository";
import { initializeSystem } from "../../../initialize";

// Validation schemas for bulk operations
const bulkDeleteSchema = z.object({
  ids: z.array(z.string()).min(1, "<PERSON><PERSON><PERSON> chọn ít nhất một thuộc tính"),
});

const bulkUpdateSchema = z.object({
  ids: z.array(z.string()).min(1, "Phải chọn ít nhất một thuộc tính"),
  data: z.object({
    isRequired: z.boolean().optional(),
    isFilterable: z.boolean().optional(),
    sortOrder: z.number().optional(),
  }),
});

const bulkReorderSchema = z.object({
  attributes: z.array(
    z.object({
      id: z.string(),
      sortOrder: z.number(),
    })
  ),
});

// POST /api/admin/attributes/bulk - Bulk operations for attributes
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { action } = body;

    switch (action) {
      case "delete":
        return await handleBulkDelete(body);
      case "update":
        return await handleBulkUpdate(body);
      case "reorder":
        return await handleBulkReorder(body);
      default:
        return NextResponse.json(
          { error: "Hành động không hợp lệ" },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error("Error in bulk operation:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi thực hiện thao tác" },
      { status: 500 }
    );
  }
}

async function handleBulkDelete(body: any) {
  const validatedData = bulkDeleteSchema.parse(body);

  // Initialize system and get repository
  initializeSystem();
  const attributeRepository = container.resolve<AttributeRepository>(
    SERVICE_IDENTIFIERS.ATTRIBUTE_REPOSITORY
  );

  // Check if any attributes are being used by products
  const attributesInUse = await attributeRepository.findMany({
    where: {
      id: { in: validatedData.ids },
    },
  });

  // Check which attributes are being used
  const attributesWithProducts = [];
  for (const attr of attributesInUse) {
    const isUsed = await attributeRepository.isAttributeUsed(attr.id);
    if (isUsed) {
      attributesWithProducts.push(attr);
    }
  }

  if (attributesWithProducts.length > 0) {
    return NextResponse.json(
      {
        error: `Không thể xóa ${attributesWithProducts.length} thuộc tính vì đang được sử dụng bởi sản phẩm`,
        attributesInUse: attributesWithProducts.map((attr) => ({
          id: attr.id,
          name: attr.name,
        })),
      },
      { status: 400 }
    );
  }

  // Delete attributes (values will be deleted automatically due to cascade)
  let deletedCount = 0;
  for (const id of validatedData.ids) {
    try {
      await attributeRepository.delete(id);
      deletedCount++;
    } catch (error) {
      console.error(`Failed to delete attribute ${id}:`, error);
    }
  }

  return NextResponse.json({
    message: `Đã xóa ${deletedCount} thuộc tính thành công`,
    deletedCount: deletedCount,
  });
}

async function handleBulkUpdate(body: any) {
  const validatedData = bulkUpdateSchema.parse(body);

  // Initialize system and get repository
  initializeSystem();
  const attributeRepository = container.resolve<AttributeRepository>(
    SERVICE_IDENTIFIERS.ATTRIBUTE_REPOSITORY
  );

  // Check if attributes exist
  const existingAttributes = await attributeRepository.findMany({
    where: {
      id: { in: validatedData.ids },
    },
  });

  if (existingAttributes.length !== validatedData.ids.length) {
    return NextResponse.json(
      { error: "Một số thuộc tính không tồn tại" },
      { status: 404 }
    );
  }

  // Update attributes
  let updatedCount = 0;
  for (const id of validatedData.ids) {
    try {
      await attributeRepository.update(id, validatedData.data);
      updatedCount++;
    } catch (error) {
      console.error(`Failed to update attribute ${id}:`, error);
    }
  }

  return NextResponse.json({
    message: `Đã cập nhật ${updatedCount} thuộc tính thành công`,
    updatedCount: updatedCount,
  });
}

async function handleBulkReorder(body: any) {
  const validatedData = bulkReorderSchema.parse(body);

  // Initialize system and get repository
  initializeSystem();
  const attributeRepository = container.resolve<AttributeRepository>(
    SERVICE_IDENTIFIERS.ATTRIBUTE_REPOSITORY
  );

  // Verify all attributes exist
  const attributeIds = validatedData.attributes.map((attr) => attr.id);
  const existingAttributes = await attributeRepository.findMany({
    where: {
      id: { in: attributeIds },
    },
  });

  if (existingAttributes.length !== attributeIds.length) {
    return NextResponse.json(
      { error: "Một số thuộc tính không tồn tại" },
      { status: 404 }
    );
  }

  // Update sort orders
  for (const attr of validatedData.attributes) {
    await attributeRepository.update(attr.id, { sortOrder: attr.sortOrder });
  }

  return NextResponse.json({
    message: "Cập nhật thứ tự thuộc tính thành công",
  });
}

// GET /api/admin/attributes/bulk - Get bulk operation status or statistics
export async function GET(_request: NextRequest) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    // Initialize system and get repository
    initializeSystem();
    const attributeRepository = container.resolve<AttributeRepository>(
      SERVICE_IDENTIFIERS.ATTRIBUTE_REPOSITORY
    );

    // Get bulk statistics - use repository method
    const attributeStats = await attributeRepository.getAttributeStats();
    const stats = attributeStats.byType.map((stat) => ({
      type: stat.type,
      _count: { _all: stat.count },
    }));

    const totalAttributes = await attributeRepository.count();
    const attributesWithValues = await attributeRepository.count({
      values: {
        some: {},
      },
    });
    const attributesInUse = await attributeRepository.count({
      products: {
        some: {},
      },
    });

    return NextResponse.json({
      total: totalAttributes,
      withValues: attributesWithValues,
      inUse: attributesInUse,
      byType: stats.reduce(
        (acc: Record<string, number>, stat: any) => {
          acc[stat.type] = stat._count._all;
          return acc;
        },
        {} as Record<string, number>
      ),
    });
  } catch (error) {
    console.error("Error fetching bulk statistics:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi tải thống kê" },
      { status: 500 }
    );
  }
}
