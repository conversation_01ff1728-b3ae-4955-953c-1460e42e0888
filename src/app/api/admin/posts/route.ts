import { NextRequest, NextResponse } from "next/server";
import { PostController } from "@/lib/admin/controllers/PostController";
import { verifyAdminToken } from "@/lib/admin-auth";

const postController = new PostController();

// GET /api/admin/posts - Get all posts with pagination and filters
export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminToken = await verifyAdminToken(request);
    if (!adminToken) {
      return NextResponse.json(
        { success: false, error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);

    // Extract query parameters
    const params = {
      page: parseInt(searchParams.get("page") || "1"),
      limit: parseInt(searchParams.get("limit") || "20"),
      search: searchParams.get("search") || undefined,
      status: searchParams.get("status") as
        | "DRAFT"
        | "PUBLISHED"
        | "ARCHIVED"
        | undefined,
      featured:
        searchParams.get("featured") === "true"
          ? true
          : searchParams.get("featured") === "false"
            ? false
            : undefined,
      categoryId: searchParams.get("categoryId") || undefined,
      authorId: searchParams.get("authorId") || undefined,
      sortBy: searchParams.get("sortBy") || "createdAt",
      sortOrder: (searchParams.get("sortOrder") as "asc" | "desc") || "desc",
    };

    const result = await postController.list(params);
    return NextResponse.json(result);
  } catch (error) {
    console.error("Get posts error:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi tải danh sách bài viết" },
      { status: 500 }
    );
  }
}

// POST /api/admin/posts - Create new post
export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminToken = await verifyAdminToken(request);
    if (!adminToken) {
      return NextResponse.json(
        { success: false, error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    const body = await request.json();

    // Add author ID from token
    const postData = {
      ...body,
      authorId: adminToken.id,
    };

    const result = await postController.create(postData);

    if (result.success) {
      return NextResponse.json(result, { status: 201 });
    } else {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error("Create post error:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi tạo bài viết" },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/posts - Bulk delete posts
export async function DELETE(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminToken = await verifyAdminToken(request);
    if (!adminToken || adminToken.role !== "ADMIN") {
      return NextResponse.json(
        { success: false, error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get("action");
    const ids = searchParams.get("ids")?.split(",") || [];

    if (action === "bulk-delete") {
      const result = await postController.bulkDelete(ids);

      if (result.success) {
        return NextResponse.json(result);
      } else {
        return NextResponse.json(
          { success: false, error: result.error },
          { status: 400 }
        );
      }
    } else if (action === "bulk-status") {
      const status = searchParams.get("status") as
        | "DRAFT"
        | "PUBLISHED"
        | "ARCHIVED";

      if (!status || !["DRAFT", "PUBLISHED", "ARCHIVED"].includes(status)) {
        return NextResponse.json(
          { success: false, error: "Trạng thái không hợp lệ" },
          { status: 400 }
        );
      }

      const result = await postController.bulkUpdateStatus(ids, status);

      if (result.success) {
        return NextResponse.json(result);
      } else {
        return NextResponse.json(
          { success: false, error: result.error },
          { status: 400 }
        );
      }
    } else {
      return NextResponse.json(
        { success: false, error: "Action không hợp lệ" },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error("Bulk operation error:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi thực hiện thao tác" },
      { status: 500 }
    );
  }
}

// PATCH /api/admin/posts - Bulk update posts
export async function PATCH(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminToken = await verifyAdminToken(request);
    if (!adminToken) {
      return NextResponse.json(
        { success: false, error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get("action");

    if (action === "bulk-status") {
      const body = await request.json();
      const { ids, status } = body;

      if (!ids || !Array.isArray(ids) || ids.length === 0) {
        return NextResponse.json(
          { success: false, error: "Không có bài viết nào được chọn" },
          { status: 400 }
        );
      }

      if (!status || !["DRAFT", "PUBLISHED", "ARCHIVED"].includes(status)) {
        return NextResponse.json(
          { success: false, error: "Trạng thái không hợp lệ" },
          { status: 400 }
        );
      }

      const result = await postController.bulkUpdateStatus(ids, status);

      if (result.success) {
        return NextResponse.json(result);
      } else {
        return NextResponse.json(
          { success: false, error: result.error },
          { status: 400 }
        );
      }
    } else {
      return NextResponse.json(
        { success: false, error: "Action không hợp lệ" },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error("Bulk update error:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi cập nhật" },
      { status: 500 }
    );
  }
}
