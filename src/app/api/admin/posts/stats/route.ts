import { NextRequest, NextResponse } from "next/server";
import { PostController } from "@/lib/admin/controllers/PostController";
import { verifyAdminToken } from "@/lib/admin-auth";

const postController = new PostController();

// GET /api/admin/posts/stats - Get posts statistics
export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminToken = await verifyAdminToken(request);
    if (!adminToken) {
      return NextResponse.json(
        { success: false, error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    const result = await postController.getStats();

    if (result.success) {
      return NextResponse.json(result);
    } else {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error("Get posts stats error:", error);
    return NextResponse.json(
      { success: false, error: "<PERSON><PERSON> lỗi xảy ra khi lấy thống kê bài viết" },
      { status: 500 }
    );
  }
}
