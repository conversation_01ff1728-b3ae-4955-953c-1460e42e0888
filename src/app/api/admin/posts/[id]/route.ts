import { NextRequest, NextResponse } from "next/server";
import { PostController } from "@/lib/admin/controllers/PostController";
import { verifyAdminToken } from "@/lib/admin-auth";

const postController = new PostController();

// GET /api/admin/posts/[id] - Get single post
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify admin authentication
    const adminToken = await verifyAdminToken(request);
    if (!adminToken) {
      return NextResponse.json(
        { success: false, error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    const result = await postController.get(params.id);

    if (result.success) {
      return NextResponse.json(result);
    } else {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 404 }
      );
    }
  } catch (error) {
    console.error("Get post error:", error);
    return NextResponse.json(
      { success: false, error: "C<PERSON> lỗi xảy ra khi tải bài viết" },
      { status: 500 }
    );
  }
}

// PUT /api/admin/posts/[id] - Update post
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify admin authentication
    const adminToken = await verifyAdminToken(request);
    if (!adminToken) {
      return NextResponse.json(
        { success: false, error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const result = await postController.update(params.id, body);

    if (result.success) {
      return NextResponse.json(result);
    } else {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error("Update post error:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi cập nhật bài viết" },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/posts/[id] - Delete single post
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify admin authentication
    const adminToken = await verifyAdminToken(request);
    if (!adminToken || adminToken.role !== "ADMIN") {
      return NextResponse.json(
        { success: false, error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    const result = await postController.delete(params.id);

    if (result.success) {
      return NextResponse.json(result);
    } else {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error("Delete post error:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi xóa bài viết" },
      { status: 500 }
    );
  }
}

// PATCH /api/admin/posts/[id] - Partial update (e.g., toggle featured)
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify admin authentication
    const adminToken = await verifyAdminToken(request);
    if (!adminToken) {
      return NextResponse.json(
        { success: false, error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get("action");

    if (action === "toggle-featured") {
      const result = await postController.toggleFeatured(params.id);
      
      if (result.success) {
        return NextResponse.json(result);
      } else {
        return NextResponse.json(
          { success: false, error: result.error },
          { status: 400 }
        );
      }
    } else {
      // Regular partial update
      const body = await request.json();
      const result = await postController.update(params.id, body);

      if (result.success) {
        return NextResponse.json(result);
      } else {
        return NextResponse.json(
          { success: false, error: result.error },
          { status: 400 }
        );
      }
    }
  } catch (error) {
    console.error("Patch post error:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi cập nhật bài viết" },
      { status: 500 }
    );
  }
}
