import { NextRequest, NextResponse } from "next/server";
import { container } from "@/app/api/di-container";
import { ADMIN_USER_SERVICE } from "@/app/api/services/service-identifiers";
import type { AdminUserService } from "@/app/api/services/admin-user.service";
import { z } from "zod";
import { jwtVerify } from "jose";
import { logCrudOperation } from "@/lib/audit-logger";

// Helper function to verify admin JWT token
async function verifyAdminToken(request: NextRequest) {
  try {
    const adminToken = request.cookies.get("admin-session")?.value;
    if (!adminToken) return null;

    const secret = new TextEncoder().encode(
      process.env.NEXTAUTH_SECRET || "fallback-secret"
    );
    const { payload } = await jwtVerify(adminToken, secret);
    return payload;
  } catch {
    return null;
  }
}

const createAdminSchema = z.object({
  email: z.string().email("<PERSON>ail không hợp lệ"),
  name: z.string().min(2, "<PERSON>ê<PERSON> phải có ít nhất 2 ký tự"),
  password: z.string().min(6, "<PERSON>ật khẩu phải có ít nhất 6 ký tự"),
  role: z.enum(["ADMIN", "MODERATOR"]),
  phone: z.string().optional(),
  department: z.string().optional(),
  permissions: z.record(z.boolean()).optional(),
});

// GET /api/admin/admins - Get list of admin users
export async function GET(request: NextRequest) {
  try {
    const adminToken = await verifyAdminToken(request);

    if (!adminToken || adminToken.type !== "admin") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    // Only ADMIN can view all admin users
    if (adminToken.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Chỉ Admin mới có quyền xem danh sách quản trị viên" },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const search = searchParams.get("search");
    const role = searchParams.get("role");

    const adminUserService =
      container.resolve<AdminUserService>(ADMIN_USER_SERVICE);

    // Create admin user entity for request
    const adminUserEntity = {
      id: adminToken.id as string,
      email: adminToken.email as string,
      name: adminToken.name as string,
      role: adminToken.role as string,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    } as any;

    // Get admin users with pagination using service
    const result = await adminUserService.getAdminUsers(
      {
        page,
        limit,
        search: search || undefined,
        role: role as any,
        sortBy: "createdAt",
        sortOrder: "desc" as const,
      },
      adminUserEntity
    );

    return NextResponse.json({
      adminUsers: result.data,
      pagination: {
        page,
        limit,
        total: result.total,
        pages: Math.ceil(result.total / limit),
      },
    });
  } catch (error) {
    console.error("Get admin users error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi lấy danh sách quản trị viên" },
      { status: 500 }
    );
  }
}

// POST /api/admin/admins - Create new admin user
export async function POST(request: NextRequest) {
  try {
    const adminToken = await verifyAdminToken(request);

    if (!adminToken || adminToken.type !== "admin") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    // Only ADMIN can create new admin users
    if (adminToken.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Chỉ Admin mới có quyền tạo tài khoản quản trị viên" },
        { status: 403 }
      );
    }

    const body = await request.json();
    const data = createAdminSchema.parse(body);

    const adminUserService =
      container.resolve<AdminUserService>(ADMIN_USER_SERVICE);

    // Create admin user entity for request
    const adminUserEntity = {
      id: adminToken.id as string,
      email: adminToken.email as string,
      name: adminToken.name as string,
      role: adminToken.role as string,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    } as any;

    // Create admin user using service
    const adminUser = await adminUserService.createAdminUser(
      {
        name: data.name,
        email: data.email,
        password: data.password,
        role: data.role as any,
        permissions: data.permissions
          ? Object.keys(data.permissions).filter(
              (key) => data.permissions![key]
            )
          : undefined,
      },
      adminUserEntity
    );

    // Log audit trail
    await logCrudOperation(
      "CREATE",
      "ADMIN_USER",
      adminUser.id,
      adminToken.id as string,
      {
        newValues: {
          name: data.name,
          email: data.email,
          role: data.role,
          department: data.department,
        },
        description: `Tạo tài khoản admin mới: ${data.name} (${data.email})`,
        ipAddress:
          request.headers.get("x-forwarded-for") ||
          request.headers.get("x-real-ip") ||
          "unknown",
        userAgent: request.headers.get("user-agent") || "unknown",
      }
    );

    return NextResponse.json(
      {
        message: "Tạo tài khoản quản trị viên thành công",
        adminUser,
      },
      { status: 201 }
    );
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Dữ liệu không hợp lệ", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Create admin user error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi tạo tài khoản quản trị viên" },
      { status: 500 }
    );
  }
}
