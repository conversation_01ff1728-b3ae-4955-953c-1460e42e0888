import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { container } from "../../di-container";
import { INVENTORY_SERVICE } from "../../services/service-identifiers";
import { InventoryService } from "../../services/inventory.service";
import { initializeSystem } from "../../initialize";

// Validation schema for inventory update
const inventoryUpdateSchema = z.object({
  quantity: z.number().min(0, "Số lượng phải >= 0"),
  minStock: z.number().min(0, "<PERSON><PERSON><PERSON> tồn kho tối thiểu phải >= 0"),
  maxStock: z.number().min(0, "<PERSON><PERSON><PERSON> tồn kho tối đa phải >= 0").optional(),
  location: z.string().optional(),
});

// GET /api/admin/inventory - Get inventory entries with pagination and filters
export async function GET(request: NextRequest) {
  try {
    // Initialize system and get service
    initializeSystem();
    const inventoryService =
      container.resolve<InventoryService>(INVENTORY_SERVICE);

    // Create admin user entity
    const adminUser = {
      id: "admin",
      role: "admin",
      type: "admin",
    } as any;

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const search = searchParams.get("search") || "";
    const location = searchParams.get("location") || "";
    const lowStock = searchParams.get("lowStock") === "true";
    const outOfStock = searchParams.get("outOfStock") === "true";
    const brandId = searchParams.get("brandId") || "";
    const categoryId = searchParams.get("categoryId") || "";
    const _sortBy = searchParams.get("sortBy") || "createdAt";
    const _sortOrder = searchParams.get("sortOrder") || "desc";

    // Build where clause (simplified for service)
    const _where: any = {};

    // Filters will be handled by service

    // Use InventoryService to get inventory list
    const filters = {
      page,
      limit,
      search,
      location,
      brandId: brandId || undefined,
      categoryId: categoryId || undefined,
      lowStock,
      outOfStock,
    };

    const result = await inventoryService.getInventories(filters, adminUser);

    return NextResponse.json({
      success: true,
      data: result.data,
      pagination: {
        page: result.page,
        limit: result.limit,
        total: result.total,
        totalPages: result.totalPages,
      },
    });
  } catch (error) {
    console.error("Get inventory error:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi lấy danh sách kho hàng" },
      { status: 500 }
    );
  }
}

// POST /api/admin/inventory - Create inventory entry for product
export async function POST(request: NextRequest) {
  try {
    // Initialize system and get service
    initializeSystem();
    const inventoryService =
      container.resolve<InventoryService>(INVENTORY_SERVICE);

    // Create admin user entity
    const adminUser = {
      id: "admin",
      role: "admin",
      type: "admin",
    } as any;

    const body = await request.json();
    const { productId, ...inventoryData } = body;

    // Validate input
    const validatedData = inventoryUpdateSchema.parse(inventoryData);

    // Create inventory entry using service
    const inventoryEntry = await inventoryService.createInventory(
      {
        productId,
        ...validatedData,
      },
      adminUser
    );

    // Service handles all validation and creation

    return NextResponse.json({
      success: true,
      data: inventoryEntry,
      message: "Thông tin kho hàng đã được tạo thành công",
    });
  } catch (error) {
    console.error("Create inventory error:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: error.errors[0].message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi tạo thông tin kho hàng" },
      { status: 500 }
    );
  }
}
