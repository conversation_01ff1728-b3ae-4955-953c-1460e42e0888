import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
// Note: StockMovementService not implemented yet

// Validation schema for stock movement
const _stockMovementSchema = z.object({
  inventoryEntryId: z.string().min(1, "ID kho hàng là bắt buộc"),
  type: z.enum(["IN", "OUT", "TRANSFER", "ADJUSTMENT"], {
    errorMap: () => ({ message: "Loại giao dịch không hợp lệ" }),
  }),
  quantity: z.number().min(1, "Số lượng phải > 0"),
  reason: z.string().optional(),
  reference: z.string().optional(),
  notes: z.string().optional(),
  createdBy: z.string().optional(),
});

// GET /api/admin/inventory/movements - Get stock movements with pagination
export async function GET(_request: NextRequest) {
  try {
    // TODO: Implement StockMovementService
    return NextResponse.json({
      success: true,
      data: [],
      pagination: {
        page: 1,
        limit: 20,
        total: 0,
        totalPages: 0,
      },
      message: "StockMovementService not implemented yet",
    });
  } catch (error) {
    console.error("Get stock movements error:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi lấy lịch sử giao dịch kho" },
      { status: 500 }
    );
  }
}

// POST /api/admin/inventory/movements - Create stock movement
export async function POST(_request: NextRequest) {
  try {
    // TODO: Implement StockMovementService
    return NextResponse.json(
      {
        success: false,
        error: "StockMovementService not implemented yet",
      },
      { status: 501 }
    );
  } catch (error) {
    console.error("Create stock movement error:", error);
    return NextResponse.json(
      { success: false, error: "StockMovementService not implemented yet" },
      { status: 501 }
    );
  }
}
