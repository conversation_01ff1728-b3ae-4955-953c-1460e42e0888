import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { container } from "../../../di-container";
import { INVENTORY_SERVICE } from "../../../services/service-identifiers";
import { InventoryService } from "../../../services/inventory.service";
import { initializeSystem } from "../../../initialize";

// Validation schema for inventory update
const inventoryUpdateSchema = z.object({
  quantity: z.number().min(0, "Số lượng phải >= 0").optional(),
  minStock: z.number().min(0, "<PERSON><PERSON><PERSON> tồn kho tối thiểu phải >= 0").optional(),
  maxStock: z.number().min(0, "M<PERSON>c tồn kho tối đa phải >= 0").optional(),
  location: z.string().optional(),
});

// GET /api/admin/inventory/[id] - Get single inventory entry
export async function GET(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Initialize system and get repository
    initializeSystem();
    const inventoryService =
      container.resolve<InventoryService>(INVENTORY_SERVICE);

    // Create admin user entity
    const adminUser = {
      id: "admin",
      role: "admin",
      type: "admin",
    } as any;

    const inventoryEntry = await inventoryService.getInventoryById(
      params.id,
      adminUser
    );

    if (!inventoryEntry) {
      return NextResponse.json(
        { success: false, error: "Không tìm thấy thông tin kho hàng" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: inventoryEntry,
    });
  } catch (error) {
    console.error("Get inventory entry error:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi lấy thông tin kho hàng" },
      { status: 500 }
    );
  }
}

// PUT /api/admin/inventory/[id] - Update inventory entry
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Initialize system and get repository
    initializeSystem();
    const inventoryService =
      container.resolve<InventoryService>(INVENTORY_SERVICE);

    // Create admin user entity
    const adminUser = {
      id: "admin",
      role: "admin",
      type: "admin",
    } as any;

    const body = await request.json();

    // Validate input
    const validatedData = inventoryUpdateSchema.parse(body);

    // Check if inventory entry exists
    const existingEntry = await inventoryService.getInventoryById(
      params.id,
      adminUser
    );

    if (!existingEntry) {
      return NextResponse.json(
        { success: false, error: "Không tìm thấy thông tin kho hàng" },
        { status: 404 }
      );
    }

    // Calculate quantity change if quantity is being updated
    let _quantityChange = 0;
    const updateData: any = { ...validatedData };
    if (validatedData.quantity !== undefined) {
      _quantityChange = validatedData.quantity - existingEntry.quantity;
      updateData.available = validatedData.quantity - existingEntry.reserved;
    }

    // Update inventory entry
    const inventoryEntry = await inventoryService.updateInventory(
      params.id,
      updateData,
      adminUser
    );

    // Create stock movement if quantity changed (would need StockMovementRepository)
    // if (quantityChange !== 0) {
    //   await stockMovementRepository.create({
    //     inventoryEntryId: params.id,
    //     type: quantityChange > 0 ? "IN" : "OUT",
    //     quantity: Math.abs(quantityChange),
    //     reason: "Điều chỉnh kho hàng",
    //     notes: `Cập nhật số lượng từ ${existingEntry.quantity} thành ${validatedData.quantity}`,
    //   });
    // }

    return NextResponse.json({
      success: true,
      data: inventoryEntry,
      message: "Thông tin kho hàng đã được cập nhật thành công",
    });
  } catch (error) {
    console.error("Update inventory error:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: error.errors[0].message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: "Có lỗi xảy ra khi cập nhật thông tin kho hàng",
      },
      { status: 500 }
    );
  }
}
