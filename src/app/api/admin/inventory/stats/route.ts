import { NextRequest, NextResponse } from "next/server";
import { container, SERVICE_IDENTIFIERS } from "../../../di-container";
import { InventoryRepository } from "../../../repositories/inventory.repository";
import { initializeSystem } from "../../../initialize";

// GET /api/admin/inventory/stats - Get inventory statistics
export async function GET(_request: NextRequest) {
  try {
    // Initialize system and get repository
    initializeSystem();
    const inventoryRepository = container.resolve<InventoryRepository>(
      SERVICE_IDENTIFIERS.INVENTORY_REPOSITORY
    );

    // Get total products with inventory
    const totalProducts = await inventoryRepository.count();

    // Get total stock value - simplified
    // Note: Repository doesn't support include, so we'll use basic data
    const inventoryWithProducts = await inventoryRepository.findMany();

    const totalStock = inventoryWithProducts.reduce(
      (sum, entry) => sum + entry.quantity,
      0
    );
    // TODO: Get product prices to calculate total value
    const totalValue = 0; // Placeholder since we can't access product.price

    // Get low stock products (available <= minStock) - simplified
    const allInventory = await inventoryRepository.findMany();
    const lowStockProducts = allInventory.filter(
      (entry) => entry.available <= entry.minStock && entry.minStock > 0
    ).length;

    // Get out of stock products - simplified
    const outOfStockProducts = allInventory.filter(
      (entry) => entry.available <= 0
    ).length;

    // Get recent stock movements - simplified (would need StockMovementRepository)
    const recentMovements: any[] = [];

    // Get inventory alerts (low stock and out of stock) - simplified
    // Note: Repository doesn't support include, so we'll use basic data
    const lowStockEntries = await inventoryRepository.findMany();

    // Filter for low stock and out of stock
    const filteredEntries = lowStockEntries.filter(
      (entry) =>
        (entry.available <= entry.minStock && entry.minStock > 0) ||
        entry.available <= 0
    );

    const alerts = filteredEntries.slice(0, 20).map((entry) => ({
      id: entry.id,
      productId: entry.productId,
      product: { id: entry.productId, name: "Product Name", sku: "SKU" }, // Placeholder
      type: entry.available <= 0 ? "OUT_OF_STOCK" : "LOW_STOCK",
      message:
        entry.available <= 0
          ? `Sản phẩm đã hết hàng`
          : `Sản phẩm sắp hết hàng (còn ${entry.available}/${entry.minStock})`,
      threshold: entry.minStock,
      currentStock: entry.available,
      createdAt: new Date(),
    }));

    const stats = {
      totalProducts,
      totalStock,
      lowStockProducts,
      outOfStockProducts,
      totalValue,
      recentMovements,
      alerts,
    };

    return NextResponse.json({
      success: true,
      data: stats,
    });
  } catch (error) {
    console.error("Get inventory stats error:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi lấy thống kê kho hàng" },
      { status: 500 }
    );
  }
}
