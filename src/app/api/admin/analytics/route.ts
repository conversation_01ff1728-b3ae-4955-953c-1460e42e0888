import { NextRequest, NextResponse } from "next/server";
import { jwtVerify } from "jose";

// GET /api/admin/analytics - <PERSON><PERSON>y dữ liệu thống kê
export async function GET(request: NextRequest) {
  try {
    // Verify admin session
    const adminToken = request.cookies.get("admin-session")?.value;

    if (!adminToken) {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    try {
      const secret = new TextEncoder().encode(
        process.env.NEXTAUTH_SECRET || "fallback-secret"
      );
      const { payload } = await jwtVerify(adminToken, secret);

      // Check if user is admin or moderator (both can view analytics)
      if (
        !payload.role ||
        (payload.role !== "ADMIN" && payload.role !== "MODERATOR")
      ) {
        return NextResponse.json(
          { error: "<PERSON>hông có quyền truy cập" },
          { status: 403 }
        );
      }
    } catch {
      return NextResponse.json(
        { error: "Token không hợp lệ" },
        { status: 401 }
      );
    }

    // Mock analytics data for testing - TODO: Replace with real database queries
    const analyticsData = {
      overview: {
        totalRevenue: 15000000, // 15M VND
        totalOrders: 150,
        totalCustomers: 75,
        totalProducts: 50,
        revenueGrowth: 25.5,
        ordersGrowth: 15.2,
        customersGrowth: 18.7,
      },
      monthlyRevenue: [
        { month: "Jan 2024", revenue: 1200000, orders: 12 },
        { month: "Feb 2024", revenue: 1350000, orders: 15 },
        { month: "Mar 2024", revenue: 1100000, orders: 11 },
        { month: "Apr 2024", revenue: 1450000, orders: 16 },
        { month: "May 2024", revenue: 1600000, orders: 18 },
        { month: "Jun 2024", revenue: 1300000, orders: 14 },
        { month: "Jul 2024", revenue: 1750000, orders: 20 },
        { month: "Aug 2024", revenue: 1500000, orders: 17 },
        { month: "Sep 2024", revenue: 1650000, orders: 19 },
        { month: "Oct 2024", revenue: 1400000, orders: 15 },
        { month: "Nov 2024", revenue: 1800000, orders: 22 },
        { month: "Dec 2024", revenue: 1900000, orders: 25 },
      ],
      topProducts: [
        { id: "1", name: "iPhone 15 Pro", totalSold: 25, revenue: 6250000 },
        { id: "2", name: "Samsung Galaxy S24", totalSold: 20, revenue: 4800000 },
        { id: "3", name: "MacBook Air M3", totalSold: 15, revenue: 4500000 },
        { id: "4", name: "iPad Pro", totalSold: 18, revenue: 3600000 },
        { id: "5", name: "AirPods Pro", totalSold: 30, revenue: 2400000 },
      ],
      topCategories: [
        { id: "1", name: "Điện thoại", revenue: 8500000, productCount: 15 },
        { id: "2", name: "Laptop", revenue: 5200000, productCount: 8 },
        { id: "3", name: "Tablet", revenue: 3800000, productCount: 6 },
        { id: "4", name: "Phụ kiện", revenue: 2100000, productCount: 12 },
        { id: "5", name: "Đồng hồ", revenue: 1800000, productCount: 5 },
      ],
      ordersByStatus: [
        { status: "PENDING", count: 15, percentage: 10.0 },
        { status: "CONFIRMED", count: 20, percentage: 13.3 },
        { status: "PROCESSING", count: 25, percentage: 16.7 },
        { status: "SHIPPED", count: 30, percentage: 20.0 },
        { status: "DELIVERED", count: 50, percentage: 33.3 },
        { status: "CANCELLED", count: 10, percentage: 6.7 },
      ],
      customerStats: {
        newCustomers: 25,
        returningCustomers: 50,
        averageOrderValue: 750000,
      },
    };

    return NextResponse.json(analyticsData);
  } catch (error) {
    console.error("Analytics API error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi lấy dữ liệu thống kê" },
      { status: 500 }
    );
  }
}
