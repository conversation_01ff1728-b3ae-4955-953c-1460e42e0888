import { NextRequest, NextResponse } from "next/server";
import { container, SERVICE_IDENTIFIERS } from "@/app/api/di-container";
import type { OrderService } from "@/app/api/services/order.service";
import type { ProductService } from "@/app/api/services/product.service";
import type { UserService } from "@/app/api/services/user.service";
import type { AuditLogService } from "@/app/api/services/audit-log.service";
import { initializeSystem } from "@/app/api/initialize";
import { verifyAdminToken } from "@/lib/admin-auth";

export async function GET(request: NextRequest) {
  try {
    const adminToken = await verifyAdminToken(request);
    if (!adminToken) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const range = searchParams.get("range") || "30d";

    // Calculate date ranges
    const now = new Date();
    const getDaysAgo = (days: number) => {
      const date = new Date(now);
      date.setDate(date.getDate() - days);
      return date;
    };

    let _startDate: Date;
    let _previousStartDate: Date;

    switch (range) {
      case "7d":
        _startDate = getDaysAgo(7);
        _previousStartDate = getDaysAgo(14);
        break;
      case "90d":
        _startDate = getDaysAgo(90);
        _previousStartDate = getDaysAgo(180);
        break;
      case "1y":
        _startDate = getDaysAgo(365);
        _previousStartDate = getDaysAgo(730);
        break;
      default: // 30d
        _startDate = getDaysAgo(30);
        _previousStartDate = getDaysAgo(60);
        break;
    }

    // Initialize system and resolve services
    initializeSystem();
    const _orderService = container.resolve<OrderService>(
      SERVICE_IDENTIFIERS.ORDER_SERVICE
    );
    const _productService = container.resolve<ProductService>(
      SERVICE_IDENTIFIERS.PRODUCT_SERVICE
    );
    const _userService = container.resolve<UserService>(
      SERVICE_IDENTIFIERS.USER_SERVICE
    );
    const auditLogService = container.resolve<AuditLogService>(
      SERVICE_IDENTIFIERS.AUDIT_LOG_SERVICE
    );

    // Get current period data - placeholder values
    // TODO: Implement proper service methods for dashboard analytics
    const currentRevenue = 0;
    const totalOrders = 0;
    const totalCustomers = 0;
    const totalProducts = 0;
    const prevRevenue = 0;
    const previousOrders = 0;
    const previousCustomers = 0;
    const previousProducts = 0;

    // Calculate growth percentages
    const calculateGrowth = (current: number, previous: number) => {
      if (previous === 0) return current > 0 ? 100 : 0;
      return ((current - previous) / previous) * 100;
    };

    // Revenue and products are already available from service calls

    // Get monthly revenue data - placeholder
    // TODO: Implement OrderService.getMonthlyRevenueData method
    const monthlyRevenue = Array.from({ length: 12 }, (_, i) => {
      const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
      return {
        month: date.toLocaleDateString("vi-VN", {
          month: "short",
          year: "numeric",
        }),
        revenue: 0,
        orders: 0,
      };
    }).reverse();

    // Get top products - placeholder
    // TODO: Implement ProductService.getTopSellingProducts method
    const topProductsWithDetails = [
      {
        id: "placeholder",
        name: "Top Products",
        totalSold: 0,
        revenue: 0,
      },
    ];

    // Get top categories - simplified placeholder
    // TODO: Implement CategoryService.getTopCategoriesByRevenue method
    const topCategoriesArray = [
      {
        id: "placeholder",
        name: "Top Categories",
        productCount: 0,
        revenue: 0,
      },
    ];

    // Get order status distribution - simplified placeholder
    // TODO: Implement OrderService.getOrderStatusDistribution method
    const orderStatusData = [
      { status: "PENDING", count: 0, percentage: 0 },
      { status: "CONFIRMED", count: 0, percentage: 0 },
      { status: "DELIVERED", count: 0, percentage: 0 },
    ];

    // Get recent activity using service
    const adminUser = { id: "admin", role: "admin", type: "admin" } as any;
    const recentActivityResult = await auditLogService.getAuditLogs(
      { page: 1, limit: 10 },
      adminUser
    );

    const activityData = recentActivityResult.data.map((log: any) => ({
      id: log.id,
      action: log.action,
      description: log.description || `${log.action} on ${log.resource}`,
      timestamp: new Date(log.createdAt).toLocaleDateString("vi-VN", {
        hour: "2-digit",
        minute: "2-digit",
        day: "2-digit",
        month: "2-digit",
      }),
      type: log.resource.toLowerCase().includes("order")
        ? "order"
        : log.resource.toLowerCase().includes("product")
          ? "product"
          : log.resource.toLowerCase().includes("user")
            ? "user"
            : "system",
    }));

    const dashboardData = {
      overview: {
        totalRevenue: currentRevenue,
        totalOrders,
        totalCustomers,
        totalProducts: totalProducts,
        revenueGrowth: calculateGrowth(currentRevenue, prevRevenue),
        ordersGrowth: calculateGrowth(totalOrders, previousOrders),
        customersGrowth: calculateGrowth(totalCustomers, previousCustomers),
        productsGrowth: calculateGrowth(totalProducts, previousProducts),
      },
      monthlyRevenue,
      topProducts: topProductsWithDetails,
      topCategories: topCategoriesArray,
      ordersByStatus: orderStatusData,
      recentActivity: activityData,
    };

    return NextResponse.json(dashboardData);
  } catch (error) {
    console.error("Dashboard overview error:", error);
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}
