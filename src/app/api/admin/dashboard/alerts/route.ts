import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { adminAuthOptions } from "@/lib/admin-auth";
import { container } from "../../../di-container";
import {
  PRODUCT_SERVICE,
  ORDER_SERVICE,
  AUDIT_LOG_SERVICE,
  NOTIFICATION_SERVICE,
  ADMIN_USER_SERVICE,
} from "../../../services/service-identifiers";
import type { ProductService } from "../../../services/product.service";
import type { OrderService } from "../../../services/order.service";
import type { AuditLogService } from "../../../services/audit-log.service";
import type { NotificationService } from "../../../services/notification.service";
import {
  ProductStatus,
  OrderStatus,
  PaymentStatus,
  UserRole,
} from "@/app/models/common.model";
import type { AdminUserService } from "../../../services/admin-user.service";
import { initializeSystem } from "../../../initialize";

interface SystemAlert {
  id: string;
  type: "warning" | "error" | "info";
  title: string;
  message: string;
  createdAt: string;
  actionUrl?: string;
}

export async function GET(_request: NextRequest) {
  try {
    // Check admin authentication
    const session = await getServerSession(adminAuthOptions);
    if (!session?.user || session.user.type !== "admin") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Initialize system and get services
    initializeSystem();
    const productService = container.resolve<ProductService>(PRODUCT_SERVICE);
    const orderService = container.resolve<OrderService>(ORDER_SERVICE);
    const auditLogService =
      container.resolve<AuditLogService>(AUDIT_LOG_SERVICE);
    const notificationService =
      container.resolve<NotificationService>(NOTIFICATION_SERVICE);
    const adminUserService =
      container.resolve<AdminUserService>(ADMIN_USER_SERVICE);

    // Create admin user entity
    const adminUser = {
      id: session.user.id,
      email: session.user.email || "",
      name: session.user.name || "",
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      role: (session.user.role || "ADMIN") as UserRole,
    };

    const alerts: SystemAlert[] = [];

    // Check for low stock products
    const lowStockProducts = await productService.getLowStockProducts(10, 5);

    if (lowStockProducts.length > 0) {
      alerts.push({
        id: "low-stock-alert",
        type: "warning",
        title: "Sản phẩm sắp hết hàng",
        message: `${lowStockProducts.length} sản phẩm có số lượng tồn kho thấp (≤ 10)`,
        createdAt: new Date().toISOString(),
        actionUrl: "/admin/products?filter=low-stock",
      });
    }

    // Check for out of stock products
    const outOfStockProducts = await productService.getProductCount({
      status: ProductStatus.ACTIVE,
    });

    if (outOfStockProducts > 0) {
      alerts.push({
        id: "out-of-stock-alert",
        type: "error",
        title: "Sản phẩm hết hàng",
        message: `${outOfStockProducts} sản phẩm đã hết hàng`,
        createdAt: new Date().toISOString(),
        actionUrl: "/admin/products?filter=out-of-stock",
      });
    }

    // Check for pending orders
    const pendingOrders = await orderService.getOrderCount({
      status: OrderStatus.PENDING,
    });

    if (pendingOrders > 0) {
      alerts.push({
        id: "pending-orders-alert",
        type: "warning",
        title: "Đơn hàng chờ xử lý",
        message: `${pendingOrders} đơn hàng đã chờ xử lý hơn 24 giờ`,
        createdAt: new Date().toISOString(),
        actionUrl: "/admin/orders?status=pending",
      });
    }

    // Check for failed payments in last 24 hours
    const failedPayments = await orderService.getOrderCount({
      paymentStatus: PaymentStatus.FAILED,
    });

    if (failedPayments > 5) {
      alerts.push({
        id: "failed-payments-alert",
        type: "error",
        title: "Thanh toán thất bại",
        message: `${failedPayments} thanh toán thất bại trong 24 giờ qua`,
        createdAt: new Date().toISOString(),
        actionUrl: "/admin/orders?status=payment-failed",
      });
    }

    // Check for recent system errors
    const recentErrorsResult = await auditLogService.getAuditLogs(
      {
        search: "FAILED",
        limit: 100,
      },
      adminUser
    );
    const recentErrors = recentErrorsResult.total;

    if (recentErrors > 10) {
      alerts.push({
        id: "system-errors-alert",
        type: "error",
        title: "Lỗi hệ thống",
        message: `${recentErrors} lỗi hệ thống trong 1 giờ qua`,
        createdAt: new Date().toISOString(),
        actionUrl: "/admin/audit-logs?filter=failed",
      });
    }

    // Check for unread urgent notifications
    const urgentNotificationsStats =
      await notificationService.getNotificationStats();
    const urgentNotifications = urgentNotificationsStats.unread;

    if (urgentNotifications > 0) {
      alerts.push({
        id: "urgent-notifications-alert",
        type: "warning",
        title: "Thông báo khẩn cấp",
        message: `${urgentNotifications} thông báo khẩn cấp chưa được đọc`,
        createdAt: new Date().toISOString(),
        actionUrl: "/admin/notifications?priority=urgent&read=false",
      });
    }

    // Check for inactive admin users
    const inactiveAdminsResult = await adminUserService.getAdminUsers(
      {
        limit: 100,
      },
      adminUser
    );
    const inactiveAdmins = inactiveAdminsResult.data.filter(
      (admin) => !admin.isActive
    ).length;

    if (inactiveAdmins > 0) {
      alerts.push({
        id: "inactive-admins-alert",
        type: "info",
        title: "Admin không hoạt động",
        message: `${inactiveAdmins} tài khoản admin đang bị vô hiệu hóa`,
        createdAt: new Date().toISOString(),
        actionUrl: "/admin/users?type=admin&status=inactive",
      });
    }

    // Check for high order volume (potential system load)
    const todayOrders = await orderService.getOrderCount({});

    if (todayOrders > 100) {
      // Threshold for high volume
      alerts.push({
        id: "high-order-volume-alert",
        type: "info",
        title: "Khối lượng đơn hàng cao",
        message: `${todayOrders} đơn hàng được tạo hôm nay - theo dõi hiệu suất hệ thống`,
        createdAt: new Date().toISOString(),
        actionUrl: "/admin/orders?date=today",
      });
    }

    // Check for products without images
    const productsWithoutImages = await productService.getProductCount({
      status: ProductStatus.ACTIVE,
    });

    if (productsWithoutImages > 0) {
      alerts.push({
        id: "products-no-images-alert",
        type: "warning",
        title: "Sản phẩm thiếu hình ảnh",
        message: `${productsWithoutImages} sản phẩm đang hoạt động nhưng chưa có hình ảnh`,
        createdAt: new Date().toISOString(),
        actionUrl: "/admin/products?filter=no-images",
      });
    }

    // Sort alerts by severity and creation time
    const sortedAlerts = alerts.sort((a, b) => {
      const severityOrder = { error: 0, warning: 1, info: 2 };
      const severityDiff = severityOrder[a.type] - severityOrder[b.type];
      if (severityDiff !== 0) return severityDiff;

      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });

    return NextResponse.json({
      alerts: sortedAlerts,
      totalAlerts: sortedAlerts.length,
      criticalAlerts: sortedAlerts.filter((a) => a.type === "error").length,
      warningAlerts: sortedAlerts.filter((a) => a.type === "warning").length,
      infoAlerts: sortedAlerts.filter((a) => a.type === "info").length,
    });
  } catch (error) {
    console.error("Error fetching dashboard alerts:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
