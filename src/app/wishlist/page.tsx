"use client";

import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useUser } from "@/contexts";
import { Header, Footer } from "@/components/layout";

import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Heart, Trash2, ArrowLeft, ShoppingCart } from "lucide-react";
import { toast } from "sonner";

export default function WishlistPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const { wishlistItems, loading, removeFromWishlist } = useUser();

  const handleRemoveFromWishlist = async (productId: string) => {
    try {
      await removeFromWishlist(productId);
      toast.success("Đã xóa khỏi danh sách yêu thích");
    } catch {
      toast.error("Có lỗi xảy ra khi xóa sản phẩm");
    }
  };

  if (!session) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <div className="flex-1 bg-gray-50">
          <div className="container mx-auto px-4 py-8">
            <div className="text-center py-16">
              <Heart className="h-24 w-24 text-gray-300 mx-auto mb-6" />
              <h2 className="text-2xl font-semibold mb-4">
                Vui lòng đăng nhập
              </h2>
              <p className="text-muted-foreground mb-8 max-w-md mx-auto">
                Bạn cần đăng nhập để xem danh sách yêu thích của mình.
              </p>
              <Button
                onClick={() =>
                  router.push("/auth/signin?callbackUrl=/wishlist")
                }
                className="bg-pink-600 hover:bg-pink-700"
              >
                Đăng nhập
              </Button>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  if (status !== "authenticated" || loading) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <div className="flex-1 bg-gray-50">
          <div className="container mx-auto px-4 py-8">
            <div className="animate-pulse space-y-6">
              <div className="h-8 bg-gray-200 rounded w-64" />
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {Array.from({ length: 6 }, (_, i) => (
                  <div key={i} className="bg-white rounded-lg p-4">
                    <div className="h-48 bg-gray-200 rounded mb-4" />
                    <div className="h-4 bg-gray-200 rounded mb-2" />
                    <div className="h-4 bg-gray-200 rounded w-2/3" />
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <div className="flex-1 bg-gray-50">
        <div className="container mx-auto px-4 py-8">
          {/* Header */}
          <div className="flex items-center gap-4 mb-8">
            <Button variant="ghost" onClick={() => router.back()}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Quay lại
            </Button>
            <div>
              <h1 className="text-3xl font-bold flex items-center gap-2">
                <Heart className="h-8 w-8 text-pink-600" />
                Danh sách yêu thích
              </h1>
              <p className="text-muted-foreground">
                {wishlistItems ? wishlistItems.length : 0} sản phẩm trong danh
                sách yêu thích
              </p>
            </div>
          </div>

          {!wishlistItems || wishlistItems.length === 0 ? (
            <div className="text-center py-16">
              <Heart className="h-24 w-24 text-gray-300 mx-auto mb-6" />
              <h2 className="text-2xl font-semibold mb-4">
                Danh sách yêu thích trống
              </h2>
              <p className="text-muted-foreground mb-8 max-w-md mx-auto">
                Bạn chưa có sản phẩm nào trong danh sách yêu thích. Hãy khám phá
                và thêm những sản phẩm bạn yêu thích!
              </p>
              <Link href="/products">
                <Button className="bg-pink-600 hover:bg-pink-700">
                  Khám phá sản phẩm
                </Button>
              </Link>
            </div>
          ) : (
            <div className="text-center py-16">
              <Heart className="h-24 w-24 text-pink-300 mx-auto mb-6" />
              <h2 className="text-2xl font-semibold mb-4">
                Danh sách yêu thích
              </h2>
              <p className="text-muted-foreground mb-8 max-w-md mx-auto">
                Bạn có {wishlistItems.length} sản phẩm trong danh sách yêu
                thích. Tính năng hiển thị chi tiết sản phẩm đang được phát
                triển.
              </p>
              <div className="space-y-2">
                {wishlistItems.map((productId, index) => (
                  <div
                    key={productId}
                    className="flex items-center justify-between bg-white p-4 rounded-lg shadow-sm"
                  >
                    <span>
                      Sản phẩm #{index + 1}: {productId}
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleRemoveFromWishlist(productId)}
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Xóa
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Actions */}
          {wishlistItems && wishlistItems.length > 0 && (
            <div className="mt-8 text-center">
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  variant="outline"
                  onClick={() => {
                    if (
                      confirm(
                        "Bạn có chắc chắn muốn xóa tất cả sản phẩm khỏi danh sách yêu thích?"
                      )
                    ) {
                      // Clear all wishlist items
                      if (wishlistItems) {
                        wishlistItems.forEach((productId) => {
                          handleRemoveFromWishlist(productId);
                        });
                      }
                    }
                  }}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Xóa tất cả
                </Button>
                <Link href="/products">
                  <Button className="bg-pink-600 hover:bg-pink-700">
                    Tiếp tục mua sắm
                  </Button>
                </Link>
              </div>
            </div>
          )}
        </div>
      </div>
      <Footer />
    </div>
  );
}
