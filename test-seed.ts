import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function testConnection() {
  console.log("Testing database connection...");

  try {
    // Test basic connection
    const userCount = await prisma.user.count();
    console.log(`Current user count: ${userCount}`);

    // Test creating a simple user
    const testUser = await prisma.user.upsert({
      where: { email: "<EMAIL>" },
      update: {},
      create: {
        email: "<EMAIL>",
        name: "Test User",
        password: "test123",
      },
    });

    console.log("Test user created:", testUser.email);
    console.log("✅ Database connection test successful!");
  } catch (error) {
    console.error("❌ Database test failed:", error);
  } finally {
    await prisma.$disconnect();
  }
}

testConnection();
