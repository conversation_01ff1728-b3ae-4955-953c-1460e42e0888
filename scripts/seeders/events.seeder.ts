/**
 * Events Seeder
 * Creates promotional events and marketing campaigns
 */

import { BaseSeeder } from "./lib/base-seeder";

export class EventsSeeder extends BaseSeeder {
  getName(): string {
    return "Events";
  }

  async seed(): Promise<any[]> {
    this.logStart();

    // Get admin user for event creation
    const adminUser = await this.prisma.adminUser.findFirst({
      where: { role: "ADMIN" }
    });

    if (!adminUser) {
      this.logWarning("No admin user found. Please run AdminUserSeeder first.");
      return [];
    }

    // Get some media for event images
    const eventMedia = await this.prisma.media.findMany({
      where: { 
        folder: "categories",
        isActive: true
      },
      take: 10
    });

    const now = new Date();
    const eventsData = [
      {
        title: "Black Friday 2024",
        description: "Giảm giá lên đến 70% tất cả sản phẩm thời trang",
        content: `
          <h2>Black Friday - Ng<PERSON>y hội mua sắm lớn nhất năm!</h2>
          <p>Ch<PERSON>o mừng bạn đến với sự kiện Black Friday 2024 tại NS Shop!</p>
          <ul>
            <li>Giảm giá lên đến 70% tất cả sản phẩm</li>
            <li>Miễn phí vận chuyển cho đơn hàng từ 500.000đ</li>
            <li>Tặng voucher 100.000đ cho 100 khách hàng đầu tiên</li>
            <li>Ưu đãi đặc biệt cho thành viên VIP</li>
          </ul>
        `,
        slug: "black-friday-2024",
        type: "SALE",
        status: "PUBLISHED",
        startDate: new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        endDate: new Date(now.getTime() + 33 * 24 * 60 * 60 * 1000), // 3 days sale
        isAllDay: true,
        location: "Online",
        maxAttendees: null,
        currentAttendees: 0,
        price: null,
        currency: "VND",
        tags: ["sale", "blackfriday", "fashion", "discount"],
        metadata: {
          discountPercentage: 70,
          freeShippingThreshold: 500000,
          voucherValue: 100000,
          voucherQuantity: 100
        },
        seoTitle: "Black Friday 2024 - Giảm giá lên đến 70% | NS Shop",
        seoDescription: "Đừng bỏ lỡ Black Friday 2024 tại NS Shop với ưu đãi giảm giá lên đến 70% tất cả sản phẩm thời trang.",
        createdBy: adminUser.id,
      },
      {
        title: "Bộ sưu tập Xuân Hè 2024",
        description: "Ra mắt bộ sưu tập thời trang Xuân Hè mới nhất",
        content: `
          <h2>Bộ sưu tập Xuân Hè 2024 - Tươi mới và năng động!</h2>
          <p>Khám phá những xu hướng thời trang mới nhất cho mùa Xuân Hè 2024.</p>
          <p>Bộ sưu tập bao gồm:</p>
          <ul>
            <li>Váy đầm nhẹ nhàng với họa tiết hoa</li>
            <li>Áo thun basic thoáng mát</li>
            <li>Quần short phong cách năng động</li>
            <li>Phụ kiện thời trang trendy</li>
          </ul>
        `,
        slug: "bo-suu-tap-xuan-he-2024",
        type: "LAUNCH",
        status: "PUBLISHED",
        startDate: new Date(now.getTime() + 15 * 24 * 60 * 60 * 1000),
        endDate: new Date(now.getTime() + 90 * 24 * 60 * 60 * 1000),
        isAllDay: true,
        location: "Online & All Stores",
        tags: ["collection", "spring", "summer", "fashion", "new"],
        metadata: {
          collectionId: "SS2024",
          preOrderDiscount: 15
        },
        seoTitle: "Bộ sưu tập Xuân Hè 2024 - Thời trang tươi mới | NS Shop",
        seoDescription: "Khám phá bộ sưu tập Xuân Hè 2024 với những thiết kế tươi mới và năng động nhất.",
        createdBy: adminUser.id,
      },
      {
        title: "Workshop Phối đồ Chuyên nghiệp",
        description: "Học cách phối đồ từ các chuyên gia thời trang",
        content: `
          <h2>Workshop Phối đồ Chuyên nghiệp</h2>
          <p>Tham gia workshop học cách phối đồ từ các chuyên gia hàng đầu!</p>
          <h3>Nội dung workshop:</h3>
          <ul>
            <li>Nguyên tắc cơ bản về phối màu</li>
            <li>Cách chọn trang phục phù hợp với vóc dáng</li>
            <li>Phối đồ cho các dịp khác nhau</li>
            <li>Xu hướng thời trang hiện tại</li>
          </ul>
          <p><strong>Thời gian:</strong> 14:00 - 17:00</p>
          <p><strong>Địa điểm:</strong> NS Shop Flagship Store, Q1, TP.HCM</p>
        `,
        slug: "workshop-phoi-do-chuyen-nghiep",
        type: "WORKSHOP",
        status: "PUBLISHED",
        startDate: new Date(now.getTime() + 20 * 24 * 60 * 60 * 1000),
        endDate: new Date(now.getTime() + 20 * 24 * 60 * 60 * 1000 + 3 * 60 * 60 * 1000), // 3 hours
        isAllDay: false,
        location: "NS Shop Flagship Store, 123 Nguyễn Trãi, Q1, TP.HCM",
        maxAttendees: 30,
        currentAttendees: 0,
        price: 200000,
        currency: "VND",
        tags: ["workshop", "fashion", "styling", "education"],
        metadata: {
          instructors: ["Nguyễn Minh Tú", "Trần Thanh Hà"],
          materials: ["Tài liệu hướng dẫn", "Voucher giảm giá 20%"],
          requirements: ["Không yêu cầu kinh nghiệm"]
        },
        seoTitle: "Workshop Phối đồ Chuyên nghiệp - Học từ chuyên gia | NS Shop",
        seoDescription: "Tham gia workshop phối đồ chuyên nghiệp, học cách tạo phong cách thời trang ấn tượng từ các chuyên gia hàng đầu.",
        createdBy: adminUser.id,
      },
      {
        title: "Flash Sale Cuối Tuần",
        description: "Giảm giá 50% trong 48 giờ - Số lượng có hạn!",
        content: `
          <h2>Flash Sale Cuối Tuần</h2>
          <p>Cơ hội không thể bỏ lỡ! Giảm giá 50% chỉ trong 48 giờ!</p>
          <h3>Sản phẩm áp dụng:</h3>
          <ul>
            <li>Áo thun basic - Giá chỉ từ 150.000đ</li>
            <li>Váy đầm công sở - Giá chỉ từ 300.000đ</li>
            <li>Quần jeans - Giá chỉ từ 250.000đ</li>
            <li>Phụ kiện thời trang - Giá chỉ từ 50.000đ</li>
          </ul>
          <p><strong>Lưu ý:</strong> Số lượng có hạn, hết hàng không bù!</p>
        `,
        slug: "flash-sale-cuoi-tuan",
        type: "SALE",
        status: "SCHEDULED",
        startDate: new Date(now.getTime() + 5 * 24 * 60 * 60 * 1000),
        endDate: new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000),
        isAllDay: true,
        location: "Online",
        tags: ["flash-sale", "weekend", "limited", "50-percent"],
        metadata: {
          discountPercentage: 50,
          isLimitedQuantity: true,
          urgency: "high"
        },
        seoTitle: "Flash Sale Cuối Tuần - Giảm 50% chỉ 48 giờ | NS Shop",
        seoDescription: "Flash Sale cuối tuần với ưu đãi giảm giá 50% trong 48 giờ. Số lượng có hạn, mua ngay kẻo lỡ!",
        createdBy: adminUser.id,
      },
      {
        title: "Ngày Phụ nữ Việt Nam 20/10",
        description: "Chương trình ưu đãi đặc biệt dành cho phái đẹp",
        content: `
          <h2>Chào mừng Ngày Phụ nữ Việt Nam 20/10</h2>
          <p>NS Shop gửi tặng những ưu đãi đặc biệt đến tất cả chị em phụ nữ!</p>
          <h3>Ưu đãi trong tháng 10:</h3>
          <ul>
            <li>Giảm 30% tất cả sản phẩm dành cho nữ</li>
            <li>Tặng kèm phụ kiện thời trang cho đơn hàng từ 800.000đ</li>
            <li>Ưu đãi đặc biệt cho khách hàng sinh nhật tháng 10</li>
            <li>Chương trình tích điểm đổi quà hấp dẫn</li>
          </ul>
        `,
        slug: "ngay-phu-nu-viet-nam-2024",
        type: "SEASONAL",
        status: "DRAFT",
        startDate: new Date(2024, 9, 1), // October 1st, 2024
        endDate: new Date(2024, 9, 31), // October 31st, 2024
        isAllDay: true,
        location: "Online & All Stores",
        tags: ["womens-day", "october", "seasonal", "women"],
        metadata: {
          targetGender: "female",
          discountPercentage: 30,
          giftThreshold: 800000,
          birthdayBonus: true
        },
        seoTitle: "Ngày Phụ nữ Việt Nam 20/10 - Ưu đãi đặc biệt | NS Shop",
        seoDescription: "Chào mừng Ngày Phụ nữ Việt Nam với những ưu đãi đặc biệt dành cho phái đẹp tại NS Shop.",
        createdBy: adminUser.id,
      }
    ];

    const createdEvents = [];
    for (let i = 0; i < eventsData.length; i++) {
      const eventData = eventsData[i];
      
      // Assign random media if available
      const randomMedia = eventMedia.length > 0 ? 
        this.dataGenerator.randomChoice(eventMedia) : null;

      const event = await this.upsertRecord(
        this.prisma.event,
        { slug: eventData.slug },
        {
          ...eventData,
          imageId: randomMedia?.id || null,
        }
      );
      createdEvents.push(event);
    }

    this.logSuccess(createdEvents.length, "events");
    return createdEvents;
  }
}