/**
 * Admin User Seeder
 * Creates admin and moderator accounts
 */

import { BaseSeeder } from './lib/base-seeder';
import bcrypt from 'bcryptjs';

export class AdminUserSeeder extends BaseSeeder {
  getName(): string {
    return 'Admin Users';
  }

  async seed(): Promise<{ admin: any; moderator: any }> {
    this.logStart();

    // Create admin user
    const admin = await this.upsertRecord(
      this.prisma.adminUser,
      { email: '<EMAIL>' },
      {
        email: '<EMAIL>',
        password: await bcrypt.hash('admin123', 12),
        name: 'NS Shop Admin',
        role: 'ADMIN',
        isActive: true,
        lastLoginAt: new Date(),
      }
    );

    // Create moderator user
    const moderator = await this.upsertRecord(
      this.prisma.adminUser,
      { email: '<EMAIL>' },
      {
        email: '<EMAIL>',
        password: await bcrypt.hash('moderator123', 12),
        name: 'NS Shop Moderator',
        role: 'MODERATOR',
        isActive: true,
        lastLoginAt: new Date(),
      }
    );

    this.logSuccess(2, 'admin users');
    return { admin, moderator };
  }
}
