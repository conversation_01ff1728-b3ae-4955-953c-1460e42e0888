/**
 * Promotions Seeder
 * Creates discount codes and promotional campaigns
 */

import { BaseSeeder } from "./lib/base-seeder";

export class PromotionsSeeder extends BaseSeeder {
  getName(): string {
    return "Promotions";
  }

  async seed(): Promise<any[]> {
    this.logStart();

    // Get some categories and products for targeted promotions
    const categories = await this.prisma.category.findMany({
      where: { parentId: null },
      take: 5,
    });

    const products = await this.prisma.product.findMany({
      where: { status: "ACTIVE" },
      take: 10,
    });

    const now = new Date();
    const promotionsData = [
      {
        name: "<PERSON>h<PERSON>ch hàng mới - Giảm 15%",
        description: "Ưu đãi chào mừng khách hàng mới đăng ký",
        code: "WELCOME15",
        type: "PERCENTAGE",
        value: 15,
        minOrderAmount: 300000,
        maxDiscountAmount: 100000,
        usageLimit: 1000,
        usageCount: 0,
        userUsageLimit: 1,
        startDate: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000), // Started 1 week ago
        endDate: new Date(now.getTime() + 90 * 24 * 60 * 60 * 1000), // Valid for 3 months
        isActive: true,
        applicableProducts: [],
        applicableCategories: [],
        excludedProducts: [],
        excludedCategories: [],
        createdBy: "admin",
      },
      {
        name: "Flash Sale - Giảm 100K",
        description: "Giảm ngay 100.000đ cho đơn hàng từ 1.000.000đ",
        code: "FLASH100K",
        type: "FIXED_AMOUNT",
        value: 100000,
        minOrderAmount: 1000000,
        maxDiscountAmount: null,
        usageLimit: 500,
        usageCount: 0,
        userUsageLimit: 1,
        startDate: new Date(now.getTime() + 1 * 24 * 60 * 60 * 1000), // Starts tomorrow
        endDate: new Date(now.getTime() + 8 * 24 * 60 * 60 * 1000), // Valid for 1 week
        isActive: true,
        applicableProducts: [],
        applicableCategories: [],
        excludedProducts: [],
        excludedCategories: [],
        createdBy: "admin",
      },
      {
        name: "Miễn phí vận chuyển",
        description: "Miễn phí vận chuyển cho đơn hàng từ 500.000đ",
        code: "FREESHIP",
        type: "FREE_SHIPPING",
        value: 0,
        minOrderAmount: 500000,
        maxDiscountAmount: null,
        usageLimit: null, // Unlimited
        usageCount: 0,
        userUsageLimit: null, // Unlimited per user
        startDate: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000), // Started 1 month ago
        endDate: null, // No expiry
        isActive: true,
        applicableProducts: [],
        applicableCategories: [],
        excludedProducts: [],
        excludedCategories: [],
        createdBy: "admin",
      },
      {
        name: "Áo thun - Giảm 25%",
        description: "Giảm 25% tất cả áo thun trong cửa hàng",
        code: "TSHIRT25",
        type: "PERCENTAGE",
        value: 25,
        minOrderAmount: 200000,
        maxDiscountAmount: 200000,
        usageLimit: 300,
        usageCount: 0,
        userUsageLimit: 3,
        startDate: new Date(now.getTime() + 3 * 24 * 60 * 60 * 1000),
        endDate: new Date(now.getTime() + 17 * 24 * 60 * 60 * 1000), // 2 weeks
        isActive: true,
        applicableProducts: [],
        applicableCategories:
          categories.length > 0
            ? [categories.find((c) => c.slug.includes("ao-thun"))?.id].filter(
                Boolean
              )
            : [],
        excludedProducts: [],
        excludedCategories: [],
        createdBy: "admin",
      },
      {
        name: "Combo 2 sản phẩm - Giảm 20%",
        description: "Mua 2 sản phẩm bất kỳ, giảm ngay 20%",
        code: "COMBO20",
        type: "PERCENTAGE",
        value: 20,
        minOrderAmount: 600000,
        maxDiscountAmount: 300000,
        usageLimit: 200,
        usageCount: 0,
        userUsageLimit: 2,
        startDate: new Date(now.getTime() + 5 * 24 * 60 * 60 * 1000),
        endDate: new Date(now.getTime() + 35 * 24 * 60 * 60 * 1000), // 1 month
        isActive: true,
        applicableProducts: [],
        applicableCategories: [],
        excludedProducts: [],
        excludedCategories: [],
        createdBy: "admin",
      },
      {
        name: "VIP Member - Giảm 30%",
        description: "Ưu đãi đặc biệt dành cho thành viên VIP",
        code: "VIP30",
        type: "PERCENTAGE",
        value: 30,
        minOrderAmount: 800000,
        maxDiscountAmount: 500000,
        usageLimit: 100,
        usageCount: 0,
        userUsageLimit: 5,
        startDate: new Date(now.getTime() - 10 * 24 * 60 * 60 * 1000),
        endDate: new Date(now.getTime() + 50 * 24 * 60 * 60 * 1000),
        isActive: true,
        applicableProducts: [],
        applicableCategories: [],
        excludedProducts: [],
        excludedCategories: [],
        createdBy: "admin",
      },
      {
        name: "Black Friday - Giảm 50%",
        description: "Black Friday Sale - Giảm tới 50%",
        code: "BLACKFRIDAY50",
        type: "PERCENTAGE",
        value: 50,
        minOrderAmount: 1500000,
        maxDiscountAmount: 1000000,
        usageLimit: 50,
        usageCount: 0,
        userUsageLimit: 1,
        startDate: new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000),
        endDate: new Date(now.getTime() + 33 * 24 * 60 * 60 * 1000), // 3 days only
        isActive: true,
        applicableProducts: [],
        applicableCategories: [],
        excludedProducts: [],
        excludedCategories: [],
        createdBy: "admin",
      },
      {
        name: "Sinh nhật NS Shop - Giảm 35%",
        description: "Kỷ niệm sinh nhật NS Shop với ưu đãi đặc biệt",
        code: "BIRTHDAY35",
        type: "PERCENTAGE",
        value: 35,
        minOrderAmount: 700000,
        maxDiscountAmount: 400000,
        usageLimit: 150,
        usageCount: 0,
        userUsageLimit: 2,
        startDate: new Date(now.getTime() + 60 * 24 * 60 * 60 * 1000),
        endDate: new Date(now.getTime() + 67 * 24 * 60 * 60 * 1000), // 1 week
        isActive: true,
        applicableProducts: [],
        applicableCategories: [],
        excludedProducts: [],
        excludedCategories: [],
        createdBy: "admin",
      },
    ];

    const createdPromotions = [];
    for (const promotionData of promotionsData) {
      const promotion = await this.upsertRecord(
        this.prisma.promotion,
        { code: promotionData.code },
        promotionData
      );
      createdPromotions.push(promotion);
    }

    this.logSuccess(createdPromotions.length, "promotions");

    // Show summary of active vs scheduled promotions
    const activeCount = createdPromotions.filter(
      (p: any) =>
        new Date(p.startDate) <= now &&
        (p.endDate === null || new Date(p.endDate) > now)
    ).length;

    const scheduledCount = createdPromotions.filter(
      (p: any) => new Date(p.startDate) > now
    ).length;

    console.log(`   └── Active promotions: ${activeCount}`);
    console.log(`   └── Scheduled promotions: ${scheduledCount}`);

    return createdPromotions;
  }
}
