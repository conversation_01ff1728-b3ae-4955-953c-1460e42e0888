# NS Shop Database Seeders

This directory contains database seeders for the NS Shop application. The seeders create sample data for development and testing purposes.

## Features

- **Complete E-commerce Setup**: Full business-ready database with 15 seeders
- **Picsum Integration**: Uses placeholder images from Picsum Photos for all media records
- **Safe Relations**: All foreign key relationships are verified before creation
- **Vietnamese Content**: Sample data is tailored for Vietnamese e-commerce
- **Progressive Seeding**: Seeders run in dependency order across 8 phases
- **Business Operations**: Contact info, menus, shipping, promotions ready to use
- **Marketing Tools**: Events, promotions, email templates for campaigns
- **SEO Optimized**: Complete SEO settings and page configurations

## Setup

No additional setup required! The seeders use [Picsum Photos](https://picsum.photos/) which provides placeholder images without requiring an API key.

## Usage

Run all seeders:
```bash
npm run db:seed
```

Reset database and run seeders:
```bash
npm run db:reset
```

## Seeder Order

### Phase 1: Core System Data
1. **AdminUserSeeder** - Creates admin accounts
2. **SettingsSeeder** - Creates system settings

### Phase 2: Media Assets  
3. **MediaSeeder** - Creates media records with Picsum placeholder images

### Phase 3: Product Catalog Foundation
4. **BrandsSeeder** - Creates fashion brands with logos
5. **CategoriesSeeder** - Creates product categories with images
6. **AttributesSeeder** - Creates product attributes (size, color, etc.)

### Phase 4: Users and Customers
7. **UsersSeeder** - Creates customer accounts with addresses

### Phase 5: Products and Inventory
8. **ProductsSeeder** - Creates products with media, attributes, and inventory

### Phase 6: Business Operations
9. **ContactInfoSeeder** - Creates shop contact information
10. **MenuSeeder** - Creates navigation menus (header, footer, mobile)
11. **ShippingSeeder** - Creates shipping zones and methods for Vietnam

### Phase 7: Marketing & Communications
12. **EventsSeeder** - Creates promotional events and campaigns
13. **PromotionsSeeder** - Creates discount codes and promotions
14. **EmailSMTPSeeder** - Creates email templates and SMTP configs

### Phase 8: SEO & Optimization
15. **SEOSeeder** - Creates SEO settings and page configurations

## Image Categories

The seeders use different seed ranges for various categories to ensure visual variety:

- **Brands**: Seeds 10-99 (consistent brand-style images)
- **Categories**: Seeds 110-199 (category-appropriate images)  
- **T-shirts**: Seeds 310-399
- **Dresses**: Seeds 410-499
- **Jeans**: Seeds 510-599
- **Jackets**: Seeds 610-699
- **Accessories**: Seeds 710-799
- **Shoes**: Seeds 810-899
- **General Fashion**: Seeds 100-899

## Safety Features

- **Relation Verification**: All foreign keys are checked before creation
- **Duplicate Prevention**: Prevents duplicate relationships
- **Error Handling**: Graceful fallback for API failures
- **Consistent Images**: Uses seeded random images for reproducible results

## Environment Variables

```env
# Database connection (only required environment variable)
DATABASE_URL="postgresql://username:password@localhost:5432/database_name"
```

## Troubleshooting

### Image Loading Issues
- Picsum Photos uses placeholder images and should always work
- Images are served via CDN and cached automatically
- No API keys or rate limits to worry about

### Relation Errors
- Ensure seeders run in the correct order
- Check that required data exists before running dependent seeders
- Review console warnings for missing relationships

### Performance
- Seeding complete database takes 1-2 minutes
- ~200+ media records with Picsum images
- Progress indicators show current status for each phase
- Images are generated instantly without API calls
- All seeders include safe relation checks