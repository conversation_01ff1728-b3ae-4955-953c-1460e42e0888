/**
 * Base Seeder Class
 * Provides common functionality for all seeders
 */

import { PrismaClient } from '@prisma/client';
import { DataGenerator } from './data-generator';

export abstract class BaseSeeder {
  protected prisma: PrismaClient;
  protected dataGenerator: typeof DataGenerator;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
    this.dataGenerator = DataGenerator;
  }

  /**
   * Abstract method that each seeder must implement
   */
  abstract seed(): Promise<any>;

  /**
   * Get seeder name for logging
   */
  abstract getName(): string;

  /**
   * Log seeding start
   */
  protected logStart(): void {
    console.log(`🌱 Seeding ${this.getName()}...`);
  }

  /**
   * Log seeding success
   */
  protected logSuccess(count: number, itemName: string = 'items'): void {
    console.log(`✅ ${this.getName()} seeded successfully: ${count} ${itemName}`);
  }

  /**
   * Log seeding error
   */
  protected logError(error: any): void {
    console.error(`❌ Error seeding ${this.getName()}:`, error);
  }

  /**
   * Log warning
   */
  protected logWarning(message: string): void {
    console.log(`⚠️ ${this.getName()}: ${message}`);
  }

  /**
   * Create media record for external images
   */
  protected async createMediaRecord(data: {
    filename: string;
    folder: string;
    url: string;
    alt?: string;
    title?: string;
    width?: number;
    height?: number;
  }): Promise<any> {
    return await this.prisma.media.create({
      data: {
        filename: data.filename,
        path: `/${data.folder}/${data.filename}`,
        url: data.url,
        mimeType: 'image/jpeg',
        size: this.dataGenerator.randomInt(100000, 600000),
        width: data.width || 400,
        height: data.height || 400,
        alt: data.alt || data.title || data.filename,
        title: data.title || data.filename,
        folder: data.folder,
        type: 'EXTERNAL',
        externalUrl: data.url,
      },
    });
  }

  /**
   * Create media record with Picsum image
   */
  protected async createPicsumMediaRecord(data: {
    filename: string;
    folder: string;
    category: string;
    alt?: string;
    title?: string;
    width?: number;
    height?: number;
  }): Promise<any> {
    const width = data.width || 400;
    const height = data.height || 400;
    const url = this.dataGenerator.generateImageUrl(data.category, width, height);
    
    return await this.createMediaRecord({
      filename: data.filename,
      folder: data.folder,
      url,
      alt: data.alt,
      title: data.title,
      width,
      height,
    });
  }

  /**
   * Get random existing records from a table
   */
  protected async getRandomRecords<T>(
    model: any,
    count: number,
    where?: any
  ): Promise<T[]> {
    const totalCount = await model.count({ where });
    if (totalCount === 0) return [];

    const skip = Math.max(0, Math.floor(Math.random() * (totalCount - count)));
    return await model.findMany({
      where,
      skip,
      take: count,
    });
  }

  /**
   * Get a random existing record from a table
   */
  protected async getRandomRecord<T>(model: any, where?: any): Promise<T | null> {
    const records = await this.getRandomRecords<T>(model, 1, where);
    return records[0] || null;
  }

  /**
   * Check if records exist in a table
   */
  protected async hasRecords(model: any, where?: any): Promise<boolean> {
    const count = await model.count({ where });
    return count > 0;
  }

  /**
   * Get all records from a table
   */
  protected async getAllRecords<T>(model: any, where?: any): Promise<T[]> {
    return await model.findMany({ where });
  }

  /**
   * Batch create records with error handling
   */
  protected async batchCreate<T>(
    model: any,
    data: any[],
    batchSize: number = 100
  ): Promise<T[]> {
    const results: T[] = [];
    
    for (let i = 0; i < data.length; i += batchSize) {
      const batch = data.slice(i, i + batchSize);
      
      try {
        for (const item of batch) {
          const created = await model.create({ data: item });
          results.push(created);
        }
      } catch (error) {
        console.error(`Error in batch ${i / batchSize + 1}:`, error);
        throw error;
      }
    }
    
    return results;
  }

  /**
   * Upsert records (create or update)
   */
  protected async upsertRecord<T>(
    model: any,
    where: any,
    data: any,
    update?: any
  ): Promise<T> {
    return await model.upsert({
      where,
      create: data,
      update: update || data,
    });
  }

  /**
   * Generate unique slug
   */
  protected async generateUniqueSlug(
    model: any,
    baseSlug: string,
    field: string = 'slug'
  ): Promise<string> {
    let slug = baseSlug;
    let counter = 1;

    while (true) {
      const existing = await model.findFirst({
        where: { [field]: slug },
      });

      if (!existing) {
        return slug;
      }

      slug = `${baseSlug}-${counter}`;
      counter++;
    }
  }

  /**
   * Generate unique code
   */
  protected async generateUniqueCode(
    model: any,
    baseCode: string,
    field: string = 'code',
    length: number = 8
  ): Promise<string> {
    let code = baseCode;
    let counter = 1;

    while (true) {
      const existing = await model.findFirst({
        where: { [field]: code },
      });

      if (!existing) {
        return code;
      }

      code = `${baseCode}${counter.toString().padStart(length - baseCode.length, '0')}`;
      counter++;
    }
  }

  /**
   * Wait for a short time (useful for rate limiting)
   */
  protected async wait(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Shuffle array
   */
  protected shuffle<T>(array: T[]): T[] {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  }

  /**
   * Generate weighted random choice
   */
  protected weightedRandomChoice<T>(items: T[], weights: number[]): T {
    if (items.length !== weights.length) {
      throw new Error('Items and weights arrays must have the same length');
    }

    const totalWeight = weights.reduce((sum, weight) => sum + weight, 0);
    let random = Math.random() * totalWeight;

    for (let i = 0; i < items.length; i++) {
      random -= weights[i];
      if (random <= 0) {
        return items[i];
      }
    }

    return items[items.length - 1];
  }

  /**
   * Generate date range
   */
  protected generateDateRange(startDaysAgo: number, endDaysAgo: number = 0): {
    start: Date;
    end: Date;
  } {
    const now = new Date();
    const start = new Date(now.getTime() - startDaysAgo * 24 * 60 * 60 * 1000);
    const end = new Date(now.getTime() - endDaysAgo * 24 * 60 * 60 * 1000);
    
    return { start, end };
  }

  /**
   * Generate random date between two dates
   */
  protected randomDateBetween(start: Date, end: Date): Date {
    const startTime = start.getTime();
    const endTime = end.getTime();
    const randomTime = startTime + Math.random() * (endTime - startTime);
    return new Date(randomTime);
  }
}
