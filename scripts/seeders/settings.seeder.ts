/**
 * Settings Seeder
 * Creates system settings
 */

import { BaseSeeder } from "./lib/base-seeder";

export class SettingsSeeder extends BaseSeeder {
  getName(): string {
    return "Settings";
  }

  async seed(): Promise<any[]> {
    this.logStart();

    const settings = [
      {
        key: "site_name",
        value: "NS Shop",
        type: "string",
      },
      {
        key: "site_description",
        value: "Thời trang hiện đại cho mọi phong cách",
        type: "string",
      },
      {
        key: "contact_email",
        value: "<EMAIL>",
        type: "string",
      },
      {
        key: "contact_phone",
        value: "0901234567",
        type: "string",
      },
      {
        key: "contact_address",
        value: "123 Nguyễn Trãi, Quận 1, TP.HCM",
        type: "string",
      },
      {
        key: "currency",
        value: "VND",
        type: "string",
      },
      {
        key: "tax_rate",
        value: 10,
        type: "number",
      },
      {
        key: "free_shipping_threshold",
        value: 500000,
        type: "number",
      },
      {
        key: "max_cart_items",
        value: 50,
        type: "number",
      },
      {
        key: "enable_reviews",
        value: true,
        type: "boolean",
      },
    ];

    const createdSettings = [];
    for (const setting of settings) {
      const created = await this.upsertRecord(
        this.prisma.setting,
        { key: setting.key },
        setting
      );
      createdSettings.push(created);
    }

    this.logSuccess(createdSettings.length, "settings");
    return createdSettings;
  }
}
