/**
 * <PERSON><PERSON> Seeder
 * Creates SEO settings and page-specific SEO configurations
 */

import { BaseSeeder } from "./lib/base-seeder";

export class SEOSeeder extends BaseSeeder {
  getName(): string {
    return "SEO";
  }

  async seed(): Promise<any[]> {
    this.logStart();

    // Create global SEO settings
    let seoSettings = await this.prisma.sEOSettings.findFirst({
      where: { siteName: "NS Shop" }
    });
    
    if (!seoSettings) {
      seoSettings = await this.prisma.sEOSettings.create({
        data: {
          siteName: "NS Shop",
        siteDescription:
          "Cửa hàng thời trang trực tuyến hàng đầu Việt Nam với những xu hướng mới nhất và chất lượng tốt nhất.",
        siteKeywords: [
          "thời trang",
          "quần áo",
          "v<PERSON><PERSON> đầm",
          "áo thun",
          "quần jeans",
          "ph<PERSON> kiện",
          "gi<PERSON>y dép",
          "túi xách",
          "online shopping",
          "NS Shop",
        ],
        defaultTitle: "NS Shop - Thời trang trực tuyến",
        titleTemplate: "%s | NS Shop",
        defaultDescription:
          "Khám phá bộ sưu tập thời trang đa dạng tại NS Shop. Giao hàng nhanh, đổi trả dễ dàng, ưu đãi hấp dẫn.",
        defaultImage: "https://nsshop.com/images/og-default.jpg",

        // Social media settings
        ogSiteName: "NS Shop",
        ogType: "website",
        twitterSite: "@nsshop",
        twitterCreator: "@nsshop",

        // Technical SEO
        robotsTxt: `User-agent: *
Allow: /

# Sitemaps
Sitemap: https://nsshop.com/sitemap.xml
Sitemap: https://nsshop.com/products-sitemap.xml
Sitemap: https://nsshop.com/categories-sitemap.xml

# Disallow admin and private areas
Disallow: /admin/
Disallow: /api/
Disallow: /cart/
Disallow: /checkout/
Disallow: /account/
Disallow: /search?*

# Allow important directories
Allow: /api/products/
Allow: /api/categories/`,
        sitemapEnabled: true,
        sitemapFrequency: "weekly",

        // Analytics (placeholder values)
        googleAnalyticsId: "GA_MEASUREMENT_ID",
        googleTagManagerId: "GTM_CONTAINER_ID",
        facebookPixelId: "FB_PIXEL_ID",

        // Schema.org settings
        organizationName: "NS Shop Vietnam",
        organizationLogo: "https://nsshop.com/images/logo.png",
        organizationType: "Organization",
        contactEmail: "<EMAIL>",
        contactPhone: "+***********",
        address: {
          streetAddress: "123 Nguyễn Trãi",
          addressLocality: "Quận 1",
          addressRegion: "TP. Hồ Chí Minh",
          postalCode: "700000",
          addressCountry: "VN",
        },

        // Search Console verification
        googleSiteVerification: "google_verification_code",
        bingSiteVerification: "bing_verification_code",
        }
      });
    }

    // Create page-specific SEO settings
    const pageSEOData = [
      {
        path: "/",
        title: "NS Shop - Thời trang trực tuyến hàng đầu Việt Nam",
        description:
          "Mua sắm thời trang online tại NS Shop với hàng nghìn sản phẩm chất lượng cao. Giao hàng miễn phí, đổi trả dễ dàng, ưu đãi hấp dẫn mỗi ngày.",
        keywords: [
          "thời trang",
          "mua sắm online",
          "quần áo",
          "giày dép",
          "phụ kiện",
          "NS Shop",
        ],
        ogTitle: "NS Shop - Thời trang trực tuyến hàng đầu Việt Nam",
        ogDescription:
          "Khám phá thế giới thời trang đa dạng tại NS Shop. Từ casual đến formal, từ cơ bản đến trendy - chúng tôi có tất cả!",
        ogImage: "https://nsshop.com/images/og-homepage.jpg",
        ogType: "website",
        twitterTitle: "NS Shop - Thời trang trực tuyến",
        twitterDescription: "Mua sắm thời trang thông minh tại NS Shop",
        twitterImage: "https://nsshop.com/images/twitter-homepage.jpg",
        twitterCard: "summary_large_image",
        priority: 1.0,
        changefreq: "daily",
        isActive: true,
      },
      {
        path: "/products",
        title: "Tất cả sản phẩm - Thời trang NS Shop",
        description:
          "Duyệt qua hàng nghìn sản phẩm thời trang chất lượng cao tại NS Shop. Áo quần, giày dép, phụ kiện với giá tốt nhất.",
        keywords: [
          "sản phẩm thời trang",
          "áo quần",
          "giày dép",
          "phụ kiện",
          "mua sắm",
        ],
        ogTitle: "Sản phẩm thời trang chất lượng cao",
        ogDescription:
          "Khám phá bộ sưu tập thời trang đa dạng với hàng nghìn lựa chọn",
        priority: 0.9,
        changefreq: "daily",
        isActive: true,
      },
      {
        path: "/categories/*",
        title: "{{category_name}} - Danh mục sản phẩm | NS Shop",
        description:
          "Mua {{category_name}} chất lượng cao tại NS Shop. Đa dạng mẫu mã, giá cả phải chăng, giao hàng nhanh chóng.",
        keywords: ["{{category_name}}", "thời trang", "mua sắm online"],
        priority: 0.8,
        changefreq: "weekly",
        isActive: true,
      },
      {
        path: "/products/*",
        title: "{{product_name}} - {{category_name}} | NS Shop",
        description:
          "{{product_description}} Mua ngay tại NS Shop với giá ưu đãi. Giao hàng miễn phí, đổi trả trong 30 ngày.",
        keywords: ["{{product_name}}", "{{category_name}}", "thời trang"],
        ogTitle: "{{product_name}} - NS Shop",
        ogDescription: "{{product_description}}",
        ogImage: "{{product_image}}",
        ogType: "product",
        priority: 0.7,
        changefreq: "weekly",
        isActive: true,
      },
      {
        path: "/brands",
        title: "Thương hiệu thời trang - NS Shop",
        description:
          "Khám phá các thương hiệu thời trang nổi tiếng tại NS Shop. Từ local brand đến quốc tế, chất lượng được đảm bảo.",
        keywords: ["thương hiệu thời trang", "brand", "nhãn hiệu"],
        priority: 0.6,
        changefreq: "monthly",
        isActive: true,
      },
      {
        path: "/brands/*",
        title: "{{brand_name}} - Thương hiệu thời trang | NS Shop",
        description:
          "Sản phẩm thời trang từ {{brand_name}} tại NS Shop. Chính hãng, chất lượng cao, giá cả hợp lý.",
        keywords: ["{{brand_name}}", "thương hiệu", "thời trang"],
        priority: 0.6,
        changefreq: "weekly",
        isActive: true,
      },
      {
        path: "/blog",
        title: "Blog thời trang - Xu hướng & Cẩm nang | NS Shop",
        description:
          "Cập nhật xu hướng thời trang mới nhất, cẩm nang phối đồ và tips làm đẹp từ các chuyên gia tại NS Shop Blog.",
        keywords: ["blog thời trang", "xu hướng", "cẩm nang phối đồ", "tips"],
        priority: 0.5,
        changefreq: "weekly",
        isActive: true,
      },
      {
        path: "/about-us",
        title: "Về chúng tôi - NS Shop",
        description:
          "Tìm hiểu về NS Shop - câu chuyện thương hiệu, sứ mệnh và cam kết mang đến trải nghiệm mua sắm tốt nhất.",
        keywords: ["về chúng tôi", "NS Shop", "thông tin công ty"],
        priority: 0.3,
        changefreq: "monthly",
        isActive: true,
      },
      {
        path: "/contact",
        title: "Liên hệ - NS Shop",
        description:
          "Liên hệ với NS Shop qua hotline, email hoặc đến trực tiếp cửa hàng. Chúng tôi luôn sẵn sàng hỗ trợ bạn 24/7.",
        keywords: ["liên hệ", "hỗ trợ khách hàng", "địa chỉ cửa hàng"],
        priority: 0.4,
        changefreq: "monthly",
        isActive: true,
      },
      {
        path: "/privacy-policy",
        title: "Chính sách bảo mật - NS Shop",
        description:
          "Chính sách bảo mật thông tin khách hàng của NS Shop. Cam kết bảo vệ dữ liệu cá nhân một cách an toàn.",
        keywords: ["chính sách bảo mật", "bảo vệ thông tin"],
        priority: 0.2,
        changefreq: "yearly",
        noindex: false,
        nofollow: false,
        isActive: true,
      },
      {
        path: "/terms-of-service",
        title: "Điều khoản sử dụng - NS Shop",
        description:
          "Điều khoản và điều kiện sử dụng dịch vụ của NS Shop. Vui lòng đọc kỹ trước khi sử dụng.",
        keywords: ["điều khoản sử dụng", "điều kiện"],
        priority: 0.2,
        changefreq: "yearly",
        noindex: false,
        nofollow: false,
        isActive: true,
      },
      {
        path: "/return-policy",
        title: "Chính sách đổi trả - NS Shop",
        description:
          "Chính sách đổi trả hàng hóa của NS Shop. Đổi trả miễn phí trong 30 ngày, điều kiện áp dụng.",
        keywords: ["chính sách đổi trả", "hoàn tiền", "bảo hành"],
        priority: 0.4,
        changefreq: "monthly",
        isActive: true,
      },
      {
        path: "/shopping-guide",
        title: "Hướng dẫn mua hàng - NS Shop",
        description:
          "Hướng dẫn mua hàng online tại NS Shop từ A-Z. Đặt hàng, thanh toán, giao nhận một cách dễ dàng.",
        keywords: ["hướng dẫn mua hàng", "đặt hàng online", "thanh toán"],
        priority: 0.4,
        changefreq: "monthly",
        isActive: true,
      },
      {
        path: "/size-guide",
        title: "Bảng size chuẩn - Hướng dẫn chọn size | NS Shop",
        description:
          "Bảng size chuẩn cho quần áo, giày dép tại NS Shop. Hướng dẫn đo và chọn size phù hợp.",
        keywords: ["bảng size", "hướng dẫn chọn size", "đo size"],
        priority: 0.5,
        changefreq: "monthly",
        isActive: true,
      },
      {
        path: "/search",
        title: "Tìm kiếm sản phẩm - NS Shop",
        description:
          "Tìm kiếm sản phẩm thời trang tại NS Shop. Sử dụng bộ lọc để tìm đúng sản phẩm bạn cần.",
        keywords: ["tìm kiếm", "lọc sản phẩm"],
        priority: 0.3,
        changefreq: "daily",
        noindex: true,
        nofollow: true,
        isActive: true,
      },
    ];

    const createdPageSEO = [];
    for (const pageData of pageSEOData) {
      const pageSEO = await this.upsertRecord(
        this.prisma.pageSEO,
        { path: pageData.path },
        pageData
      );
      createdPageSEO.push(pageSEO);
    }

    this.logSuccess(1, "SEO settings");
    console.log(
      `   └── Created ${createdPageSEO.length} page SEO configurations`
    );

    // Show summary by page type
    const staticPages = createdPageSEO.filter(
      (p: any) => !p.path.includes("*")
    ).length;
    const dynamicPages = createdPageSEO.filter((p: any) =>
      p.path.includes("*")
    ).length;
    const noindexPages = createdPageSEO.filter((p: any) => p.noindex).length;

    console.log(`   └── Static pages: ${staticPages}`);
    console.log(`   └── Dynamic page templates: ${dynamicPages}`);
    console.log(`   └── No-index pages: ${noindexPages}`);

    return [seoSettings, ...createdPageSEO];
  }
}
