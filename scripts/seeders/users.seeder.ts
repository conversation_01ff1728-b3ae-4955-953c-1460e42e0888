/**
 * Users Seeder
 * Creates customer accounts with addresses
 */

import { BaseSeeder } from "./lib/base-seeder";
import bcrypt from "bcryptjs";

export class UsersSeeder extends BaseSeeder {
  getName(): string {
    return "Users";
  }

  async seed(): Promise<any[]> {
    this.logStart();

    const userCount = 20;
    const createdUsers = [];

    for (let i = 1; i <= userCount; i++) {
      const name = this.dataGenerator.generateVietnameseName();
      const email = this.dataGenerator.generateEmail(name);
      const phone = this.dataGenerator.generatePhoneNumber();
      const address = this.dataGenerator.generateVietnameseAddress();

      // Create user
      const user = await this.upsertRecord<any>(
        this.prisma.user,
        { email },
        {
          email,
          password: await bcrypt.hash("password123", 12),
          name,
          phone,
          isActive: true,
          dateOfBirth: this.dataGenerator.randomBoolean(0.6)
            ? this.dataGenerator.generatePastDate(365 * 30) // 18-48 years old
            : null,
          gender: this.dataGenerator.randomBoolean(0.8)
            ? this.dataGenerator.randomChoice(["MALE", "FEMALE"])
            : null,
        }
      );
      createdUsers.push(user);

      // Create user address
      await this.prisma.address.create({
        data: {
          userId: user.id,
          fullName: name,
          phone: phone,
          address: address.street,
          ward: address.ward,
          district: address.district,
          province: address.province,
          isDefault: true,
        },
      });

      // 30% chance to have additional address
      if (this.dataGenerator.randomBoolean(0.3)) {
        const additionalAddress =
          this.dataGenerator.generateVietnameseAddress();
        await this.prisma.address.create({
          data: {
            userId: user.id,
            fullName: name,
            phone: phone,
            address: additionalAddress.street,
            ward: additionalAddress.ward,
            district: additionalAddress.district,
            province: additionalAddress.province,
            isDefault: false,
          },
        });
      }
    }

    this.logSuccess(createdUsers.length, "users");

    // Count addresses created
    const addressCount = await this.prisma.address.count();
    console.log(`   └── Created ${addressCount} addresses`);

    return createdUsers;
  }
}
