/**
 * Contact Info Seeder
 * Creates shop contact information records
 */

import { BaseSeeder } from "./lib/base-seeder";

export class ContactInfoSeeder extends BaseSeeder {
  getName(): string {
    return "ContactInfo";
  }

  async seed(): Promise<any[]> {
    this.logStart();

    const contactInfoData = [
      {
        phone: "0901234567",
        email: "<EMAIL>",
        website: "https://nsshop.com",
        address: "123 Nguyễn Trãi",
        district: "Quận 1",
        city: "TP. Hồ Chí Minh",
        province: "TP. Hồ Chí Minh",
        postalCode: "700000",
        country: "Vietnam",
        googleMapsUrl: "https://maps.google.com/?q=10.762622,106.660172",
        googleMapsEmbed: "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3919.4326!2d106.660172!3d10.762622",
        latitude: 10.762622,
        longitude: 106.660172,
        businessHours: {
          monday: { open: "08:00", close: "22:00", closed: false },
          tuesday: { open: "08:00", close: "22:00", closed: false },
          wednesday: { open: "08:00", close: "22:00", closed: false },
          thursday: { open: "08:00", close: "22:00", closed: false },
          friday: { open: "08:00", close: "22:00", closed: false },
          saturday: { open: "08:00", close: "23:00", closed: false },
          sunday: { open: "09:00", close: "21:00", closed: false }
        },
        facebookUrl: "https://facebook.com/nsshop",
        instagramUrl: "https://instagram.com/nsshop",
        tiktokUrl: "https://tiktok.com/@nsshop",
        youtubeUrl: "https://youtube.com/@nsshop",
        zaloUrl: "https://zalo.me/nsshop",
        whatsappNumber: "+***********",
        telegramUsername: "@nsshop",
        skypeId: "nsshop.official",
        isActive: true,
        displayOrder: 1
      },
      {
        phone: "0287654321",
        email: "<EMAIL>",
        address: "456 Hoàng Hoa Thám",
        district: "Ba Đình",
        city: "Hà Nội",
        province: "Hà Nội",
        postalCode: "100000",
        country: "Vietnam",
        googleMapsUrl: "https://maps.google.com/?q=21.028511,105.804841",
        latitude: 21.028511,
        longitude: 105.804841,
        businessHours: {
          monday: { open: "08:00", close: "21:00", closed: false },
          tuesday: { open: "08:00", close: "21:00", closed: false },
          wednesday: { open: "08:00", close: "21:00", closed: false },
          thursday: { open: "08:00", close: "21:00", closed: false },
          friday: { open: "08:00", close: "21:00", closed: false },
          saturday: { open: "08:00", close: "22:00", closed: false },
          sunday: { open: "09:00", close: "20:00", closed: false }
        },
        isActive: true,
        displayOrder: 2
      },
      {
        phone: "0236123456",
        email: "<EMAIL>",
        address: "789 Trần Phú",
        district: "Hải Châu",
        city: "Đà Nẵng",
        province: "Đà Nẵng",
        postalCode: "550000",
        country: "Vietnam",
        googleMapsUrl: "https://maps.google.com/?q=16.047079,108.206230",
        latitude: 16.047079,
        longitude: 108.206230,
        businessHours: {
          monday: { open: "08:30", close: "21:30", closed: false },
          tuesday: { open: "08:30", close: "21:30", closed: false },
          wednesday: { open: "08:30", close: "21:30", closed: false },
          thursday: { open: "08:30", close: "21:30", closed: false },
          friday: { open: "08:30", close: "21:30", closed: false },
          saturday: { open: "08:30", close: "22:30", closed: false },
          sunday: { open: "09:00", close: "21:00", closed: false }
        },
        isActive: true,
        displayOrder: 3
      }
    ];

    const createdContactInfo = [];
    for (const contactData of contactInfoData) {
      // Check if contact info with this phone already exists
      const existing = await this.prisma.contactInfo.findFirst({
        where: {
          phone: contactData.phone,
          email: contactData.email
        }
      });

      let contactInfo;
      if (existing) {
        // Update existing record
        contactInfo = await this.prisma.contactInfo.update({
          where: { id: existing.id },
          data: contactData
        });
      } else {
        // Create new record
        contactInfo = await this.prisma.contactInfo.create({
          data: contactData
        });
      }
      createdContactInfo.push(contactInfo);
    }

    this.logSuccess(createdContactInfo.length, "contact info records");
    return createdContactInfo;
  }
}