import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkData() {
  try {
    console.log('🔍 Checking database data...\n');

    // Check products
    const productCount = await prisma.product.count();
    const products = await prisma.product.findMany({
      take: 5,
      include: {
        category: true,
        brand: true,
        media: {
          include: {
            media: true
          }
        }
      }
    });

    console.log(`📦 Products: ${productCount} total`);
    if (products.length > 0) {
      console.log('Sample products:');
      products.forEach(p => {
        console.log(`  - ${p.name} (${p.sku}) - ${p.price.toLocaleString()}đ - Category: ${p.category.name} - Brand: ${p.brand?.name || 'N/A'}`);
      });
    }
    console.log('');

    // Check categories
    const categoryCount = await prisma.category.count();
    const categories = await prisma.category.findMany({
      include: {
        children: true,
        _count: {
          select: {
            products: true
          }
        }
      }
    });

    console.log(`📂 Categories: ${categoryCount} total`);
    categories.forEach(c => {
      console.log(`  - ${c.name} (${c.slug}) - ${c._count.products} products`);
      if (c.children.length > 0) {
        c.children.forEach(child => {
          console.log(`    └─ ${child.name} (${child.slug})`);
        });
      }
    });
    console.log('');

    // Check brands
    const brandCount = await prisma.brand.count();
    const brands = await prisma.brand.findMany({
      include: {
        _count: {
          select: {
            products: true
          }
        }
      }
    });

    console.log(`🏷️ Brands: ${brandCount} total`);
    brands.forEach(b => {
      console.log(`  - ${b.name} (${b.slug}) - ${b._count.products} products`);
    });
    console.log('');

    // Check users
    const userCount = await prisma.user.count();
    console.log(`👥 Users: ${userCount} total`);

    // Check orders
    const orderCount = await prisma.order.count();
    console.log(`🛒 Orders: ${orderCount} total`);

    // Check inventory
    const inventoryCount = await prisma.inventoryEntry.count();
    console.log(`📊 Inventory entries: ${inventoryCount} total`);

    // Check media
    const mediaCount = await prisma.media.count();
    console.log(`🖼️ Media files: ${mediaCount} total`);

  } catch (error) {
    console.error('❌ Error checking data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkData();
