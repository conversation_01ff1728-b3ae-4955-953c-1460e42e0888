import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function checkAllTables() {
  console.log("🔍 Checking all database tables...\n");

  try {
    // Check all models and their counts
    const results = await Promise.all([
      // Core entities
      prisma.user.count(),
      prisma.adminUser.count(),
      prisma.product.count(),
      prisma.category.count(),
      prisma.brand.count(),
      prisma.attribute.count(),
      prisma.attributeValue.count(),
      prisma.media.count(),

      // Product related
      prisma.productAttribute.count(),
      prisma.productMedia.count(),
      prisma.inventoryEntry.count(),
      prisma.stockMovement.count(),

      // Shopping flow
      prisma.cart.count(),
      prisma.cartItem.count(),
      prisma.order.count(),
      prisma.orderItem.count(),
      prisma.address.count(),
      prisma.wishlistItem.count(),

      // Content
      prisma.review.count(),
      prisma.reviewMedia.count(),
      prisma.post.count(),
      prisma.page.count(),
      prisma.menu.count(),
      prisma.menuItem.count(),
      prisma.event.count(),

      // System
      prisma.setting.count(),
      prisma.emailTemplate.count(),
      prisma.emailLog.count(),
      prisma.notification.count(),
      prisma.auditLog.count(),

      // E-commerce features
      prisma.promotion.count(),
      prisma.paymentGateway.count(),
      prisma.paymentTransaction.count(),
      prisma.shippingZone.count(),
      prisma.shippingMethod.count(),

      // SEO & Config
      prisma.sEOSettings.count(),
      prisma.pageSEO.count(),
      prisma.sMTPConfig.count(),

      // Security
      prisma.passwordResetToken.count(),
    ]);

    const tableData = [
      // Core entities
      { name: "User", count: results[0], priority: "✅ Seeded" },
      { name: "AdminUser", count: results[1], priority: "✅ Seeded" },
      { name: "Product", count: results[2], priority: "✅ Seeded" },
      { name: "Category", count: results[3], priority: "✅ Seeded" },
      { name: "Brand", count: results[4], priority: "✅ Seeded" },
      { name: "Attribute", count: results[5], priority: "✅ Seeded" },
      { name: "AttributeValue", count: results[6], priority: "✅ Seeded" },
      { name: "Media", count: results[7], priority: "✅ Seeded" },

      // Product related
      {
        name: "ProductAttribute",
        count: results[8],
        priority:
          results[8] > 0
            ? "✅ Has data"
            : "🔴 HIGH - Link products to attributes",
      },
      { name: "ProductMedia", count: results[9], priority: "✅ Seeded" },
      { name: "InventoryEntry", count: results[10], priority: "✅ Seeded" },
      { name: "StockMovement", count: results[11], priority: "✅ Seeded" },

      // Shopping flow
      { name: "Cart", count: results[12], priority: "✅ Seeded" },
      {
        name: "CartItem",
        count: results[13],
        priority:
          results[13] > 0 ? "✅ Has data" : "🔴 HIGH - Cart items for testing",
      },
      {
        name: "Order",
        count: results[14],
        priority: results[14] > 0 ? "✅ Has data" : "🔴 HIGH - Order history",
      },
      {
        name: "OrderItem",
        count: results[15],
        priority: results[15] > 0 ? "✅ Has data" : "🔴 HIGH - Order details",
      },
      { name: "Address", count: results[16], priority: "✅ Seeded" },
      {
        name: "WishlistItem",
        count: results[17],
        priority: results[17] > 0 ? "✅ Has data" : "🔴 HIGH - Wishlist items",
      },

      // Content
      {
        name: "Review",
        count: results[18],
        priority: results[18] > 0 ? "✅ Has data" : "🔴 HIGH - Product reviews",
      },
      {
        name: "ReviewMedia",
        count: results[19],
        priority: results[19] > 0 ? "✅ Has data" : "🟡 MED - Review images",
      },
      {
        name: "Post",
        count: results[20],
        priority: results[20] > 0 ? "✅ Has data" : "🟡 MED - Blog posts",
      },
      {
        name: "Page",
        count: results[21],
        priority: results[21] > 0 ? "✅ Has data" : "🟡 MED - Static pages",
      },
      {
        name: "Menu",
        count: results[22],
        priority: results[22] > 0 ? "✅ Has data" : "🟡 MED - Navigation menu",
      },
      {
        name: "MenuItem",
        count: results[23],
        priority: results[23] > 0 ? "✅ Has data" : "🟡 MED - Menu items",
      },
      {
        name: "Event",
        count: results[24],
        priority: results[24] > 0 ? "✅ Has data" : "⚪ LOW - Events",
      },

      // System
      { name: "Setting", count: results[25], priority: "✅ Seeded" },
      { name: "EmailTemplate", count: results[26], priority: "✅ Seeded" },
      {
        name: "EmailLog",
        count: results[27],
        priority: results[27] > 0 ? "✅ Has data" : "⚪ LOW - Email logs",
      },
      {
        name: "Notification",
        count: results[28],
        priority: results[28] > 0 ? "✅ Has data" : "⚪ LOW - Notifications",
      },
      {
        name: "AuditLog",
        count: results[29],
        priority: results[29] > 0 ? "✅ Has data" : "⚪ LOW - Audit logs",
      },

      // E-commerce features
      {
        name: "Promotion",
        count: results[30],
        priority: results[30] > 0 ? "✅ Has data" : "🟡 MED - Promotions",
      },
      {
        name: "PaymentGateway",
        count: results[31],
        priority: results[31] > 0 ? "✅ Has data" : "🟡 MED - Payment gateways",
      },
      {
        name: "PaymentTransaction",
        count: results[32],
        priority: results[32] > 0 ? "✅ Has data" : "⚪ LOW - Payment logs",
      },
      {
        name: "ShippingZone",
        count: results[33],
        priority: results[33] > 0 ? "✅ Has data" : "🟡 MED - Shipping zones",
      },
      {
        name: "ShippingMethod",
        count: results[34],
        priority: results[34] > 0 ? "✅ Has data" : "🟡 MED - Shipping methods",
      },

      // SEO & Config
      {
        name: "SEOSettings",
        count: results[35],
        priority: results[35] > 0 ? "✅ Has data" : "⚪ LOW - SEO settings",
      },
      {
        name: "PageSEO",
        count: results[36],
        priority: results[36] > 0 ? "✅ Has data" : "⚪ LOW - Page SEO",
      },
      {
        name: "SMTPConfig",
        count: results[37],
        priority: results[37] > 0 ? "✅ Has data" : "⚪ LOW - SMTP config",
      },

      // Security
      {
        name: "PasswordResetToken",
        count: results[38],
        priority: results[38] > 0 ? "✅ Has data" : "⚪ LOW - Reset tokens",
      },
    ];

    // Group by priority
    const highPriority = tableData.filter((t) =>
      t.priority.includes("🔴 HIGH")
    );
    const medPriority = tableData.filter((t) => t.priority.includes("🟡 MED"));
    const lowPriority = tableData.filter((t) => t.priority.includes("⚪ LOW"));
    const seeded = tableData.filter((t) => t.priority.includes("✅"));

    console.log("📊 DATABASE STATUS SUMMARY\n");

    console.log("🔴 HIGH PRIORITY (Missing - Critical for e-commerce):");
    highPriority.forEach((t) =>
      console.log(`  - ${t.name}: ${t.count} records - ${t.priority}`)
    );

    console.log("\n🟡 MEDIUM PRIORITY (Missing - Important for UX):");
    medPriority.forEach((t) =>
      console.log(`  - ${t.name}: ${t.count} records - ${t.priority}`)
    );

    console.log("\n⚪ LOW PRIORITY (Missing - Admin/System features):");
    lowPriority.forEach((t) =>
      console.log(`  - ${t.name}: ${t.count} records - ${t.priority}`)
    );

    console.log("\n✅ ALREADY SEEDED:");
    seeded.forEach((t) => console.log(`  - ${t.name}: ${t.count} records`));

    console.log(
      `\n📈 SUMMARY: ${seeded.length}/${tableData.length} tables have data`
    );
    console.log(`🔴 High priority missing: ${highPriority.length}`);
    console.log(`🟡 Medium priority missing: ${medPriority.length}`);
    console.log(`⚪ Low priority missing: ${lowPriority.length}`);
  } catch (error) {
    console.error("❌ Error checking tables:", error);
  } finally {
    await prisma.$disconnect();
  }
}

checkAllTables();
