code --add-mcp '{"name":"repomix","command":"npx","args":["-y","repomix","--mcp"]}'
code --add-mcp '{
    "mcpServers": {
        "serena": {
            "command": "uvx",
            "args": ["--from", "git+https://github.com/oraios/serena", "serena-mcp-server"]
        }
    }
}'

claude mcp add repomix -- npx -y repomix --mcp
claude mcp add serena -- uvx --from git+https://github.com/oraios/serena serena-mcp-server --context ide-assistant --project $(pwd)